/* client/src/styles/main.css */

:root {
  --primary-color: #6C63FF;
  --secondary-color: #FF6584;
  --accent-color: #43C59E;
  --dark-color: #333333;
  --light-color: #FFFFFF;
  --background-color: #F8F9FA;
  --card-background: #FFFFFF;
  --border-radius: 8px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;

  /* Energy Colors */
  --artha-color: #F39C12;
  --karma-color: #27AE60;
  --gnana-color: #2980B9;
  --bhakti-color: #8E44AD;

  /* Region Colors - More vibrant and distinct */
  --north-color: rgba(33, 150, 243, 0.95);      /* Vibrant blue */
  --west-color: rgba(76, 175, 80, 0.95);        /* Vibrant green */
  --south-color: rgba(255, 191, 102, 0.95);     /* Light orange */
  --central-color: rgba(156, 39, 176, 0.95);    /* Vibrant purple */
  --east-color: #fd7c03ef;        /* Orange */
  --northeast-color: rgba(233, 30, 99, 0.95);   /* Vibrant pink */
  --jyotirlinga-color: rgba(255, 236, 130, 0.95); /* Light yellow for jyotirlingas */
  --airport-color: rgba(103, 58, 183, 0.95);    /* Vibrant indigo for airports */

  /* Journey Types */
  --inner-color: rgba(25, 118, 210, 0.95);     /* Dark blue for inner journey */
  --outer-color: rgba(56, 142, 60, 0.95);      /* Dark green for outer journey */
}

/* Base Styles */
body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background: var(--background-color);
  color: var(--dark-color);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0.5rem 0;
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
}

h2 {
  font-size: 1.8rem;
  color: var(--dark-color);
}

h3 {
  font-size: 1.5rem;
}

/* Button Styles */
button {
  cursor: pointer;
  padding: 0.6rem 1.2rem;
  background: var(--primary-color);
  color: var(--light-color);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

button:hover {
  background: #5A52D5;
  transform: translateY(-2px);
}

button:active {
  transform: translateY(0);
}

button.secondary {
  background: var(--secondary-color);
}

button.secondary:hover {
  background: #E55A78;
}

button.accent {
  background: var(--accent-color);
}

button.accent:hover {
  background: #3AB08D;
}

/* Form Elements */
input {
  padding: 0.8rem 1rem;
  border: 1px solid #DDD;
  border-radius: var(--border-radius);
  font-size: 1rem;
  width: 100%;
  transition: var(--transition);
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(108, 99, 255, 0.2);
}

/* Card Styles */
.card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

/* Player Mat Styles */
.player-mat {
  border-radius: var(--border-radius);
  padding: 1rem;
  background: var(--card-background);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.player-mat.active {
  border: 2px solid var(--accent-color);
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Game Board Layout */
.game-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.board-container {
  background-color: #f5f5f5;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  position: relative;
  margin: 20px 0;
  height: 600px;
}

.face-up-cards {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 1.5rem 0;
}

.card-item {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 1rem;
  width: 150px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.card-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Energy Cube Visual Styles */
.energy-cube {
  font-weight: bold;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.energy-cube.artha {
  background-color: var(--artha-color);
}

.energy-cube.karma {
  background-color: var(--karma-color);
}

.energy-cube.gnana {
  background-color: var(--gnana-color);
}

.energy-cube.bhakti {
  background-color: var(--bhakti-color);
}

/* Selectable Energy Cubes */
.energy-cube.selectable {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid transparent;
}

.energy-cube.selectable:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}

.energy-cube.selected {
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #333;
  transform: translateY(-3px);
}

/* Character Card Styles */
.character-card {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.character-card-image {
  width: 120px;
  height: 120px;
  background-color: #eee;
  border-radius: 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  color: #555;
  border: 1px solid #ccc;
  overflow: hidden;
  object-fit: cover;
  position: relative;
}

.character-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-card-title {
  font-weight: 600;
  font-size: 1rem;
  color: #333;
  margin-bottom: 4px;
  text-align: center;
}

.character-card-description {
  font-size: 0.8rem;
  color: #666;
  text-align: center;
}

.trade-btn {
  margin-top: 10px;
  width: 100%;
  padding: 8px;
  font-size: 0.9rem;
}

.board-svg {
  height: 100%;
  width: 100%;
  display: block;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.board-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

.board-fullscreen-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.board-fullscreen-content {
  width: 100%;
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.board-fullscreen-svg {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-image: url('../../public/assets/images/map_take_2.jpg');
  background-size: cover;
  background-position: center;
}

.location-label {
  position: absolute;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.location-label-bg {
  fill: rgba(255, 255, 255, 0.8);
  stroke: rgba(0, 0, 0, 0.3);
  stroke-width: 1;
  rx: 4;
  ry: 4;
  filter: drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.2));
}

.location-label-text {
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  font-weight: 500;
  fill: #333;
  text-anchor: middle;
  dominant-baseline: middle;
  filter: drop-shadow(0px 1px 1px rgba(255, 255, 255, 0.7));
}

.node-region-north,
.node-region-west,
.node-region-south,
.node-region-central,
.node-region-east,
.node-region-northeast {
  stroke: white;
  stroke-width: 2;
  cursor: pointer;
  transition: all 0.2s ease;
}

.node-region-jyotirlinga {
  fill: var(--jyotirlinga-color);
}

.node-region-airport {
  fill: var(--airport-color);
}

.node-region-jyotirlinga:hover,
.node-region-airport:hover,
.node-region-north:hover,
.node-region-west:hover,
.node-region-south:hover,
.node-region-central:hover,
.node-region-east:hover,
.node-region-northeast:hover {
  stroke-width: 3;
  transform: scale(1.1);
}

.node {
  cursor: pointer;
  transition: transform 0.2s ease, filter 0.2s ease;
  filter: drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.3));
}

.node-group {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.node-group:hover .location-label-background {
  opacity: 1;
  transform: scale(1.05);
  fill: rgba(255, 255, 255, 0.9);
  filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.3));
}

.node-label-container {
  transition: opacity 0.3s ease;
}

.hide-labels .node-label-container {
  opacity: 0;
}

.node-group:hover .enhanced-location-label {
  opacity: 1;
  transform: translateY(-5px);
}

.enhanced-location-label {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  transition: all 0.3s ease;
}

.location-label-background {
  fill: rgba(255, 255, 255, 0.7);
  stroke: rgba(0, 0, 0, 0.1);
  stroke-width: 1;
  rx: 4;
  ry: 4;
  transition: fill 0.3s ease, transform 0.3s ease, filter 0.3s ease;
  filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.2));
}

path {
  stroke: #fff;
  stroke-width: 2.5;
  stroke-linecap: round;
  fill: none;
  opacity: 0.8;
  filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.2));
  transition: stroke-width 0.2s ease, opacity 0.2s ease;
}

path:hover {
  stroke-width: 4;
  opacity: 1;
  filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.3));
  cursor: pointer;
}

.board-controls {
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
  padding: 10px;
  position: absolute;
  right: 340px;
  top: 20px;
}

.board-controls button {
  font-size: 0.85rem;
  padding: 6px 12px;
  margin-bottom: 5px;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.gap-sm {
  gap: 0.5rem;
}

.gap-md {
  gap: 1rem;
}

.gap-lg {
  gap: 1.5rem;
}

.mt-sm {
  margin-top: 0.5rem;
}

.mt-md {
  margin-top: 1rem;
}

.mt-lg {
  margin-top: 1.5rem;
}

/* Game Over Modal */
.game-over {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.game-over h1 {
  color: white;
  font-size: 3rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.5rem;
  color: var(--primary-color);
  font-weight: 600;
}

.loading::after {
  content: "...";
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: "."; }
  40% { content: ".."; }
  60%, 100% { content: "..."; }
}

.app-navigation {
  display: flex;
  background-color: var(--primary-color);
  padding: 15px 20px;
}

.app-navigation a {
  color: var(--light-color);
  text-decoration: none;
  padding: 10px 20px;
  border-radius: 5px;
  margin-right: 10px;
  position: relative;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.app-navigation a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.app-navigation a.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.app-navigation a.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10%;
  width: 80%;
  height: 3px;
  background-color: var(--light-color);
  border-radius: 3px 3px 0 0;
}

.compact-player-mat.active {
  border: 2px solid var(--accent-color);
}

.energy-cube.artha, .energy-cube.karma, .energy-cube.gnana, .energy-cube.bhakti {
  color: white;
  text-align: center;
}

.board-fullscreen ::-webkit-scrollbar {
  width: 10px;
}

.board-fullscreen ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.board-fullscreen ::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}

.board-fullscreen ::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.node-highlighted {
  animation: pulse 1.5s infinite alternate;
  filter: drop-shadow(0px 0px 8px rgba(255, 255, 255, 0.8));
}

@keyframes pulse {
  0% {
    transform: scale(1);
    filter: drop-shadow(0px 0px 5px rgba(255, 255, 255, 0.5));
  }
  100% {
    transform: scale(1.15);
    filter: drop-shadow(0px 0px 15px rgba(255, 255, 255, 0.9));
  }
}

@keyframes moveAnimation {
  0% {
    transform: translate(var(--start-x), var(--start-y));
  }
  10% {
    transform: translate(calc(var(--start-x) + (var(--end-x) - var(--start-x)) * 0.1), 
                         calc(var(--start-y) + (var(--end-y) - var(--start-y)) * 0.1 - 20px));
  }
  90% {
    transform: translate(calc(var(--start-x) + (var(--end-x) - var(--start-x)) * 0.9), 
                         calc(var(--start-y) + (var(--end-y) - var(--start-y)) * 0.9 - 20px));
  }
  100% {
    transform: translate(var(--end-x), var(--end-y));
  }
}

.card-animation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 2000;
  perspective: 1000px;
}

.animated-card {
  position: absolute;
  width: 80px;
  height: 120px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  animation: cardGlow 1.5s infinite alternate;
}

@keyframes cardGlow {
  0% {
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8),
                0 0 20px rgba(108, 99, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.9),
                0 0 30px rgba(108, 99, 255, 0.8);
  }
}

.card-animation-container .animated-card {
  animation: moveToHand 1s forwards;
  animation-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Game setup styles */
.game-setup {
  max-width: 500px;
  margin: 2rem auto;
  padding: 2rem;
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.game-setup h2 {
  text-align: center;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.setup-options {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-group label {
  font-weight: 500;
  color: var(--dark-color);
}

.option-group select,
.option-group input {
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.game-setup button {
  margin-top: 1rem;
  padding: 0.8rem 0;
  width: 100%;
  background: var(--primary-color);
  color: white;
  font-weight: 500;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.game-setup button:hover {
  background: #5A52D5;
  transform: translateY(-2px);
}

.game-setup button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

/* Game title and header styles */
.game-title {
  text-align: center;
  color: var(--primary-color);
  margin: 1rem 0;
}

.game-header {
  display: flex;
  justify-content: space-between;
  background: var(--card-background);
  padding: 1rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 1.5rem;
}

.current-player, .game-round {
  font-size: 1.1rem;
}

/* Fullscreen button styles */
.fullscreen-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 5;
  background-color: rgba(255, 255, 255, 0.8);
}

/* Player information panel styles */
.player-info-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 320px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow-y: auto;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 20px;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.player-info-panel::-webkit-scrollbar {
  width: 6px;
}

.player-info-panel::-webkit-scrollbar-track {
  background: transparent;
}

.player-info-panel::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.player-information, .actions-panel {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 15px;
}

.player-information h3, .actions-panel h3 {
  color: var(--primary-color);
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.player-stats {
  margin-bottom: 15px;
}

.resources-section, .travel-cards-section, .journey-cards-section {
  margin-bottom: 20px;
}

.resources-section h4, .travel-cards-section h4, .journey-cards-section h4 {
  color: var(--dark-color);
  margin-bottom: 10px;
}

.energy-cubes-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.energy-cube-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.travel-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.travel-card {
  width: 70px;
  height: 90px;
  background-color: #f0f7ff;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  padding: 10px;
  position: relative;
  overflow: hidden;
}

.travel-card.car {
  background-color: #e3f2fd;
}

.travel-card.bus {
  background-color: #e8f5e9;
}

.travel-card.truck {
  background-color: #fff3e0;
}

.travel-card.train {
  background-color: #f3e5f5;
}

.travel-card-value {
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 5px;
}

.travel-card-type {
  font-size: 0.8rem;
  text-align: center;
  text-transform: capitalize;
}

.travel-card::before {
  content: '';
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.travel-card.car::before {
  background-image: url('../../public/assets/images/vehicles/2/car.png');
  background-size: contain;
}

.travel-card.bus::before {
  background-image: url('../../public/assets/images/vehicles/2/bus.png');
  background-size: contain;
}

.travel-card.truck::before {
  background-image: url('../../public/assets/images/vehicles/3/truck.png');
  background-size: contain;
}

.travel-card.train::before {
  background-image: url('../../public/assets/images/vehicles/3/train.png');
  background-size: contain;
}

.journey-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
}

.journey-card {
  width: 120px;
  padding: 10px;
  border-radius: 5px;
  background-color: #f5f5f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.journey-card:hover {
  transform: translateY(-3px);
}

.journey-card.inner {
  background-color: var(--inner-color);
  color: white;
}

.journey-card.outer {
  background-color: var(--outer-color);
  color: white;
}

.journey-location {
  font-size: 0.8rem;
  margin-bottom: 5px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.journey-points {
  font-weight: bold;
  font-size: 1rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.move-button, .trade-button, .end-turn-button {
  padding: 10px;
  border-radius: var(--border-radius);
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.move-button {
  background-color: var(--primary-color);
  color: white;
}

.trade-button {
  background-color: var(--accent-color);
  color: white;
}

.end-turn-button {
  background-color: var(--secondary-color);
  color: white;
}

.move-button:hover, .trade-button:hover, .end-turn-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Adjust player pieces current player indicator */
.player-piece.current-player {
  filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.8));
  animation: pulse 1.5s infinite alternate;
}

/* Add region background colors for nodes */
.node-region-north {
  fill: var(--north-color);
}

.node-region-west {
  fill: var(--west-color);
}

.node-region-south {
  fill: var(--south-color);
}

.node-region-central {
  fill: var(--central-color);
}

.node-region-east {
  fill: var(--east-color);
}

.node-region-northeast {
  fill: var(--northeast-color);
}