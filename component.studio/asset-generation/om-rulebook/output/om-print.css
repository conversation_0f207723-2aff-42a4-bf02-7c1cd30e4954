@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Bebas+Neue:wght@400&family=Lato:wght@400;700&display=swap");
@page {
  size: letter;
  /* uniform ¾‑inch margins all around */
  margin: 0.75in;
  /* page numbers only (bottom‑centre) */
  @bottom-center {
    content: counter(page);
    font-family: "Lato", sans-serif;
    font-size: 10pt;
    color: #555;
  }
}
.page-break {
  break-after: page;
}

html {
  font-size: 12pt;
  line-height: 24pt;
}

body {
  font-family: "Lato", sans-serif;
  font-size: 10pt;
  line-height: 14pt;
  color: #555;
  background: #FFFFFF;
  margin: 0;
  padding: 0;
}

.container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 12pt;
  max-width: 100%;
}

h1 {
  font-family: "Bebas Neue", cursive;
  font-size: 36pt;
  line-height: 36pt;
  color: #205F80;
  text-transform: uppercase;
  margin: 0 0 24pt 0;
  font-weight: 400;
}

h2 {
  font-family: "Bebas Neue", cursive;
  font-size: 26pt;
  line-height: 26pt;
  color: #205F80;
  text-transform: uppercase;
  margin: 24pt 0 12pt 0;
  font-weight: 400;
}

h3 {
  font-family: "Lato", sans-serif;
  font-size: 14pt;
  line-height: 18pt;
  color: #205F80;
  font-weight: 700;
  margin: 18pt 0 6pt 0;
}

h4 {
  font-family: "Lato", sans-serif;
  font-size: 12pt;
  line-height: 16pt;
  color: #E87E1E;
  font-weight: 700;
  margin: 12pt 0 6pt 0;
}

p {
  margin: 0 0 12pt 0;
  font-family: "Lato", sans-serif;
  font-size: 10pt;
  line-height: 14pt;
}

.main-content.with-sidebar-right p {
  font-family: "Lato", sans-serif;
  font-size: 10pt;
  line-height: 14pt;
}

/* ----- List Defaults ----- */
ul {
  list-style-type: disc;
  margin-left: 18pt;
}

ol {
  list-style-type: decimal;
  margin-left: 18pt;
}

ul, ol {
  margin: 0 0 12pt 0;
  padding-left: 18pt;
}
ul li, ol li {
  margin-bottom: 6pt;
}

.callout-box {
  background: #F4EBDD;
  border: 1pt solid #205F80;
  border-radius: 4px;
  padding: 5pt;
  margin: 12pt 0;
}
.callout-box.important {
  background: rgb(250.9193548387, 232.1129032258, 215.0806451613);
  border-color: #E87E1E;
}
.callout-box.setup {
  background: rgb(158.6, 206.046875, 230.9);
  border-color: #205F80;
}
.callout-box p:last-child {
  margin-bottom: 0;
}

.sidebar-left {
  grid-column: 1/4;
}

.sidebar-right {
  grid-column: 10/13;
}

.main-content {
  grid-column: 1/13;
}
.main-content.with-sidebar-left {
  grid-column: 5/13;
}
.main-content.with-sidebar-right {
  grid-column: 1/9;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 12pt 0;
  font-size: 9pt;
  line-height: 12pt;
}
table th {
  background: #205F80;
  color: #FFFFFF;
  padding: 6pt;
  font-weight: 700;
  text-align: left;
}
table td {
  padding: 6pt;
  border-bottom: 0.5pt solid rgb(56.6, 154.25, 205.4);
}
table tr:nth-child(even) {
  background: rgb(237.7666666667, 223.6666666667, 201.7333333333);
}

.component-table table th:first-child {
  width: 15%;
}
.component-table table th:nth-child(2) {
  width: 20%;
}
.component-table table th:nth-child(3) {
  width: 65%;
}

.icon {
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 6pt;
}
.icon.energy-cube {
  border-radius: 3px;
}
.icon.energy-cube.artha {
  background: #FFD700;
}
.icon.energy-cube.karma {
  background: #32CD32;
}
.icon.energy-cube.gnana {
  background: #4169E1;
}
.icon.energy-cube.bhakti {
  background: #9370DB;
}
.icon.om-token {
  background: #E87E1E;
  border-radius: 50%;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 10pt;
}

.game-image {
  max-width: 100%;
  height: auto;
  border: 1pt solid #205F80;
  background: #F4EBDD;
  margin: 12pt 0;
  display: block;
}
.game-image.board-section {
  width: 100%;
  height: 300px;
  margin: 12pt 0;
}

.cover-page {
  grid-column: 1/13;
  background: linear-gradient(135deg, #205F80 0%, #E87E1E 100%);
  color: #FFFFFF;
  padding: 72pt 36pt;
  text-align: center;
  height: 9.5in; /* Letter size minus margins */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.cover-page h1 {
  color: #FFFFFF;
  font-size: 48pt;
  line-height: 48pt;
  margin-bottom: 24pt;
}
.cover-page .subtitle {
  font-size: 18pt;
  line-height: 24pt;
  margin-bottom: 36pt;
}
.cover-page .cover-image {
  width: 80%;
  margin: 24pt 0;
}
.cover-page .cover-image img {
  width: 100%;
  height: auto;
  border: 4pt solid #FFFFFF;
}
.cover-page .game-info {
  font-size: 14pt;
  line-height: 18pt;
  margin: 36pt 0;
}
.cover-page .game-info .players, .cover-page .game-info .time {
  display: inline-block;
  margin: 0 12pt;
}

.component-spread {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 12pt;
}
.component-spread .component-category {
  grid-column: span 6;
  margin-bottom: 24pt;
}
.component-spread .component-category h3 {
  color: #E87E1E;
  border-bottom: 1pt solid #E87E1E;
  padding-bottom: 3pt;
}
.component-spread .component-category .component-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12pt;
  margin-top: 12pt;
}
.component-spread .component-category .component-item {
  text-align: center;
}
.component-spread .component-category .component-item .component-image {
  width: 80px;
  height: 80px;
  background: #F4EBDD;
  border: 1pt solid #205F80;
  border-radius: 4px;
  margin: 0 auto 6pt auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8pt;
  color: #555;
}
.component-spread .component-category .component-item .component-name {
  font-weight: 700;
  font-size: 9pt;
  margin-bottom: 3pt;
}
.component-spread .component-category .component-item .component-desc {
  font-size: 8pt;
  line-height: 10pt;
}

.icon-legend {
  background: #F4EBDD;
  border-top: 1pt solid #205F80;
  padding: 6pt 12pt;
  margin-top: 24pt;
  font-size: 8pt;
  position: fixed;
  bottom: 0.5in;
  left: 0.75in;
  right: 0.75in;
}
.icon-legend .legend-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12pt;
}
.icon-legend .legend-row .legend-item {
  display: flex;
  align-items: center;
}
.icon-legend .legend-row .legend-item .icon {
  width: 16px;
  height: 16px;
  margin-right: 3pt;
}

.board-diagram {
  background: #F4EBDD;
  border: 2pt solid #205F80;
  border-radius: 8px;
  padding: 12pt;
  margin: 18pt 0;
}
.board-diagram p {
  margin: 6pt 0 0 0;
}

.example-turn {
  background: rgb(253.2842741935, 245.377016129, 238.2157258065);
  border-left: 4pt solid #E87E1E;
  padding: 12pt;
  margin: 18pt 0;
}
.example-turn .turn-header {
  font-weight: 700;
  color: #E87E1E;
  margin-bottom: 6pt;
}
.example-turn .turn-step {
  margin: 6pt 0;
  padding-left: 12pt;
}
.example-turn .turn-step:before {
  content: "→";
  color: #E87E1E;
  font-weight: 700;
  margin-right: 6pt;
  margin-left: -12pt;
}

.reference-card {
  background: #FFFFFF;
  border: 1pt solid #205F80;
  border-radius: 6px;
  padding: 8pt;
  margin: 6pt;
  display: inline-block;
  vertical-align: top;
  width: 200px;
}
.reference-card .card-title {
  background: #205F80;
  color: #FFFFFF;
  padding: 4pt 6pt;
  margin: -8pt -8pt 6pt -8pt;
  border-radius: 5px 5px 0 0;
  font-weight: 700;
  font-size: 9pt;
}
.reference-card .card-cost {
  font-size: 8pt;
  margin-bottom: 4pt;
}
.reference-card .card-cost .icon {
  width: 12px;
  height: 12px;
  margin-right: 2pt;
}
.reference-card .card-points {
  color: #E87E1E;
  font-weight: 700;
  text-align: right;
  font-size: 14pt;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: 700;
}

.color-navy {
  color: #205F80;
}

.color-orange {
  color: #E87E1E;
}

.margin-top {
  margin-top: 12pt;
}

.margin-bottom {
  margin-bottom: 12pt;
}

.padding {
  padding: 6pt;
}