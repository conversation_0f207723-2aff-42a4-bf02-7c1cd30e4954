@import url("https://fonts.googleapis.com/css2?family=Bebas+Neue:wght@400&family=Lato:wght@400;700&display=swap");
html {
  font-size: 12pt;
  line-height: 1.5;
}

body {
  font-family: "Lato", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #333;
  background: #FFFFFF;
  margin: 0;
  padding: 0;
}

h1 {
  font-family: "Bebas Neue", cursive;
  font-size: 2.5rem;
  color: #205F80;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
  page-break-after: avoid;
}

h2 {
  font-family: "Bebas Neue", cursive;
  font-size: 2rem;
  color: #205F80;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
  page-break-after: avoid;
}

h3 {
  font-family: "Lato", sans-serif;
  font-size: 1.3rem;
  color: #205F80;
  font-weight: 700;
  margin: 1.2rem 0 0.8rem;
  page-break-after: avoid;
}

h4 {
  font-family: "Lato", sans-serif;
  font-size: 1.1rem;
  color: #E87E1E;
  font-weight: 700;
  margin: 1rem 0 0.5rem;
  page-break-after: avoid;
}

p {
  margin: 0 0 1rem;
}

strong, b {
  font-weight: 700;
}

.rulebook-container {
  margin: 0;
  padding: 0;
}

.rulebook-section {
  margin-bottom: 1rem;
  page-break-before: always;
}
.rulebook-section:first-of-type {
  page-break-before: auto;
}

.section-title {
  color: #205F80;
  border-bottom: 2px solid #E87E1E;
  padding-bottom: 0.5rem;
}

ul, ol {
  margin: 0 0 1rem;
  padding-left: 1.5rem;
}
ul li, ol li {
  margin-bottom: 0.5rem;
  page-break-inside: avoid;
}

.callout-box {
  background: #F4EBDD;
  border: 2px solid #205F80;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem 0;
  page-break-inside: avoid;
}
.callout-box.important {
  background: rgb(250.9193548387, 232.1129032258, 215.0806451613);
  border-color: #E87E1E;
}
.callout-box.setup {
  background: rgb(158.6, 206.046875, 230.9);
  border-color: #205F80;
}
.callout-box h3 {
  margin-top: 0;
}
.callout-box p:last-child {
  margin-bottom: 0;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.9rem;
  page-break-inside: avoid;
}
table th {
  background: #205F80;
  color: #FFFFFF;
  padding: 0.75rem;
  font-weight: 700;
  text-align: left;
}
table td {
  padding: 0.75rem;
  border-bottom: 1px solid rgb(56.6, 154.25, 205.4);
}
table tr:nth-child(even) {
  background: rgb(237.7666666667, 223.6666666667, 201.7333333333);
}

.component-table {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}
.component-table .component-item {
  border: 1px solid #205F80;
  border-radius: 6px;
  padding: 1rem;
  background: #F4EBDD;
  page-break-inside: avoid;
}
.component-table .component-item img {
  max-width: 100%;
  height: auto;
  border: 2px solid white;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}
.component-table .component-item h4 {
  margin-top: 0;
  color: #205F80;
}

.cover-page {
  text-align: center;
  margin: 0;
  padding: 0;
  height: 10.5in;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  page-break-after: always;
}
.cover-page .game-title {
  font-family: "Bebas Neue", cursive;
  font-size: 3.5rem;
  color: #205F80;
  margin-bottom: 1rem;
}
.cover-page .game-subtitle {
  font-family: "Lato", sans-serif;
  font-size: 1.5rem;
  color: #E87E1E;
  margin-bottom: 2rem;
}
.cover-page .cover-image {
  max-width: 80%;
  height: auto;
  margin: 1.5rem 0;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 6px;
}

.energy-cube {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: inline-block;
}
.energy-cube.artha {
  background-color: #FFD700;
}
.energy-cube.karma {
  background-color: #4CAF50;
}
.energy-cube.gnana {
  background-color: #1976D2;
}
.energy-cube.bhakti {
  background-color: #7B1FA2;
}

.om-token {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #E87E1E;
  border-radius: 50%;
  color: white;
  font-weight: bold;
}

.image-placeholder {
  background-color: #f0f0f0;
  border: 2px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  page-break-inside: avoid;
}
.image-placeholder.small {
  width: 100%;
  height: 150px;
}
.image-placeholder.medium {
  width: 100%;
  height: 250px;
}
.image-placeholder.large {
  width: 100%;
  height: 350px;
}
.image-placeholder p {
  font-style: italic;
  color: #777;
}

@media print {
  a {
    text-decoration: none;
    color: #333;
  }
  .page-break {
    page-break-before: always;
  }
  .no-break {
    page-break-inside: avoid;
  }
  .board-diagram, .game-image {
    max-width: 100%;
    page-break-inside: avoid;
  }
  .rulebook-nav,
  .back-to-top,
  .rulebook-footer {
    display: none;
  }
  @page {
    @bottom-center {
      content: counter(page);
    }
  }
  .icon-legend {
    page-break-before: always;
  }
}
.text-center {
  text-align: center;
}

.example-box {
  background: rgb(219.8, 237.125, 246.2);
  border: 1px solid #205F80;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem 0;
  page-break-inside: avoid;
}
.example-box h4 {
  margin-top: 0;
  color: #205F80;
}