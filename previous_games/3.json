{"comment": "first game won by om tokens", "players": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "position": 57, "hand": [{"id": "E7", "type": "wildCube"}], "energyCubes": ["bhakti", "artha", "artha"], "omTemp": 3, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "outerScore": 60, "innerScore": 0, "collectedJourneys": [{"id": "JO8", "locationId": 17, "reward": {"outer": 20}}, {"id": "JO7", "locationId": 13, "reward": {"outer": 20}}, {"id": "JO14", "locationId": 29, "reward": {"outer": 20}}], "didMoveThisTurn": true, "didSelectionActionThisTurn": false}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "position": 60, "hand": [{"id": "T13", "type": "travel", "value": 3}, {"id": "T10", "type": "travel", "value": 2}, {"id": "T7", "type": "travel", "value": 2}], "energyCubes": ["artha"], "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "outerScore": 0, "innerScore": 60, "collectedJourneys": [{"id": "JI5", "locationId": 8, "reward": {"inner": 30}}, {"id": "JI11", "locationId": 20, "reward": {"inner": 30}}], "didMoveThisTurn": false, "didSelectionActionThisTurn": false}], "started": true, "turnIndex": 1, "roundCount": 14, "travelDeck": [{"id": "T9", "type": "travel", "value": 2}, {"id": "T15", "type": "travel", "value": 3}, {"id": "T8", "type": "travel", "value": 2}, {"id": "T6", "type": "travel", "value": 2}], "eventDeck": [{"id": "E1", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}, {"id": "E2", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "journeyDeckOuter": [{"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "travelDiscard": [{"id": "T12", "type": "travel", "value": 3}, {"id": "T11", "type": "travel", "value": 3}, {"id": "T2", "type": "travel", "value": 1}, {"id": "T14", "type": "travel", "value": 3}], "eventDiscard": [], "journeyInnerDiscard": [{"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyOuterDiscard": [{"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}], "faceUpTravel": [{"id": "T1", "type": "travel", "value": 1}, {"id": "T3", "type": "travel", "value": 1}, {"id": "T4", "type": "travel", "value": 1}, {"id": "T5", "type": "travel", "value": 1}], "faceUpEvent": [{"id": "E4", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "faceUpJourneyOuter": [{"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "locationCubes": {"1": "artha", "2": "bhakti", "3": null, "4": null, "5": "karma", "6": null, "7": null, "8": null, "9": null, "10": null, "11": null, "12": null, "13": null, "14": null, "15": "artha", "16": "karma", "17": null, "18": "karma", "19": null, "20": "gnana", "21": "gnana", "22": "karma", "23": "gnana", "24": "bhakti", "25": "bhakti", "26": "gnana", "27": "artha", "28": "karma", "29": null, "30": "bhakti", "31": "bhakti", "32": "karma", "33": "bhakti", "34": "gnana", "35": "artha", "36": "karma", "37": "artha", "38": "artha", "39": "karma", "40": "artha", "41": "bhakti", "42": "artha", "43": "gnana", "44": "karma", "45": "gnana", "46": "karma", "47": "artha", "48": "artha", "undefined": "gnana"}, "locationOm": {"49": false, "50": false, "51": false, "52": false, "53": false, "54": false, "55": true, "56": false, "57": false, "58": false, "59": false, "60": false}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "gameEvents": [{"type": "DEAL_INITIAL_CARD", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 0, "turnIndex": 0, "timestamp": 1743125257922, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 2}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 0, "turnIndex": 0, "timestamp": 1743125257922, "data": {"cardType": "travel", "card": {"id": "T5", "type": "travel", "value": 1}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743125257922, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743125257922, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 0, "turnIndex": 0, "timestamp": 1743125302364, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3}, {"id": "T11", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 0, "turnIndex": 0, "timestamp": 1743125315683, "data": {"location": 50}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 0, "turnIndex": 0, "timestamp": 1743125315683, "data": {"path": [64, 62, 11, 12, 50], "travelCardIds": ["T5", "T14"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 0, "turnIndex": 1, "timestamp": 1743125318497, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743126188804, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743126192596, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743126215127, "data": {"location": 54}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743126215127, "data": {"path": [61, 6, 7, 8, 54], "travelCardIds": ["T7", "T9"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743126232579, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743126232580, "data": {"roundNumber": 1, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 0, "collectedJourneysCount": 0}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 0, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 1, "turnIndex": 0, "timestamp": 1743126318613, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 3}, {"id": "T8", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 1, "turnIndex": 0, "timestamp": 1743126324598, "data": {"path": [50, 12, 13, 14], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 1, "turnIndex": 1, "timestamp": 1743126327362, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743126396532, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743126402977, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743126414462, "data": {"location": 56}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743126414462, "data": {"path": [54, 9, 1, 2, 3, 56], "travelCardIds": ["T6", "T12"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743126421548, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743126421548, "data": {"roundNumber": 2, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 0}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 0, "collectedJourneysCount": 0}]}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 2, "turnIndex": 0, "timestamp": 1743126446515, "data": {"path": [14, 16, 17], "travelCardIds": ["T10"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 2, "turnIndex": 0, "timestamp": 1743126451433, "data": {"journeyCard": {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, "journeyType": "outer", "location": {"id": 17, "name": "<PERSON><PERSON><PERSON>", "region": "West", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["karma", "karma"], "energyCubesAfter": ["bhakti", "artha", "artha"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 20, "innerScore": 0}}}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 2, "turnIndex": 1, "timestamp": 1743126455712, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743126929013, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743126934952, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743126938269, "data": {"path": [56, 3, 2, 1, 9], "travelCardIds": ["T4", "T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743127025451, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743127025451, "data": {"roundNumber": 3, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 20, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 1}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 0}]}}, {"type": "PICK_DECK_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 3, "turnIndex": 0, "timestamp": 1743127076594, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 3, "turnIndex": 0, "timestamp": 1743127078644, "data": {"cardType": "travel", "drawnCards": [{"id": "T14", "type": "travel", "value": 3}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 3, "turnIndex": 0, "timestamp": 1743127085164, "data": {"location": 49}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 3, "turnIndex": 0, "timestamp": 1743127085164, "data": {"path": [17, 16, 14, 13, 12, 11, 10, 49], "travelCardIds": ["T15", "T8", "T7"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 3, "turnIndex": 1, "timestamp": 1743127087527, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743127168974, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743127176620, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743127269396, "data": {"path": [9, 1, 2, 3], "travelCardIds": ["T3", "T5", "T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743127271638, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743127271638, "data": {"roundNumber": 4, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 20, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 1}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 2, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 4, "turnIndex": 0, "timestamp": 1743127308614, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 4, "turnIndex": 0, "timestamp": 1743127311944, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 4, "turnIndex": 0, "timestamp": 1743127316881, "data": {"path": [49, 10], "travelCardIds": ["T4"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 4, "turnIndex": 1, "timestamp": 1743127319478, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743127398409, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743127410506, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743127414874, "data": {"path": [3, 4], "travelCardIds": ["T2"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743127417790, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743127417790, "data": {"roundNumber": 5, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 20, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 2, "collectedJourneysCount": 1}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 0}]}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 5, "turnIndex": 0, "timestamp": 1743127437649, "data": {"path": [10, 11, 12, 13], "travelCardIds": ["T14"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 5, "turnIndex": 0, "timestamp": 1743127445981, "data": {"journeyCard": {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, "journeyType": "outer", "location": {"id": 13, "name": "<PERSON><PERSON>", "region": "West", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["karma", "karma", "bhakti"], "energyCubesAfter": ["bhakti", "artha", "artha"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 20, "innerScore": 0}, "newScore": {"outerScore": 40, "innerScore": 0}}}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 5, "turnIndex": 1, "timestamp": 1743127451296, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743127630947, "data": {"cardType": "event", "pickedCards": [{"id": "E8", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743127639026, "data": {"path": [4, 6, 7, 8], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743127642775, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743127642776, "data": {"roundNumber": 6, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 40, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 1, "energyCubesCount": 2, "collectedJourneysCount": 2}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 4, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 6, "turnIndex": 0, "timestamp": 1743127661126, "data": {"cardType": "event", "pickedCards": [{"id": "E7", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 6, "turnIndex": 1, "timestamp": 1743127670378, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "COLLECT_JOURNEY", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743127701735, "data": {"journeyCard": {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, "journeyType": "inner", "location": {"id": 8, "name": "Badrinath Temple", "region": "North", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["bhakti", "gnana", "gnana", "artha"], "energyCubesAfter": ["artha"], "wildCubesUsed": 1, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 0, "innerScore": 30}}}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743127723383, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743127723383, "data": {"roundNumber": 7, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 40, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 2, "collectedJourneysCount": 2}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 7, "turnIndex": 0, "timestamp": 1743127758709, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 7, "turnIndex": 0, "timestamp": 1743127764160, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 7, "turnIndex": 0, "timestamp": 1743127788779, "data": {"path": [13, 14, 19], "travelCardIds": ["T10"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 7, "turnIndex": 1, "timestamp": 1743127790791, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743127840284, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743127865374, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743127893421, "data": {"path": [8, 7, 11, 12], "travelCardIds": ["T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743127895539, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743127895539, "data": {"roundNumber": 8, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 40, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 2}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 2, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 8, "turnIndex": 0, "timestamp": 1743127951714, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 8, "turnIndex": 0, "timestamp": 1743127957682, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 8, "turnIndex": 0, "timestamp": 1743127982166, "data": {"location": 52}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 8, "turnIndex": 0, "timestamp": 1743127982166, "data": {"path": [19, 62, 64, 15, 5, 52], "travelCardIds": ["T12", "T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 8, "turnIndex": 1, "timestamp": 1743127985265, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743128022607, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743128026144, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743128048366, "data": {"path": [12, 11], "travelCardIds": ["T3"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743128050723, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743128050723, "data": {"roundNumber": 9, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 40, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 2}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 1}]}}, {"type": "PICK_DECK_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 9, "turnIndex": 0, "timestamp": 1743128216133, "data": {"cardType": "travel", "drawnCards": [{"id": "T2", "type": "travel", "value": 1}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 9, "turnIndex": 0, "timestamp": 1743128218231, "data": {"cardType": "travel", "drawnCards": [{"id": "T13", "type": "travel", "value": 3}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 9, "turnIndex": 0, "timestamp": 1743128235468, "data": {"location": 53}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 9, "turnIndex": 0, "timestamp": 1743128235468, "data": {"path": [52, 5, 15, 18, 53], "travelCardIds": ["T15", "T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 9, "turnIndex": 1, "timestamp": 1743128238181, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743128279244, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743128282026, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743128290137, "data": {"path": [11, 7], "travelCardIds": ["T5"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743128292106, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743128292106, "data": {"roundNumber": 10, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 40, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 2}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 4, "collectedJourneysCount": 1}]}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 10, "turnIndex": 0, "timestamp": 1743128329100, "data": {"path": [53, 18, 28, 29], "travelCardIds": ["T13"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 10, "turnIndex": 0, "timestamp": 1743128332447, "data": {"journeyCard": {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, "journeyType": "outer", "location": {"id": 29, "name": "<PERSON><PERSON><PERSON>", "region": "Central", "journeyType": "Outer"}, "omSpent": 2, "energyCubesBefore": ["karma", "bhakti", "artha", "artha"], "energyCubesAfter": ["bhakti", "artha", "artha"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 40, "innerScore": 0}, "newScore": {"outerScore": 60, "innerScore": 0}}}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 10, "turnIndex": 1, "timestamp": 1743128335978, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743128402485, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743128431857, "data": {"path": [7, 6], "travelCardIds": ["T4"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743128433496, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743128433496, "data": {"roundNumber": 11, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 60, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 3}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 5, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 11, "turnIndex": 0, "timestamp": 1743128442707, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 11, "turnIndex": 0, "timestamp": 1743128446441, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 11, "turnIndex": 0, "timestamp": 1743128511677, "data": {"location": 51}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 11, "turnIndex": 0, "timestamp": 1743128511677, "data": {"path": [29, 28, 21, 20, 51], "travelCardIds": ["T8", "T10"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 11, "turnIndex": 1, "timestamp": 1743128513843, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743128542964, "data": {"path": [6, 61, 63, 20], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743128547425, "data": {"journeyCard": {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, "journeyType": "inner", "location": {"id": 20, "name": "Meenakshi Temple", "region": "South", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["artha", "gnana", "gnana", "bhakti", "bhakti"], "energyCubesAfter": ["artha"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 30}, "newScore": {"outerScore": 0, "innerScore": 60}}}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743128565444, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743128565445, "data": {"roundNumber": 12, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 60, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 3}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 60, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 12, "turnIndex": 0, "timestamp": 1743128591074, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 3}, {"id": "T6", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 12, "turnIndex": 0, "timestamp": 1743128596644, "data": {"location": 59}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 12, "turnIndex": 0, "timestamp": 1743128596644, "data": {"path": [51, 20, 21, 22, 23, 59], "travelCardIds": ["T15", "T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 12, "turnIndex": 1, "timestamp": 1743128598874, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_DECK_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743128732612, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743128737505, "data": {"cardType": "travel", "drawnCards": [{"id": "T13", "type": "travel", "value": 3}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743128752599, "data": {"location": 58}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743128752599, "data": {"path": [20, 63, 65, 31, 32, 58], "travelCardIds": ["T7", "T14"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743128755243, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1743128755243, "data": {"roundNumber": 13, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 60, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 3}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 60, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 13, "turnIndex": 0, "timestamp": 1743128767825, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 13, "turnIndex": 0, "timestamp": 1743128771142, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 13, "turnIndex": 0, "timestamp": 1743128789280, "data": {"path": [59, 23, 24, 63, 66], "travelCardIds": ["T9", "T8"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 13, "turnIndex": 1, "timestamp": 1743128795687, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743128824245, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743128854675, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743128899328, "data": {"location": 60}}, {"type": "move", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743128899328, "data": {"path": [58, 32, 31, 45, 44, 43, 60], "travelCardIds": ["T12", "T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "eN4YWzJwC3PqLLbHAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743128905874, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1743128905874, "data": {"roundNumber": 14, "playerCount": 2, "playerStats": [{"id": "uq-USgrDPjp7CiG8AAAD", "name": "gopal", "outerScore": 60, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 3}, {"id": "eN4YWzJwC3PqLLbHAAAF", "name": "<PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 60, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 2}]}}, {"type": "PICK_DECK_CARDS", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 14, "turnIndex": 0, "timestamp": 1743128909437, "data": {"cardType": "travel", "drawnCards": [{"id": "T14", "type": "travel", "value": 3}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 14, "turnIndex": 0, "timestamp": 1743128923157, "data": {"location": 57}}, {"type": "move", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 14, "turnIndex": 0, "timestamp": 1743128923157, "data": {"path": [66, 42, 48, 41, 57], "travelCardIds": ["T2", "T14"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "uq-USgrDPjp7CiG8AAAD", "playerName": "gopal", "roundCount": 14, "turnIndex": 1, "timestamp": 1743128925672, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}]}