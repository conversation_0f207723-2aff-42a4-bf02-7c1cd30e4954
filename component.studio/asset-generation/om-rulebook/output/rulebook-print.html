<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Om: The Journey Rulebook - Om: The Journey Rulebook</title>
    <link rel="stylesheet" href="print-friendly.css">
    <style>
        @page {
            size: letter; /* 8.5" x 11" */
            margin: 0.75in;
        }
    </style>
</head>
<body class="print-friendly">
    <!-- Cover page -->
    <div class="cover-page">
        <img src="assets/images/om_the_journey_poster.jpg" alt="Om: The Journey" class="cover-image">
        <h1 class="game-title">Om: The Journey</h1>
        <h2 class="game-subtitle">Rulebook</h2>
    </div>
    
    <!-- Main content with page breaks between sections -->
    <div class="rulebook-container">
        <section id="01-introduction" class="rulebook-section"><h1 class="section-title">Introduction & Theme</h1><h1>Introduction &amp; Theme</h1>
<p>In <strong>Om: The Journey</strong>, each player follows the path of a modern traveller exploring the vast lands of India—physically journeying across important sites (<strong>Outer Journey</strong>) while simultaneously cultivating spiritual growth (<strong>Inner Journey</strong>).</p>
<p>The board depicts six major regions—<strong>North</strong>, <strong>West</strong>, <strong>South</strong>, <strong>East</strong>, <strong>North-East</strong> and <strong>Central</strong>—each containing notable locations associated with either the Outer or Inner quest.</p>
<h2>Your Spiritual Adventure</h2>
<div class="callout-box">
Along your journey, you will:
<p>• <strong>Collect Energy Cubes</strong> of four types: Artha (wealth), Karma (action), Gnana (knowledge), and Bhakti (devotion)</p>
<p>• <strong>Acquire Destination Cards</strong> to mark progress on both your Outer and Inner tracks</p>
<p>• <strong>Earn Om Tokens</strong> from sacred Jyotirlinga locations, which influence turn order through the dynamic Om Turn Track</p>
</div>
<div class="board-diagram">
    <div class="image-placeholder large">
        <p>Image: Game Board Overview - The six regions of India await your exploration</p>
    </div>
</div>
<h2>Game Overview</h2>
<div class="sidebar-right">
    <div class="callout-box important">
        <h4>At a Glance</h4>
        <p><strong>Players:</strong> 2-3</p>
        <p><strong>Time:</strong> 45-75 minutes</p>
        <p><strong>Goal:</strong> First to 100+ combined points</p>
    </div>
</div>
<div class="main-content with-sidebar-right">
    Your objective is simple yet profound: be the first player whose **Outer Score + Inner Score exceeds 100 points**. This triggers a final round, ensuring all players have equal turns.
<pre><code>&lt;p&gt;The game balances physical exploration with spiritual development, requiring strategic decisions about which path to prioritize while managing limited resources and adapting to changing conditions through Global Event cards.&lt;/p&gt;

Each turn, you'll choose between acquiring travel cards to move across India or collecting cards that advance your scoring tracks. The dynamic Om Turn Track adds tactical depth, as **when you collect Om Tokens** from sacred sites it influences turn order for future rounds.
</code></pre>
</div> </section><section id="02-components" class="rulebook-section"><h1 class="section-title">Components</h1><h1>Components</h1>
<div class="component-table">
<table>
<thead>
<tr>
<th>Item</th>
<th>Quantity</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Main Game Board</strong></td>
<td>1</td>
<td>30&quot; × 22&quot; board depicting India’s six regions, travel routes, and 12 Jyotirlinga spaces (each starts with 1 Om Token)</td>
</tr>
<tr>
<td><strong>Om Turn Track</strong></td>
<td>1</td>
<td>Printed on main board with numbered spaces 0-5 for dynamic turn order</td>
</tr>
<tr>
<td><strong>Player Markers</strong></td>
<td>12 total</td>
<td>4 meeples per player color: 1 traveller pawn, 2 score markers (Inner &amp; Outer tracks), 1 Om Turn track marker</td>
</tr>
<tr>
<td><strong>Energy Cubes</strong></td>
<td>48 total</td>
<td>12 each of Artha <span class="icon energy-cube artha"></span> (yellow), Karma <span class="icon energy-cube karma"></span> (green), Gnana <span class="icon energy-cube gnana"></span> (blue), Bhakti <span class="icon energy-cube bhakti"></span> (purple)</td>
</tr>
<tr>
<td><strong>Om Tokens</strong></td>
<td>12</td>
<td>Wooden tokens (0.5&quot;) placed on Jyotirlinga locations <span class="icon om-token">ॐ</span></td>
</tr>
<tr>
<td><strong>Character Cards</strong></td>
<td>4</td>
<td>Unique roles: Engineer, Professor, Merchant, Pilgrim</td>
</tr>
<tr>
<td><strong>Travel Cards</strong></td>
<td>24</td>
<td>Movement cards showing values 1-3 with vehicle types (Camel, Horse, Truck, Bus, Train, Helicopter, etc.)</td>
</tr>
<tr>
<td><strong>cards</strong></td>
<td>48 total</td>
<td>26 Outer cards + 22 Inner cards representing destinations and spiritual sites</td>
</tr>
<tr>
<td><strong>Global Event Cards</strong></td>
<td>30</td>
<td>Cards affecting all players each round with diverse thematic effects</td>
</tr>
<tr>
<td><strong>Scoring Tracks</strong></td>
<td>2</td>
<td>Outer Journey (0-100) and Inner Journey (0-100) printed on main board</td>
</tr>
</tbody>
</table>
</div>
<h2>Component Details</h2>
<div class="sidebar-left">
    <div class="callout-box setup">
        <h4>Quick Setup Check</h4>
        <p>Before your first game, verify you have:</p>
        <ul>
            <li>All 48 Energy Cubes (12 of each color)</li>
            <li>12 Om Tokens on Jyotirlinga spaces</li>
            <li>4 Character cards</li>
            <li>All player meeples (4 per color)</li>
        </ul>
    </div>
</div>
<div class="main-content with-sidebar-left">
<h3>Energy Cubes</h3>
<p>The four types of Energy Cubes represent different aspects of your journey:</p>
<ul>
<li><strong>Artha</strong> <span class="icon energy-cube artha"></span> (Yellow) - Material wealth and resources</li>
<li><strong>Karma</strong> <span class="icon energy-cube karma"></span> (Green) - Action and good deeds</li>
<li><strong>Gnana</strong> <span class="icon energy-cube gnana"></span> (Blue) - Knowledge and wisdom</li>
<li><strong>Bhakti</strong> <span class="icon energy-cube bhakti"></span> (Purple) - Devotion and spiritual practice</li>
</ul>
<p>These cubes serve as currency for acquiring cards and represent the balanced approach needed for both outer and inner development.</p>
<h3>cards</h3>
<p>cards are divided into two types:</p>
<p><strong>Outer cards</strong> focus on physical destinations, cultural sites, and worldly experiences. These require combinations of Artha and Karma cubes.</p>
<p><strong>Inner cards</strong> represent spiritual locations, temples, and places of worship. These require combinations of Gnana and Bhakti cubes.</p>
<h3>Global Event Cards</h3>
<p>Each round begins with a new Global Event that affects all players. These cards add thematic flavor and tactical variety, representing the changing conditions of travel in India - from monsoon delays to festival celebrations.</p>
</div>
<div class="page-break"></div>
<h2>Visual Component Guide</h2>
<div class="component-spread">
    <!-- Component spread template will be inserted here -->
</div> </section><section id="03-setup" class="rulebook-section"><h1 class="section-title">Setup</h1><h1>Setup</h1>
<p>Follow these steps to prepare for your journey across India.</p>
<h2>1. Board Setup</h2>
<div class="callout-box setup">
<h3>Om Tokens</h3>
<p>Place all 12 <strong>Om Tokens</strong> <span class="icon om-token">ॐ</span> on the Jyotirlinga locations marked on the board. These sacred sites are scattered across all six regions of India.</p>
</div>
<div class="board-diagram">
    <div class="image-placeholder large"><p>Image: Board Setup</p></div>
    <p class="text-center"><em>Initial board state with Om Tokens placed on all Jyotirlinga locations</em></p>
</div>
<h2>2. Energy Cubes Distribution</h2>
<div class="sidebar-right">
    <div class="callout-box important">
        <h4>Energy Cube Check</h4>
        <p>You should have exactly <strong>6 cubes</strong> remaining in the supply after placement:</p>
        <ul>
            <li>4 cubes (1 of each type) set aside initially</li>
            <li>2 extra cubes from random distribution</li>
        </ul>
        <p>If you have more than 6, check for missing placements on the board.</p>
    </div>
</div>
<div class="main-content with-sidebar-right">
<ol>
<li>
<p><strong>Reserve Supply</strong>: Set aside 1 Energy Cube of each color <span class="icon energy-cube artha"></span> <span class="icon energy-cube karma"></span> <span class="icon energy-cube gnana"></span> <span class="icon energy-cube bhakti"></span> in the cube tray.</p>
</li>
<li>
<p><strong>Random Distribution</strong>: Place all remaining cubes in the cup and shuffle thoroughly.</p>
</li>
<li>
<p><strong>Board Placement</strong>: Draw cubes one at a time and place them on locations throughout the board until all locations have cubes.</p>
</li>
<li>
<p><strong>Final Supply</strong>: Add the 2 remaining cubes to the 4 reserved cubes for a total supply of 6 cubes.</p>
</li>
</ol>
</div>
<h2>3. Player Setup</h2>
<h3>Character Assignment</h3>
<div class="example-turn">
    <div class="turn-header">Character Selection</div>
    <div class="turn-step">Shuffle the 4 Character cards</div>
    <div class="turn-step">Deal 1 card face-up to each player</div>
    <div class="turn-step">Return unused cards to the box</div>
    <div class="turn-step">Each player studies their character's special ability</div>
</div>
<h3>Player Pieces</h3>
<p>Each player chooses a color and takes:</p>
<ul>
<li>1 <strong>Traveller pawn</strong> (place on any of the 6 airport locations)</li>
<li>2 <strong>Score markers</strong> (place both on 0 of their respective tracks)</li>
<li>1 <strong>Om Turn Track marker</strong> (place according to turn order below)</li>
</ul>
<div class="callout-box">
<h3>Turn Order Determination</h3>
<p><strong>If today’s date ends in an even number</strong>: Youngest player goes first, followed clockwise.</p>
<p><strong>If today’s date ends in an odd number</strong>: Oldest player goes first, followed clockwise.</p>
<p><strong>Om Turn Track</strong>: First player’s marker goes on space 0 (bottom), second player on space 1, third player on space 2.</p>
</div>
<h2>4. Card Setup</h2>
<div class="sidebar-left">
    <div class="callout-box">
        <h4>Card Markets</h4>
        <p>Both card markets should be visible to all players and easily accessible during the game.</p>
    </div>
</div>
<div class="main-content with-sidebar-left">
<h3>cards</h3>
<ol>
<li><strong>Separate</strong> Inner and Outer cards (look for “I” or “O” markings)</li>
<li><strong>Shuffle</strong> each deck separately</li>
<li><strong>Deal 4 cards</strong> face-up from each deck to create the markets</li>
<li><strong>Place remaining decks</strong> nearby for refills</li>
</ol>
<h3>Travel Cards</h3>
<ul>
<li><strong>Shuffle</strong> the Travel deck</li>
<li><strong>Place face-down</strong> within reach of all players</li>
<li><strong>No initial market</strong> - players draw directly from the deck</li>
</ul>
<h3>Global Event Cards</h3>
<ul>
<li><strong>Shuffle</strong> the Global Event deck</li>
<li><strong>Reveal the top card</strong> - this affects Round 1</li>
<li><strong>Place the deck</strong> nearby for future rounds</li>
</ul>
</div>
<h2>5. Final Setup Check</h2>
<div class="board-diagram">
    <div class="image-placeholder medium"><p>Image: Complete Setup</p></div>
    <p class="text-center"><em>Ready to begin - all components in starting positions</em></p>
</div>
<div class="callout-box important">
<h3>Before You Begin</h3>
<ul>
<li>[ ] All Om Tokens placed on Jyotirlinga locations</li>
<li>[ ] Energy Cubes distributed on board with 6 in supply</li>
<li>[ ] Each player has their 4 pieces placed correctly</li>
<li>[ ] Character cards dealt and abilities understood</li>
<li>[ ] card markets (4 Inner + 4 Outer) face-up</li>
<li>[ ] First Global Event card revealed</li>
<li>[ ] Turn order established on Om Turn Track</li>
</ul>
<p><strong>You’re ready to begin your spiritual journey across India!</strong></p>
</div> </section><section id="04-gameplay" class="rulebook-section"><h1 class="section-title">Gameplay</h1><h1>Gameplay</h1>
<p>Each player’s turn follows a structured sequence. The game continues until one player’s combined Outer + Inner score exceeds 100 points, triggering the final round.</p>
<h2>Turn Sequence</h2>
<div class="example-turn">
    <div class="turn-header">Your Turn (in order)</div>
    <div class="turn-step">1. Character Trading (Optional)</div>
    <div class="turn-step">2. Choose ONE Main Action</div>
    <div class="turn-step">3. Travelling (Optional)</div>
    <div class="turn-step">4. Collect Om Tokens (if applicable)</div>
    <div class="turn-step">5. Acquire card (if Action B chosen)</div>
    <div class="turn-step">6. End-of-Turn Cleanup</div>
</div>
<h2>1. Character Trading (Optional)</h2>
<div class="sidebar-left">
    <div class="callout-box">
        <h4>Character Abilities</h4>
        <p><strong>Engineer</strong> <span class="icon energy-cube karma"></span><br/>Trade any cube → 1 Karma</p>
        <p><strong>Professor</strong> <span class="icon energy-cube gnana"></span><br/>Trade any cube → 1 Gnana</p>
        <p><strong>Merchant</strong> <span class="icon energy-cube artha"></span><br/>Trade any cube → 1 Artha</p>
        <p><strong>Pilgrim</strong> <span class="icon energy-cube bhakti"></span><br/>Trade any cube → 1 Bhakti</p>
    </div>
</div>
<div class="main-content with-sidebar-left">
<p><strong>Once per turn</strong>, you may use your Character’s ability to exchange Energy Cubes. Give any cube from your supply to the central pile and take 1 cube of your character’s type.</p>
<p><strong>Important</strong>: You cannot trade a cube of the same type you receive (e.g., Merchant cannot trade Artha for Artha).</p>
<p>This ability helps you obtain the specific cube types needed for cards while managing your cube collection efficiently.</p>
</div>
<h2>2. Main Actions (Choose ONE)</h2>
<p>You must choose exactly one of these actions each turn:</p>
<h3>Action A: Acquire Travel Cards</h3>
<div class="callout-box">
**Take any 2 Travel Cards** from the market and/or deck in any combination:
- Draw from the face-down deck
- Take from the market (then refill empty market slots)
- Combine both options
<p><strong>No cost required</strong> - Travel Cards are free to acquire.</p>
</div>
<h3>Action B: Acquire 1 card</h3>
<div class="callout-box important">
**Choose and pay for 1 card** from either market:
- **Pay the Energy Cube cost** (return cubes to central supply)
- **Pay the Om Token cost** based on your mat slot (1-1-2-3)
- **Place the card** in the next available slot of the correct track
- **Advance your score marker** by the card's point value
</div>
<div class="board-diagram">
    <div class="image-placeholder medium"><p>Image: card Examples</p></div>
    <p class="text-center"><em>Examples of Outer and Inner cards with costs and rewards</em></p>
</div>
<h2>3. Travelling (Optional)</h2>
<div class="sidebar-right">
    <div class="callout-box setup">
        <h4>Movement Rules</h4>
        <ul>
            <li>Each card = exact number of hops</li>
            <li>Can't revisit locations during single card's movement</li>
            <li>Can chain multiple cards</li>
            <li>Movement is always optional</li>
        </ul>
    </div>
</div>
<div class="main-content with-sidebar-right">
<p>Play any number of <strong>Travel Cards</strong> from your hand to move your traveller pawn across the board.</p>
<h3>Movement Mechanics</h3>
<ul>
<li><strong>Exact Hops</strong>: A card showing “3” requires exactly 3 steps along connected routes</li>
<li><strong>No Backtracking</strong>: Cannot revisit the same location during a single card’s movement</li>
<li><strong>Chaining Cards</strong>: Play multiple cards in sequence (e.g., “2” + “1” = 3 total hops)</li>
<li><strong>Route Connections</strong>: Follow the printed routes between locations</li>
</ul>
<h3>Example Movement</h3>
<div class="example-turn">
    <div class="turn-header">Maya's Movement</div>
    <div class="turn-step">Maya plays a "2" Travel Card showing a Train</div>
    <div class="turn-step">She moves 2 hops: Airport → Delhi → Agra</div>
    <div class="turn-step">Then plays a "1" card showing Bus</div>
    <div class="turn-step">She moves 1 more hop: Agra → Jaipur</div>
    <div class="turn-step">Total movement: 3 hops using 2 cards</div>
</div>
</div>
<h2>4. Collecting Om Tokens</h2>
<div class="callout-box important">
**If you end movement on a Jyotirlinga location** with an Om Token <span class="icon om-token">ॐ</span> **and have space** (max 3 tokens), immediately:
<ol>
<li><strong>Take the Om Token</strong> from the board</li>
<li><strong>Advance</strong> your pawn on the Om Turn Track one space per token collected this turn</li>
<li><strong>Stack</strong> on top if multiple pawns occupy the same space</li>
</ol>
</div>
<p><strong>Note</strong>: Om Tokens collected through Global Events also advance you on the Om Turn Track.</p>
<h2>5. Acquiring cards (Action B only)</h2>
<p>If you chose Action B this turn, now resolve the card acquisition:</p>
<div class="example-turn">
    <div class="turn-header">card Acquisition Example</div>
    <div class="turn-step">Raj wants the "Taj Mahal" Outer card (Cost: 2 Karma + 1 Artha, Worth: 27 points)</div>
    <div class="turn-step">He pays 2 <span class="icon energy-cube karma"></span> + 1 <span class="icon energy-cube artha"></span> to the supply</div>
    <div class="turn-step">This goes in his 3rd Outer slot, so he pays 2 <span class="icon om-token">ॐ</span></div>
    <div class="turn-step">He places the card and advances his Outer score by 27 points</div>
    <div class="turn-step">The market slot is refilled from the Outer Journey deck</div>
</div>
<h2>6. End-of-Turn Steps</h2>
<ul>
<li><strong>Refill</strong> any empty market slots (cards only)</li>
<li><strong>Pass play</strong> clockwise to the next player</li>
</ul>
<p>When all players have taken a turn, the <strong>round ends</strong>. Proceed to <strong>Om Turn Track &amp; Turn Order</strong> (next section).</p>
<div class="page-break"></div> </section><section id="05-om-turn-track" class="rulebook-section"><h1 class="section-title">Om Turn Track & Turn Order</h1><h1>Om Turn Track &amp; Turn Order</h1>
<p>The <strong>Om Turn Track</strong> creates a dynamic turn order system that rewards strategic Om Token collection and adds tactical depth to your journey planning.</p>
<h2>How It Works</h2>
<div class="sidebar-right">
    <div class="callout-box">
        <h4>Key Points</h4>
        <ul>
            <li>Spaces 0-5 on the track</li>
            <li>Higher = earlier turn order</li>
            <li>Stack markers when tied</li>
            <li>Max advancement: space 5</li>
        </ul>
    </div>
</div>
<div class="main-content with-sidebar-right">
<p>At the end of each round, turn order for the next round is determined by reading the Om Turn Track from <strong>highest to lowest</strong>:</p>
<ol>
<li><strong>Highest pawn</strong> (topmost on tallest stack) becomes <strong>First Player</strong></li>
<li><strong>Second highest</strong> becomes Second Player</li>
<li><strong>Lowest</strong> becomes Last Player</li>
</ol>
<h3>Stacking Rules</h3>
<p>When multiple pawns occupy the same space, they <strong>stack vertically</strong>. The pawn that <strong>arrived most recently</strong> goes on <strong>top</strong> of the stack.</p>
</div>
<h2>Advancing on the Track</h2>
<div class="callout-box important">
<strong>When you collect Om Tokens</strong> <span class="icon om-token">ॐ</span>, <strong>immediately advance</strong> your pawn on the Om Turn Track:
<ul>
<li><strong>1 Om Token</strong> = advance 1 space</li>
<li><strong>2 Om Tokens</strong> = advance 2 spaces</li>
<li><strong>3 Om Tokens</strong> = advance 3 spaces</li>
</ul>
<p><strong>Maximum</strong>: Cannot advance beyond space 5, even with excess tokens.</p>
</div>
<h3>Sources of Om Tokens</h3>
<ul>
<li><strong>Jyotirlinga Visits</strong>: End movement on a Jyotirlinga location with a token</li>
<li><strong>Global Events</strong>: Some Event cards grant Om Tokens to players</li>
<li><strong>Both Sources</strong>: All Om Tokens collected in a turn advance you on the track</li>
</ul>
<h2>Turn Order Examples</h2>
<div class="example-turn">
    <div class="turn-header">Round 1 → Round 2 Turn Order</div>
    <div class="turn-step"><strong>Start of Round 1:</strong> Maya (space 0), Raj (space 1), Anil (space 2)</div>
    <div class="turn-step"><strong>During Round 1:</strong> Maya collects 2 Om Tokens → advances to space 2</div>
    <div class="turn-step"><strong>Raj collects 1 Om Token</strong> → advances to space 2</div>
    <div class="turn-step"><strong>End of Round 1:</strong> Maya and Raj both on space 2, Anil on space 2</div>
    <div class="turn-step"><strong>Stacking:</strong> Raj arrived at space 2 most recently, so goes on top</div>
    <div class="turn-step"><strong>Round 2 Order:</strong> Raj (1st), Maya (2nd), Anil (3rd)</div>
</div>
<div class="board-diagram">
    <div class="image-placeholder medium"><p>Image: Om Turn Track Example</p></div>
    <p class="text-center"><em>Example showing pawn positions and stacking on the Om Turn Track</em></p>
</div>
<h2>Strategic Considerations</h2>
<div class="sidebar-left">
    <div class="callout-box setup">
        <h4>Tactical Tips</h4>
        <p><strong>Early Turns:</strong> Good for securing desired cards</p>
        <p><strong>Late Turns:</strong> Better reaction to Global Events and other players' moves</p>
        <p><strong>Denial:</strong> Sometimes leaving Om Tokens for others can be strategic</p>
    </div>
</div>
<div class="main-content with-sidebar-left">
<h3>When to Prioritize Om Tokens</h3>
<ul>
<li><strong>Powerful Global Events</strong>: Going first lets you capitalize on beneficial events</li>
<li><strong>Limited cards</strong>: Early access to high-value or needed cards</li>
<li><strong>Endgame Timing</strong>: Controlling when the final round triggers</li>
</ul>
<h3>When to Hold Back</h3>
<ul>
<li><strong>Restrictive Events</strong>: Sometimes going later is advantageous</li>
<li><strong>Resource Planning</strong>: Other players’ actions might create better opportunities</li>
<li><strong>Psychological Factor</strong>: Appearing less threatening can be valuable</li>
</ul>
<p>The Om Turn Track transforms what could be a static player order into a dynamic, strategic element that’s deeply integrated with the game’s spiritual theme of collecting sacred tokens.</p>
</div> </section><section id="06-reference" class="rulebook-section"><h1 class="section-title">Reference Material</h1><h1>Reference Material</h1>
<p>This section provides quick reference information for all game components.</p>
<h2>Character Abilities</h2>
<div class="reference-card">
    <div class="card-title">Engineer</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube karma"></span> Karma</div>
    <p>Specializes in action and construction projects. Cannot trade Karma for Karma.</p>
</div>
<div class="reference-card">
    <div class="card-title">Professor</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube gnana"></span> Gnana</div>
    <p>Master of knowledge and wisdom. Cannot trade Gnana for Gnana.</p>
</div>
<div class="reference-card">
    <div class="card-title">Merchant</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube artha"></span> Artha</div>
    <p>Expert in commerce and material wealth. Cannot trade Artha for Artha.</p>
</div>
<div class="reference-card">
    <div class="card-title">Pilgrim</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube bhakti"></span> Bhakti</div>
    <p>Devoted spiritual seeker. Cannot trade Bhakti for Bhakti.</p>
</div>
<div class="page-break"></div>
<h2>Global Event Cards</h2>
<h3>Movement &amp; Travel Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Drizzle of Delay</strong></td>
<td>Max 2 moves; ending in North or East costs 1 <span class="icon energy-cube artha"></span></td>
</tr>
<tr>
<td><strong>Turbulent Skies</strong></td>
<td>No airport travel allowed this round</td>
</tr>
<tr>
<td><strong>Frozen North</strong></td>
<td>Starting in North: moves cost ×2; if moved, +7 Inner points</td>
</tr>
<tr>
<td><strong>Excess Baggage</strong></td>
<td>Hand limit 2; discard down immediately</td>
</tr>
</tbody>
</table>
</div>
<h3>Scoring &amp; Bonus Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Diwali Distraction</strong></td>
<td>All gain +5 Inner points but no cube pickup</td>
</tr>
<tr>
<td><strong>Maha Kumbh</strong></td>
<td>Visit any Jyotirlinga for 7 Inner points; skip for 1 bonus cube</td>
</tr>
<tr>
<td><strong>Bountiful Bhandara</strong></td>
<td>Draw 2 random cubes; +5 Outer points if any card collected</td>
</tr>
<tr>
<td><strong>Triathlon</strong></td>
<td>+7 Outer points if three different Travel card values used</td>
</tr>
</tbody>
</table>
</div>
<h3>Character-Specific Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Merchant’s Midas</strong></td>
<td>Merchant trade for Artha: +7 Outer points</td>
</tr>
<tr>
<td><strong>Professor’s Insight</strong></td>
<td>Professor trade for Gnana: +7 Inner points</td>
</tr>
<tr>
<td><strong>Pilgrim’s Grace</strong></td>
<td>Pilgrim trade for Bhakti: +7 Inner points</td>
</tr>
<tr>
<td><strong>Engineer’s Precision</strong></td>
<td>Engineer trade for Karma: +7 Outer points</td>
</tr>
</tbody>
</table>
</div>
<h3>Vehicle-Specific Events</h3>
<div class="component-table">
<table>
<thead>
<tr>
<th>Event</th>
<th>Effect</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Eco Trail</strong></td>
<td>Cycle or Trek: +5 Inner points</td>
</tr>
<tr>
<td><strong>Rajput Caravans</strong></td>
<td>Horse or Camel: +5 Outer points</td>
</tr>
<tr>
<td><strong>Urban Ride</strong></td>
<td>Motorbike or Rickshaw: +5 Outer points</td>
</tr>
<tr>
<td><strong>Road Warriors</strong></td>
<td>Car or Bus: +5 Outer points</td>
</tr>
<tr>
<td><strong>Rails and Sails</strong></td>
<td>Train or Boat: +5 Outer points</td>
</tr>
<tr>
<td><strong>Heavy Haul</strong></td>
<td>Use Truck for 10 Outer points but lose 2 cubes</td>
</tr>
</tbody>
</table>
</div>
<div class="page-break"></div>
<h2>cards Reference</h2>
<h3>Outer cards (Selection)</h3>
<div class="reference-card">
    <div class="card-title">Taj Mahal (North)</div>
    <div class="card-cost">2 <span class="icon energy-cube karma"></span> + 1 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">27 pts</div>
</div>
<div class="reference-card">
    <div class="card-title">Jaisalmer Fort (West)</div>
    <div class="card-cost">2 <span class="icon energy-cube karma"></span> + 2 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">30 pts</div>
</div>
<div class="reference-card">
    <div class="card-title">Hampi (South)</div>
    <div class="card-cost">2 <span class="icon energy-cube karma"></span> + 1 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">27 pts</div>
</div>
<div class="reference-card">
    <div class="card-title">Living Root Bridge (NE)</div>
    <div class="card-cost">1 <span class="icon energy-cube karma"></span> + 2 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">27 pts</div>
</div>
<h3>Inner cards (Selection)</h3>
<div class="reference-card">
    <div class="card-title">Golden Temple (North)</div>
    <div class="card-cost">2 <span class="icon energy-cube bhakti"></span> + 2 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">30 pts</div>
</div>
<div class="reference-card">
    <div class="card-title">Meenakshi Temple (South)</div>
    <div class="card-cost">2 <span class="icon energy-cube bhakti"></span> + 2 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">30 pts</div>
</div>
<div class="reference-card">
    <div class="card-title">Jagannath Temple (East)</div>
    <div class="card-cost">2 <span class="icon energy-cube bhakti"></span> + 2 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">30 pts</div>
</div>
<div class="reference-card">
    <div class="card-title">Kamakhya Temple (NE)</div>
    <div class="card-cost">1 <span class="icon energy-cube bhakti"></span> + 1 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">24 pts</div>
</div>
<h2>Quick Reference Summary</h2>
<div class="callout-box important">
<h3>Turn Sequence</h3>
<ol>
<li>Character Trading (optional)</li>
<li>Main Action (A: Travel Cards OR B: card)</li>
<li>Travelling (optional)</li>
<li>Collect Om Tokens (if applicable)</li>
<li>Acquire card (if Action B)</li>
<li>End-of-Turn cleanup</li>
</ol>
<h3>Victory Condition</h3>
<p>First player to exceed <strong>100 combined points</strong> (Outer + Inner) triggers final round.</p>
<h3>Om Token Collection</h3>
<ul>
<li>Max 3 tokens per player</li>
<li>Advance 1 space per token on Om Turn Track</li>
<li>Collected from Jyotirlingas and some Global Events</li>
</ul>
</div> </section>
    </div>
    
    <!-- Footer with icon legend -->
    <div class="icon-legend">
        <h3>Icon Reference</h3>
        <div class="legend-row">
            <div class="legend-item">
                <div class="icon energy-cube artha"></div>
                <span>Artha (Wealth)</span>
            </div>
            <div class="legend-item">
                <div class="icon energy-cube karma"></div>
                <span>Karma (Action)</span>
            </div>
            <div class="legend-item">
                <div class="icon energy-cube gnana"></div>
                <span>Gnana (Knowledge)</span>
            </div>
            <div class="legend-item">
                <div class="icon energy-cube bhakti"></div>
                <span>Bhakti (Devotion)</span>
            </div>
            <div class="legend-item">
                <div class="icon om-token">ॐ</div>
                <span>Om Token</span>
            </div>
        </div>
    </div>
</body>
</html> 