/* SimulationDashboard.css */
.simulation-dashboard {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  background-color: #f9f9f9;
  min-height: 100vh;
}

.simulation-dashboard h1 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
}

.simulation-dashboard h2 {
  color: #3498db;
  margin: 20px 0 15px;
  font-size: 1.4rem;
}

.simulation-dashboard h3 {
  color: #2c3e50;
  margin: 15px 0 10px;
  font-size: 1.1rem;
}

/* Loading and Error States */
.simulation-dashboard.loading,
.simulation-dashboard.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  text-align: center;
}

.simulation-dashboard.error button {
  margin-top: 20px;
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Navigation */
.dashboard-nav {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.dashboard-nav button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  color: #7f8c8d;
  position: relative;
}

.dashboard-nav button.active {
  color: #3498db;
}

.dashboard-nav button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #3498db;
}

/* Overview Tab */
.dashboard-overview {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.metric-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px;
  text-align: center;
}

.metric-card h3 {
  font-size: 0.9rem;
  margin: 0 0 10px;
  color: #7f8c8d;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.pulse-dot {
  width: 10px;
  height: 10px;
  background-color: #27ae60;
  border-radius: 50%;
  display: inline-block;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
  }
}

/* Player Statistics */
.player-stats-panel {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow-x: auto;
}

.player-stats-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.player-stats-table th,
.player-stats-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.player-stats-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #7f8c8d;
}

/* Event Distribution */
.event-stats-panel {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.event-bars {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.event-bar-container {
  display: flex;
  align-items: center;
}

.event-label {
  width: 150px;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.event-bar-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  height: 24px;
}

.event-bar {
  height: 24px;
  background-color: #3498db;
  border-radius: 4px;
  min-width: 2px;
}

.event-count {
  margin-left: 10px;
  font-size: 0.9rem;
  color: #7f8c8d;
}

/* Control Panel */
.dashboard-control {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.simulation-config {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-group label {
  font-weight: bold;
  color: #7f8c8d;
}

.bot-config {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.bot-strategy-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bot-strategy-selector select {
  flex: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 0.9rem;
}

.remove-bot {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-bot {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  margin-top: 10px;
  align-self: flex-start;
}

.simulation-controls {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.start-simulation,
.stop-simulation {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.start-simulation {
  background-color: #27ae60;
  color: white;
}

.start-simulation:hover {
  background-color: #2ecc71;
}

.stop-simulation {
  background-color: #e74c3c;
  color: white;
}

.stop-simulation:hover {
  background-color: #f5574a;
}

.current-simulation {
  margin-top: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e0e0e0;
}

.progress-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

/* Simulation Details */
.dashboard-details {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.simulation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.simulation-meta {
  display: flex;
  gap: 20px;
}

.winner-panel {
  margin-bottom: 30px;
}

.winner-card {
  background-color: #fff9e0;
  border: 2px solid #f1c40f;
  border-radius: 8px;
  padding: 15px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 15px;
  max-width: 300px;
}

.winner-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.winner-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #f1c40f;
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
  padding: 5px 10px;
  border-radius: 4px;
}

.players-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.player-card {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
}

.player-card.winner {
  border-color: #f1c40f;
  background-color: #fff9e0;
}

.player-name {
  font-weight: bold;
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: #2c3e50;
}

.player-score-breakdown {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.player-om {
  font-weight: bold;
  color: #8e44ad;
}

.events-panel {
  margin-top: 30px;
}

.event-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.event-filters button {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
}

.event-filters button:hover {
  background-color: #e9ecef;
}

.events-log {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.events-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.events-table th,
.events-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.events-table th {
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
}

.event-detail {
  margin-bottom: 5px;
}

.detail-key {
  font-weight: bold;
  margin-right: 5px;
}

/* Event type styling */
.event-type-MOVE {
  background-color: #e8f7f9;
}

.event-type-PICK_CARDS {
  background-color: #e8f4f9;
}

.event-type-COLLECT_JOURNEY {
  background-color: #f9f4e8;
}

.event-type-END_TURN {
  background-color: #f9e8e8;
}

.event-type-GAME_START {
  background-color: #e8f9e8;
}

.event-type-GAME_END {
  background-color: #f9e8f9;
}

.no-events {
  padding: 20px;
  text-align: center;
  color: #7f8c8d;
}

/* Responsive Design */
@media (min-width: 768px) {
  .dashboard-overview {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 1200px) {
  .dashboard-overview {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .simulation-meta {
    flex-direction: column;
    gap: 5px;
  }
  
  .metric-card {
    padding: 10px;
  }
  
  .metric-value {
    font-size: 1.5rem;
  }
  
  .player-score-breakdown {
    grid-template-columns: 1fr;
  }
} 