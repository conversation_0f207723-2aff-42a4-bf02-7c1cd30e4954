{"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> won in an epic finale where he reached convergence and <PERSON><PERSON><PERSON> was about to win with a higher convergence but got blocked by Dr<PERSON> of Spirits", "players": [{"id": "XChbcADtwbILF8cWAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 10, "hand": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "energyCubes": ["gnana", "bhakti", "karma"], "omTemp": [1], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}], "outerScore": 45, "innerScore": 54, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "y6_KO_xC6PKXEMQjAAAD", "name": "NaughtyNunko", "position": 5, "hand": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "energyCubes": ["gnana"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "outerScore": 44, "innerScore": 63, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}], "started": true, "turnIndex": 1, "roundCount": 12, "travelDeck": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "eventDeck": [{"id": "E4", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}, {"id": "E8", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "travelDiscard": [], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "faceUpEvent": [{"id": "E2", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}, {"id": "E1", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "faceUpJourneyOuter": [{"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "locationCubes": {"1": "artha", "2": "bhakti", "6": "karma", "7": "artha", "11": "karma", "15": "bhakti", "18": "bhakti", "19": "artha", "20": "gnana", "22": "karma", "24": "gnana", "25": "gnana", "27": "artha", "28": "gnana", "29": "bhakti", "30": "artha", "31": "artha", "32": "artha", "33": "gnana", "36": "karma", "38": "karma", "39": "karma", "45": "gnana", "46": "artha", "47": "gnana", "48": "artha", "undefined": "bhakti"}, "locationOm": {"52": true, "53": true, "55": true, "58": true}, "finalRound": true, "finalRoundStarter": 1, "finalRoundEnd": 0, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849402486, "data": {"eventId": "global-event-3", "eventName": "<PERSON><PERSON>", "eventEffect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849538130, "data": {"cardType": "travel", "card": {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849538130, "data": {"cardType": "travel", "card": {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849538130, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849538130, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "move", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849825372, "data": {"path": [63, 21, 20, 51], "travelCards": ["T5", "T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849868005, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744849874556, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1744849879363, "data": {"nextPlayerId": "li8k0-ZVrz4SIijoAAAD", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 0, "turnIndex": 1, "timestamp": 1744849907083, "data": {"path": [62, 11, 7, 54], "travelCards": ["T4", "T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 0, "turnIndex": 1, "timestamp": 1744849915273, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1744849919474, "data": {"eventId": "global-event-17", "eventName": "Top Gear", "eventEffect": "top_gear_reward"}}, {"type": "endTurn", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 1, "turnIndex": 0, "timestamp": 1744849919475, "data": {"nextPlayerId": "UNJFdFaIJfaj58y5AAAB", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1744849990054, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1744850007879, "data": {"cardType": "travel", "drawnCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "travel_card_reward", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1744850029259, "data": {"vehicle": "car", "effect": "top_gear_reward", "outerPoints": 5}}, {"type": "move", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1744850029259, "data": {"path": [51, 20, 21], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1744850325040, "data": {"nextPlayerId": "li8k0-ZVrz4SIijoAAAD", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 1, "turnIndex": 1, "timestamp": 1744850420846, "data": {"path": [54, 9], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 1, "turnIndex": 1, "timestamp": 1744850426089, "data": {"journeyType": "outer", "journeyCardId": "JO4", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1744850428731, "data": {"eventId": "global-event-8", "eventName": "Triathlon", "eventEffect": "triathlon_bonus"}}, {"type": "endTurn", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 2, "turnIndex": 0, "timestamp": 1744850428731, "data": {"nextPlayerId": "UNJFdFaIJfaj58y5AAAB", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1744850558353, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1744850660208, "data": {"path": [21, 22, 23, 59], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744850690492, "data": {"nextPlayerId": "li8k0-ZVrz4SIijoAAAD", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 2, "turnIndex": 1, "timestamp": 1744850740710, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 2, "turnIndex": 1, "timestamp": 1744850756314, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 2, "turnIndex": 1, "timestamp": 1744850808423, "data": {"path": [9, 1, 2, 3, 56], "travelCards": ["T1", "T10"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1744850814377, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "endTurn", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 3, "turnIndex": 0, "timestamp": 1744850814377, "data": {"nextPlayerId": "UNJFdFaIJfaj58y5AAAB", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1744850966731, "data": {"path": [59, 23], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1744851106853, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "UNJFdFaIJfaj58y5AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1744851108674, "data": {"nextPlayerId": "li8k0-ZVrz4SIijoAAAD", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "li8k0-ZVrz4SIijoAAAD", "playerName": "NaughtyNunko", "roundCount": 3, "turnIndex": 1, "timestamp": 1744851124247, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 1, "timestamp": 1744851322595, "data": {"timestamp": 1744851322595, "loadedStateRoundCount": 3, "playerCount": 2}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1744851336374, "data": {"eventId": "global-event-19", "eventName": "Bullet Train", "eventEffect": "bullet_train_reward"}}, {"type": "endTurn", "playerId": "QMRiPaG8Pqqq5WDnAAAD", "playerName": "NaughtyNunko", "roundCount": 4, "turnIndex": 0, "timestamp": 1744851336374, "data": {"nextPlayerId": "1AH6kFbyvtAPYScgAAAB", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1744851367659, "data": {"path": [23, 38, 37], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1744851373504, "data": {"journeyType": "outer", "journeyCardId": "JO20", "omRequirement": 1}}, {"type": "endTurn", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1744851380352, "data": {"nextPlayerId": "QMRiPaG8Pqqq5WDnAAAD", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "QMRiPaG8Pqqq5WDnAAAD", "playerName": "NaughtyNunko", "roundCount": 4, "turnIndex": 1, "timestamp": 1744851441224, "data": {"path": [3, 4], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "QMRiPaG8Pqqq5WDnAAAD", "playerName": "NaughtyNunko", "roundCount": 4, "turnIndex": 1, "timestamp": 1744851449871, "data": {"journeyType": "inner", "journeyCardId": "JI3", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1744851452961, "data": {"eventId": "global-event-12", "eventName": "Footpath Reverie", "eventEffect": "footpath_reverie_reward"}}, {"type": "endTurn", "playerId": "QMRiPaG8Pqqq5WDnAAAD", "playerName": "NaughtyNunko", "roundCount": 5, "turnIndex": 0, "timestamp": 1744851452961, "data": {"nextPlayerId": "1AH6kFbyvtAPYScgAAAB", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1744851549319, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1744851570361, "data": {"vehicle": "trek", "effect": "footpath_reverie_reward", "innerPoints": 5}}, {"type": "move", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1744851570361, "data": {"path": [37, 65, 62, 13, 14], "travelCards": ["T12", "T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1744851601747, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1744851608210, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "1AH6kFbyvtAPYScgAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1744851614781, "data": {"nextPlayerId": "QMRiPaG8Pqqq5WDnAAAD", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 1, "timestamp": 1744851744534, "data": {"timestamp": 1744851744534, "loadedStateRoundCount": 5, "playerCount": 2}}, {"type": "move", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 5, "turnIndex": 1, "timestamp": 1744851833478, "data": {"path": [4, 3, 47, 57], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 5, "turnIndex": 1, "timestamp": 1744851838565, "data": {"cardType": "travel", "drawnCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 5, "turnIndex": 1, "timestamp": 1744851849938, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1744851854390, "data": {"eventId": "global-event-30", "eventName": "Breezy East", "eventEffect": "breezy_east"}}, {"type": "endTurn", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 6, "turnIndex": 0, "timestamp": 1744851854390, "data": {"nextPlayerId": "HsN6Qg3uQxiuEITzAAAD", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1744851879804, "data": {"path": [14, 16], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1744851884504, "data": {"journeyType": "inner", "journeyCardId": "JI9", "omRequirement": 1}}, {"type": "endTurn", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1744851920075, "data": {"nextPlayerId": "QZ2rP4NmkisoTMuyAAAB", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 6, "turnIndex": 1, "timestamp": 1744851977241, "data": {"path": [57, 48, 42, 43], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 6, "turnIndex": 1, "timestamp": 1744851989678, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1744851994368, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "endTurn", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 7, "turnIndex": 0, "timestamp": 1744851994368, "data": {"nextPlayerId": "HsN6Qg3uQxiuEITzAAAD", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1744852031601, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1744852039272, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "region_based_reward", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1744852094467, "data": {"region": "West", "vehicle": "camel", "effect": "sandy_west", "innerPoints": 7}}, {"type": "move", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1744852094467, "data": {"path": [16, 14, 13, 12], "travelCards": ["T7", "T1"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744852099176, "data": {"nextPlayerId": "QZ2rP4NmkisoTMuyAAAB", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 7, "turnIndex": 1, "timestamp": 1744852107265, "data": {"path": [43, 42], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 7, "turnIndex": 1, "timestamp": 1744852110434, "data": {"journeyType": "inner", "journeyCardId": "JI21", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1744852115942, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "endTurn", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 8, "turnIndex": 0, "timestamp": 1744852115943, "data": {"nextPlayerId": "HsN6Qg3uQxiuEITzAAAD", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1744852176282, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1744852201679, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1744852210609, "data": {"path": [12, 50], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1744852220492, "data": {"nextPlayerId": "QZ2rP4NmkisoTMuyAAAB", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 8, "turnIndex": 1, "timestamp": 1744852288249, "data": {"path": [42, 43, 60], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 8, "turnIndex": 1, "timestamp": 1744852310131, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1744852315714, "data": {"eventId": "global-event-9", "eventName": "Riots", "eventEffect": "riots_discard"}}, {"type": "endTurn", "playerId": "QZ2rP4NmkisoTMuyAAAB", "playerName": "NaughtyNunko", "roundCount": 9, "turnIndex": 0, "timestamp": 1744852315715, "data": {"nextPlayerId": "HsN6Qg3uQxiuEITzAAAD", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1744852402223, "data": {"path": [50, 12, 13], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1744852406888, "data": {"journeyType": "outer", "journeyCardId": "JO7", "omRequirement": 1}}, {"type": "endTurn", "playerId": "HsN6Qg3uQxiuEITzAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1744852410675, "data": {"nextPlayerId": "QZ2rP4NmkisoTMuyAAAB", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 1, "timestamp": 1744852603756, "data": {"timestamp": 1744852603756, "loadedStateRoundCount": 9, "playerCount": 2}}, {"type": "move", "playerId": "1WYRKWheOzjSvMnlAAAB", "playerName": "NaughtyNunko", "roundCount": 9, "turnIndex": 1, "timestamp": 1744852665393, "data": {"path": [60, 43, 44], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1WYRKWheOzjSvMnlAAAB", "playerName": "NaughtyNunko", "roundCount": 9, "turnIndex": 1, "timestamp": 1744852706486, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1744852711163, "data": {"eventId": "global-event-7", "eventName": "Election Campaigns", "eventEffect": "double_trade_no_travel"}}, {"type": "endTurn", "playerId": "1WYRKWheOzjSvMnlAAAB", "playerName": "NaughtyNunko", "roundCount": 10, "turnIndex": 0, "timestamp": 1744852711164, "data": {"nextPlayerId": "ze2SNTXxXWnLAXEMAAAD", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "endTurn", "playerId": "ze2SNTXxXWnLAXEMAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1744852781062, "data": {"nextPlayerId": "1WYRKWheOzjSvMnlAAAB", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1744852788496, "data": {"eventId": "global-event-10", "eventName": "Om Meditation", "eventEffect": "om_meditation"}}, {"type": "endTurn", "playerId": "1WYRKWheOzjSvMnlAAAB", "playerName": "NaughtyNunko", "roundCount": 11, "turnIndex": 0, "timestamp": 1744852788498, "data": {"nextPlayerId": "ze2SNTXxXWnLAXEMAAAD", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1744852856904, "data": {"timestamp": 1744852856904, "loadedStateRoundCount": 11, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1744853074741, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1744853089497, "data": {"cardType": "travel", "drawnCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickFromTop": true}}, {"type": "move", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1744853099057, "data": {"path": [13, 34], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1744853101832, "data": {"nextPlayerId": "y6_KO_xC6PKXEMQjAAAD", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "y6_KO_xC6PKXEMQjAAAD", "playerName": "NaughtyNunko", "roundCount": 11, "turnIndex": 1, "timestamp": 1744853184794, "data": {"path": [44, 66, 64, 15, 5], "travelCards": ["T4", "T9"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "y6_KO_xC6PKXEMQjAAAD", "playerName": "NaughtyNunko", "roundCount": 11, "turnIndex": 1, "timestamp": 1744853189996, "data": {"journeyType": "outer", "journeyCardId": "JO2", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "y6_KO_xC6PKXEMQjAAAD", "playerName": "NaughtyNunko", "roundCount": 11, "turnIndex": 1, "timestamp": 1744853189997, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 107, "playerOmTotal": 4, "finalRoundStarter": 1, "finalRoundEnd": 0}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1744853193029, "data": {"eventId": "global-event-4", "eventName": "Drought of Spirits", "eventEffect": "no_inner_journey_cards"}}, {"type": "endTurn", "playerId": "y6_KO_xC6PKXEMQjAAAD", "playerName": "NaughtyNunko", "roundCount": 12, "turnIndex": 0, "timestamp": 1744853193029, "data": {"nextPlayerId": "XChbcADtwbILF8cWAAAB", "newRound": true, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1744853337131, "data": {"path": [34, 13, 12, 11, 10], "travelCards": ["T11", "T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1744853341910, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1744853344196, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "XChbcADtwbILF8cWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1744853345678, "data": {"nextPlayerId": "y6_KO_xC6PKXEMQjAAAD", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 1, "timestamp": 1744853345680, "data": {"winner": {"id": "y6_KO_xC6PKXEMQjAAAD", "name": "NaughtyNunko", "outerScore": 44, "innerScore": 63, "totalScore": 107, "omTotal": 4}, "totalRounds": 12, "players": [{"id": "XChbcADtwbILF8cWAAAB", "name": "<PERSON><PERSON><PERSON>", "outerScore": 45, "innerScore": 54, "totalScore": 99, "omTotal": 4}, {"id": "y6_KO_xC6PKXEMQjAAAD", "name": "NaughtyNunko", "outerScore": 44, "innerScore": 63, "totalScore": 107, "omTotal": 4}]}}], "characterDeck": [{"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}], "energyCubePile": {"artha": 3, "karma": 6, "gnana": 5, "bhakti": 6}, "currentGlobalEvent": {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, "globalEventDeck": [{"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}], "globalEventDiscard": [{"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}], "nameMode": false}