const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Configuration
const sourceDir = '../../client/public/assets/images/vehicles';
const targetDir = '../../client/public/assets/images/vehicles-resized';
const targetWidth = 825;
const targetHeight = 1125;

// Create target directory if it doesn't exist
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Process all vehicle subdirectories (1, 2, 3)
async function processImages() {
  try {
    // Get all subdirectories
    const vehicleDirs = fs.readdirSync(sourceDir)
      .filter(item => !item.startsWith('.') && fs.statSync(path.join(sourceDir, item)).isDirectory());

    console.log(`Found ${vehicleDirs.length} vehicle directories: ${vehicleDirs.join(', ')}`);

    // Process each directory
    for (const dir of vehicleDirs) {
      const sourceDirPath = path.join(sourceDir, dir);
      const targetDirPath = path.join(targetDir, dir);

      // Create target subdirectory
      if (!fs.existsSync(targetDirPath)) {
        fs.mkdirSync(targetDirPath, { recursive: true });
      }

      // Get all image files
      const imageFiles = fs.readdirSync(sourceDirPath)
        .filter(file => !file.startsWith('.') && file.toLowerCase().endsWith('.png'));

      console.log(`Processing directory ${dir}: Found ${imageFiles.length} images`);

      // Process each image
      for (const imageFile of imageFiles) {
        const sourceImagePath = path.join(sourceDirPath, imageFile);
        const targetImagePath = path.join(targetDirPath, imageFile);

        console.log(`Resizing: ${sourceImagePath} -> ${targetImagePath}`);

        // Resize the image
        await sharp(sourceImagePath)
          .resize({
            width: targetWidth,
            height: targetHeight,
            fit: 'contain',
            background: { r: 0, g: 0, b: 0, alpha: 0 } // Transparent background
          })
          .toFile(targetImagePath);
      }
    }

    console.log('All images have been resized successfully!');
    console.log(`Resized images are saved in: ${targetDir}`);
  } catch (error) {
    console.error('Error processing images:', error);
  }
}

// Run the process
processImages();
