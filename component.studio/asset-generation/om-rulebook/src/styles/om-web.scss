// Om: The Journey Web Rulebook Styles
// Based on the TTW-inspired design but optimized for web viewing

// Color Palette
$navy-blue: #205F80;
$burnt-orange: #E87E1E;
$sand: #F4EBDD;
$body-grey: #555;
$white: #FFFFFF;

// Typography
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue:wght@400&family=Lato:wght@400;700&display=swap');

// Base Styles
html {
  font-size: 16px;
  line-height: 1.5;
  scroll-behavior: smooth;
}

body {
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: $body-grey;
  background: $white;
  margin: 0;
  padding: 0;
}

// Typography Hierarchy
h1 {
  font-family: 'Bebas Neue', cursive;
  font-size: 2.5rem;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
}

h2 {
  font-family: '<PERSON><PERSON> Neue', cursive;
  font-size: 2rem;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
}

h3 {
  font-family: 'Lato', sans-serif;
  font-size: 1.3rem;
  color: $navy-blue;
  font-weight: 700;
  margin: 1.2rem 0 0.8rem;
}

h4 {
  font-family: 'Lato', sans-serif;
  font-size: 1.1rem;
  color: $burnt-orange;
  font-weight: 700;
  margin: 1rem 0 0.5rem;
}

p {
  margin: 0 0 1rem;
}

// Main Layout
.rulebook-container {
  max-width: 1200px;
  margin: 80px auto 2rem;
  padding: 0 1rem;
}

.rulebook-section {
  margin-bottom: 3rem;
  padding-top: 1rem;
}

.section-title {
  color: $navy-blue;
  border-bottom: 2px solid $burnt-orange;
  padding-bottom: 0.5rem;
}

// Lists
ul, ol {
  margin: 0 0 1rem;
  padding-left: 1.5rem;
  
  li {
    margin-bottom: 0.5rem;
  }
}

// Boxes & Call-outs
.callout-box {
  background: $sand;
  border: 2px solid $navy-blue;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &.important {
    background: lighten($burnt-orange, 40%);
    border-color: $burnt-orange;
  }
  
  &.setup {
    background: lighten($navy-blue, 45%);
    border-color: $navy-blue;
  }
  
  h3 {
    margin-top: 0;
  }
  
  p:last-child {
    margin-bottom: 0;
  }
}

// Tables
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.9rem;
  
  th {
    background: $navy-blue;
    color: $white;
    padding: 0.75rem;
    font-weight: 700;
    text-align: left;
  }
  
  td {
    padding: 0.75rem;
    border-bottom: 1px solid lighten($navy-blue, 20%);
  }
  
  tr:nth-child(even) {
    background: darken($sand, 5%);
  }
}

// Component table (3-column for Om's components)
.component-table {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
  
  .component-item {
    border: 1px solid $navy-blue;
    border-radius: 6px;
    padding: 1rem;
    background: $sand;
    
    img {
      max-width: 100%;
      height: auto;
      border: 2px solid white;
      border-radius: 4px;
      margin-bottom: 0.75rem;
    }
    
    h4 {
      margin-top: 0;
      color: $navy-blue;
    }
  }
}

// Cover page
.cover-page {
  text-align: center;
  margin: 2rem 0 3rem;
  
  .game-title {
    font-family: 'Bebas Neue', cursive;
    font-size: 3.5rem;
    color: $navy-blue;
    margin-bottom: 1rem;
  }
  
  .game-subtitle {
    font-family: 'Lato', sans-serif;
    font-size: 1.5rem;
    color: $burnt-orange;
    margin-bottom: 2rem;
  }
  
  .cover-image {
    max-width: 100%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

// Icons and components
.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 6px;
}

.energy-cube {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  
  &.artha {
    background-color: #FFD700; // Gold for wealth
  }
  
  &.karma {
    background-color: #E64A19; // Deep orange for action
  }
  
  &.gnana {
    background-color: #1976D2; // Blue for knowledge 
  }
  
  &.bhakti {
    background-color: #7B1FA2; // Purple for devotion
  }
}

.om-token {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: $burnt-orange;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

// Icon legend in footer
.icon-legend {
  max-width: 800px;
  margin: 0 auto;
  
  h3 {
    text-align: center;
    margin-bottom: 1rem;
  }
  
  .legend-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    
    span {
      margin-left: 0.5rem;
    }
  }
}

// Images
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1.5rem auto;
  border-radius: 4px;
}

.image-caption {
  text-align: center;
  font-size: 0.9rem;
  color: $navy-blue;
  margin-top: -1rem;
  margin-bottom: 1.5rem;
  font-style: italic;
}

// Two-column layout for larger screens
@media (min-width: 768px) {
  .two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 1.5rem 0;
  }
  
  .three-column {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin: 1.5rem 0;
  }
  
  .sidebar-content {
    display: grid;
    grid-template-columns: 7fr 3fr;
    gap: 2rem;
    margin: 1.5rem 0;
    
    &.sidebar-left {
      grid-template-columns: 3fr 7fr;
    }
    
    .sidebar {
      background: $sand;
      padding: 1rem;
      border-radius: 6px;
      border-left: 4px solid $burnt-orange;
      
      h3 {
        margin-top: 0;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 767px) {
  html {
    font-size: 14px;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.7rem;
  }
  
  .rulebook-container {
    padding: 0 0.75rem;
  }
  
  table {
    font-size: 0.8rem;
  }
  
  th, td {
    padding: 0.5rem;
  }
  
  .legend-row {
    flex-direction: column;
    align-items: flex-start;
  }
} 