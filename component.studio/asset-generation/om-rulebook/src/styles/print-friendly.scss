// Om: The Journey Print-Friendly Rulebook Styles

// Color Palette
$navy-blue: #205F80;
$burnt-orange: #E87E1E;
$sand: #F4EBDD;
$body-grey: #333;
$white: #FFFFFF;

// Typography
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue:wght@400&family=Lato:wght@400;700&display=swap');

// Base Styles
html {
  font-size: 12pt; // Standard for print
  line-height: 1.5;
}

body {
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: $body-grey;
  background: $white;
  margin: 0;
  padding: 0;
}

// Typography Hierarchy
h1 {
  font-family: '<PERSON>bas Neue', cursive;
  font-size: 2.5rem;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
  page-break-after: avoid;
}

h2 {
  font-family: '<PERSON><PERSON> Neue', cursive;
  font-size: 2rem;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
  page-break-after: avoid;
}

h3 {
  font-family: 'Lato', sans-serif;
  font-size: 1.3rem;
  color: $navy-blue;
  font-weight: 700;
  margin: 1.2rem 0 0.8rem;
  page-break-after: avoid;
}

h4 {
  font-family: 'Lato', sans-serif;
  font-size: 1.1rem;
  color: $burnt-orange;
  font-weight: 700;
  margin: 1rem 0 0.5rem;
  page-break-after: avoid;
}

p {
  margin: 0 0 1rem;
}

strong, b {
  font-weight: 700;
}

// Main Layout
.rulebook-container {
  margin: 0;
  padding: 0;
}

.rulebook-section {
  margin-bottom: 1rem;
  page-break-before: always;
  
  &:first-of-type {
    page-break-before: auto;
  }
}

.section-title {
  color: $navy-blue;
  border-bottom: 2px solid $burnt-orange;
  padding-bottom: 0.5rem;
}

// Lists
ul, ol {
  margin: 0 0 1rem;
  padding-left: 1.5rem;
  
  li {
    margin-bottom: 0.5rem;
    page-break-inside: avoid;
  }
}

// Boxes & Call-outs
.callout-box {
  background: $sand;
  border: 2px solid $navy-blue;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem 0;
  page-break-inside: avoid;
  
  &.important {
    background: lighten($burnt-orange, 40%);
    border-color: $burnt-orange;
  }
  
  &.setup {
    background: lighten($navy-blue, 45%);
    border-color: $navy-blue;
  }
  
  h3 {
    margin-top: 0;
  }
  
  p:last-child {
    margin-bottom: 0;
  }
}

// Tables
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.9rem;
  page-break-inside: avoid;
  
  th {
    background: $navy-blue;
    color: $white;
    padding: 0.75rem;
    font-weight: 700;
    text-align: left;
  }
  
  td {
    padding: 0.75rem;
    border-bottom: 1px solid lighten($navy-blue, 20%);
  }
  
  tr:nth-child(even) {
    background: darken($sand, 5%);
  }
}

// Component table (3-column for Om's components)
.component-table {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
  
  .component-item {
    border: 1px solid $navy-blue;
    border-radius: 6px;
    padding: 1rem;
    background: $sand;
    page-break-inside: avoid;
    
    img {
      max-width: 100%;
      height: auto;
      border: 2px solid white;
      border-radius: 4px;
      margin-bottom: 0.75rem;
    }
    
    h4 {
      margin-top: 0;
      color: $navy-blue;
    }
  }
}

// Cover page
.cover-page {
  text-align: center;
  margin: 0;
  padding: 0;
  height: 10.5in;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  page-break-after: always;
  
  .game-title {
    font-family: 'Bebas Neue', cursive;
    font-size: 3.5rem;
    color: $navy-blue;
    margin-bottom: 1rem;
  }
  
  .game-subtitle {
    font-family: 'Lato', sans-serif;
    font-size: 1.5rem;
    color: $burnt-orange;
    margin-bottom: 2rem;
  }
  
  .cover-image {
    max-width: 80%;
    height: auto;
    margin: 1.5rem 0;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}

// Icons and components
.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 6px;
}

.energy-cube {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: inline-block;
  
  &.artha {
    background-color: #FFD700; // Gold for wealth
  }
  
  &.karma {
    background-color: #4CAF50; // Green for action - FIXED as per requirement
  }
  
  &.gnana {
    background-color: #1976D2; // Blue for knowledge 
  }
  
  &.bhakti {
    background-color: #7B1FA2; // Purple for devotion
  }
}

.om-token {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: $burnt-orange;
  border-radius: 50%;
  color: white;
  font-weight: bold;
}

// Image placeholders
.image-placeholder {
  background-color: #f0f0f0;
  border: 2px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem 0;
  page-break-inside: avoid;
  
  &.small {
    width: 100%;
    height: 150px;
  }
  
  &.medium {
    width: 100%;
    height: 250px;
  }
  
  &.large {
    width: 100%;
    height: 350px;
  }
  
  p {
    font-style: italic;
    color: #777;
  }
}

// Print-specific styles
@media print {
  a {
    text-decoration: none;
    color: $body-grey;
  }
  
  .page-break {
    page-break-before: always;
  }
  
  .no-break {
    page-break-inside: avoid;
  }
  
  .board-diagram, .game-image {
    max-width: 100%;
    page-break-inside: avoid;
  }
  
  // Hide any web-specific elements
  .rulebook-nav, 
  .back-to-top,
  .rulebook-footer {
    display: none;
  }
  
  // Add page numbers
  @page {
    @bottom-center {
      content: counter(page);
    }
  }
  
  // Make sure the icon legend appears at the end
  .icon-legend {
    page-break-before: always;
  }
}

// Layout helpers
.text-center {
  text-align: center;
}

.example-box {
  background: lighten($navy-blue, 60%);
  border: 1px solid $navy-blue;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem 0;
  page-break-inside: avoid;
  
  h4 {
    margin-top: 0;
    color: $navy-blue;
  }
} 