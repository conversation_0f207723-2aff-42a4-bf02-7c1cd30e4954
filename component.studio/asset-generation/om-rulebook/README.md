# Om: The Journey - Professional Rulebook

A professional-grade rulebook for the **Om: The Journey** board game, built using HTML/CSS with Paged.js for PDF generation. This project follows the visual specifications inspired by Trekking the World (TTW) for a polished, print-ready result.

## 🎯 Features

- **Professional Typography**: <PERSON>bas Neue headings + Lato body text
- **TTW-Inspired Layout**: ½-inch margins, 12-column grid, 24pt baseline
- **Color Palette**: Navy blue (#205F80), burnt orange (#E87E1E), sand (#F4EBDD)
- **Visual Elements**: Rounded call-out boxes, component spreads, icon legends
- **Responsive Design**: Works for both screen viewing and print
- **Live Development**: Watch mode with auto-rebuild and browser refresh

## 📁 Project Structure

```
om-rulebook/
├─ src/
│  ├─ pages/               # Markdown content files
│  │  ├─ 01-introduction.md
│  │  ├─ 02-components.md
│  │  ├─ 03-setup.md
│  │  ├─ 04-gameplay.md
│  │  ├─ 05-om-turn-track.md
│  │  └─ 06-reference.md
│  ├─ templates/           # Handlebars templates
│  │  ├─ base.hbs          # Main HTML structure
│  │  ├─ cover.hbs         # Cover page layout
│  │  └─ component-spread.hbs # 2-page component layout
│  └─ styles/
│     └─ om.scss           # Main stylesheet with TTW specifications
├─ assets/
│  ├─ images/              # Game images (placeholders included)
│  └─ icons/               # SVG icons for energy cubes, Om tokens, etc.
├─ scripts/
│  ├─ build.js             # Production build → PDF
│  └─ watch.js             # Development server with live reload
├─ output/                 # Generated files
│  ├─ rulebook.html        # Complete HTML version
│  ├─ om-journey-rulebook.pdf # Final PDF
│  └─ assets/              # Copied assets
└─ package.json
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd component.studio/asset-generation/om-rulebook
npm install
```

### 2. Development Mode

Start the development server with live reload:

```bash
npm run dev
```

This will:
- Build the rulebook
- Start a server at `http://localhost:3000`
- Open your browser automatically
- Watch for changes and rebuild automatically

### 3. Production Build

Generate the final PDF:

```bash
npm run build
```

Output files will be in the `output/` directory:
- `rulebook.html` - Complete HTML version
- `om-journey-rulebook.pdf` - Print-ready PDF

## 🎨 Customization

### Adding Real Images

Replace the placeholder images in `assets/images/` with actual game photography:

```
assets/images/
├─ cover-art.jpg           # Main cover illustration
├─ board-overview.jpg      # Full board photograph
├─ setup-board.jpg         # Setup state photo
├─ setup-complete.jpg      # Ready-to-play photo
├─ journey-card-examples.jpg # Sample cards
├─ board-preview.jpg       # Board detail shots
├─ om-track.jpg           # Om Turn Track close-up
├─ travel-card-example.jpg # Travel card samples
├─ outer-journey-card.jpg  # Outer Journey card
├─ inner-journey-card.jpg  # Inner Journey card
└─ event-card-example.jpg  # Global Event card
```

### Editing Content

Modify the markdown files in `src/pages/` to update content:

- **01-introduction.md** - Game theme and overview
- **02-components.md** - Component lists and descriptions  
- **03-setup.md** - Step-by-step setup instructions
- **04-gameplay.md** - Turn sequence and actions
- **05-om-turn-track.md** - Turn order mechanics
- **06-reference.md** - Quick reference tables

### Styling Changes

Edit `src/styles/om.scss` to modify:

- **Colors**: Update the color palette variables
- **Typography**: Change font sizes and spacing
- **Layout**: Adjust grid systems and margins
- **Components**: Modify call-out boxes, tables, etc.

### Templates

Customize the Handlebars templates in `src/templates/`:

- **base.hbs** - Overall HTML structure and icon legend
- **cover.hbs** - Cover page design
- **component-spread.hbs** - 2-page component overview

## 🎯 Design Specifications

### Typography
- **Headings**: Bebas Neue (36pt H1, 26pt H2)
- **Body**: Lato Regular 10pt / 14pt leading
- **Hierarchy**: Clear visual hierarchy with consistent spacing

### Color Palette
- **Navy Blue**: #205F80 (headings, borders)
- **Burnt Orange**: #E87E1E (accents, highlights)
- **Sand**: #F4EBDD (backgrounds, call-outs)
- **Body Grey**: #555 (main text)

### Layout Grid
- **Margins**: ½-inch outer, ⅜-inch inner
- **Grid**: 12-column baseline grid with 24pt leading
- **Spacing**: Consistent 12pt increments

### Visual Elements
- **Call-out Boxes**: 4px rounded corners, 1pt borders, drop shadows
- **Tables**: Zebra striping with 5% darker sand
- **Icons**: 32×32px with 6pt label spacing
- **Images**: 2pt white keylines on colored backgrounds

## 📋 Content Guidelines

### Writing Style
- **Clear and Concise**: Easy-to-follow instructions
- **Consistent Terminology**: Use the same terms throughout
- **Visual Hierarchy**: Proper use of headings and emphasis
- **Examples**: Include gameplay examples where helpful

### Image Requirements
- **High Resolution**: 300 DPI for print quality
- **Consistent Lighting**: Professional game photography
- **Clean Backgrounds**: Remove distractions
- **Proper Cropping**: Focus on relevant game elements

## 🔧 Technical Details

### Build Process
1. **SCSS Compilation**: Converts styles to CSS
2. **Markdown Processing**: Converts .md files to HTML
3. **Template Rendering**: Combines content with Handlebars templates
4. **Asset Copying**: Moves images and icons to output
5. **PDF Generation**: Uses Puppeteer + Paged.js for print layout

### Browser Compatibility
- **Chrome/Chromium**: Recommended for development
- **Paged.js**: Handles CSS page layout for print
- **Print Styles**: Optimized for PDF generation

## 📦 Dependencies

- **puppeteer**: PDF generation
- **pagedjs**: CSS paged media polyfill
- **handlebars**: Template engine
- **markdown-it**: Markdown processing
- **sass**: CSS preprocessing
- **chokidar**: File watching
- **express**: Development server

## 🎮 Game Integration

This rulebook is designed specifically for **Om: The Journey** and includes:

- Complete rules from the original text rulebook
- Professional visual design matching board game standards
- Print-ready PDF output for manufacturing
- Digital-friendly HTML version for online distribution
- Modular structure for easy updates and localization

## 📄 License

This rulebook template is created for the Om: The Journey board game. The build system and templates can be adapted for other projects with appropriate attribution.

---

**Ready to create your professional rulebook!** 🚀

For questions or issues, refer to the build logs or check the generated HTML output for debugging. 