import '../styles/JourneyCard.css';

interface JourneyCardProps {
  card: {
    location: string;
    // ... other card properties
  };
  faceUp: boolean;
}

const JourneyCard: React.FC<JourneyCardProps> = ({ card, faceUp }) => {
  console.log('Journey Card Data:', card); // Add this to debug
  return (
    <div className={`journey-card ${faceUp ? 'face-up' : 'face-down'}`}>
      {faceUp ? (
        <div className="journey-card-content">
          <div className="location-name">{card?.location || 'Unknown Location'}</div>
          <div className="card-details">
            {/* ... existing card details ... */}
          </div>
        </div>
      ) : (
        <div className="journey-card-back">
          <span>Journey</span>
        </div>
      )}
    </div>
  );
}

export default JourneyCard; 