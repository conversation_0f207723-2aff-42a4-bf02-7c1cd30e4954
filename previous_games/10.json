{"description": "<PERSON><PERSON><PERSON> won with many bonuses whereas <PERSON><PERSON><PERSON> was blocked by the event cards.", "players": [{"id": "mgIZDXkFLQtUdnwuAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 28, "hand": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "energyCubes": ["artha", "bhakti", "artha"], "omTemp": [1], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "outerScore": 64, "innerScore": 36, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "g8gjMwgLSs38YTEsAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 19, "hand": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "energyCubes": ["karma"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "outerScore": 47, "innerScore": 32, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}}], "started": true, "turnIndex": 0, "roundCount": 11, "travelDeck": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "eventDeck": [{"id": "E9", "type": "wildCube"}, {"id": "E1", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E8", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "journeyDeckOuter": [{"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "travelDiscard": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "faceUpEvent": [{"id": "E7", "type": "wildCube"}, {"id": "E3", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}], "faceUpJourneyOuter": [{"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "locationCubes": {"1": "bhakti", "2": "bhakti", "3": "artha", "4": "gnana", "5": "artha", "6": "bhakti", "7": "bhakti", "8": "bhakti", "9": "karma", "10": "artha", "11": "karma", "12": "gnana", "14": "gnana", "15": "artha", "16": "gnana", "17": "artha", "20": "artha", "21": "gnana", "22": "gnana", "23": "gnana", "25": "artha", "30": "artha", "32": "bhakti", "34": "bhakti", "35": "karma", "36": "artha", "37": "karma", "38": "artha", "39": "gnana", "46": "gnana", "47": "karma", "48": "artha", "undefined": "bhakti"}, "locationOm": {"49": true, "50": true, "54": true, "55": true, "58": true}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907579795, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907640721, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907640721, "data": {"cardType": "travel", "card": {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907640721, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907640721, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "move", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907683741, "data": {"path": [64, 15, 5, 52], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907729944, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743907731915}, {"type": "move", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743907796896, "data": {"path": [61, 4, 3, 56], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743907801878, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743907807557, "data": {"cardType": "travel", "drawnCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743907811345}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743907811347, "data": {"eventId": "global-event-15", "eventName": "Biker Gang", "eventEffect": "biker_gang_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743907844303, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743907854202, "data": {"vehicle": "motorbike", "effect": "biker_gang_reward", "outerPoints": 5}}, {"type": "move", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743907854202, "data": {"path": [52, 5, 15, 64, 29, 33], "travelCards": ["T11", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743907858670, "data": {"journeyType": "outer", "journeyCardId": "JO17", "omRequirement": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743907862951, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743907866177}, {"type": "move", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743907926239, "data": {"path": [56, 3, 47, 57], "travelCards": ["T7", "T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743907945015, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743907960137, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743907962182}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743907962183, "data": {"eventId": "global-event-11", "eventName": "Pedal Power", "eventEffect": "pedal_power_reward"}}, {"type": "PICK_DECK_CARDS", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743907983802, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "move", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743908000898, "data": {"path": [33, 29, 28, 18, 53], "travelCards": ["T6", "T7"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743908008569, "data": {"cardType": "travel", "drawnCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743908014092}, {"type": "move", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743908025943, "data": {"path": [57, 47, 46, 45], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743908032636, "data": {"journeyType": "outer", "journeyCardId": "JO24", "omRequirement": 1}}, {"type": "endTurn", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743908036425}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743908036426, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "move", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743908075758, "data": {"path": [53, 18, 15, 64, 63, 20, 51], "travelCards": ["T10", "T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743908097529, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743908102074, "data": {"cardType": "travel", "drawnCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "TjbdZ9B6HEUax0N4AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743908104315}, {"type": "move", "playerId": "rDSy3g81TOXwHLbFAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743908131857, "data": {"path": [45, 31], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 1, "timestamp": 1743908230890, "data": {"timestamp": 1743908230890, "loadedStateRoundCount": 3, "playerCount": 2}}, {"type": "move", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743908266375, "data": {"path": [45, 44, 43, 60], "travelCards": ["T5", "T3"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743908285673, "data": {"cardType": "travel", "drawnCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743908293565, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743908296537}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743908296539, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743908306722, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743908354476, "data": {"vehicle": "camel", "effect": "desert_caravan_reward", "outerPoints": 5}}, {"type": "move", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743908354476, "data": {"path": [51, 20, 21, 28, 18], "travelCards": ["T11", "T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743908369456, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743908372790}, {"type": "PICK_DECK_CARDS", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743908445595, "data": {"cardType": "travel", "drawnCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743908453928, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743908496004, "data": {"path": [60, 43], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743908499121}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743908499122, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "move", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743908519426, "data": {"path": [18, 28, 29], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743908522367, "data": {"journeyType": "outer", "journeyCardId": "JO14", "omRequirement": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743908582961, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743908634847, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743908678603, "data": {"path": [29, 33, 34, 17, 16, 27], "travelCards": ["T10", "T5"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743908682421}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743908727729, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743908734936, "data": {"path": [43, 44], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743908773609, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743908775280}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743908775280, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "move", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743908801281, "data": {"path": [27, 26], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743908805780, "data": {"journeyType": "inner", "journeyCardId": "JI15", "omRequirement": 1}}, {"type": "endTurn", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743908814449}, {"type": "move", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743908893213, "data": {"path": [44, 43, 42], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743908916766, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743908918711}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743908918712, "data": {"eventId": "global-event-1", "eventName": "Drizzle of Delay", "eventEffect": "max_moves_2_and_cost_artha_north_east"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743908978934, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743908989706, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743908997378, "data": {"path": [26, 25, 24], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743908999909}, {"type": "move", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743909049889, "data": {"path": [47, 57, 41], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743909136165}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743909136165, "data": {"eventId": "global-event-21", "eventName": "Heavy Haul", "eventEffect": "heavy_haul_reward"}}, {"type": "move", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743909145934, "data": {"path": [24, 23, 59], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743909196210, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743909203743, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743909205254}, {"type": "move", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743909275071, "data": {"path": [41, 57, 47, 46, 45, 31], "travelCards": ["T9", "T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743909288329, "data": {"journeyType": "outer", "journeyCardId": "JO16", "omRequirement": 1}}, {"type": "endTurn", "playerId": "FneIoHsDzVsI-SkkAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743909306814}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743909306814, "data": {"eventId": "global-event-8", "eventName": "Triathlon", "eventEffect": "triathlon_bonus"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743909323915, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743909373676, "data": {"path": [59, 23, 22, 21, 28, 18, 40], "travelCards": ["T4", "T7", "T11"], "extraHopCards": []}}, {"type": "triathlon_bonus", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743909392926, "data": {"bonus": 7, "usedValues": [1, 2, 3]}}, {"type": "endTurn", "playerId": "6ATKL0VVaGGzoPBUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743909392926}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 1, "timestamp": 1743909472035, "data": {"timestamp": 1743909472035, "loadedStateRoundCount": 9, "playerCount": 2}}, {"type": "move", "playerId": "g8gjMwgLSs38YTEsAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743909604557, "data": {"path": [31, 65, 62, 13], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "g8gjMwgLSs38YTEsAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743909620050, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "g8gjMwgLSs38YTEsAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743909631530, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "g8gjMwgLSs38YTEsAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743909634563}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743909634564, "data": {"eventId": "global-event-23", "eventName": "Merchant's Midas", "eventEffect": "merchants_midas_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "mgIZDXkFLQtUdnwuAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743909681001, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "mgIZDXkFLQtUdnwuAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743909690150, "data": {"path": [40, 18, 28], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "character_trade_reward", "playerId": "mgIZDXkFLQtUdnwuAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743909713534, "data": {"character": "merchant", "cubeReceived": "artha", "effect": "merchants_midas_reward", "outerPoints": 7}}, {"type": "trade", "playerId": "mgIZDXkFLQtUdnwuAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743909713534, "data": {"cubesTraded": ["gnana", "gnana"], "cubeReceived": "artha", "count": 1}}, {"type": "endTurn", "playerId": "mgIZDXkFLQtUdnwuAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743909716932}, {"type": "move", "playerId": "g8gjMwgLSs38YTEsAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743909730897, "data": {"path": [13, 14, 19], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "g8gjMwgLSs38YTEsAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743909735058, "data": {"journeyType": "inner", "journeyCardId": "JI10", "omRequirement": 1}}, {"type": "endTurn", "playerId": "g8gjMwgLSs38YTEsAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743909740383}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743909740384, "data": {"eventId": "global-event-25", "eventName": "<PERSON><PERSON><PERSON>'s Grace", "eventEffect": "pilgrims_grace_reward"}}], "characterDeck": [{"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}], "energyCubePile": {"artha": 2, "karma": 7, "gnana": 6, "bhakti": 5}, "currentGlobalEvent": {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, "globalEventDeck": [{"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}], "globalEventDiscard": [{"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}]}