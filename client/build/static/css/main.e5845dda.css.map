{"version": 3, "file": "static/css/main.e5845dda.css", "mappings": "AAEA,MACE,uBAAwB,CACxB,yBAA0B,CAC1B,sBAAuB,CACvB,iBAAqB,CACrB,kBAAsB,CACtB,0BAA2B,CAC3B,sBAA0B,CAC1B,mBAAoB,CACpB,gCAA0C,CAC1C,0BAA2B,CAG3B,qBAAsB,CACtB,qBAAsB,CACtB,qBAAsB,CACtB,sBAAuB,CAGvB,uBAAuC,CACvC,sBAAqC,CACrC,uBAAwC,CACxC,yBAAyC,CACzC,sBAAuB,CACvB,2BAA0C,CAC1C,6BAA8C,CAC9C,yBAAyC,CAGzC,uBAAuC,CACvC,uBACF,CAGA,KAIE,kBAAmC,CAAnC,kCAAmC,CACnC,UAAwB,CAAxB,uBAAwB,CAHxB,gIAC8D,CAG9D,eAAgB,CALhB,QAMF,CAEA,kBAEE,eAAgB,CAChB,eAAgB,CAFhB,cAGF,CAEA,GAEE,aAA2B,CAA3B,0BAA2B,CAD3B,gBAEF,CAEA,GAEE,UAAwB,CAAxB,uBAAwB,CADxB,gBAEF,CAEA,GACE,gBACF,CAGA,OAGE,kBAAgC,CAAhC,+BAAgC,CAEhC,WAAY,CACZ,iBAAmC,CAAnC,kCAAmC,CAInC,8BAA6B,CAA7B,4BAA6B,CAN7B,UAAyB,CAAzB,wBAAyB,CAHzB,cAAe,CAOf,eAAiB,CADjB,eAAgB,CALhB,oBAAsB,CAOtB,uBAA6B,CAA7B,4BAEF,CAEA,aACE,kBAAmB,CACnB,0BACF,CAEA,cACE,uBACF,CAEA,iBACE,kBAAkC,CAAlC,iCACF,CAEA,uBACE,kBACF,CAEA,cACE,kBAA+B,CAA/B,8BACF,CAEA,oBACE,kBACF,CAGA,MAEE,qBAAsB,CACtB,iBAAmC,CAAnC,kCAAmC,CACnC,cAAe,CAHf,kBAAoB,CAKpB,uBAA6B,CAA7B,4BAA6B,CAD7B,UAEF,CAEA,YAEE,oBAAkC,CAAlC,iCAAkC,CAClC,8BAA6C,CAF7C,YAGF,CAGA,MAGE,cAEF,CAGA,kBAPE,eAAkC,CAAlC,iCAAkC,CAClC,iBAAmC,CAAnC,kCAAmC,CAEnC,8BAA6B,CAA7B,4BAUF,CANA,YAEE,YAAa,CAGb,uBAA6B,CAA7B,4BACF,CAEA,mBACE,wBAAqC,CAArC,oCAAqC,CAErC,gCAA0C,CAD1C,0BAEF,CAGA,gBAEE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAEA,iBACE,aACF,CAEA,eACE,YAAa,CAEb,cAAe,CADf,QAAS,CAET,eACF,CAEA,WACE,eAAkC,CAAlC,iCAAkC,CAClC,iBAAmC,CAAnC,kCAAmC,CAGnC,8BAA6B,CAA7B,4BAA6B,CAF7B,YAAa,CAGb,uBAA6B,CAA7B,4BAA6B,CAF7B,WAGF,CAEA,iBAEE,+BAAyC,CADzC,0BAEF,CAGA,aAIE,iBAAkB,CAHlB,oBAAqB,CAErB,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAEA,mBACE,wBAAoC,CAApC,mCACF,CAEA,mBACE,wBAAoC,CAApC,mCACF,CAEA,mBACE,wBAAoC,CAApC,mCACF,CAEA,oBACE,wBAAqC,CAArC,oCACF,CAGA,wBAGE,sBAA6B,CAF7B,cAAe,CACf,iDAEF,CAEA,8BAEE,8BAAyC,CADzC,0BAEF,CAEA,sBACE,qBAAsB,CACtB,yBAA0B,CAC1B,0BACF,CAGA,gBASE,kBAAmB,CARnB,wBAAyB,CAKzB,qBAAsB,CAJtB,iBAAkB,CAElB,8BAAwC,CAGxC,YAAa,CACb,qBAAsB,CAHtB,kBAAmB,CAFnB,YAOF,CAEA,sBAQE,kBAAmB,CALnB,qBAAsB,CAQtB,qBAAsB,CAPtB,iBAAkB,CAMlB,UAAW,CAJX,YAAa,CAGb,cAAe,CAPf,YAAa,CAKb,sBAAuB,CAFvB,iBAAkB,CAQlB,gBAAiB,CADjB,eAAgB,CAEhB,iBAAkB,CAblB,WAcF,CAEA,0BAIE,aAAc,CAFd,WAAY,CACZ,gBAAiB,CAFjB,UAIF,CAEA,sBAEE,eAAiB,CADjB,eAAiB,CAEjB,iBAAkB,CAClB,iBACF,CAEA,4BAGE,UAAW,CAFX,eAAiB,CACjB,iBAEF,CAEA,WAEE,eAAiB,CACjB,cAAe,CAFf,mBAGF,CAGA,WAME,wBAA6B,CAF7B,aAAc,CAFd,WAAY,CAGZ,aAAc,CAFd,eAAgB,CAFhB,UAMF,CAGA,kBAOE,wBAAyB,CAFzB,QAAS,CAFT,MAAO,CAKP,eAAgB,CAPhB,cAAe,CAGf,OAAQ,CAFR,KAAM,CAIN,YAGF,CAEA,yBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,0BAKE,kBAAmB,CAFnB,YAAa,CADb,WAAY,CAEZ,sBAAuB,CAEvB,eAAgB,CAChB,eAAgB,CANhB,UAOF,CAEA,sBAEE,WAAY,CAEZ,eAAgB,CADhB,cAAe,CAFf,UAIF,CAGA,gBACE,mBAAoB,CACpB,wBAAiB,CAAjB,gBACF,CAEA,mBACE,cAA+B,CAC/B,WAAY,CACZ,gBAAiB,CACjB,IAAK,CACL,IAAK,CACL,4CACF,CAEA,qBAGE,kBAAmB,CACnB,SAAU,CACV,wBAAyB,CAJzB,cAAe,CACf,eAIF,CAGA,oKAQE,SAAa,CACb,WAAY,CACZ,gBACF,CAMA,8CACE,0CACF,CAEA,sEAGE,cAA8B,CAC9B,4DAAmE,CACnE,qBAAsB,CACtB,UACF,CAGA,MACE,cAAe,CAEf,4CAAmD,CADnD,8BAGF,CAEA,kBAHE,8BAMF,CAHA,YACE,sBAEF,CAEA,6CACE,SAA4B,CAC5B,WAAY,CACZ,cAAe,CACf,4CAAmD,CACnD,qBACF,CAGA,sBACE,mBAAoB,CACpB,wBAAiB,CAAjB,gBACF,CAEA,mCACE,YACF,CAEA,2CAEE,SAAU,CADV,eAEF,CAGA,yBAGE,kBAAmB,CACnB,SAAU,CACV,wBAAyB,CAJzB,cAAe,CACf,eAAgB,CAIhB,wBACF,CAEA,2BACE,cAA+B,CAC/B,WAAY,CACZ,gBAAiB,CACjB,IAAK,CACL,IAAK,CACL,4CAAmD,CACnD,uBACF,CAGA,KACE,WAAe,CACf,gBAAiB,CACjB,oBAAqB,CAErB,SAAU,CACV,0CAAmD,CAFnD,WAAa,CAGb,uBACF,CAEA,WAEE,gBAAiB,CACjB,gDAAyD,CAFzD,SAGF,CAGA,gBAOE,gBAAoC,CAEpC,iBAAkB,CAClB,8BAAwC,CALxC,YAAa,CACb,OAAQ,CAHR,SAAU,CAKV,WAAY,CAPZ,iBAAkB,CAClB,QAAS,CAET,WAOF,CAGA,MACE,YACF,CAEA,UACE,qBACF,CAEA,QACE,SACF,CAEA,QACE,QACF,CAEA,QACE,QACF,CAEA,OACE,gBACF,CAEA,OACE,eACF,CAEA,OACE,eACF,CAGA,WAKE,eAAkC,CAAlC,iCAAkC,CAClC,iBAAmC,CAAnC,kCAAmC,CACnC,8BAA6B,CAA7B,4BAA6B,CAJ7B,gBAAiB,CADjB,eAAgB,CAEhB,YAAa,CAHb,iBAOF,CAEA,cACE,cAAe,CACf,oBACF,CAGA,SAEE,kBAAmB,CAInB,aAA2B,CAA3B,0BAA2B,CAL3B,YAAa,CAIb,gBAAiB,CAFjB,sBAAuB,CACvB,gBAGF,CAEA,eAEE,qCAA2C,CAD3C,aAEF,CAEA,gBACE,OAAU,WAAc,CACxB,IAAM,YAAe,CACrB,IAAM,aAAgB,CACtB,OAAY,UAAa,CAC3B,CAGA,gBAEE,wBAAyB,CADzB,YAAa,CAEb,cACF,CAEA,kBAEE,eAAgB,CAChB,WAAY,CACZ,eAA+B,CAE/B,cAAe,CAIf,oBAAqB,CALrB,cAAe,CAJf,iBAAkB,CAOlB,iBAAkB,CAClB,oBAAqB,CAFrB,yCAIF,CAEA,wBACE,0BAA0C,CAC1C,UACF,CAEA,yBAEE,0BAA2C,CAD3C,UAEF,CAEA,+BAOE,wBAAyB,CAJzB,QAAS,CAFT,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,UAGF,CAGA,2BACE,0BAAuC,CAAvC,sCACF,CAEA,6EACE,iBACF,CAGA,sCACE,SACF,CAEA,4CACE,oBAA+B,CAC/B,iBACF,CAEA,4CACE,gBAA8B,CAC9B,iBACF,CAEA,kDACE,oBACF,CAGA,kBACE,uCAAwC,CACxC,wDACF,CAcA,yBACE,GAEE,8CAAmD,CADnD,oBAEF,CACA,IAEE,+CAAoD,CADpD,oBAEF,CACA,GAEE,8CAAmD,CADnD,oBAEF,CACF,CAGA,0BAKE,WAAY,CAFZ,MAAO,CAGP,mBAAoB,CALpB,cAAe,CACf,KAAM,CAEN,UAAW,CAGX,YACF,CAEA,eAIE,0CAA2C,CAD3C,yCAAoD,CAFpD,iBAAkB,CAClB,4CAGF,CAEA,oBACE,GACE,+BACF,CACA,GACE,+BACF,CACF,CAGA,yCACE,mBAAqB,CACrB,kCACF,CAGA,iCAGE,UAAW,CADX,cAAe,CADf,gBAGF,CC9pBA,sBAIE,wBAAyB,CADzB,UAAW,CADX,wHAAwI,CAGxI,gBAAiB,CAJjB,YAKF,CAEA,yBAGE,+BAAgC,CAFhC,aAAc,CACd,kBAAmB,CAEnB,mBACF,CAEA,yBACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,yBACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAGA,0DAIE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,WAAY,CADZ,sBAAuB,CAEvB,iBACF,CAEA,mCAGE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CANf,eAAgB,CAChB,gBAMF,CAGA,eAGE,+BAAgC,CAFhC,YAAa,CACb,kBAEF,CAEA,sBAEE,eAAgB,CAChB,WAAY,CAGZ,aAAc,CAFd,cAAe,CACf,cAAe,CAJf,iBAAkB,CAMlB,iBACF,CAEA,6BACE,aACF,CAEA,mCAOE,wBAAyB,CAJzB,WAAY,CAFZ,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,UAGF,CAGA,oBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yBAEF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,eACF,CAEA,aACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CACxC,YAAa,CACb,iBACF,CAEA,gBAGE,aAAc,CAFd,eAAiB,CACjB,eAEF,CAEA,cAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,kBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CADT,sBAEF,CAEA,WAME,6BAA8B,CAH9B,wBAAyB,CACzB,iBAAkB,CAClB,oBAAqB,CAHrB,WAAY,CADZ,UAMF,CAEA,iBACE,GAEE,4BAA0C,CAD1C,oBAEF,CAEA,IAEE,+BAA2C,CAD3C,kBAEF,CAEA,GAEE,4BAAwC,CADxC,oBAEF,CACF,CAGA,oBACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CAExC,eAAgB,CADhB,YAEF,CAEA,oBAEE,wBAAyB,CACzB,eAAiB,CAFjB,UAGF,CAEA,8CAIE,4BAA6B,CAF7B,YAAa,CACb,eAEF,CAEA,uBACE,wBAAyB,CAEzB,aAAc,CADd,eAEF,CAGA,mBACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CACxC,YACF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,eACF,CAEA,qBAEE,kBAAmB,CADnB,YAEF,CAEA,aAGE,aAAc,CADd,eAAiB,CADjB,WAGF,CAEA,mBAGE,kBAAmB,CADnB,YAAa,CADb,QAAO,CAGP,WACF,CAEA,WAEE,wBAAyB,CACzB,iBAAkB,CAFlB,WAAY,CAGZ,aACF,CAEA,aAGE,aAAc,CADd,eAAiB,CADjB,gBAGF,CAGA,mBACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CACxC,YACF,CAEA,mBAGE,QAAS,CACT,eACF,CAEA,+BANE,YAAa,CACb,qBASF,CAJA,YAGE,QACF,CAEA,kBAEE,aAAc,CADd,eAEF,CAEA,YACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,8BAGE,wBAAyB,CACzB,iBAAkB,CAHlB,QAAO,CAIP,eAAiB,CAHjB,WAIF,CAEA,YASE,kBAAmB,CARnB,wBAAyB,CAOzB,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CALvB,UAMF,CAEA,qBAVE,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAeF,CATA,SAQE,qBAAsB,CAPtB,wBAAyB,CAMzB,eAAgB,CAFhB,gBAIF,CAEA,qBAEE,YAAa,CACb,sBAAuB,CAFvB,eAGF,CAEA,mCAGE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CADf,cAAe,CADf,eAAiB,CAHjB,iBAAkB,CAMlB,+BACF,CAEA,kBACE,wBAAyB,CACzB,UACF,CAEA,wBACE,wBACF,CAEA,iBACE,wBAAyB,CACzB,UACF,CAEA,uBACE,wBACF,CAEA,oBAEE,wBAAyB,CAGzB,wBAAyB,CAFzB,iBAAkB,CAFlB,eAAgB,CAGhB,YAEF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,eACF,CAGA,mBACE,qBAAuB,CACvB,iBAAkB,CAClB,8BAAwC,CACxC,YACF,CAEA,mBAIE,+BAAgC,CAEhC,kBAAmB,CADnB,mBAEF,CAEA,iBACE,YAAa,CACb,QACF,CAEA,cACE,kBACF,CAEA,aACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAGlB,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,eAAgB,CAChB,eAAgB,CANhB,YAAa,CACb,iBAMF,CAEA,aAGE,aAAc,CAFd,gBAAiB,CACjB,eAEF,CAEA,cAIE,wBAAyB,CAKzB,iBAAkB,CAJlB,UAAY,CAEZ,eAAiB,CADjB,eAAiB,CAEjB,gBAAiB,CAPjB,iBAAkB,CAElB,WAAY,CADZ,SAQF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,eACF,CAEA,aACE,qBAAuB,CACvB,wBAAyB,CACzB,iBAAkB,CAClB,YACF,CAEA,oBAEE,wBAAyB,CADzB,oBAEF,CAEA,aAIE,aAAc,CAFd,gBAAiB,CADjB,eAAiB,CAEjB,kBAEF,CAEA,wBAGE,aAAS,CAFT,YAAa,CAIb,eAAiB,CAFjB,QAAS,CADT,mCAAqC,CAErC,kBAEF,CAEA,WAEE,aAAc,CADd,eAEF,CAEA,cACE,eACF,CAEA,eACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,sBAEE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAJf,gBAKF,CAEA,4BACE,wBACF,CAEA,YAGE,wBAAyB,CACzB,iBAAkB,CAHlB,gBAAiB,CACjB,eAGF,CAEA,cAEE,wBAAyB,CACzB,eAAiB,CAFjB,UAGF,CAEA,kCAIE,4BAA6B,CAF7B,gBAAiB,CACjB,eAEF,CAEA,iBACE,wBAAyB,CACzB,eAAgB,CAChB,KACF,CAEA,cACE,iBACF,CAEA,YACE,eAAiB,CACjB,gBACF,CAGA,iBACE,wBACF,CAEA,uBACE,wBACF,CAEA,4BACE,wBACF,CAEA,qBACE,wBACF,CAEA,uBACE,wBACF,CAEA,qBACE,wBACF,CAEA,WAGE,aAAc,CAFd,YAAa,CACb,iBAEF,CAGA,yBACE,oBACE,yBACF,CACF,CAEA,0BACE,oBACE,yBACF,CACF,CAEA,yBACE,iBACE,qBAAsB,CACtB,OACF,CAEA,aACE,YACF,CAEA,cACE,gBACF,CAEA,wBACE,yBACF,CACF,CC1iBA,iBAIE,wBAAyB,CAFzB,YAAa,CACb,qBAAsB,CAFtB,gBAIF,CAEA,mBAME,kBAAmB,CALnB,wBAAyB,CAMzB,8BAAwC,CALxC,UAAY,CAEZ,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,sBAEE,gBAAiB,CADjB,QAEF,CAEA,wBACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,eAAiB,CAHjB,gBAAiB,CAIjB,+BACF,CAEA,8BACE,wBACF,CAEA,sBACE,QAAO,CAEP,cAAe,CADf,SAAU,CAEV,UACF,CAEA,mBACE,wBAAyB,CACzB,aAAc,CAGd,eAAiB,CAFjB,iBAAkB,CAClB,iBAEF", "sources": ["styles/main.css", "components/SimulationDashboard.css", "styles/simulation-page.css"], "sourcesContent": ["/* client/src/styles/main.css */\n\n:root {\n  --primary-color: #6C63FF;\n  --secondary-color: #FF6584;\n  --accent-color: #43C59E;\n  --dark-color: #333333;\n  --light-color: #FFFFFF;\n  --background-color: #F8F9FA;\n  --card-background: #FFFFFF;\n  --border-radius: 8px;\n  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  --transition: all 0.3s ease;\n\n  /* Energy Colors */\n  --artha-color: #F39C12;\n  --karma-color: #27AE60;\n  --gnana-color: #2980B9;\n  --bhakti-color: #8E44AD;\n\n  /* Region Colors - More vibrant and distinct */\n  --north-color: rgba(33, 150, 243, 0.95);      /* Vibrant blue */\n  --west-color: rgba(76, 175, 80, 0.95);        /* Vibrant green */\n  --south-color: rgba(255, 191, 102, 0.95);     /* Light orange */\n  --central-color: rgba(156, 39, 176, 0.95);    /* Vibrant purple */\n  --east-color: #fd7c03ef;        /* Orange */\n  --northeast-color: rgba(233, 30, 99, 0.95);   /* Vibrant pink */\n  --jyotirlinga-color: rgba(255, 236, 130, 0.95); /* Light yellow for jyotirlingas */\n  --airport-color: rgba(103, 58, 183, 0.95);    /* Vibrant indigo for airports */\n\n  /* Journey Types */\n  --inner-color: rgba(25, 118, 210, 0.95);     /* Dark blue for inner journey */\n  --outer-color: rgba(56, 142, 60, 0.95);      /* Dark green for outer journey */\n}\n\n/* Base Styles */\nbody {\n  margin: 0;\n  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,\n    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n  background: var(--background-color);\n  color: var(--dark-color);\n  line-height: 1.6;\n}\n\nh1, h2, h3, h4, h5, h6 {\n  margin: 0.5rem 0;\n  font-weight: 600;\n  line-height: 1.2;\n}\n\nh1 {\n  font-size: 2.5rem;\n  color: var(--primary-color);\n}\n\nh2 {\n  font-size: 1.8rem;\n  color: var(--dark-color);\n}\n\nh3 {\n  font-size: 1.5rem;\n}\n\n/* Button Styles */\nbutton {\n  cursor: pointer;\n  padding: 0.6rem 1.2rem;\n  background: var(--primary-color);\n  color: var(--light-color);\n  border: none;\n  border-radius: var(--border-radius);\n  font-weight: 500;\n  font-size: 0.9rem;\n  transition: var(--transition);\n  box-shadow: var(--box-shadow);\n}\n\nbutton:hover {\n  background: #5A52D5;\n  transform: translateY(-2px);\n}\n\nbutton:active {\n  transform: translateY(0);\n}\n\nbutton.secondary {\n  background: var(--secondary-color);\n}\n\nbutton.secondary:hover {\n  background: #E55A78;\n}\n\nbutton.accent {\n  background: var(--accent-color);\n}\n\nbutton.accent:hover {\n  background: #3AB08D;\n}\n\n/* Form Elements */\ninput {\n  padding: 0.8rem 1rem;\n  border: 1px solid #DDD;\n  border-radius: var(--border-radius);\n  font-size: 1rem;\n  width: 100%;\n  transition: var(--transition);\n}\n\ninput:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 2px rgba(108, 99, 255, 0.2);\n}\n\n/* Card Styles */\n.card {\n  background: var(--card-background);\n  border-radius: var(--border-radius);\n  padding: 1.5rem;\n  box-shadow: var(--box-shadow);\n}\n\n/* Player Mat Styles */\n.player-mat {\n  border-radius: var(--border-radius);\n  padding: 1rem;\n  background: var(--card-background);\n  box-shadow: var(--box-shadow);\n  transition: var(--transition);\n}\n\n.player-mat.active {\n  border: 2px solid var(--accent-color);\n  transform: translateY(-5px);\n  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);\n}\n\n/* Game Board Layout */\n.game-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem;\n}\n\n.board-container {\n  margin: 2rem 0;\n}\n\n.face-up-cards {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n  margin: 1.5rem 0;\n}\n\n.card-item {\n  background: var(--card-background);\n  border-radius: var(--border-radius);\n  padding: 1rem;\n  width: 150px;\n  box-shadow: var(--box-shadow);\n  transition: var(--transition);\n}\n\n.card-item:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\n}\n\n/* Energy Cube Visual Styles */\n.energy-cube {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  border-radius: 4px;\n  margin-right: 5px;\n}\n\n.energy-cube.artha {\n  background-color: var(--artha-color);\n}\n\n.energy-cube.karma {\n  background-color: var(--karma-color);\n}\n\n.energy-cube.gnana {\n  background-color: var(--gnana-color);\n}\n\n.energy-cube.bhakti {\n  background-color: var(--bhakti-color);\n}\n\n/* Selectable Energy Cubes */\n.energy-cube.selectable {\n  cursor: pointer;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  border: 1px solid transparent;\n}\n\n.energy-cube.selectable:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);\n}\n\n.energy-cube.selected {\n  border: 2px solid #fff;\n  box-shadow: 0 0 0 2px #333;\n  transform: translateY(-3px);\n}\n\n/* Character Card Styles */\n.character-card {\n  background-color: #f8f8f8;\n  border-radius: 8px;\n  padding: 10px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);\n  margin-bottom: 12px;\n  border: 1px solid #ddd;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.character-card-image {\n  width: 120px;\n  height: 120px;\n  background-color: #eee;\n  border-radius: 8px;\n  margin-bottom: 8px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: 24px;\n  color: #555;\n  border: 1px solid #ccc;\n  overflow: hidden;\n  object-fit: cover;\n  position: relative;\n}\n\n.character-card-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  display: block;\n}\n\n.character-card-title {\n  font-weight: bold;\n  font-size: 0.9rem;\n  margin-bottom: 4px;\n  text-align: center;\n}\n\n.character-card-description {\n  font-size: 0.8rem;\n  text-align: center;\n  color: #555;\n}\n\n.trade-btn {\n  padding: 0.4rem 0.8rem;\n  font-size: 0.8rem;\n  margin-top: 8px;\n}\n\n/* SVG Board Styling */\n.board-svg {\n  width: 100%;\n  height: auto;\n  max-height: 70vh;\n  display: block;\n  margin: 0 auto;\n  background-color: transparent;\n}\n\n/* Full-screen board styles */\n.board-fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n  background-color: #ecf5fe; /* Slightly darker sky blue for fullscreen */\n  overflow: hidden;\n}\n\n.board-fullscreen-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.board-fullscreen-content {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n  padding: 0 180px; /* Adjust padding to match sidebar width */\n}\n\n.board-fullscreen-svg {\n  width: 100%;\n  height: 100%;\n  max-width: none;\n  max-height: none;\n}\n\n/* Location labels with improved visibility */\n.location-label {\n  pointer-events: none;\n  user-select: none;\n}\n\n.location-label-bg {\n  fill: rgba(255, 255, 255, 0.98);\n  stroke: #222;\n  stroke-width: 1.5;\n  rx: 8;\n  ry: 8;\n  filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.4));\n}\n\n.location-label-text {\n  font-size: 12px;\n  font-weight: 700;\n  text-anchor: middle;\n  fill: #000;\n  dominant-baseline: middle;\n}\n\n/* Improved contrast for node region colors - all white with different border colors */\n.node-region-north,\n.node-region-west,\n.node-region-south,\n.node-region-central,\n.node-region-east,\n.node-region-northeast,\n.node-region-jyotirlinga,\n.node-region-airport {\n  fill: #ffffff;\n  stroke: #333;\n  stroke-width: 2px;\n}\n\n.node-region-jyotirlinga {\n  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.3));\n}\n\n.node-region-airport {\n  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.3));\n}\n\n.node-region-jyotirlinga:hover,\n.node-region-airport:hover,\n.node:hover {\n  fill: rgba(255, 255, 255, 0.9);\n  filter: brightness(1.1) drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.5));\n  transform: scale(1.05);\n  z-index: 10;\n}\n\n/* Enhanced styles for node hover effects */\n.node {\n  cursor: pointer;\n  transition: all 0.2s ease-in-out;\n  filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.3));\n  transform-origin: center center;\n}\n\n.node-group {\n  transform-box: fill-box;\n  transform-origin: center center;\n}\n\n.node-group:hover .location-label-background {\n  fill: rgba(255, 255, 255, 1);\n  stroke: #000;\n  stroke-width: 2;\n  filter: drop-shadow(0px 3px 6px rgba(0, 0, 0, 0.4));\n  transform: scale(1.02);\n}\n\n/* Enhanced location labels with improved visibility */\n.node-label-container {\n  pointer-events: none;\n  user-select: none;\n}\n\n.hide-labels .node-label-container {\n  display: none;\n}\n\n.node-group:hover .enhanced-location-label {\n  font-weight: 800;\n  fill: #000;\n}\n\n/* Enhanced labels for full-screen mode */\n.enhanced-location-label {\n  font-size: 12px;\n  font-weight: 700;\n  text-anchor: middle;\n  fill: #000;\n  dominant-baseline: middle;\n  text-shadow: 0px 0px 3px rgba(255, 255, 255, 1);\n}\n\n.location-label-background {\n  fill: rgba(255, 255, 255, 0.98);\n  stroke: #333;\n  stroke-width: 1.5;\n  rx: 8;\n  ry: 8;\n  filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.4));\n  transition: all 0.2s ease;\n}\n\n/* Line styling - now applied to path elements */\npath {\n  stroke: #ffffff;\n  stroke-width: 2.5;\n  stroke-linecap: round;\n  opacity: 0.85;\n  fill: none;\n  filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.2));\n  transition: all 0.2s ease;\n}\n\npath:hover {\n  opacity: 1;\n  stroke-width: 3.5;\n  filter: drop-shadow(0px 0px 5px rgba(255, 255, 255, 0.6));\n}\n\n/* Board controls styling */\n.board-controls {\n  position: absolute;\n  top: 10px;\n  left: 10px;\n  z-index: 100;\n  display: flex;\n  gap: 8px;\n  background: rgba(255, 255, 255, 0.8);\n  padding: 8px;\n  border-radius: 8px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n}\n\n/* Layout Helpers */\n.flex {\n  display: flex;\n}\n\n.flex-col {\n  flex-direction: column;\n}\n\n.gap-sm {\n  gap: 0.5rem;\n}\n\n.gap-md {\n  gap: 1rem;\n}\n\n.gap-lg {\n  gap: 2rem;\n}\n\n.mt-sm {\n  margin-top: 0.5rem;\n}\n\n.mt-md {\n  margin-top: 1rem;\n}\n\n.mt-lg {\n  margin-top: 2rem;\n}\n\n/* Game Over Screen */\n.game-over {\n  text-align: center;\n  max-width: 600px;\n  margin: 3rem auto;\n  padding: 2rem;\n  background: var(--card-background);\n  border-radius: var(--border-radius);\n  box-shadow: var(--box-shadow);\n}\n\n.game-over h1 {\n  font-size: 3rem;\n  margin-bottom: 1.5rem;\n}\n\n/* Loading State */\n.loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  font-size: 1.5rem;\n  color: var(--primary-color);\n}\n\n.loading::after {\n  content: \"...\";\n  animation: dots 1.5s steps(4, end) infinite;\n}\n\n@keyframes dots {\n  0%, 20% { content: '.'; }\n  40% { content: '..'; }\n  60% { content: '...'; }\n  80%, 100% { content: ''; }\n}\n\n/* App Navigation */\n.app-navigation {\n  display: flex;\n  background-color: #2c3e50;\n  padding: 0 20px;\n}\n\n.app-navigation a {\n  padding: 15px 20px;\n  background: none;\n  border: none;\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 1rem;\n  cursor: pointer;\n  transition: background-color 0.2s, color 0.2s;\n  position: relative;\n  text-decoration: none;\n  display: inline-block;\n}\n\n.app-navigation a:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  color: white;\n}\n\n.app-navigation a.active {\n  color: white;\n  background-color: rgba(255, 255, 255, 0.15);\n}\n\n.app-navigation a.active::after {\n  content: '';\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 3px;\n  background-color: #3498db;\n}\n\n/* Styles for compact components in fullscreen mode */\n.compact-player-mat.active {\n  box-shadow: 0 0 8px var(--accent-color);\n}\n\n.energy-cube.artha, .energy-cube.karma, .energy-cube.gnana, .energy-cube.bhakti {\n  border-radius: 3px;\n}\n\n/* Scrollbars for fullscreen sidebars */\n.board-fullscreen ::-webkit-scrollbar {\n  width: 6px;\n}\n\n.board-fullscreen ::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 3px;\n}\n\n.board-fullscreen ::-webkit-scrollbar-thumb {\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 3px;\n}\n\n.board-fullscreen ::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 0, 0, 0.3);\n}\n\n/* Highlighted node animation */\n.node-highlighted {\n  animation: pulse 1.5s infinite alternate;\n  filter: drop-shadow(0 0 8px rgba(255, 87, 34, 0.8)) !important;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));\n  }\n  100% {\n    transform: scale(1.08);\n    filter: drop-shadow(0 0 10px rgba(255, 87, 34, 0.9));\n  }\n}\n\n/* Player movement animation */\n@keyframes moveAnimation {\n  0% {\n    transform: scale(0.95);\n    filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));\n  }\n  50% {\n    transform: scale(1.1);\n    filter: drop-shadow(0 0 15px rgba(255, 87, 34, 0.9));\n  }\n  100% {\n    transform: scale(0.95);\n    filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));\n  }\n}\n\n/* Animation for card picking */\n.card-animation-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 9999;\n}\n\n.animated-card {\n  position: absolute;\n  transition: all 1.8s cubic-bezier(0.2, 0.8, 0.2, 1.0); /* Slowed down by 20% */\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\n  animation: cardGlow 1.8s infinite alternate; /* Slowed down by 20% */\n}\n\n@keyframes cardGlow {\n  0% {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n  }\n  100% {\n    box-shadow: 0 4px 20px rgba(25, 118, 210, 0.7);\n  }\n}\n\n/* Make sure the animation is visible for a longer time */\n.card-animation-container .animated-card {\n  opacity: 1 !important;\n  transition-duration: 1.8s !important;\n}\n\n/* Connection info styles */\n.app-navigation .connection-info {\n  margin-left: auto;\n  font-size: 12px;\n  color: #666;\n}", "/* SimulationDashboard.css */\n.simulation-dashboard {\n  padding: 20px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n  color: #333;\n  background-color: #f9f9f9;\n  min-height: 100vh;\n}\n\n.simulation-dashboard h1 {\n  color: #2c3e50;\n  margin-bottom: 20px;\n  border-bottom: 2px solid #e0e0e0;\n  padding-bottom: 10px;\n}\n\n.simulation-dashboard h2 {\n  color: #3498db;\n  margin: 20px 0 15px;\n  font-size: 1.4rem;\n}\n\n.simulation-dashboard h3 {\n  color: #2c3e50;\n  margin: 15px 0 10px;\n  font-size: 1.1rem;\n}\n\n/* Loading and Error States */\n.simulation-dashboard.loading,\n.simulation-dashboard.error {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 70vh;\n  text-align: center;\n}\n\n.simulation-dashboard.error button {\n  margin-top: 20px;\n  padding: 8px 16px;\n  background-color: #3498db;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n/* Navigation */\n.dashboard-nav {\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.dashboard-nav button {\n  padding: 10px 20px;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #7f8c8d;\n  position: relative;\n}\n\n.dashboard-nav button.active {\n  color: #3498db;\n}\n\n.dashboard-nav button.active::after {\n  content: '';\n  position: absolute;\n  bottom: -1px;\n  left: 0;\n  width: 100%;\n  height: 3px;\n  background-color: #3498db;\n}\n\n/* Overview Tab */\n.dashboard-overview {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 20px;\n}\n\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.metric-card {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 15px;\n  text-align: center;\n}\n\n.metric-card h3 {\n  font-size: 0.9rem;\n  margin: 0 0 10px;\n  color: #7f8c8d;\n}\n\n.metric-value {\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n}\n\n.pulse-dot {\n  width: 10px;\n  height: 10px;\n  background-color: #27ae60;\n  border-radius: 50%;\n  display: inline-block;\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(0.95);\n    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);\n  }\n  \n  70% {\n    transform: scale(1);\n    box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);\n  }\n  \n  100% {\n    transform: scale(0.95);\n    box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);\n  }\n}\n\n/* Player Statistics */\n.player-stats-panel {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 20px;\n  overflow-x: auto;\n}\n\n.player-stats-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.9rem;\n}\n\n.player-stats-table th,\n.player-stats-table td {\n  padding: 10px;\n  text-align: left;\n  border-bottom: 1px solid #eee;\n}\n\n.player-stats-table th {\n  background-color: #f8f9fa;\n  font-weight: bold;\n  color: #7f8c8d;\n}\n\n/* Event Distribution */\n.event-stats-panel {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 20px;\n}\n\n.event-bars {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  margin-top: 15px;\n}\n\n.event-bar-container {\n  display: flex;\n  align-items: center;\n}\n\n.event-label {\n  width: 150px;\n  font-size: 0.9rem;\n  color: #7f8c8d;\n}\n\n.event-bar-wrapper {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  height: 24px;\n}\n\n.event-bar {\n  height: 24px;\n  background-color: #3498db;\n  border-radius: 4px;\n  min-width: 2px;\n}\n\n.event-count {\n  margin-left: 10px;\n  font-size: 0.9rem;\n  color: #7f8c8d;\n}\n\n/* Control Panel */\n.dashboard-control {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 20px;\n}\n\n.simulation-config {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  margin-top: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.form-group label {\n  font-weight: bold;\n  color: #7f8c8d;\n}\n\n.bot-config {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.bot-strategy-selector {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.bot-strategy-selector select {\n  flex: 1;\n  padding: 8px;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  font-size: 0.9rem;\n}\n\n.remove-bot {\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  width: 28px;\n  height: 28px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-bot {\n  background-color: #3498db;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  padding: 8px 16px;\n  cursor: pointer;\n  margin-top: 10px;\n  align-self: flex-start;\n}\n\n.simulation-controls {\n  margin-top: 30px;\n  display: flex;\n  justify-content: center;\n}\n\n.start-simulation,\n.stop-simulation {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 4px;\n  font-weight: bold;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.start-simulation {\n  background-color: #27ae60;\n  color: white;\n}\n\n.start-simulation:hover {\n  background-color: #2ecc71;\n}\n\n.stop-simulation {\n  background-color: #e74c3c;\n  color: white;\n}\n\n.stop-simulation:hover {\n  background-color: #f5574a;\n}\n\n.current-simulation {\n  margin-top: 30px;\n  background-color: #f9f9f9;\n  border-radius: 8px;\n  padding: 15px;\n  border: 1px solid #e0e0e0;\n}\n\n.progress-info {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));\n  gap: 15px;\n  margin-top: 10px;\n}\n\n/* Simulation Details */\n.dashboard-details {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 20px;\n}\n\n.simulation-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #e0e0e0;\n  padding-bottom: 15px;\n  margin-bottom: 20px;\n}\n\n.simulation-meta {\n  display: flex;\n  gap: 20px;\n}\n\n.winner-panel {\n  margin-bottom: 30px;\n}\n\n.winner-card {\n  background-color: #fff9e0;\n  border: 2px solid #f1c40f;\n  border-radius: 8px;\n  padding: 15px;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n  margin-top: 15px;\n  max-width: 300px;\n}\n\n.winner-name {\n  font-size: 1.2rem;\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.winner-badge {\n  position: absolute;\n  top: -10px;\n  right: -10px;\n  background-color: #f1c40f;\n  color: white;\n  font-weight: bold;\n  font-size: 0.8rem;\n  padding: 5px 10px;\n  border-radius: 4px;\n}\n\n.players-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 15px;\n  margin-top: 15px;\n}\n\n.player-card {\n  background-color: white;\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.player-card.winner {\n  border-color: #f1c40f;\n  background-color: #fff9e0;\n}\n\n.player-name {\n  font-weight: bold;\n  font-size: 1.1rem;\n  margin-bottom: 10px;\n  color: #2c3e50;\n}\n\n.player-score-breakdown {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 10px;\n  margin-bottom: 10px;\n  font-size: 0.9rem;\n}\n\n.player-om {\n  font-weight: bold;\n  color: #8e44ad;\n}\n\n.events-panel {\n  margin-top: 30px;\n}\n\n.event-filters {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n\n.event-filters button {\n  padding: 6px 12px;\n  background-color: #f8f9fa;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.event-filters button:hover {\n  background-color: #e9ecef;\n}\n\n.events-log {\n  max-height: 400px;\n  overflow-y: auto;\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n}\n\n.events-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.9rem;\n}\n\n.events-table th,\n.events-table td {\n  padding: 8px 12px;\n  text-align: left;\n  border-bottom: 1px solid #eee;\n}\n\n.events-table th {\n  background-color: #f8f9fa;\n  position: sticky;\n  top: 0;\n}\n\n.event-detail {\n  margin-bottom: 5px;\n}\n\n.detail-key {\n  font-weight: bold;\n  margin-right: 5px;\n}\n\n/* Event type styling */\n.event-type-MOVE {\n  background-color: #e8f7f9;\n}\n\n.event-type-PICK_CARDS {\n  background-color: #e8f4f9;\n}\n\n.event-type-COLLECT_JOURNEY {\n  background-color: #f9f4e8;\n}\n\n.event-type-END_TURN {\n  background-color: #f9e8e8;\n}\n\n.event-type-GAME_START {\n  background-color: #e8f9e8;\n}\n\n.event-type-GAME_END {\n  background-color: #f9e8f9;\n}\n\n.no-events {\n  padding: 20px;\n  text-align: center;\n  color: #7f8c8d;\n}\n\n/* Responsive Design */\n@media (min-width: 768px) {\n  .dashboard-overview {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (min-width: 1200px) {\n  .dashboard-overview {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  .simulation-meta {\n    flex-direction: column;\n    gap: 5px;\n  }\n  \n  .metric-card {\n    padding: 10px;\n  }\n  \n  .metric-value {\n    font-size: 1.5rem;\n  }\n  \n  .player-score-breakdown {\n    grid-template-columns: 1fr;\n  }\n} ", "/* Simulation Page Styles */\n.simulation-page {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f8f9fa;\n}\n\n.simulation-header {\n  background-color: #2c3e50;\n  color: white;\n  padding: 15px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.simulation-header h1 {\n  margin: 0;\n  font-size: 1.4rem;\n}\n\n.header-controls button {\n  background-color: #3498db;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s;\n}\n\n.header-controls button:hover {\n  background-color: #2980b9;\n}\n\n.simulation-container {\n  flex: 1;\n  padding: 0;\n  max-width: 100%;\n  width: 100%;\n}\n\n.simulation-footer {\n  background-color: #2c3e50;\n  color: #ecf0f1;\n  padding: 15px 20px;\n  text-align: center;\n  font-size: 0.9rem;\n} "], "names": [], "sourceRoot": ""}