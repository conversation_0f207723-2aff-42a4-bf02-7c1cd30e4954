const winston = require('winston');
const path = require('path');

// Enable debug mode for logger troubleshooting
const DEBUG_LOGGER = process.env.DEBUG_LOGGER === 'true';

// Custom format that includes file name and line number
const fileLineFormat = winston.format.printf(({ level, message, timestamp, file, line, ...metadata }) => {
  // Create base message with timestamp and level
  let msg = `${timestamp} [${level.toUpperCase()}]`;

  // Add file and line information if available
  if (file && line) {
    // Extract just the filename without the path and add to beginning of message
    const filename = path.basename(file);
    msg += ` [${filename}:${line}]`;
  }

  // Add the message
  msg += ` ${message}`;

  // Add any remaining metadata if it exists and isn't just the empty file/line properties
  const remainingKeys = Object.keys(metadata).filter(key => key !== 'file' && key !== 'line');
  if (remainingKeys.length > 0) {
    const remainingMeta = {};
    for (const key of remainingKeys) {
      remainingMeta[key] = metadata[key];
    }
    try {
      msg += ` ${JSON.stringify(remainingMeta)}`;
    } catch (e) {
      msg += ` [metadata too complex to stringify]`;
    }
  }

  return msg;
});

// Create the logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.colorize(),
    fileLineFormat
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        fileLineFormat
      )
    })
  ]
});

// Helper function to get caller information
function getCallerInfo() {
  // Save original prepareStackTrace function
  const originalPrepareStackTrace = Error.prepareStackTrace;

  try {
    // Override with function that returns raw stack frames
    Error.prepareStackTrace = (_, stack) => stack;

    // Create an error and immediately capture stack
    const err = new Error();
    Error.captureStackTrace(err, getCallerInfo);
    const stack = err.stack;

    // Find the first non-logger frame in the stack
    // This is more reliable than assuming a fixed position
    if (stack && stack.length > 0) {
      // Debug mode: print the entire stack trace to help with troubleshooting
      if (DEBUG_LOGGER) {
        console.log('Logger stack trace:');
        for (let i = 0; i < stack.length; i++) {
          const frame = stack[i];
          const fileName = frame.getFileName() || 'unknown';
          const lineNumber = frame.getLineNumber() || 'unknown';
          const functionName = frame.getFunctionName() || 'anonymous';
          console.log(`  [${i}] ${path.basename(fileName)}:${lineNumber} - ${functionName}()`);
        }
      }

      // Skip frames from logger.js itself
      for (let i = 0; i < stack.length; i++) {
        const frame = stack[i];
        const fileName = frame.getFileName();

        // Skip frames from this file, node_modules, and internal Node.js files
        if (fileName &&
            !fileName.includes('logger.js') &&
            !fileName.includes('node_modules') &&
            !fileName.includes('internal/')) {
          // In debug mode, log which frame we're using
          if (DEBUG_LOGGER) {
            console.log(`Using stack frame ${i}: ${path.basename(fileName)}:${frame.getLineNumber()}`);
          }
          return {
            file: fileName,
            line: frame.getLineNumber()
          };
        }
      }
    }

    return {};
  } catch (e) {
    console.error('Error getting caller info:', e);
    return {};
  } finally {
    // Always restore the original function
    Error.prepareStackTrace = originalPrepareStackTrace;
  }
}

// Create wrapped logger methods that add file and line information
const wrappedLogger = {
  info: function(message, meta = {}) {
    // Using function() instead of arrow function to preserve call stack
    const caller = getCallerInfo();

    // Check if caller info was provided in meta (allows overriding the automatic detection)
    const fileInfo = meta.file ? { file: meta.file, line: meta.line || 0 } : caller;

    // Remove file and line from meta to avoid duplication
    const { file, line, ...restMeta } = meta;

    logger.info(message, { file: fileInfo.file, line: fileInfo.line, ...restMeta });
  },
  warn: function(message, meta = {}) {
    const caller = getCallerInfo();

    // Check if caller info was provided in meta
    const fileInfo = meta.file ? { file: meta.file, line: meta.line || 0 } : caller;

    // Remove file and line from meta to avoid duplication
    const { file, line, ...restMeta } = meta;

    logger.warn(message, { file: fileInfo.file, line: fileInfo.line, ...restMeta });
  },
  error: function(message, meta = {}) {
    const caller = getCallerInfo();

    // Check if caller info was provided in meta
    const fileInfo = meta.file ? { file: meta.file, line: meta.line || 0 } : caller;

    // Remove file and line from meta to avoid duplication
    const { file, line, ...restMeta } = meta;

    logger.error(message, { file: fileInfo.file, line: fileInfo.line, ...restMeta });
  },
  debug: function(message, meta = {}) {
    const caller = getCallerInfo();

    // Check if caller info was provided in meta
    const fileInfo = meta.file ? { file: meta.file, line: meta.line || 0 } : caller;

    // Remove file and line from meta to avoid duplication
    const { file, line, ...restMeta } = meta;

    logger.debug(message, { file: fileInfo.file, line: fileInfo.line, ...restMeta });
  }
};

module.exports = wrappedLogger;