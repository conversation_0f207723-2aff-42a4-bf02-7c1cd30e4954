{"description": "<PERSON><PERSON><PERSON> won by <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> on her last turn by collecting a journey card and trading using the energy cube from the location", "players": [{"id": "jsGOPSmfKkruOlb3AAAD", "name": "<PERSON><PERSON><PERSON>", "position": 33, "hand": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "energyCubes": ["bhakti"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}], "outerScore": 45, "innerScore": 56, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}}, {"id": "hU3-AOwu1ZJ7CK43AAAB", "name": "<PERSON><PERSON><PERSON>", "position": 51, "hand": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "energyCubes": ["karma"], "omTemp": [1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "outerScore": 27, "innerScore": 49, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}}], "started": true, "turnIndex": 0, "roundCount": 11, "travelDeck": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "eventDeck": [{"id": "E2", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}, {"id": "E4", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}], "travelDiscard": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "faceUpEvent": [{"id": "E1", "type": "extraHop"}, {"id": "E8", "type": "wildCube"}, {"id": "E5", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "faceUpJourneyOuter": [{"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "locationCubes": {"1": "bhakti", "2": "artha", "3": "artha", "4": "karma", "5": "bhakti", "6": "karma", "7": "gnana", "8": "gnana", "9": "gnana", "10": "artha", "12": "bhakti", "15": "karma", "16": "gnana", "17": "bhakti", "18": "karma", "19": "artha", "22": "gnana", "23": "bhakti", "24": "artha", "25": "karma", "26": "gnana", "27": "gnana", "28": "bhakti", "29": "artha", "30": "karma", "31": "karma", "32": "karma", "34": "gnana", "37": "gnana", "38": "gnana", "39": "bhakti", "40": "artha", "44": "gnana", "45": "bhakti", "46": "bhakti", "47": "karma", "48": "artha", "undefined": "gnana"}, "locationOm": {"52": true, "54": true, "56": true, "59": true}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1743878856850, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879021612, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879021612, "data": {"cardType": "travel", "card": {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879021612, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879021612, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879115143, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879121080, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879134271, "data": {"path": [62, 11, 12, 50], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743879138474}, {"type": "move", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743879167275, "data": {"path": [65, 31, 30, 55], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743879175298, "data": {"cardType": "travel", "drawnCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743879176332, "data": {"cardType": "travel", "drawnCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743879179232}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743879179233, "data": {"eventId": "global-event-31", "eventName": "Himalayan NE", "eventEffect": "himalayan_ne"}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743879235858, "data": {"path": [50, 12, 11, 10, 49], "travelCards": ["T7", "T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743879243844, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743879285624, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743879287572}, {"type": "move", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743879308787, "data": {"path": [55, 30, 31, 32, 58], "travelCards": ["T9", "T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743879326210, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743879331319, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743879334800}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743879334800, "data": {"eventId": "global-event-22", "eventName": "Up and Over", "eventEffect": "up_and_over_reward"}}, {"type": "PICK_DECK_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743879350174, "data": {"cardType": "travel", "drawnCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickFromTop": true}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743879371846, "data": {"path": [49, 10, 11], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743879435232, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743879436441}, {"type": "move", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743879455476, "data": {"path": [58, 32, 36], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743879465073, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743879482283, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743879485510}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743879485511, "data": {"eventId": "global-event-17", "eventName": "Top Gear", "eventEffect": "top_gear_reward"}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743879599893, "data": {"path": [11, 12, 13], "travelCards": ["T1", "T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743879663442, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743879674134, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743879676182}, {"type": "move", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743879762473, "data": {"path": [36, 32, 31, 65, 63, 20, 21], "travelCards": ["T10", "T11"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743879767392, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 1}}, {"type": "endTurn", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743879792004}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743879792004, "data": {"eventId": "global-event-30", "eventName": "Breezy East", "eventEffect": "breezy_east"}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743879826835, "data": {"path": [13, 62, 66, 42], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743879832446, "data": {"journeyType": "inner", "journeyCardId": "JI21", "omRequirement": 1}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743879842664}, {"type": "move", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743879908406, "data": {"path": [21, 20], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743879928906, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743879936020, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743879937867}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743879937869, "data": {"eventId": "global-event-23", "eventName": "Merchant's Midas", "eventEffect": "merchants_midas_reward"}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743880038682, "data": {"path": [42, 43], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743880048410, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743880057813, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743880060976}, {"type": "move", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743880076668, "data": {"path": [20, 21, 28, 29, 33, 34, 35], "travelCards": ["T5", "T11", "T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743880085187, "data": {"journeyType": "outer", "journeyCardId": "JO19", "omRequirement": 1}}, {"type": "endTurn", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743880098585}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743880098586, "data": {"eventId": "global-event-5", "eventName": "Bountiful Bhandara", "eventEffect": "draw_2_cubes_bonus_5_outer"}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743880208872, "data": {"path": [43, 42, 48, 41], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743880216355, "data": {"journeyType": "inner", "journeyCardId": "JI20", "omRequirement": 1}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743880220545}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743880440867, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743880453182, "data": {"path": [35, 34, 33, 29, 28, 18, 53], "travelCards": ["T9", "T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "8k0wphXq6Dr5ZOrvAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743880461868}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743880461868, "data": {"eventId": "global-event-9", "eventName": "Riots", "eventEffect": "riots_discard"}}, {"type": "riots_discard_cube", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743880461869, "data": {"cubeType": "karma"}}, {"type": "move", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743880516012, "data": {"path": [41, 57], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743880523872, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743880529385, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "6PfH-O1WS0AhhKypAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743880531986}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 1, "timestamp": 1743880550368, "data": {"timestamp": 1743880550368, "loadedStateRoundCount": 7, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743880581958, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743880585562, "data": {"cardType": "travel", "drawnCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743880587396, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "move", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743880608187, "data": {"path": [53, 18, 15, 64, 62, 13, 14], "travelCards": ["T10", "T9"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743880622234}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743880622235, "data": {"eventId": "global-event-32", "eventName": "Central Heart", "eventEffect": "central_heart"}}, {"type": "move", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743880669410, "data": {"path": [57, 41, 48, 42, 43, 60], "travelCards": ["T11", "T7"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743880709166, "data": {"cardType": "travel", "drawnCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743880733208, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743880735904}, {"type": "collectJourney", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743880740688, "data": {"journeyType": "inner", "journeyCardId": "JI7", "omRequirement": 1}}, {"type": "endTurn", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743880744834}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743880744835, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "move", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743880814104, "data": {"path": [60, 43, 42, 66, 64, 29], "travelCards": ["T12", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743880819428, "data": {"journeyType": "outer", "journeyCardId": "JO14", "omRequirement": 1}}, {"type": "endTurn", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743880821886}, {"type": "PICK_FACE_UP_CARDS", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743880904401, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743880911620, "data": {"path": [14, 13, 62, 63, 20, 51], "travelCards": ["T5", "T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743880918352}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743880918352, "data": {"eventId": "global-event-25", "eventName": "<PERSON><PERSON><PERSON>'s Grace", "eventEffect": "pilgrims_grace_reward"}}, {"type": "move", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743880935063, "data": {"path": [29, 33], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743880939955, "data": {"journeyType": "outer", "journeyCardId": "JO17", "omRequirement": 1}}, {"type": "character_trade_reward", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743880997507, "data": {"character": "pilgrim", "cubeReceived": "bhakti", "effect": "pilgrims_grace_reward", "innerPoints": 7}}, {"type": "trade", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743880997507, "data": {"cubesTraded": ["artha", "artha"], "cubeReceived": "bhakti", "count": 1}}, {"type": "endTurn", "playerId": "jsGOPSmfKkruOlb3AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743881004366}, {"type": "endTurn", "playerId": "hU3-AOwu1ZJ7CK43AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743881046938}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743881046939, "data": {"eventId": "global-event-20", "eventName": "Scenic Cruise", "eventEffect": "scenic_cruise_reward"}}], "characterDeck": [{"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}], "energyCubePile": {"artha": 5, "karma": 5, "gnana": 3, "bhakti": 4}, "currentGlobalEvent": {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, "globalEventDeck": [{"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}], "globalEventDiscard": [{"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}]}