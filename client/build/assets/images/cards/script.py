import os

# Define the two lists
list1 = [
    "Vaishno Devi", "Chail", "Golden Temple", "Durgiana Temple", "Taj Mahal",
    "Haridwar", "Badrinath Temple", "Valley of Flowers", "Ambaji Temple",
    "Rann of Kutch", "Jaisalmer Fort", "Hawa Mahal", "Brahma Temple, Pushkar",
    "Siddhivinayak Temple", "Malshej Ghat", "Basilica of Bom Jesus",
    "Meenakshi Temple", "Annamalaiyar Temple", "Nilgiri Hills", "Sabarimala",
    "Padmanabhaswamy Temple", "Backwaters of Kerala", "Udupi Krishna Temple",
    "Hampi", "Kurukshetra", "Shirdi Sai Baba Temple", "Ajanta and Ellora Caves",
    "Mysore Palace", "Coorg", "Khajuraho Temples", "Pachmarhi", "Chitrakote Falls",
    "Charminar", "Ramoji Film City", "Tirumala Venkateswara Temple",
    "Jagannath Temple", "Chilika Lake", "Tarapith", "Betla National Park",
    "Bodh Gaya", "Tawang Monastery", "Kamakhya Temple", "Living Root Bridge",
    "Ujjayanta Palace", "Kangla Fort", "Phawngpui Blue Mountain",
    "Dzükou Valley", "Rumtek Monastery"
]

list2 = [
    "Vaishno Devi", "Chail", "Golden Temple", "Durgiana Temple", "Kurukshetra",
    "Taj Mahal", "Haridwar", "Badrinath Temple", "Valley of Flowers", "Ambaji Temple",
    "Rann of Kutch", "Jaisalmer Fort", "Hawa Mahal", "Brahma Temple, Pushkar",
    "Shirdi Sai Baba Temple", "Siddhivinayak Temple", "Malshej Ghat",
    "Ajanta and Ellora Caves", "Basilica of Bom Jesus", "Meenakshi Temple",
    "Annamalaiyar Temple", "Nilgiri Hills", "Sabarimala", "Padmanabhaswamy Temple",
    "Backwaters of Kerala", "Udupi Krishna Temple", "Hampi", "Mysore Palace",
    "Coorg", "Charminar", "Ramoji Film City", "Tirumala Venkateswara Temple",
    "Khajuraho Temples", "Pachmarhi", "Chitrakote Falls", "Jagannath Temple",
    "Chilika Lake", "Tarapith", "Betla National Park", "Bodh Gaya",
    "Tawang Monastery", "Kamakhya Temple", "Living Root Bridge",
    "Ujjayanta Palace", "Kangla Fort", "Phawngpui Blue Mountain",
    "Dzükou Valley", "Rumtek Monastery"
]

# Create a mapping from original file number (from list1) to new file number (from list2)
mapping = {}
for i, loc in enumerate(list1, start=1):
    new_index = list2.index(loc) + 1  # list2 positions are 1-indexed
    mapping[i] = new_index

# Print the mapping for verification
print("Mapping (old.png -> new.png):")
for old, new in mapping.items():
    print(f"{old}.png -> {new}.png")

# Rename files safely by first renaming to temporary names
for old in mapping.keys():
    src = f"{old}.png"
    temp = f"temp_{old}.png"
    if os.path.exists(src):
        os.rename(src, temp)
    else:
        print(f"Warning: {src} does not exist!")

# Then rename temporary files to final names
for old, new in mapping.items():
    temp = f"temp_{old}.png"
    dst = f"{new}.png"
    if os.path.exists(temp):
        os.rename(temp, dst)
    else:
        print(f"Warning: {temp} does not exist!")