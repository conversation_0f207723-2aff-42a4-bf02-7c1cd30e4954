import React, { useState } from 'react';
import TradeModal from './TradeModal';

function PlayerMat({ player, isActive, winner, socket, gameState }) {
  // Add null check for player
  if (!player) {
    console.error('PlayerMat received null or undefined player');
    return <div className="player-mat error">Error: Player data missing</div>;
  }

  const outerOM = player.omSlotsOuter ? player.omSlotsOuter.reduce((a, b) => a + b, 0) : 0;
  const innerOM = player.omSlotsInner ? player.omSlotsInner.reduce((a, b) => a + b, 0) : 0;
  const currentGlobalEvent = gameState?.currentGlobalEvent;

  // State for trade modal
  const [showTradeModal, setShowTradeModal] = useState(false);

  // Check if this player is the winner
  const isWinner = winner && player.id === winner.id;

  // Determine which win condition was met (if this player is the winner)
  const winByOm = isWinner && winner.winByOm;
  const winByScore = isWinner && winner.winByScore;

  // Check if player has all three travel cards (1, 2, and 3) and current global event is triathlon
  const showTriathlonButton = () => {
    // Check if current global event is Triathlon
    const isTriathlonEvent = currentGlobalEvent?.effect === 'triathlon_bonus';

    if (!isTriathlonEvent) return false;

    // Check for travel cards 1, 2, and 3
    const travelCardValues = player.hand && Array.isArray(player.hand)
      ? player.hand
        .filter(card => card && card.value !== undefined)
        .map(card => card.value)
      : [];

    return travelCardValues.includes(1) && travelCardValues.includes(2) && travelCardValues.includes(3);
  };

  // Handle triathlon button click
  const handleTriathlonClick = () => {
    if (showTriathlonButton()) {
      socket.emit('triathlonMovement');
    }
  };

  // Render character card
  const renderCharacterCard = () => {
    if (!player.character) return null;

    const character = player.character;
    const characterType = character.type.toLowerCase();

    return (
      <div className="character-card" style={{ margin: '1rem 0' }}>
        <div className="character-card-image" style={{
          borderRadius: '8px',
          width: '100%',
          height: '180px',
          overflow: 'hidden',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
        }}>
          {/* Use the character image if available */}
          <img
            src={`/assets/images/characters/${characterType}.jpg`}
            alt={character.type}
            className="character-debug-img"
            style={{ width: '100%', height: '100%', objectFit: 'contain', borderRadius: 'inherit' }}
            onLoad={(e) => {
              console.log(`Character image loaded successfully: ${characterType}.jpg`);
            }}
            onError={(e) => {
              // Fallback to first letter if image fails to load
              console.error(`Failed to load character image: ${characterType}.jpg, src: ${e.target.src}`);
              e.target.style.display = 'none';
              e.target.parentNode.innerText = characterType.charAt(0).toUpperCase();
            }}
          />
        </div>
        <div className="character-card-title" style={{
          textAlign: 'center',
          fontWeight: 'bold',
          marginTop: '0.5rem',
          fontSize: '1.1rem'
        }}>
          {character.type}
        </div>
        <div style={{
          textAlign: 'center',
          fontSize: '0.8rem',
          marginTop: '0.25rem',
          color: '#666'
        }}>
          {character.description}
        </div>
      </div>
    );
  };

  // Render energy cubes visually
  const renderEnergyCubes = () => {
    if (!player.energyCubes || !Array.isArray(player.energyCubes)) {
      return null;
    }

    return player.energyCubes.map((cube, index) => (
      <div
        key={index}
        className={`energy-cube ${cube ? cube.toLowerCase() : 'unknown'}`}
        title={cube || 'Unknown'}
      />
    ));
  };

  // Render hand cards in a nicer format
  const renderHandCards = () => {
    if (!player.hand || !Array.isArray(player.hand)) {
      return null;
    }

    return player.hand.map((card, index) => {
      if (!card) return null;
      let cardText = '';
      let cardType = '';
      let cardBg = '#f3e5f5'; // default background
      let cardColor = '#000';
      let vehicleImagePath = null;

      // Handle travel cards (which have a value property)
      if (card.value !== undefined) {
        cardText = `Move ${card.value} spaces`;
        cardType = 'Travel Card';
        cardBg = '#FFF8E1'; // Cream color background
        cardColor = '#1976d2';
        vehicleImagePath = `/assets/images/vehicles/${card.value}/${card.vehicle || 'camel'}.png`;

        // For travel cards, render just the image
        return (
          <div
            key={index}
            className="card-item"
            style={{
              padding: '0.75rem',
              margin: '0.5rem 0',
              background: cardBg,
              borderRadius: '8px',
              border: `1px solid ${cardColor}`,
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '150px'
            }}
          >
            <img
              src={vehicleImagePath}
              alt={`${card.vehicle} (${card.value})`}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
              onError={(e) => {
                // Fallback if image doesn't load
                e.target.style.display = 'none';
              }}
            />
          </div>
        );
      }

      // For non-travel cards, continue with the existing rendering
      if (card.type === 'extraHop') {
        cardText = 'Extra Hop';
        cardType = 'Extra Hop';
        cardBg = '#e8f5e9';
        cardColor = '#2e7d32';
      } else if (card.type === 'event') {
        cardText = card.name || card.type;
        cardType = 'Event Card';
        cardBg = '#f3e5f5';
        cardColor = '#7b1fa2';
      } else if (card.type === 'journey') {
        // Format the required resources if available
        let requiredText = '';
        if (card.required && typeof card.required === 'object') {
          requiredText = Object.entries(card.required)
            .map(([k, v]) => `${k}:${v}`)
            .join(', ');
        }

        // Format the reward if available
        let rewardText = '';
        if (card.reward) {
          if (card.reward.outer !== undefined) {
            rewardText = `Outer: ${card.reward.outer}`;
          } else if (card.reward.inner !== undefined) {
            rewardText = `Inner: ${card.reward.inner}`;
          }
        }

        cardText = `${requiredText} → ${rewardText}`;
        cardType = 'Journey Card';
        cardBg = '#fff3e0';
        cardColor = '#f57c00';
      } else if (card.type === 'wildCube') {
        cardText = 'Wild Energy Cube';
        cardType = 'Event Card';
        cardBg = '#f3e5f5';
        cardColor = '#7b1fa2';
      }

      return (
        <div
          key={index}
          className="card-item"
          style={{
            fontSize: '0.9rem',
            padding: '0.75rem',
            margin: '0.5rem 0',
            background: cardBg,
            borderRadius: '8px',
            border: `1px solid ${cardColor}`,
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}
        >
          <div style={{
            fontWeight: 'bold',
            marginBottom: '0.5rem',
            color: cardColor,
            fontSize: '1rem'
          }}>
            {cardType}
          </div>
          <div style={{
            color: '#333',
            lineHeight: '1.4'
          }}>
            {cardText}
          </div>
        </div>
      );
    });
  };

  // Add console log for debugging when PlayerMat renders
  console.log('PlayerMat rendering for:', {
    playerName: player.name,
    playerId: player.id,
    isActive,
    position: player.position,
    hasCharacter: !!player.character,
    characterType: player.character?.type?.toLowerCase(),
    handLength: player.hand?.length || 0,
    energyCubesLength: player.energyCubes?.length || 0,
    omSlotsOuterLength: player.omSlotsOuter?.length || 0,
    omSlotsInnerLength: player.omSlotsInner?.length || 0
  });

  return (
    <div
      className={`player-mat ${isActive ? 'active' : ''} ${isWinner ? 'winner' : ''}`}
      style={{
        width: '250px',
        border: isWinner ? '3px solid gold' : undefined,
        boxShadow: isWinner ? '0 0 10px gold' : undefined,
        position: 'relative'
      }}
    >
      {isWinner && (
        <div
          style={{
            position: 'absolute',
            top: '-15px',
            right: '-15px',
            backgroundColor: 'gold',
            color: '#333',
            fontWeight: 'bold',
            padding: '5px 10px',
            borderRadius: '20px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            zIndex: 10,
            transform: 'rotate(15deg)'
          }}
        >
          {winByOm ? 'Winner by OM!' : (winByScore ? 'Winner by Score!' : 'Winner!')}
        </div>
      )}

      <h3 style={{
        borderBottom: isWinner ? '2px solid gold' : '2px solid var(--accent-color)',
        paddingBottom: '0.5rem',
        marginBottom: '0.75rem',
        color: isWinner ? 'goldenrod' : undefined,
        fontWeight: isWinner ? 'bold' : undefined
      }}>
        {player.name}
      </h3>

      {/* Triathlon Button */}
      {showTriathlonButton() && (
        <div
          onClick={handleTriathlonClick}
          style={{
            padding: '8px 12px',
            background: 'linear-gradient(45deg, #FF9800, #FF5722)',
            borderRadius: '8px',
            color: 'white',
            fontWeight: 'bold',
            textAlign: 'center',
            marginBottom: '15px',
            cursor: 'pointer',
            boxShadow: '0 0 10px rgba(255, 152, 0, 0.7)',
            animation: 'pulse 1.5s infinite'
          }}
        >
          <style>
            {`
              @keyframes pulse {
                0% { box-shadow: 0 0 10px rgba(255, 152, 0, 0.7); }
                50% { box-shadow: 0 0 20px rgba(255, 87, 34, 0.9); }
                100% { box-shadow: 0 0 10px rgba(255, 152, 0, 0.7); }
              }
            `}
          </style>
          Triathlon (6 Hops)
        </div>
      )}

      {/* Character Card */}
      {renderCharacterCard()}

      <div className="flex" style={{ justifyContent: 'space-between' }}>
        <div>
          <p><strong>Position:</strong> {player.position}</p>
          <p><strong>OM (temp):</strong> {player.omTemp && Array.isArray(player.omTemp) ? player.omTemp.length : 0}</p>
        </div>
        <div>
          <p><strong>Score:</strong></p>
          <p>Outer: {player.outerScore}</p>
          <p>Inner: {player.innerScore}</p>
          {isWinner && <p style={{ color: 'goldenrod', fontWeight: 'bold' }}>Total: {player.outerScore + player.innerScore}</p>}
        </div>
      </div>

      <div style={{ margin: '0.75rem 0' }}>
        <p><strong>OM Slots:</strong></p>
        <div className="flex" style={{ justifyContent: 'space-between' }}>
          <div>
            <p>Outer: {outerOM}/7</p>
            <div className="flex gap-sm">
              {player.omSlotsOuter && Array.isArray(player.omSlotsOuter) ? player.omSlotsOuter.map((val, idx) => (
                <div key={idx} style={{
                  width: '20px',
                  height: '20px',
                  border: isWinner && val ? '1px solid gold' : '1px solid #ccc',
                  background: val ? (isWinner ? 'gold' : 'var(--accent-color)') : 'transparent',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '20px',
                  fontSize: '0.7rem'
                }}>
                  {val || ''}
                </div>
              )) : null}
            </div>
          </div>
          <div>
            <p>Inner: {innerOM}/7</p>
            <div className="flex gap-sm">
              {player.omSlotsInner && Array.isArray(player.omSlotsInner) ? player.omSlotsInner.map((val, idx) => (
                <div key={idx} style={{
                  width: '20px',
                  height: '20px',
                  border: isWinner && val ? '1px solid gold' : '1px solid #ccc',
                  background: val ? (isWinner ? 'gold' : 'var(--primary-color)') : 'transparent',
                  borderRadius: '4px',
                  textAlign: 'center',
                  lineHeight: '20px',
                  fontSize: '0.7rem'
                }}>
                  {val || ''}
                </div>
              )) : null}
            </div>
          </div>
        </div>
      </div>

      <div style={{ margin: '0.75rem 0' }}>
        <div className="flex" style={{ justifyContent: 'space-between', alignItems: 'center' }}>
          <p><strong>Energy Cubes:</strong></p>
          {isActive && player.character && player.energyCubes && Array.isArray(player.energyCubes) && player.energyCubes.length >= 1 && !player.didTradeThisTurn && (
            <button
              className="trade-btn accent"
              onClick={() => setShowTradeModal(true)}
            >
              Trade
            </button>
          )}
        </div>

        <div className="flex gap-sm" style={{ flexWrap: 'wrap' }}>
          {renderEnergyCubes()}
        </div>
      </div>

      <div style={{ margin: '0.75rem 0' }}>
        <div className="flex" style={{ justifyContent: 'space-between', alignItems: 'center' }}>
          <p><strong>Hand ({player.hand && Array.isArray(player.hand) ? player.hand.length : 0}/4):</strong></p>
          {isActive && player.hand && Array.isArray(player.hand) && player.hand.some(card => card && card.value !== undefined) && !player.didMoveThisTurn && (
            <button
              className="travel-btn accent"
              onClick={() => socket.emit('initiateTravelCardSelection')}
              style={{
                backgroundColor: 'var(--primary-color)',
                color: 'white',
                padding: '5px 10px',
                borderRadius: '4px',
                border: 'none',
                cursor: 'pointer',
                fontWeight: 'bold',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}
            >
              Travel
            </button>
          )}
        </div>
        <div>
          {renderHandCards()}
        </div>
      </div>

      {/* Trade Modal */}
      {showTradeModal && (
        <TradeModal
          player={player}
          onClose={() => setShowTradeModal(false)}
          socket={socket}
        />
      )}
    </div>
  );
}

export default PlayerMat;