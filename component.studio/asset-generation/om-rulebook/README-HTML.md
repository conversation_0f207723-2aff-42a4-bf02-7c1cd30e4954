# Om: The Journey - HTML Rulebook

This is a responsive, web-based rulebook for the **Om: The Journey** board game. It's built using HTML, CSS, and JavaScript, with a focus on providing a user-friendly experience across all devices while maintaining the visual style of the original design.

## 🎯 Features

- **Responsive Design**: Works on mobile, tablet, and desktop screens
- **Navigation System**: Easily jump between rulebook sections
- **Professional Typography**: Bebas Neue headings + Lato body text
- **TTW-Inspired Color Scheme**: Navy blue (#205F80), burnt orange (#E87E1E), sand (#F4EBDD)
- **Modern UI Elements**: Rounded call-out boxes, component grids, icon legends
- **Live Development**: Watch mode with auto-rebuild and browser refresh

## 📁 Project Structure

```
om-rulebook/
├─ src/
│  ├─ pages/                # Markdown content files
│  │  ├─ 01-introduction.md
│  │  ├─ 02-components.md
│  │  ├─ 03-setup.md
│  │  ├─ 04-gameplay.md
│  │  ├─ 05-om-turn-track.md
│  │  └─ 06-reference.md
│  ├─ templates/            # Handlebars templates
│  │  ├─ base-html.hbs      # Main HTML structure
│  │  ├─ cover.hbs          # Cover page layout
│  │  └─ component-spread.hbs # Component grid layout
│  └─ styles/
│     └─ om-web.scss        # Main stylesheet with responsive design
├─ assets/
│  ├─ images/               # Game images
│  └─ icons/                # SVG icons for energy cubes, Om tokens, etc.
├─ scripts/
│  ├─ build-html.js         # HTML generation script
│  └─ watch-html.js         # Development server with live reload
├─ output/                  # Generated files
│  ├─ rulebook.html         # Complete HTML rulebook
│  └─ assets/               # Copied assets
└─ package.json
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd component.studio/asset-generation/om-rulebook
npm install
```

### 2. Development Mode

Start the development server with live reload:

```bash
npm run dev
```

This will:
- Build the HTML rulebook
- Start a server at `http://localhost:3000`
- Open your browser automatically
- Watch for changes and rebuild automatically

### 3. Production Build

Generate the final HTML rulebook:

```bash
npm run build:html
```

The HTML rulebook will be in the `output/` directory as `rulebook.html`.

## 🎨 Customization

### Adding Images

Place your game images in the `assets/images/` directory:

```
assets/images/
├─ cover-art.jpg           # Main cover illustration
├─ board-overview.jpg      # Full board photograph
├─ setup-board.jpg         # Setup state photo
├─ setup-complete.jpg      # Ready-to-play photo
├─ journey-card-examples.jpg # Sample cards
├─ board-preview.jpg       # Board detail shots
└─ (other game images)
```

### Editing Content

Modify the markdown files in `src/pages/` to update content:

- **01-introduction.md** - Game theme and overview
- **02-components.md** - Component lists and descriptions  
- **03-setup.md** - Step-by-step setup instructions
- **04-gameplay.md** - Turn sequence and actions
- **05-om-turn-track.md** - Turn order mechanics
- **06-reference.md** - Quick reference tables

### Styling Changes

Edit `src/styles/om-web.scss` to modify:

- **Colors**: Update the color palette variables
- **Typography**: Change font sizes and spacing
- **Layout**: Adjust responsive grid systems
- **Components**: Modify call-out boxes, tables, etc.

### Templates

Customize the Handlebars templates in `src/templates/`:

- **base-html.hbs** - Overall HTML structure and navigation
- **cover.hbs** - Cover page design
- **component-spread.hbs** - Component grid layout

## 🌐 Browser Compatibility

The HTML rulebook is designed to work on modern browsers:

- Chrome, Firefox, Safari, Edge (latest versions)
- Mobile browsers on iOS and Android
- Responsive design adapts to any screen size

## 📱 Mobile-Friendly Features

- **Navigation Menu**: Collapses to a hamburger menu on smaller screens
- **Responsive Tables**: Adapt to screen width
- **Touch-Friendly**: Optimized tap targets for mobile users
- **Readable Typography**: Adjusts font sizes for different devices

## 🛠️ Technical Details

### Build Process

1. **SCSS Compilation**: Converts styles to CSS
2. **Markdown Processing**: Converts .md files to HTML
3. **Template Rendering**: Combines content with Handlebars templates
4. **Asset Copying**: Moves images and icons to output

### Dependencies

- **handlebars**: Template engine
- **markdown-it**: Markdown processing
- **sass**: CSS preprocessing
- **chokidar**: File watching
- **express**: Development server

## 📦 Deployment

The HTML rulebook can be deployed in various ways:

1. **Web Server**: Upload to any web hosting service
2. **GitHub Pages**: Host directly from a repository
3. **Game Website**: Embed within the game's official site
4. **Local Distribution**: Include with game downloads

## 🎮 Game Integration

This rulebook is designed specifically for **Om: The Journey** and includes:

- Complete rules from the original text rulebook
- Professional visual design matching board game standards
- Digital-friendly layout for online distribution
- Mobile-responsive design for on-the-go reference

---

**Ready to create your professional HTML rulebook!** 🚀

For questions or issues, please refer to the console logs or check the generated HTML structure. 