/**
 * Character cards with special energy trading abilities
 */
const characterCards = [
  // Merchant - trading for artha (1 of any other type for 1 artha)
  {
    id: 'merchant1',
    type: 'Merchant',
    ability: {
      gives: 'artha',
      takes: ['gnana', 'bhakti', 'karma']
    },
    description: 'Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube'
  },
  {
    id: 'merchant2',
    type: 'Merchant',
    ability: {
      gives: 'artha',
      takes: ['gnana', 'bhakti', 'karma']
    },
    description: 'Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube'
  },
  
  // Pilgrim - trading for bhakti (1 of any other type for 1 bhakti)
  {
    id: 'pilgrim1',
    type: 'Pilgrim',
    ability: {
      gives: 'bhakti',
      takes: ['gnana', 'artha', 'karma']
    },
    description: 'Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube'
  },
  {
    id: 'pilgrim2',
    type: 'Pilgrim',
    ability: {
      gives: 'bhakti',
      takes: ['gnana', 'artha', 'karma']
    },
    description: 'Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube'
  },
  
  // Professor - trading for gnana (1 of any other type for 1 gnana)
  {
    id: 'professor1',
    type: 'Professor',
    ability: {
      gives: 'gnana',
      takes: ['bhakti', 'artha', 'karma']
    },
    description: 'Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube'
  },
  {
    id: 'professor2',
    type: 'Professor',
    ability: {
      gives: 'gnana',
      takes: ['bhakti', 'artha', 'karma']
    },
    description: 'Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube'
  },
  
  // Engineer - trading for karma (1 of any other type for 1 karma)
  {
    id: 'engineer1',
    type: 'Engineer',
    ability: {
      gives: 'karma',
      takes: ['bhakti', 'artha', 'gnana']
    },
    description: 'Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube'
  },
  {
    id: 'engineer2',
    type: 'Engineer',
    ability: {
      gives: 'karma',
      takes: ['bhakti', 'artha', 'gnana']
    },
    description: 'Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube'
  }
];

module.exports = characterCards; 