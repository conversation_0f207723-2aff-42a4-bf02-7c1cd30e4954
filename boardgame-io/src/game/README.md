# Om: The Journey - boardgame.io Implementation

This directory contains a complete implementation of "Om: The Journey" board game using the boardgame.io framework.

## Project Structure

- `Game.js` - Core game logic and rules using boardgame.io
- `OmJourneyClient.js` - React client for rendering the game UI
- `Server.js` - Boardgame.io server for multiplayer functionality
- `OmJourney.css` - Styling for the game board
- `App.js` - Entry point component for the boardgame.io client
- `boardLayout.js` - Defines the coordinate system for drawing the game board

## Getting Started

### Running the Game

1. To run the boardgame.io server (for multiplayer):
   ```
   cd boardgame-io && npm run server
   ```

2. To run the boardgame.io client in development mode:
   ```
   cd boardgame-io && npm start
   ```

3. To build the client for production:
   ```
   cd boardgame-io && npm run build
   ```

## Game Features

This implementation includes:

- Core gameplay mechanics from Om: The Journey
- Support for 2-4 players
- Character abilities and trading
- Inner and Outer journey tracks
- Energy cube collection and management
- Om token collection and allocation
- Journey card collection for scoring
- Movement with travel cards (exact travel only)
- Global events system
- Win condition tracking
- Game animations

## Multiplayer Setup

For multiplayer games:

1. Start the server:
   ```
   cd boardgame-io && npm run server
   ```

2. The multiplayer configuration in `OmJourneyClient.js` connects to:
   ```javascript
   multiplayer: SocketIO({ server: 'http://localhost:8000' }),
   ```

3. Each player should:
   - Enter a player name
   - Select a player position (0, 1, 2, or 3)
   - Click "Join" to join the game
   - Once all players have joined, click "Start Game"

## Game Rules

Key rules to note:

- Travel cards must be used for exact travel (you must use cards matching the exact distance needed)
- Energy cubes are randomly distributed across the board with 12 cubes per type (48 total)
- Players can collect a maximum of 5 energy cubes and 3 OM tokens
- Win conditions: score ≥ 100 points (combined inner and outer journey) OR collect ≥ 7 OM tokens

## Credits

This implementation uses [boardgame.io](https://boardgame.io/), an open-source framework for turn-based games.