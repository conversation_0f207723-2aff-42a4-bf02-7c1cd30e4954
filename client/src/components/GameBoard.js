// client/src/components/GameBoard.js

import React, { useState, useEffect } from 'react';
import PlayerMat from './PlayerMat';
import FaceUpCards from './FaceUpCards';
import InteractiveBoard from './InteractiveBoard';
import ErrorBoundary from './ErrorBoundary';
import endTurnSound from '../assets/sounds/end_turn.mp3';

function GameBoard({ socket, gameState, winner }) {
  const { players, turnIndex, finalRound } = gameState;
  const currentPlayer = players && turnIndex !== undefined && turnIndex >= 0 && turnIndex < players.length
    ? players[turnIndex]
    : null;
  const [soundEnabled, setSoundEnabled] = useState(() => {
    // Get saved preference from localStorage, default to true if not set
    return localStorage.getItem('soundEnabled') !== 'false';
  });

  const handleEndTurn = () => {
    if (soundEnabled) {
      const audio = new Audio(endTurnSound);
      audio.play();
    }
    socket.emit('endTurn');
  };

  const handleSaveGame = () => {
    socket.emit('saveGame');
  };

  const toggleSound = () => {
    const newSetting = !soundEnabled;
    setSoundEnabled(newSetting);
    localStorage.setItem('soundEnabled', newSetting.toString());
  };

  return (
    <div className="flex-column">
      <div className="flex-grow">
        <ErrorBoundary componentName="InteractiveBoard" fallback={<div>Error in game board. Please refresh the page.</div>}>
          <InteractiveBoard
            socket={socket}
            gameState={gameState}
            winner={winner}
          />
        </ErrorBoundary>
      </div>

      <div className="player-section flex" style={{ flexWrap: 'wrap', justifyContent: 'center', gap: '1rem', marginTop: '1rem' }}>
        {players.map((player, index) => (
          <ErrorBoundary key={player.id} componentName={`PlayerMat for ${player.name}`} fallback={<div>Error in player mat for {player.name}</div>}>
            <PlayerMat
              key={player.id}
              player={player}
              isActive={index === turnIndex}
              winner={winner}
              socket={socket}
              gameState={gameState}
            />
          </ErrorBoundary>
        ))}
      </div>

      <div className="face-up-cards-section" style={{ marginTop: '1rem' }}>
        <FaceUpCards
          socket={socket}
          gameState={gameState}
        />
      </div>

      <div className="flex gap-md mt-md" style={{ justifyContent: 'center' }}>
        {currentPlayer && socket && currentPlayer.id === socket.id && (
          <button
            className="accent"
            onClick={handleEndTurn}
            disabled={!!winner}
          >
            End Turn
          </button>
        )}
        <button className="secondary" onClick={handleSaveGame}>Save Game</button>
        <button
          className="secondary"
          onClick={toggleSound}
          title={soundEnabled ? "Disable sound" : "Enable sound"}
        >
          {soundEnabled ? "🔊 Sound On" : "🔇 Sound Off"}
        </button>
      </div>
    </div>
  );
}

export default GameBoard;