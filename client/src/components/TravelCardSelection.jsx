import React, { useState } from 'react';

function TravelCardSelection({ player, onCancel, onSelectCards, numberOfCardsToSelect = 1, title, description }) {
  const [selectedCards, setSelectedCards] = useState([]);
  
  // Filter just travel cards from the player's hand
  const travelCards = player.hand.filter(card => card.type === 'travel');
  
  const handleCardToggle = (card) => {
    // Check if this card is already selected
    const isSelected = selectedCards.some(sc => sc.id === card.id);
    
    if (isSelected) {
      // Remove from selection
      setSelectedCards(selectedCards.filter(sc => sc.id !== card.id));
    } else {
      // Add to selection if we haven't reached the limit
      if (selectedCards.length < numberOfCardsToSelect) {
        setSelectedCards([...selectedCards, card]);
      }
    }
  };
  
  const handleConfirm = () => {
    // For standard discard selection (typically 1-2 cards)
    if (numberOfCardsToSelect <= 10) {
      if (selectedCards.length === numberOfCardsToSelect) {
        console.log("Confirming card selection for discard:", selectedCards.map(c => c.id));
        onSelectCards(selectedCards.map(c => c.id));
      }
    } 
    // For travel selection (high numberOfCardsToSelect means "select as many as you want")
    else {
      if (selectedCards.length > 0) {
        console.log("Confirming travel card selection:", selectedCards);
        // Pass the full card objects for travel, not just IDs
        onSelectCards(selectedCards);
      }
    }
  };
  
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 9999
    }}>
      <div style={{
        backgroundColor: '#fff',
        borderRadius: '8px',
        padding: '20px',
        width: '90%',
        maxWidth: '600px',
        boxShadow: '0 5px 15px rgba(0, 0, 0, 0.3)'
      }}>
        <h2 style={{ marginTop: 0, textAlign: 'center' }}>{title || 'Select Travel Cards'}</h2>
        
        <p style={{ textAlign: 'center', marginBottom: '20px' }}>
          {description || 
            (numberOfCardsToSelect > 10 
              ? `Select travel cards for your move. The total value will determine how far you can travel.`
              : `Select ${numberOfCardsToSelect} travel card(s) to discard.`
            )
          }
        </p>
        
        <div style={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: '10px',
          justifyContent: 'center',
          marginBottom: '20px'
        }}>
          {travelCards.map(card => {
            const isSelected = selectedCards.some(sc => sc.id === card.id);
            const vehicleImagePath = `/assets/images/vehicles/${card.value}/${card.vehicle || 'camel'}.png`;
            
            return (
              <div 
                key={card.id}
                onClick={() => handleCardToggle(card)}
                style={{
                  width: '100px',
                  height: '130px',
                  padding: '8px',
                  backgroundColor: isSelected ? '#E3F2FD' : '#FFF8E1',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  border: isSelected ? '3px solid #2196F3' : '1px solid #ddd',
                  boxShadow: isSelected ? '0 0 8px rgba(33, 150, 243, 0.7)' : '0 2px 4px rgba(0,0,0,0.1)'
                }}
              >
                <div style={{ 
                  position: 'absolute', 
                  top: '8px', 
                  left: '8px', 
                  backgroundColor: isSelected ? '#2196F3' : '#FFA000',
                  color: 'white',
                  borderRadius: '50%',
                  width: '30px',
                  height: '30px',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  fontWeight: 'bold',
                  fontSize: '16px',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
                }}>
                  {card.value}
                </div>
                
                <div style={{ 
                  flexGrow: 1, 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  width: '100%',
                  height: '80px'
                }}>
                  <img 
                    src={vehicleImagePath}
                    alt={`${card.vehicle} (${card.value})`}
                    style={{ 
                      maxWidth: '80%', 
                      maxHeight: '80%', 
                      objectFit: 'contain' 
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                </div>
                
                <div style={{ 
                  marginTop: 'auto', 
                  fontSize: '12px',
                  textAlign: 'center',
                  textTransform: 'capitalize'
                }}>
                  {card.vehicle || 'Travel'}
                </div>
              </div>
            );
          })}
        </div>
        
        {travelCards.length === 0 && (
          <p style={{ textAlign: 'center', color: '#666' }}>
            You don't have any travel cards to select.
          </p>
        )}
        
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: '10px',
          marginTop: '20px' 
        }}>
          <button 
            onClick={onCancel}
            style={{
              padding: '8px 16px',
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
          
          <button 
            onClick={handleConfirm}
            disabled={
              // For discard selection
              (numberOfCardsToSelect <= 10 && selectedCards.length !== numberOfCardsToSelect) ||
              // For travel selection
              (numberOfCardsToSelect > 10 && selectedCards.length === 0) ||
              // If there are no travel cards at all
              travelCards.length === 0
            }
            style={{
              padding: '8px 16px',
              backgroundColor: 
                ((numberOfCardsToSelect <= 10 && selectedCards.length === numberOfCardsToSelect) ||
                 (numberOfCardsToSelect > 10 && selectedCards.length > 0)) && travelCards.length > 0
                  ? '#4CAF50' 
                  : '#ccc',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 
                ((numberOfCardsToSelect <= 10 && selectedCards.length === numberOfCardsToSelect) ||
                 (numberOfCardsToSelect > 10 && selectedCards.length > 0)) && travelCards.length > 0
                  ? 'pointer' 
                  : 'not-allowed'
            }}
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
}

export default TravelCardSelection; 