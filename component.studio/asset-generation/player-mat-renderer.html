<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<style>
  /* scale‑compressed version; remove “scale” to print at 300 dpi */
  :root{
    --gap: 50px;            /* 0.25 in @300 dpi */
    --travel-w: 750px;      /* 2.5 in */
    --travel-h: 1050px;     /* 3.5 in */
    --char-w:   750px;
    --char-h:   1050px;
    --token:    600px;      /* 2 × 2 in */
    --journey-w: 825px;     /* 2.75 × 4.75 in */
    --journey-h: 1425px;
  }
  body{margin:0;background:#f9f6ed;}
  .mat{
    position:relative;
    width:calc( /* travel */ (var(--travel-w)*2 + var(--gap)) +
                var(--gap) + /* character */ var(--char-w) +
                var(--gap) + /* journeys */ (var(--journey-w)*4 + var(--gap)*3) +
                var(--gap) );
    height:calc(var(--journey-h)*2 + var(--gap)*2);
  }
  .slot{
    position:absolute;border:4px solid #333;
    display:flex;align-items:center;justify-content:center;
    font-family:sans-serif;font-size:60px;text-align:center;
    line-height:1.1;white-space:pre-line;
    background:rgba(255,255,255,0.15);
  }
</style>
</head>
<body>
<div class="mat">
  <!-- Travel cards -->
  <div class="slot" style="left:0;top:0;width:var(--travel-w);height:var(--travel-h);">Travel Card</div>
  <div class="slot" style="left:calc(var(--travel-w)+var(--gap));top:0;width:var(--travel-w);height:var(--travel-h);">Travel Card</div>
  <div class="slot" style="left:0;top:calc(var(--travel-h)+var(--gap));width:var(--travel-w);height:var(--travel-h);">Travel Card</div>
  <div class="slot" style="left:calc(var(--travel-w)+var(--gap));top:calc(var(--travel-h)+var(--gap));width:var(--travel-w);height:var(--travel-h);">Travel Card</div>

  <!-- Character -->
  <div class="slot" style="left:calc((var(--travel-w)*2)+var(--gap)*2);top:0;width:var(--char-w);height:var(--char-h);">Character<br>Card</div>

  <!-- Token & energy trays -->
  <div class="slot" style="left:calc((var(--travel-w)*2)+var(--gap)*2);top:calc(var(--char-h)+var(--gap));width:var(--token);height:var(--token);">Om<br>Tokens</div>
  <div class="slot" style="left:calc((var(--travel-w)*2)+var(--gap)*3 + var(--token));top:calc(var(--char-h)+var(--gap));width:var(--token);height:var(--token);">Energy<br>Cubes</div>

  <!-- Journey grid -->
  <!-- row 1 -->
  <!-- repeat 4× -->
  <script>
    const om = [1,1,2,3];
    om.forEach((n,i)=>{
      const slot=document.createElement('div');
      slot.className='slot';
      slot.style.width='var(--journey-w)';
      slot.style.height='var(--journey-h)';
      slot.style.left=`calc((var(--travel-w)*2)+var(--gap)*3 + var(--char-w) + (${i}*(var(--journey-w)+var(--gap))))`;
      slot.style.top='0';
      slot.innerText=`Journey\nCard\n${n} Om Token${n>1?'s':''}`;
      document.querySelector('.mat').appendChild(slot.cloneNode(true));
      slot.style.top=`calc(var(--journey-h)+var(--gap))`;
      document.querySelector('.mat').appendChild(slot);
    });
  </script>
</div>
</body>
</html>