{"description": "First time that player blocked another player from getting om token costing them 1 journey card and thus the game", "players": [{"id": "0InvNkZx2hONAVsLAAAD", "name": "<PERSON><PERSON>", "position": 32, "hand": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "energyCubes": ["artha"], "omTemp": [1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "outerScore": 25, "innerScore": 57, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "scXdt6k9337IIWeWAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 34, "hand": [], "energyCubes": ["gnana"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "outerScore": 59, "innerScore": 47, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}}], "started": true, "turnIndex": 1, "roundCount": 11, "travelDeck": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "eventDeck": [{"id": "E8", "type": "wildCube"}, {"id": "E3", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}, {"id": "E5", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "travelDiscard": [], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "faceUpEvent": [{"id": "E7", "type": "wildCube"}, {"id": "E4", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "faceUpJourneyOuter": [{"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "locationCubes": {"1": "gnana", "2": "artha", "3": "karma", "5": "artha", "6": "artha", "7": "gnana", "8": "artha", "10": "gnana", "12": "bhakti", "13": "karma", "14": "karma", "15": "artha", "16": "karma", "17": "karma", "18": "bhakti", "19": "artha", "20": "karma", "22": "karma", "23": "bhakti", "25": "artha", "26": "karma", "27": "gnana", "28": "artha", "30": "bhakti", "31": "bhakti", "35": "gnana", "37": "bhakti", "38": "gnana", "39": "bhakti", "40": "artha", "43": "artha", "44": "artha", "46": "gnana", "48": "karma", "undefined": "bhakti"}, "locationOm": {"49": true, "50": true, "52": true, "55": true}, "finalRound": true, "finalRoundStarter": 1, "finalRoundEnd": 0, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815538944, "data": {"eventId": "global-event-22", "eventName": "Up and Over", "eventEffect": "up_and_over_reward"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815597315, "data": {"cardType": "travel", "card": {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815597315, "data": {"cardType": "travel", "card": {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815597315, "data": {"cardType": "travel", "card": {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815597315, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815691218, "data": {"path": [61, 4, 3, 56], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815709803, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815722871, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743815741255}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743815757716, "data": {"path": [63, 20, 51], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743815784604, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743815792255, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743815794363}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743815794364, "data": {"eventId": "global-event-16", "eventName": "<PERSON><PERSON> Rhapsody", "eventEffect": "rickshaw_rhapsody_reward"}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743815823937, "data": {"path": [56, 3, 4], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743815830688, "data": {"journeyType": "inner", "journeyCardId": "JI3", "omRequirement": 1}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743815861304}, {"type": "travel_card_reward", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743815904933, "data": {"vehicle": "rickshaw", "effect": "rickshaw_rhapsody_reward", "outerPoints": 5}}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743815904933, "data": {"path": [51, 20, 63, 24], "travelCards": ["T7", "T3"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743815913902, "data": {"cardType": "travel", "drawnCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743815921559, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743815923213}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743815923213, "data": {"eventId": "global-event-3", "eventName": "<PERSON><PERSON>", "eventEffect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743816014162, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743816031907, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743816045043, "data": {"path": [4, 61, 62, 11, 7, 8, 54], "travelCards": ["T9", "T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743816074505}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743816109173, "data": {"path": [24, 23, 59], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743816117433, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743816126158, "data": {"cardType": "travel", "drawnCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743816135637}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743816135637, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743816205176, "data": {"path": [54, 8, 7, 11], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743816226879, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743816230668, "data": {"cardType": "travel", "drawnCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743816236799}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743816254221, "data": {"path": [59, 23, 22, 21], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743816257572, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 1}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743816262771}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743816262771, "data": {"eventId": "global-event-26", "eventName": "Engineer's Precision", "eventEffect": "engineers_precision_reward"}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743816310731, "data": {"path": [11, 7, 8, 54, 9], "travelCards": ["T7", "T6"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743816321389, "data": {"journeyType": "outer", "journeyCardId": "JO4", "omRequirement": 1}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743816326575}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743816342040, "data": {"path": [21, 28, 29], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743816345731, "data": {"journeyType": "outer", "journeyCardId": "JO14", "omRequirement": 1}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743816348276}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743816348276, "data": {"eventId": "global-event-30", "eventName": "Breezy East", "eventEffect": "breezy_east"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743816395207, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743816402990, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743816414049, "data": {"path": [9, 1, 2, 3, 47], "travelCards": ["T9", "T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743816416917}, {"type": "PICK_FACE_UP_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743816537381, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743816553984, "data": {"path": [29, 64, 61, 4, 3, 47, 57], "travelCards": ["T10", "T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743816587106}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743816587106, "data": {"eventId": "global-event-21", "eventName": "Heavy Haul", "eventEffect": "heavy_haul_reward"}}, {"type": "PICK_DECK_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743816674692, "data": {"cardType": "travel", "drawnCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743816708864, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743816728343, "data": {"path": [47, 46, 45, 44, 43, 60], "travelCards": ["T5", "T6", "T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743816734705}, {"type": "PICK_FACE_UP_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743816819086, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743816828727, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743816836904, "data": {"vehicle": "truck", "effect": "heavy_haul_reward", "outerPoints": 10, "lostCubes": ["gnana"], "lostCubeCount": 1}}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743816836904, "data": {"path": [57, 41, 48, 42], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743816840158}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743816840158, "data": {"eventId": "global-event-24", "eventName": "Professor's Insight", "eventEffect": "professors_insight_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743816865955, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743816899902, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743816981204, "data": {"path": [60, 43, 44, 45], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743816984527}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743817039941, "data": {"path": [42, 48, 41], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743817046083, "data": {"journeyType": "inner", "journeyCardId": "JI20", "omRequirement": 1}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743817142428}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743817142428, "data": {"eventId": "global-event-29", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743817195420, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743817228652, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743817246213, "data": {"path": [45, 31, 32, 36], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743817255224}, {"type": "PICK_FACE_UP_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743817270046, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743817278490, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "move", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743817296335, "data": {"path": [41, 48, 42, 66, 64, 15, 18, 53], "travelCards": ["T10", "T6", "T7"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "CfHzGI_CId5S4GeOAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743817301330}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743817301330, "data": {"eventId": "global-event-18", "eventName": "Hop on Hop off", "eventEffect": "hop_on_hop_off_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743817350100, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743817355015, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743817366178, "data": {"vehicle": "bus", "effect": "hop_on_hop_off_reward", "outerPoints": 5}}, {"type": "move", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743817366178, "data": {"path": [36, 32, 58], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "NcxZfw8-cNFuwyw3AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743817377611}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 1, "timestamp": 1743817514204, "data": {"timestamp": 1743817514204, "loadedStateRoundCount": 9, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743817549872, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743817567401, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743817575086, "data": {"path": [53, 18, 28, 29, 33], "travelCards": ["T11", "T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743817578335}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743817578335, "data": {"eventId": "global-event-11", "eventName": "Pedal Power", "eventEffect": "pedal_power_reward"}}, {"type": "move", "playerId": "0InvNkZx2hONAVsLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743817600473, "data": {"path": [58, 32], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "0InvNkZx2hONAVsLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743817603136, "data": {"journeyType": "inner", "journeyCardId": "JI16", "omRequirement": 1}}, {"type": "endTurn", "playerId": "0InvNkZx2hONAVsLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743817631674}, {"type": "move", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743817649018, "data": {"path": [33, 34], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743817651141, "data": {"journeyType": "outer", "journeyCardId": "JO18", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743817651141, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 106, "playerOmTotal": 4, "finalRoundStarter": 1, "finalRoundEnd": 0}}, {"type": "endTurn", "playerId": "scXdt6k9337IIWeWAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743817652854}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743817652854, "data": {"eventId": "global-event-1", "eventName": "Drizzle of Delay", "eventEffect": "max_moves_2_and_cost_artha_north_east"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "0InvNkZx2hONAVsLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743817785888, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "0InvNkZx2hONAVsLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743817791252, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "0InvNkZx2hONAVsLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743817803231}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 1, "timestamp": 1743817803233, "data": {"winner": {"id": "scXdt6k9337IIWeWAAAB", "name": "<PERSON><PERSON><PERSON>", "outerScore": 59, "innerScore": 47, "totalScore": 106, "omTotal": 4}, "totalRounds": 11, "players": [{"id": "0InvNkZx2hONAVsLAAAD", "name": "<PERSON><PERSON>", "outerScore": 25, "innerScore": 57, "totalScore": 82, "omTotal": 4}, {"id": "scXdt6k9337IIWeWAAAB", "name": "<PERSON><PERSON><PERSON>", "outerScore": 59, "innerScore": 47, "totalScore": 106, "omTotal": 4}]}}], "characterDeck": [{"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}], "energyCubePile": {"artha": 3, "karma": 5, "gnana": 5, "bhakti": 7}, "currentGlobalEvent": {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, "globalEventDeck": [{"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}], "globalEventDiscard": [{"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}]}