const React = require('react');
const { Client } = require('boardgame.io/react');
const { SocketIO } = require('boardgame.io/multiplayer');
const OmJourney = require('./Game');
const { nodes, edges, airportNodes, jyotirlinga } = require('./boardLayout');
require('./OmJourney.css');

// Energy cube colors map
const energyColors = {
  artha: '#F44336', // Red
  karma: '#2196F3', // Blue
  gnana: '#FFC107', // Yellow
  bhakti: '#4CAF50'  // Green
};

// -----------------------------------------------------------------------
// OmJourneyBoard Component - Main game board rendering component
// -----------------------------------------------------------------------
class OmJourneyBoard extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      // UI state
      selectedTravelCards: [],
      selectedDestination: null,
      selectedJourneyCard: null,
      allowedDestinations: [],

      // Movement animation state
      animatingMovement: false,
      animationPath: [],
      animationStep: 0,

      // Heavy Haul and other special events
      showEnergyCubeSelection: false,
      showTravelCardSelection: false,
      pendingTravelCardIds: null,

      // Trade state
      tradeCubeType: null,
      receiveCubeType: null,

      // Zoom and pan for map
      zoom: 1,
      pan: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 },

      // Extra state for enhancing the UI
      journeyModalOpen: false,
      soundEnabled: true,
      nameMode: false,
      hideLabels: false,
      fullscreen: false,
      showOmTrack: false
    };

    // Refs
    this.boardRef = React.createRef();

    // Bind methods
    this.handleTravelCardSelection = this.handleTravelCardSelection.bind(this);
    this.handleEnergyCubeSelection = this.handleEnergyCubeSelection.bind(this);
    this.handleDestinationClick = this.handleDestinationClick.bind(this);
    this.handleJourneyCardSelection = this.handleJourneyCardSelection.bind(this);
    this.handleEndTurn = this.handleEndTurn.bind(this);
    this.handleTradeSelection = this.handleTradeSelection.bind(this);
    this.handlePlayerClick = this.handlePlayerClick.bind(this);
    this.handleMouseDown = this.handleMouseDown.bind(this);
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleMouseUp = this.handleMouseUp.bind(this);
    this.handleMouseLeave = this.handleMouseLeave.bind(this);
  }

  // -----------------------------------------------------------------------
  // Game Action Handlers
  // -----------------------------------------------------------------------

  // Handle when a player selects travel cards for movement
  handleTravelCardSelection(selectedCards) {
    this.setState({
      selectedTravelCards: selectedCards,
      showTravelCardSelection: false
    });

    // Calculate how many spaces we can move based on card values
    const hopCount = selectedCards.reduce((sum, card) => sum + card.value, 0);

    // Calculate destinations we can reach with exactly this many hops
    const destinations = this.getReachableLocationsExactHops(this.getCurrentPlayerPosition(), hopCount);

    // Update UI to highlight these destinations
    this.setState({ allowedDestinations: destinations });
  }

  // Handle when player selects energy cubes for Heavy Haul event
  handleEnergyCubeSelection(selectedCubes) {
    this.setState({ showEnergyCubeSelection: false });

    // Use the game's move API to send the selection to the game logic
    this.props.moves.selectCubesForHeavyHaul({
      selectedCubes: selectedCubes,
      travelCardIds: this.state.pendingTravelCardIds
    });

    // Reset state after selection
    this.setState({ pendingTravelCardIds: null });
  }

  // Handle when player clicks on their piece to initiate movement
  handlePlayerClick() {
    // No need to get currentPlayer here as we're just toggling UI state

    if (!this.state.selectedTravelCards.length) {
      // Show travel card selection modal
      this.setState({ showTravelCardSelection: true });
    } else {
      // Clear selection if already in selection mode
      this.setState({
        selectedTravelCards: [],
        allowedDestinations: []
      });
    }
  }

  // Handle when player clicks on a destination node
  handleDestinationClick(destinationId) {
    const { allowedDestinations, selectedTravelCards } = this.state;

    // Verify this is a valid destination
    if (!allowedDestinations || !allowedDestinations.includes(destinationId)) {
      return;
    }

    // Find path to destination
    const exactHopCount = selectedTravelCards.reduce((sum, card) => sum + card.value, 0);
    const path = this.findPathWithExactHops(this.getCurrentPlayerPosition(), destinationId, exactHopCount);

    if (!path || path.length < 2) {
      console.log('No valid path found with exact hop count');
      return;
    }

    // Check if Heavy Haul event is active and we're using a truck card
    const isTruckCard = selectedTravelCards.some(card => card.vehicle === 'truck');
    const isHeavyHaulEvent = this.props.G.currentGlobalEvent?.effect === 'heavy_haul_reward';
    const currentPlayer = this.getCurrentPlayer();

    if (isHeavyHaulEvent && isTruckCard && currentPlayer.energyCubes.length >= 3) {
      // Store the travel card IDs for later use
      this.setState({
        pendingTravelCardIds: selectedTravelCards.map(card => card.id),
        showEnergyCubeSelection: true
      });
      return;
    }

    // Start movement animation
    this.startMovementAnimation(path, () => {
      // After animation completes, make the move in the game
      this.props.moves.movePlayer({
        path: path,
        travelCardIds: selectedTravelCards.map(card => card.id),
        extraHopCount: 0
      });

      // Reset selection state
      this.setState({
        selectedTravelCards: [],
        allowedDestinations: [],
        selectedDestination: null
      });
    });
  }

  // Handle when player selects a journey card to collect
  handleJourneyCardSelection(journeyCard) {
    const journeyType = journeyCard.reward.outer !== undefined ? 'outer' : 'inner';

    // Get the number of journeys already completed at this location
    const journeyCount = this.getJourneyCount(journeyCard);

    // Cost increases with more journeys completed
    const costArray = [1, 1, 2, 3];
    const requiredOm = journeyCount < costArray.length ? costArray[journeyCount] : Infinity;

    // Make the move to collect the journey card
    this.props.moves.collectJourney({
      journeyCardId: journeyCard.id,
      journeyType,
      requiredOm
    });

    // Close journey modal if open
    this.setState({ journeyModalOpen: false });
  }

  // Helper: gets the number of journeys already completed at this location
  getJourneyCount(card) {
    const currentPlayer = this.getCurrentPlayer();
    if (!currentPlayer) return 0;

    // Check if it's inner or outer journey
    const journeyType = card.reward.inner !== undefined ? 'inner' : 'outer';
    const locationId = card.locationId;

    // Count completed journeys of the same type to the same location
    const completedCards = journeyType === 'inner'
      ? currentPlayer.journeyInner || []
      : currentPlayer.journeyOuter || [];

    return completedCards.filter(c => c.locationId === locationId).length;
  }

  // Handle energy cube trading
  handleTradeSelection(spendType, receiveType) {
    // Validate trade
    if (spendType === receiveType) return;

    // Make the trade move
    this.props.moves.tradeEnergyCubes({
      spending: { [spendType]: 2 },
      receiving: { [receiveType]: 1 }
    });

    this.setState({
      tradeCubeType: null,
      receiveCubeType: null
    });
  }

  // Handle the end turn button click
  handleEndTurn() {
    // Play end turn sound if sound is enabled
    if (this.state.soundEnabled) {
      const audio = new Audio('/assets/sounds/end_turn.mp3');
      audio.play().catch(e => console.log('Sound play error:', e));
    }

    // Call the game's endTurn move
    this.props.moves.endTurn();
  }

  // -----------------------------------------------------------------------
  // Animation and Visual Helpers
  // -----------------------------------------------------------------------

  // Start a movement animation along a path
  startMovementAnimation(path, onComplete) {
    if (!path || path.length <= 1) {
      if (onComplete) onComplete();
      return;
    }

    // Play movement sound if enabled
    if (this.state.soundEnabled) {
      const audio = new Audio('/assets/sounds/bus_bell.mp3');
      audio.play().catch(e => console.log('Sound play error:', e));
    }

    // Set up animation state
    this.setState({
      animatingMovement: true,
      animationPath: path,
      animationStep: 0
    });

    // Create animation interval
    let step = 0;
    const animationInterval = setInterval(() => {
      step++;

      this.setState({ animationStep: step });

      if (step >= path.length - 1) {
        // Animation complete
        clearInterval(animationInterval);
        this.setState({ animatingMovement: false });
        if (onComplete) onComplete();
      }
    }, 300); // Move every 300ms
  }

  // Helper method to toggle nameMode (displaying location names vs. IDs)
  toggleNameMode = () => {
    this.setState(prevState => ({ nameMode: !prevState.nameMode }));
  }

  // -----------------------------------------------------------------------
  // Path Finding and Game Logic Helpers
  // -----------------------------------------------------------------------

  // Get the current player object from G
  getCurrentPlayer() {
    const { G, ctx } = this.props;
    const { players } = G;
    const { currentPlayer: currentPlayerID } = ctx;

    // Handle both array and object structures for players
    if (Array.isArray(players)) {
      return players[currentPlayerID];
    } else if (typeof players === 'object') {
      return players[currentPlayerID] || Object.values(players).find(p => p.id === parseInt(currentPlayerID));
    }

    // Fallback if player not found
    return {
      name: `Player ${currentPlayerID}`,
      position: 0,
      innerScore: 0,
      outerScore: 0,
      omSlotsInner: [],
      omSlotsOuter: [],
      journeyInner: [],
      journeyOuter: [],
      hand: [],
      energyCubes: [],
      character: null
    };
  }

  // Get the current player's position
  getCurrentPlayerPosition() {
    const currentPlayer = this.getCurrentPlayer();
    return currentPlayer ? currentPlayer.position : 0;
  }

  // Find locations that can be reached with a given number of moves
  getReachableLocationsExactHops(startId, exactHops) {
    // Base case - 0 hops means we can only reach the current position
    if (exactHops <= 0) return [startId];

    const result = new Set();
    const visited = new Set();

    // Helper function to find all locations exactly hopCount away
    const findExactHops = (nodeId, remainingHops) => {
      // If we've used all our hops, this location is reachable
      if (remainingHops === 0) {
        if (nodeId !== startId) { // Don't include the start node in results
          result.add(nodeId);
        }
        return;
      }

      // Mark this node as visited to avoid cycles
      visited.add(nodeId);

      // Get neighbors
      const neighbors = this.getNeighbors(nodeId);

      // Visit each unvisited neighbor
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          // Continue DFS with one less hop and a new visited set
          findExactHops(neighbor, remainingHops - 1);
        }
      }

      // Unmark this node as visited to allow different paths
      visited.delete(nodeId);
    };

    // Start search from the starting position
    findExactHops(startId, exactHops);

    // Convert to array and return
    return Array.from(result);
  }

  // Find a path with EXACTLY the specified number of hops (no more, no less)
  findPathWithExactHops(start, destination, exactHops) {
    // If we need 0 hops and we're already at the destination, return just the start
    if (exactHops === 0 && start === destination) {
      return [start];
    }

    // Track path to avoid loops
    let foundPath = null;

    const findExactHopsPathRecursive = (current, remainingHops, path = []) => {
      // If we already found a path, no need to continue searching
      if (foundPath) return;

      // Current path including this node
      const currentPath = [...path, current];

      // If we've reached our target hop count, check if we're at the destination
      if (remainingHops === 0) {
        if (current === destination) {
          foundPath = currentPath;
        }
        return;
      }

      // Get neighbors
      const neighbors = this.getNeighbors(current);

      for (const neighbor of neighbors) {
        // Skip if we've already visited this node in this path (no loops)
        if (path.includes(neighbor)) continue;

        // Continue DFS with one less hop
        findExactHopsPathRecursive(neighbor, remainingHops - 1, currentPath);
      }
    };

    // Start DFS from the start node
    findExactHopsPathRecursive(start, exactHops, []);

    return foundPath;
  }

  // Find neighboring nodes connected to a given node
  getNeighbors(nodeId) {
    const { G } = this.props;
    const neighbors = new Set();

    // Check if G and its properties are defined
    if (!G) return [];

    // Get direct connections from edges
    if (G.edges && Array.isArray(G.edges)) {
      G.edges.forEach(edge => {
        if (edge.from === nodeId) neighbors.add(edge.to);
        if (edge.to === nodeId) neighbors.add(edge.from);
      });
    }

    // If current location is an airport, add connections to all other airports
    if (G.locations && Array.isArray(G.locations)) {
      const location = G.locations.find(loc => loc.id === nodeId);
      if (location && location.journeyType === 'Airport') {
        G.locations.forEach(loc => {
          if (loc.journeyType === 'Airport' && loc.id !== nodeId) {
            neighbors.add(loc.id);
          }
        });
      }
    }

    return Array.from(neighbors);
  }

  // Determine energy cube color based on type
  getEnergyCubeColor(type) {
    return energyColors[type] || '#ccc';
  }

  // Get the CSS class for a node based on its region
  getRegionClass(nodeId) {
    // Define which nodes belong to which regions
    const regionMap = {
      north: [1, 2, 3, 4, 6, 7, 9],
      west: [10, 11, 12, 13, 14, 16, 19],
      south: [20, 21, 22, 23, 24, 25, 27],
      central: [5, 15, 18, 28, 29, 33, 34],
      east: [30, 31, 32, 36, 37, 38, 39],
      northeast: [42, 43, 44, 45, 46, 47, 48]
    };

    // Find which region this node belongs to
    for (const [region, nodes] of Object.entries(regionMap)) {
      if (nodes.includes(nodeId)) {
        return `node-region-${region}`;
      }
    }

    // Check if it's a jyotirlinga or airport
    if (jyotirlinga.includes(nodeId)) {
      return 'node-region-jyotirlinga';
    }
    if (airportNodes.includes(nodeId)) {
      return 'node-region-airport';
    }

    // Default if no region found
    return '';
  }

  // -----------------------------------------------------------------------
  // Render Methods
  // -----------------------------------------------------------------------

  render() {
    const { G, ctx } = this.props;

    // Ensure G is defined
    if (!G) {
      return <div>Loading game state...</div>;
    }

    // Safely extract properties from G
    let players = G.players || [];
    // Convert players to array if it's an object
    if (players && typeof players === 'object' && !Array.isArray(players)) {
      players = Object.values(players);
    }
    const locations = G.locations || [];
    const currentGlobalEvent = G.currentGlobalEvent;
    const { currentPlayer: currentPlayerID } = ctx || {};

    // Handle both array and object structures for players
    let currentPlayer;
    if (Array.isArray(players)) {
      currentPlayer = players[currentPlayerID];
    } else if (typeof players === 'object') {
      currentPlayer = players[currentPlayerID] || Object.values(players).find(p => p.id === parseInt(currentPlayerID));
    }

    // Fallback if player not found
    if (!currentPlayer) {
      currentPlayer = {
        name: `Player ${currentPlayerID}`,
        innerScore: 0,
        outerScore: 0,
        omSlotsInner: [],
        omSlotsOuter: [],
        journeyInner: [],
        journeyOuter: [],
        hand: [],
        energyCubes: [],
        character: null
      };
    }

    // If game is still in setup phase
    if (ctx.phase === 'setup') {
      return (
        <div className="setup-phase">
          <h2>Om Journey - Game Setup</h2>
          <p>Waiting for game to start...</p>
          <p>You are joined as: <strong>{this.props.playerName || this.props.credentials || `Player ${this.props.playerID}`}</strong></p>
          <button onClick={() => this.props.moves.readyToStart()}>
            I'm Ready
          </button>
        </div>
      );
    }

    // Game over screen
    if (ctx.gameover) {
      const winner = players[ctx.gameover.winner];
      return (
        <div className="game-over">
          <h2>Game Over!</h2>
          <p>{winner ? `${winner.name} has won!` : 'The game has ended.'}</p>
          <div className="final-scores">
            {players.map((player, index) => (
              <div key={index} className="player-score">
                <strong>{player.name}:</strong> {player.score} points
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Get the current player's location and allowed destinations
    const currentPlayerPosition = this.getCurrentPlayerPosition();
    const { allowedDestinations, animatingMovement, animationPath, animationStep, nameMode, zoom, pan, hideLabels } = this.state;

    // Create a viewBox that covers the entire board area
    const viewBox = "0 0 1800 1400";

    // Show full-screen Om Turn Track if toggled
    if (this.state.showOmTrack) {
      return (
        <div className="om-track-fullscreen">
          <div className="om-track-header">
            <h2>Om Turn Track</h2>
            <button onClick={() => this.setState({ showOmTrack: false })}>Back to Game</button>
          </div>
          <div className="om-track-content">
            {G.omTrack && G.omTrack.map((stack, pos) => (
              <div key={pos} className="om-track-space">
                <div className="om-track-space-label">{pos}</div>
                <div className="om-track-stack">
                  {stack.map((id, idx) => (
                    <div key={`${id}-${idx}`} className={`om-track-marker player-${id}`}>
                      {id}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    // Render the board with the map background
    return (
      <div className={`board-container ${this.state.fullscreen ? 'board-fullscreen' : ''}`}>
        {/* Toggle to show Om Turn Track */}
        <div className="board-actions">
          <button onClick={() => this.setState({ showOmTrack: true })}>Show Turn Track</button>
        </div>
        {this.state.fullscreen && (
          <div className="board-fullscreen-header">
            <h2>Game Board</h2>
            <button onClick={() => this.setState({ fullscreen: false })}>Exit Fullscreen</button>
          </div>
        )}
        <div className={`${this.state.fullscreen ? 'board-fullscreen-content' : ''}`}>
          <svg 
            className={`board-svg ${this.state.fullscreen ? 'board-fullscreen-svg' : ''}`}
            viewBox={viewBox}
            preserveAspectRatio="xMidYMid meet"
            style={{
              backgroundImage: `url('/assets/images/map_take_2.jpg')`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              transform: `scale(${zoom}) translate(${pan.x}px, ${pan.y}px)`,
              cursor: this.state.isDragging ? 'grabbing' : 'grab'
            }}
            ref={this.boardRef}
            onMouseDown={this.handleMouseDown}
            onMouseMove={this.handleMouseMove}
            onMouseUp={this.handleMouseUp}
            onMouseLeave={this.handleMouseLeave}
          >
            {/* Render edges first so they're behind the nodes */}
            {edges.map((edge, idx) => {
              const fromNode = nodes[edge.from];
              const toNode = nodes[edge.to];
              
              // Skip if node coordinates aren't defined
              if (!fromNode || !toNode) return null;
              
              // Determine region for edge color
              let strokeColor = "#ffffff"; // Default color
              
              // Draw the path between nodes
              return (
                <path
                  key={`edge-${idx}`}
                  d={`M ${fromNode.x} ${fromNode.y} L ${toNode.x} ${toNode.y}`}
                  stroke={strokeColor}
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  fill="none"
                />
              );
            })}
            
            {/* Render all nodes with their labels */}
            {Object.entries(nodes).map(([id, node]) => {
              const regionClass = this.getRegionClass(parseInt(id));
              const isHighlighted = allowedDestinations.includes(parseInt(id));
              const isCurrentPosition = parseInt(id) === currentPlayerPosition;
              const nodeLabel = this.getLocationName(id);
              
              // Determine if it's a special node (airport or jyotirlinga)
              const isAirport = airportNodes.includes(parseInt(id));
              const isJyotirlinga = jyotirlinga.includes(parseInt(id));
              
              // Determine node color based on type
              let fillColor = "#ffffff";
              let nodeRadius = 15;
              
              if (isAirport) fillColor = "var(--airport-color)";
              if (isJyotirlinga) fillColor = "var(--jyotirlinga-color)";
              
              return (
                <g 
                  key={`node-${id}`} 
                  className={`node-group ${isHighlighted ? 'node-highlighted' : ''}`}
                  onClick={() => this.handleDestinationClick(parseInt(id))}
                >
                  <circle
                    cx={node.x}
                    cy={node.y}
                    r={nodeRadius}
                    className={`node ${regionClass} ${isCurrentPosition ? 'current-position' : ''}`}
                    fill={fillColor}
                    stroke="#ffffff"
                    strokeWidth="2"
                  />
                  
                  {/* Current player indicator */}
                  {isCurrentPosition && (
                    <circle
                      cx={node.x}
                      cy={node.y}
                      r={nodeRadius + 4}
                      fill="none"
                      stroke="#ff5722"
                      strokeWidth="3"
                      strokeDasharray="5,3"
                    />
                  )}
                  
                  {/* Location label */}
                  {!hideLabels && (
                    <g className="node-label-container">
                      <rect
                        x={node.x - 60}
                        y={node.y + 18}
                        width={120}
                        height={20}
                        className="location-label-background"
                      />
                      <text
                        x={node.x}
                        y={node.y + 30}
                        className="location-label-text"
                      >
                        {nodeLabel}
                      </text>
                    </g>
                  )}
                </g>
              );
            })}
            
            {/* Players on the board */}
            {players.map((player, idx) => {
              // Skip if player has no position yet
              if (!player.position) return null;
              
              const node = nodes[player.position];
              if (!node) return null;
              
              // Offset each player slightly so multiple players at the same location are visible
              const offsetX = (idx % 2 === 0) ? -10 : 10;
              const offsetY = Math.floor(idx / 2) * 10;
              
              // Player color based on index
              const playerColors = ['#ff5722', '#2196f3', '#4caf50', '#9c27b0'];
              
              return (
                <g 
                  key={`player-${idx}`}
                  className="player-piece"
                  onClick={idx === parseInt(ctx.currentPlayer) ? this.handlePlayerClick : undefined}
                >
                  <circle
                    cx={node.x + offsetX}
                    cy={node.y - 5 - offsetY}
                    r={8}
                    fill={playerColors[idx]}
                    stroke="#fff"
                    strokeWidth="2"
                    className={idx === parseInt(ctx.currentPlayer) ? 'current-player' : ''}
                  />
                </g>
              );
            })}
          </svg>
          
          {/* Player information panel when in fullscreen */}
          {this.state.fullscreen && (
            <div className="player-info-panel">
              <div className="player-information">
                <h3>Player Information</h3>
                <div className="player-stats">
                  <p>Score: <strong>{currentPlayer ? (currentPlayer.score || 0) : 0}</strong></p>
                  <p>OM Tokens: <strong>{currentPlayer ? (currentPlayer.omTokens || 0) : 0}</strong></p>
                </div>
                
                <div className="resources-section">
                  <h4>Energy Cubes</h4>
                  <div className="energy-cubes-container">
                    {currentPlayer && currentPlayer.energyCubes && 
                      Object.entries(currentPlayer.energyCubes).map(([type, count]) => (
                        <div key={type} className="energy-cube-group">
                          <div className={`energy-cube ${type.toLowerCase()}`}>
                            {count}
                          </div>
                          <span>{type}</span>
                        </div>
                      ))
                    }
                  </div>
                </div>
                
                <div className="travel-cards-section">
                  <h4>Travel Cards</h4>
                  <div className="travel-cards">
                    {currentPlayer && currentPlayer.hand && 
                      currentPlayer.hand.filter(card => card.type === 'travel').map(card => (
                        <div key={card.id} className={`travel-card ${card.vehicle}`}>
                          <div className="travel-card-value">{card.value}</div>
                          <div className="travel-card-type">{card.vehicle}</div>
                        </div>
                      ))
                    }
                  </div>
                </div>
                
                <div className="journey-cards-section">
                  <h4>Completed Journeys</h4>
                  <div className="journey-cards">
                    {currentPlayer && currentPlayer.completedJourneys && 
                      currentPlayer.completedJourneys.map(journey => (
                        <div key={journey.id} className={`journey-card ${journey.type}`}>
                          <div className="journey-location">{this.getLocationName(journey.locationId)}</div>
                          <div className="journey-points">{journey.points} pts</div>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>
              
              <div className="actions-panel">
                <h3>Actions</h3>
                <div className="action-buttons">
                  <button onClick={this.handlePlayerClick} className="move-button">
                    Move with Travel Cards
                  </button>
                  <button onClick={() => this.handleTradeSelection('artha', 'karma')} className="trade-button">
                    Trade Energy Cubes
                  </button>
                  <button onClick={this.handleEndTurn} className="end-turn-button">
                    End Turn
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {/* Zoom and pan controls when fullscreen */}
          {this.state.fullscreen && (
            <div className="board-controls">
              <button onClick={() => this.setState({ zoom: Math.min(zoom + 0.2, 2) })}>Zoom In</button>
              <button onClick={() => this.setState({ zoom: Math.max(zoom - 0.2, 0.5) })}>Zoom Out</button>
              <button onClick={() => this.setState({ zoom: 1, pan: { x: 0, y: 0 } })}>Reset</button>
              <button onClick={() => this.setState({ hideLabels: !hideLabels })}>
                {hideLabels ? 'Show Labels' : 'Hide Labels'}
              </button>
            </div>
          )}
        </div>
        
        {!this.state.fullscreen && (
          <button 
            onClick={() => this.setState({ fullscreen: true })}
            className="fullscreen-button"
          >
            Fullscreen
          </button>
        )}
      </div>
    );
  }

  // Helper: Get location name by ID
  getLocationName(locationId) {
    const { G } = this.props;
    if (!G || !G.locations || !Array.isArray(G.locations)) {
      return `Location ${locationId}`;
    }
    const location = G.locations.find(loc => loc.id === locationId);
    return location ? location.name : `Location ${locationId}`;
  }

  // Helper: Get description for global events
  getGlobalEventDescription(event) {
    // Default description based on effect type
    if (!event) return '';

    switch (event.effect) {
      case 'no_inner_journey_cards':
        return 'Inner Journey cards cannot be collected this round.';
      case 'heavy_haul_reward':
        return 'When using a Truck travel card, discard 2 energy cubes for bonus points.';
      case 'biker_gang_reward':
        return 'Motorbike travel cards provide an extra hop.';
      case 'desert_caravan_reward':
        return 'Camel travel cards provide an extra hop.';
      case 'region_effect_inner':
        return `Inner Journey cards in the ${event.region} region require one extra energy cube.`;
      case 'region_effect_outer':
        return `Outer Journey cards in the ${event.region} region require one fewer energy cube.`;
      case 'energy_cube_loss':
        return `Players in the ${event.region} region must discard one energy cube at the end of their turn.`;
      case 'helicopter_bonus':
        return 'Helicopter travel cards provide an extra hop.';
      case 'train_reward':
        return 'Train travel cards provide an extra hop.';
      case 'triple_card_reward':
        return 'Draw three cards when choosing travel cards.';
      default:
        return 'This event affects gameplay.';
    }
  }

  // Helper method to handle travel card selection from cards display
  handleTravelCardSelect(card) {
    // If already selected, remove from selection
    if (this.state.selectedTravelCards.includes(card)) {
      this.setState({
        selectedTravelCards: this.state.selectedTravelCards.filter(c => c !== card)
      });
      return;
    }

    // Add to selection
    const newSelection = [...this.state.selectedTravelCards, card];
    this.setState({ selectedTravelCards: newSelection });

    // Calculate destinations with these cards
    const hopCount = newSelection.reduce((sum, c) => sum + c.value, 0);
    const destinations = this.getReachableLocationsExactHops(this.getCurrentPlayerPosition(), hopCount);
    this.setState({ allowedDestinations: destinations });
  }

  // Handle mouse down for drag-to-pan
  handleMouseDown(e) {
    if (!this.state.fullscreen) return;
    
    this.setState({
      isDragging: true,
      dragStart: { x: e.clientX, y: e.clientY }
    });
  }

  // Handle mouse move for drag-to-pan
  handleMouseMove(e) {
    if (!this.state.isDragging) return;
    
    const dx = e.clientX - this.state.dragStart.x;
    const dy = e.clientY - this.state.dragStart.y;
    
    this.setState({
      pan: { 
        x: this.state.pan.x + dx / this.state.zoom, 
        y: this.state.pan.y + dy / this.state.zoom 
      },
      dragStart: { x: e.clientX, y: e.clientY }
    });
  }

  // Handle mouse up to end dragging
  handleMouseUp() {
    this.setState({ isDragging: false });
  }

  // Handle mouse leave to end dragging
  handleMouseLeave() {
    this.setState({ isDragging: false });
  }
}

// Create the client
const OmJourneyClient = Client({
  game: OmJourney,
  board: OmJourneyBoard,
  numPlayers: 4,
  multiplayer: SocketIO({ server: 'http://localhost:8000' }),
  debug: false
});

module.exports = { OmJourneyBoard, OmJourneyClient };