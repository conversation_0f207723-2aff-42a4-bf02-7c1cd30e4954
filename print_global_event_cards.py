#!/usr/bin/env python3
"""
Assemble global event card images into pages of four cards per sheet.
Each card is printed at 7.44 × 12.4 cm on a page sized 19.05 × 25.4 cm at 300 DPI.
Requires Pillow: pip install pillow
Run this script from the root of the project.
"""

from pathlib import Path
import re
from PIL import Image

# Configuration
DPI = 300
PAGE_W_PX = int(19.05 / 2.54 * DPI)
PAGE_H_PX = int(25.4 / 2.54 * DPI)
CARD_W_PX = int(7.44 / 2.54 * DPI)
CARD_H_PX = int(12.4 / 2.54 * DPI)

# Center the 2×2 grid on the page
MARGIN_X = (PAGE_W_PX - 2 * CARD_W_PX) // 2
MARGIN_Y = (PAGE_H_PX - 2 * CARD_H_PX) // 2

# Paste coordinates for the four slots
SLOTS = [
    (MARGIN_X,                 MARGIN_Y),               # top-left
    (MARGIN_X + CARD_W_PX,     MARGIN_Y),               # top-right
    (MARGIN_X,                 MARGIN_Y + CARD_H_PX),   # bottom-left
    (MARGIN_X + CARD_W_PX,     MARGIN_Y + CARD_H_PX),   # bottom-right
]

# Natural sort helper (e.g., card2 before card10)
def sort_key(path: Path):
    return [
        int(text) if text.isdigit() else text.lower()
        for text in re.split(r"(\d+)", path.stem)
    ]


def main():
    images_dir = Path("client/build/assets/images/global_event_cards")
    images = sorted(images_dir.glob("*.png"))
    images.sort(key=sort_key)

    if not images:
        print(f"No images found in {images_dir}")
        return

    # Process 4 at a time
    for page_idx in range(0, len(images), 4):
        sheet = Image.new("RGB", (PAGE_W_PX, PAGE_H_PX), "white")
        for slot_idx, img_path in enumerate(images[page_idx:page_idx + 4]):
            card = Image.open(img_path).convert("RGB")
            # Resize to exact card dimensions
            card = card.resize((CARD_W_PX, CARD_H_PX), Image.LANCZOS)
            sheet.paste(card, SLOTS[slot_idx])

        out_name = f"global_event_page_{page_idx//4 + 1:02}.png"
        sheet.save(out_name, dpi=(DPI, DPI))
        print(f"Wrote {out_name}")

    print("✅ All pages created. Ready to print!")


if __name__ == "__main__":
    main() 