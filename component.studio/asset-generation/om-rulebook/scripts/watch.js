const chokidar = require('chokidar');
const express = require('express');
const path = require('path');
const open = require('open');
const { main: buildMain } = require('./build.js');

const app = express();
const PORT = 3000;

// Serve static files from output directory
const outputDir = path.join(__dirname, '../output');
app.use(express.static(outputDir));

// Serve the main rulebook
app.get('/', (req, res) => {
    res.sendFile(path.join(outputDir, 'rulebook.html'));
});

let isBuilding = false;

async function rebuild() {
    if (isBuilding) return;
    
    isBuilding = true;
    console.log('\n🔄 Changes detected, rebuilding...');
    
    try {
        await buildMain();
        console.log('✅ Rebuild complete! Refresh your browser to see changes.\n');
    } catch (error) {
        console.error('❌ Rebuild failed:', error.message);
    } finally {
        isBuilding = false;
    }
}

async function startWatching() {
    // Initial build
    console.log('🚀 Starting Om: The Journey Rulebook development server...\n');
    await buildMain();
    
    // Start server
    app.listen(PORT, () => {
        console.log(`\n📖 Rulebook server running at http://localhost:${PORT}`);
        console.log('👀 Watching for changes...\n');
        
        // Open browser
        open(`http://localhost:${PORT}`);
    });
    
    // Watch for changes
    const srcDir = path.join(__dirname, '../src');
    const watcher = chokidar.watch(srcDir, {
        ignored: /(^|[\/\\])\../, // ignore dotfiles
        persistent: true,
        ignoreInitial: true
    });
    
    watcher
        .on('change', path => {
            console.log(`📝 Changed: ${path}`);
            rebuild();
        })
        .on('add', path => {
            console.log(`➕ Added: ${path}`);
            rebuild();
        })
        .on('unlink', path => {
            console.log(`🗑️  Removed: ${path}`);
            rebuild();
        });
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n👋 Shutting down development server...');
        watcher.close();
        process.exit(0);
    });
}

if (require.main === module) {
    startWatching().catch(error => {
        console.error('Failed to start development server:', error);
        process.exit(1);
    });
} 