const chokidar = require('chokidar');
const express = require('express');
const path = require('path');
const { main: buildMain } = require('./build-html.js');

const app = express();
const PORT = 3000;

// Serve static files from output directory
const outputDir = path.join(__dirname, '../output');
app.use(express.static(outputDir));

// Serve the main rulebook
app.get('/', (req, res) => {
    res.sendFile(path.join(outputDir, 'rulebook.html'));
});

let isBuilding = false;

async function rebuild() {
    if (isBuilding) return;
    
    isBuilding = true;
    console.log('\n🔄 Changes detected, rebuilding HTML rulebook...');
    
    try {
        await buildMain();
        console.log('✅ Rebuild complete! Refresh your browser to see changes.\n');
    } catch (error) {
        console.error('❌ Rebuild failed:', error.message);
    } finally {
        isBuilding = false;
    }
}

async function startWatching() {
    // Initial build
    console.log('🚀 Starting Om: The Journey HTML Rulebook development server...\n');
    await buildMain();
    
    // Start server
    const server = app.listen(PORT, async () => {
        console.log(`\n📖 Rulebook server running at http://localhost:${PORT}`);
        console.log('👀 Watching for changes...\n');
        
        // Open browser (using dynamic import for ESM module)
        try {
            const openModule = await import('open');
            openModule.default(`http://localhost:${PORT}`);
        } catch (error) {
            console.log(`Browser auto-open failed. Please manually open http://localhost:${PORT}`);
        }
    });
    
    // Watch for changes
    const srcDir = path.join(__dirname, '../src');
    const assetsDir = path.join(__dirname, '../assets');
    
    const watcher = chokidar.watch([srcDir, assetsDir], {
        ignored: /(^|[\/\\])\../, // ignore dotfiles
        persistent: true,
        ignoreInitial: true
    });
    
    watcher
        .on('change', filePath => {
            console.log(`📝 Changed: ${filePath}`);
            rebuild();
        })
        .on('add', filePath => {
            console.log(`➕ Added: ${filePath}`);
            rebuild();
        })
        .on('unlink', filePath => {
            console.log(`🗑️  Removed: ${filePath}`);
            rebuild();
        });
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('\n👋 Shutting down development server...');
        watcher.close();
        server.close();
        process.exit(0);
    });
}

if (require.main === module) {
    startWatching().catch(error => {
        console.error('Failed to start development server:', error);
        process.exit(1);
    });
} 