# Reference Material

This section provides quick reference information for all game components.

## Character Abilities

<div class="reference-card">
    <div class="card-title">Engineer</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube karma"></span> Karma</div>
    <p>Specializes in action and construction projects. Cannot trade Karma for Karma.</p>
</div>

<div class="reference-card">
    <div class="card-title">Professor</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube gnana"></span> Gnana</div>
    <p>Master of knowledge and wisdom. Cannot trade Gnana for Gnana.</p>
</div>

<div class="reference-card">
    <div class="card-title">Merchant</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube artha"></span> Artha</div>
    <p>Expert in commerce and material wealth. Cannot trade Artha for Artha.</p>
</div>

<div class="reference-card">
    <div class="card-title">Pilgrim</div>
    <div class="card-cost">Trade any cube → <span class="icon energy-cube bhakti"></span> Bhakti</div>
    <p>Devoted spiritual seeker. Cannot trade Bhakti for Bhakti.</p>
</div>

<div class="page-break"></div>

## Global Event Cards

### Movement & Travel Events

<div class="component-table">

| Event | Effect |
|-------|--------|
| **Drizzle of Delay** | Max 2 moves; ending in North or East costs 1 <span class="icon energy-cube artha"></span> |
| **Turbulent Skies** | No airport travel allowed this round |
| **Frozen North** | Starting in North: moves cost ×2; if moved, +7 Inner points |
| **Excess Baggage** | Hand limit 2; discard down immediately |

</div>

### Scoring & Bonus Events

<div class="component-table">

| Event | Effect |
|-------|--------|
| **Diwali Distraction** | All gain +5 Inner points but no cube pickup |
| **Maha Kumbh** | Visit any Jyotirlinga for 7 Inner points; skip for 1 bonus cube |
| **Bountiful Bhandara** | Draw 2 random cubes; +5 Outer points if any Journey Card collected |
| **Triathlon** | +7 Outer points if three different Travel card values used |

</div>

### Character-Specific Events

<div class="component-table">

| Event | Effect |
|-------|--------|
| **Merchant's Midas** | Merchant trade for Artha: +7 Outer points |
| **Professor's Insight** | Professor trade for Gnana: +7 Inner points |
| **Pilgrim's Grace** | Pilgrim trade for Bhakti: +7 Inner points |
| **Engineer's Precision** | Engineer trade for Karma: +7 Outer points |

</div>

### Vehicle-Specific Events

<div class="component-table">

| Event | Effect |
|-------|--------|
| **Eco Trail** | Cycle or Trek: +5 Inner points |
| **Rajput Caravans** | Horse or Camel: +5 Outer points |
| **Urban Ride** | Motorbike or Rickshaw: +5 Outer points |
| **Road Warriors** | Car or Bus: +5 Outer points |
| **Rails and Sails** | Train or Boat: +5 Outer points |
| **Heavy Haul** | Use Truck for 10 Outer points but lose 2 cubes |

</div>

<div class="page-break"></div>

## Journey Cards Reference

### Outer Journey Cards (Selection)

<div class="reference-card">
    <div class="card-title">Taj Mahal (North)</div>
    <div class="card-cost">2 <span class="icon energy-cube karma"></span> + 1 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">27 pts</div>
</div>

<div class="reference-card">
    <div class="card-title">Jaisalmer Fort (West)</div>
    <div class="card-cost">2 <span class="icon energy-cube karma"></span> + 2 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">30 pts</div>
</div>

<div class="reference-card">
    <div class="card-title">Hampi (South)</div>
    <div class="card-cost">2 <span class="icon energy-cube karma"></span> + 1 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">27 pts</div>
</div>

<div class="reference-card">
    <div class="card-title">Living Root Bridge (NE)</div>
    <div class="card-cost">1 <span class="icon energy-cube karma"></span> + 2 <span class="icon energy-cube artha"></span></div>
    <div class="card-points">27 pts</div>
</div>

### Inner Journey Cards (Selection)

<div class="reference-card">
    <div class="card-title">Golden Temple (North)</div>
    <div class="card-cost">2 <span class="icon energy-cube bhakti"></span> + 2 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">30 pts</div>
</div>

<div class="reference-card">
    <div class="card-title">Meenakshi Temple (South)</div>
    <div class="card-cost">2 <span class="icon energy-cube bhakti"></span> + 2 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">30 pts</div>
</div>

<div class="reference-card">
    <div class="card-title">Jagannath Temple (East)</div>
    <div class="card-cost">2 <span class="icon energy-cube bhakti"></span> + 2 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">30 pts</div>
</div>

<div class="reference-card">
    <div class="card-title">Kamakhya Temple (NE)</div>
    <div class="card-cost">1 <span class="icon energy-cube bhakti"></span> + 1 <span class="icon energy-cube gnana"></span></div>
    <div class="card-points">24 pts</div>
</div>

## Quick Reference Summary

<div class="callout-box important">

### Turn Sequence
1. Character Trading (optional)
2. Main Action (A: Travel Cards OR B: Journey Card)
3. Travelling (optional)
4. Collect Om Tokens (if applicable)
5. Acquire Journey Card (if Action B)
6. End-of-Turn cleanup

### Victory Condition
First player to exceed **100 combined points** (Outer + Inner) triggers final round.

### Om Token Collection
- Max 3 tokens per player
- Advance 1 space per token on Om Turn Track
- Collected from Jyotirlingas and some Global Events

</div> 