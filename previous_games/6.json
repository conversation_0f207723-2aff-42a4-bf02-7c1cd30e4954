{"description": "<PERSON><PERSON><PERSON> won by a landslide, picked cheapest cards, got some event benefits", "players": [{"id": "VZ7dynVFW3gWr8OfAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 11, "hand": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "energyCubes": ["gnana", "artha"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "outerScore": 52, "innerScore": 57, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "lRphZutjba6JzJCvAAAB", "name": "Nonkesh", "position": 33, "hand": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "energyCubes": ["artha", "bhakti", "bhakti"], "omTemp": [], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}], "outerScore": 20, "innerScore": 57, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}}], "started": true, "turnIndex": 0, "roundCount": 14, "travelDeck": [], "eventDeck": [{"id": "E1", "type": "extraHop"}, {"id": "E8", "type": "wildCube"}, {"id": "E2", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "travelDiscard": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "faceUpEvent": [{"id": "E7", "type": "wildCube"}, {"id": "E6", "type": "wildCube"}, {"id": "E4", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "faceUpJourneyOuter": [{"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "locationCubes": {"1": "bhakti", "2": "karma", "3": "artha", "5": "karma", "7": "gnana", "8": "karma", "9": "artha", "10": "artha", "12": "bhakti", "13": "artha", "14": "bhakti", "15": "karma", "16": "gnana", "17": "gnana", "18": "karma", "19": "artha", "22": "artha", "24": "karma", "25": "gnana", "27": "karma", "28": "artha", "32": "gnana", "34": "gnana", "35": "karma", "36": "gnana", "38": "gnana", "40": "bhakti", "44": "artha", "45": "gnana", "46": "bhakti", "47": "artha", "48": "bhakti", "undefined": "karma"}, "locationOm": {"49": true, "50": true, "52": true, "53": true, "54": true}, "finalRound": true, "finalRoundStarter": 0, "finalRoundEnd": 1, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1743639900119, "data": {"eventId": "global-event-9", "eventName": "Riots", "eventEffect": "riots_discard"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743639930565, "data": {"cardType": "travel", "card": {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743639930565, "data": {"cardType": "travel", "card": {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 0, "turnIndex": 0, "timestamp": 1743639930565, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 0, "turnIndex": 0, "timestamp": 1743639930565, "data": {"cardType": "travel", "card": {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}}}, {"type": "move", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743640046463, "data": {"path": [63, 61, 4], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743640123064, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743640141898, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743640144254}, {"type": "move", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 0, "turnIndex": 1, "timestamp": 1743640226506, "data": {"path": [65, 31, 30, 55], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 0, "turnIndex": 1, "timestamp": 1743640232535, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 0, "turnIndex": 1, "timestamp": 1743640249076, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 0, "turnIndex": 1, "timestamp": 1743640251278}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743640251278, "data": {"eventId": "global-event-17", "eventName": "Top Gear", "eventEffect": "top_gear_reward"}}, {"type": "travel_card_reward", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743640350876, "data": {"vehicle": "car", "effect": "top_gear_reward", "outerPoints": 5}}, {"type": "move", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743640350876, "data": {"path": [4, 3, 56], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743640378000, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743640419203, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743640421299}, {"type": "move", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 1, "turnIndex": 1, "timestamp": 1743640517377, "data": {"path": [55, 30], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 1, "turnIndex": 1, "timestamp": 1743640532694, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 1, "turnIndex": 1, "timestamp": 1743640542981}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743640542981, "data": {"eventId": "global-event-13", "eventName": "<PERSON><PERSON> of Valor", "eventEffect": "steed_of_valor_reward"}}, {"type": "move", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743640588677, "data": {"path": [56, 3, 4], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743640593277, "data": {"journeyType": "inner", "journeyCardId": "JI3", "omRequirement": 1}}, {"type": "endTurn", "playerId": "1nhl4g4wVG4tlos9AAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743640677151}, {"type": "move", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 2, "turnIndex": 1, "timestamp": 1743640894379, "data": {"path": [30, 31, 65, 63, 20, 21], "travelCards": ["T9", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 2, "turnIndex": 1, "timestamp": 1743640897600, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 1}}, {"type": "endTurn", "playerId": "LculePQcyibyObcTAAAD", "playerName": "Nonkesh", "roundCount": 2, "turnIndex": 1, "timestamp": 1743640905308}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743640905308, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743641252775, "data": {"timestamp": 1743641252775, "loadedStateRoundCount": 3, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743641276091, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743641285625, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743641290976}, {"type": "move", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 3, "turnIndex": 1, "timestamp": 1743641350262, "data": {"path": [21, 20, 63, 25, 26], "travelCards": ["T12", "T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 3, "turnIndex": 1, "timestamp": 1743641368042, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 3, "turnIndex": 1, "timestamp": 1743641369965}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743641369965, "data": {"eventId": "global-event-7", "eventName": "Election Campaigns", "eventEffect": "double_trade_no_travel"}}, {"type": "endTurn", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743641441318}, {"type": "endTurn", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 4, "turnIndex": 1, "timestamp": 1743641473924}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743641473924, "data": {"eventId": "global-event-23", "eventName": "Merchant's Midas", "eventEffect": "merchants_midas_reward"}}, {"type": "move", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743641544042, "data": {"path": [6, 4, 3, 47, 57], "travelCards": ["T7", "T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743641571339, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743641584019, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743641587140}, {"type": "move", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 5, "turnIndex": 1, "timestamp": 1743641725131, "data": {"path": [26, 25, 24, 23, 59], "travelCards": ["T11", "T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 5, "turnIndex": 1, "timestamp": 1743641738070, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 5, "turnIndex": 1, "timestamp": 1743641742433}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743641742433, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "move", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743641764759, "data": {"path": [57, 41], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743641768264, "data": {"journeyType": "inner", "journeyCardId": "JI20", "omRequirement": 1}}, {"type": "endTurn", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743641792249}, {"type": "move", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 6, "turnIndex": 1, "timestamp": 1743641805273, "data": {"path": [59, 23, 38, 39], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 6, "turnIndex": 1, "timestamp": 1743641823878, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 6, "turnIndex": 1, "timestamp": 1743641826233}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743641826233, "data": {"eventId": "global-event-22", "eventName": "Up and Over", "eventEffect": "up_and_over_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743641895816, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743642059412, "data": {"path": [41, 48, 42, 43], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743642077079, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743642080174}, {"type": "move", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 7, "turnIndex": 1, "timestamp": 1743642132770, "data": {"path": [39, 38, 23], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 7, "turnIndex": 1, "timestamp": 1743642135171, "data": {"journeyType": "inner", "journeyCardId": "JI13", "omRequirement": 1}}, {"type": "endTurn", "playerId": "Ofd-JzyUynKYA42OAAAB", "playerName": "Nonkesh", "roundCount": 7, "turnIndex": 1, "timestamp": 1743642152251}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743642152251, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "move", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743642206077, "data": {"path": [43, 60], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743642245626, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "t7ayjZTgdxb9vU_cAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743642255850}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 1, "timestamp": 1743642350403, "data": {"timestamp": 1743642350403, "loadedStateRoundCount": 8, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 8, "turnIndex": 1, "timestamp": 1743642436363, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 8, "turnIndex": 1, "timestamp": 1743642444749, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 8, "turnIndex": 1, "timestamp": 1743642456314, "data": {"path": [23, 22, 21, 20, 51], "travelCards": ["T7", "T8"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 8, "turnIndex": 1, "timestamp": 1743642461826}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743642461826, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "move", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743642610344, "data": {"path": [60, 43, 42, 66, 65, 37], "travelCards": ["T10", "T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743642616942, "data": {"journeyType": "outer", "journeyCardId": "JO20", "omRequirement": 1}}, {"type": "endTurn", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743642623596}, {"type": "move", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 9, "turnIndex": 1, "timestamp": 1743642690549, "data": {"path": [51, 20], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 9, "turnIndex": 1, "timestamp": 1743642701750, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 9, "turnIndex": 1, "timestamp": 1743642704176, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 9, "turnIndex": 1, "timestamp": 1743642707945}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743642707945, "data": {"eventId": "global-event-29", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "move", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743642772694, "data": {"path": [37, 65, 31, 32, 58], "travelCards": ["T1", "T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743642793788, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743642798909, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743642803167}, {"type": "move", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 10, "turnIndex": 1, "timestamp": 1743642865143, "data": {"path": [20, 63, 66, 42], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 10, "turnIndex": 1, "timestamp": 1743642874669, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 10, "turnIndex": 1, "timestamp": 1743642885596, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 10, "turnIndex": 1, "timestamp": 1743642889891}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743642889891, "data": {"eventId": "global-event-1", "eventName": "Drizzle of Delay", "eventEffect": "max_moves_2_and_cost_artha_north_east"}}, {"type": "move", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743643066279, "data": {"path": [58, 32, 31], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743643115521, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743643169400, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743643217117}, {"type": "move", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 11, "turnIndex": 1, "timestamp": 1743643286639, "data": {"path": [42, 66, 64], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 11, "turnIndex": 1, "timestamp": 1743643303646, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 11, "turnIndex": 1, "timestamp": 1743643309560}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743643309560, "data": {"eventId": "global-event-31", "eventName": "Himalayan NE", "eventEffect": "himalayan_ne"}}, {"type": "move", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743643453790, "data": {"path": [31, 65, 37, 40, 18, 28, 29], "travelCards": ["T9", "T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743643549018, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743643560468, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743643562927}, {"type": "move", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 12, "turnIndex": 1, "timestamp": 1743643578924, "data": {"path": [64, 29, 33], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 12, "turnIndex": 1, "timestamp": 1743643581362, "data": {"journeyType": "outer", "journeyCardId": "JO17", "omRequirement": 1}}, {"type": "endTurn", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 12, "turnIndex": 1, "timestamp": 1743643587424}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1743643587425, "data": {"eventId": "global-event-26", "eventName": "Engineer's Precision", "eventEffect": "engineers_precision_reward"}}, {"type": "move", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": *********3958, "data": {"path": [29, 64, 62, 11], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": *********8771, "data": {"journeyType": "outer", "journeyCardId": "JO5", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": *********8771, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 109, "playerOmTotal": 4, "finalRoundStarter": 0, "finalRoundEnd": 1}}, {"type": "endTurn", "playerId": "VZ7dynVFW3gWr8OfAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743643640956}, {"type": "endTurn", "playerId": "lRphZutjba6JzJCvAAAB", "playerName": "Nonkesh", "roundCount": 13, "turnIndex": 1, "timestamp": 1743643650301}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1743643650301, "data": {"eventId": "global-event-12", "eventName": "Footpath Reverie", "eventEffect": "footpath_reverie_reward"}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1743643650302, "data": {"winner": {"id": "VZ7dynVFW3gWr8OfAAAD", "name": "<PERSON><PERSON><PERSON>", "outerScore": 52, "innerScore": 57, "totalScore": 109, "omTotal": 4}, "totalRounds": 14, "players": [{"id": "VZ7dynVFW3gWr8OfAAAD", "name": "<PERSON><PERSON><PERSON>", "outerScore": 52, "innerScore": 57, "totalScore": 109, "omTotal": 4}, {"id": "lRphZutjba6JzJCvAAAB", "name": "Nonkesh", "outerScore": 20, "innerScore": 57, "totalScore": 77, "omTotal": 3}]}}], "characterDeck": [{"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}], "energyCubePile": {"artha": 3, "karma": 6, "gnana": 3, "bhakti": 7}, "currentGlobalEvent": {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, "globalEventDeck": [{"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}], "globalEventDiscard": [{"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}]}