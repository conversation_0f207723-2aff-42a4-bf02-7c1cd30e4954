/**
 * BaseStrategy.js
 * Abstract base class that defines the interface for all bot strategies.
 * Each strategy implementation should extend this class and implement its methods.
 */

class BaseStrategy {
  /**
   * Initialize a new strategy
   * @param {object} params - Configuration parameters
   * @param {string} params.name - Name of the strategy
   * @param {object} params.config - Strategy-specific configuration
   */
  constructor({ name, config = {} }) {
    this.name = name;
    this.config = config;
    this.moveHistory = [];
  }

  /**
   * Plan the next turn for a bot
   * @param {Bot} bot - The bot this strategy is planning for
   * @returns {Promise<object>} Action plan with 'action' and 'params'
   */
  async planTurn(bot) {
    throw new Error('planTurn must be implemented by strategy subclasses');
  }

  /**
   * Analyze possible moves
   * @param {Bot} bot - The bot to analyze moves for
   * @returns {Promise<Array>} List of possible moves with scores
   */
  async analyzeMoves(bot) {
    throw new Error('analyzeMoves must be implemented by strategy subclasses');
  }

  /**
   * Evaluate possible card picks
   * @param {Bot} bot - The bot to evaluate card picks for
   * @returns {Promise<object>} Best card pick action
   */
  async evaluateCardPicks(bot) {
    throw new Error('evaluateCardPicks must be implemented by strategy subclasses');
  }

  /**
   * Evaluate possible journey card collections
   * @param {Bot} bot - The bot to evaluate journey collections for
   * @returns {Promise<object>} Best journey collection action
   */
  async evaluateJourneyCollection(bot) {
    throw new Error('evaluateJourneyCollection must be implemented by strategy subclasses');
  }

  /**
   * Calculate possible paths from current position
   * @param {Bot} bot - The bot to calculate paths for
   * @returns {Array} Possible paths based on travel cards in hand
   */
  calculatePossiblePaths(bot) {
    const state = bot.getPlayerState();
    const player = state.player;
    const travelCards = player.hand.filter(card => card.type === 'travel' && card.value);
    
    // This is a placeholder - actual implementation would calculate valid paths
    // based on the board layout and available travel cards
    return [];
  }

  /**
   * Record a move in the strategy's history
   * @param {object} move - Details of the move
   */
  recordMove(move) {
    this.moveHistory.push({
      ...move,
      timestamp: Date.now()
    });
  }

  /**
   * Get strategy statistics
   * @returns {object} Strategy performance statistics
   */
  getStats() {
    return {
      name: this.name,
      config: this.config,
      moveCount: this.moveHistory.length
    };
  }
}

module.exports = BaseStrategy; 