const fs = require('fs');
const path = require('path');
const puppeteer = require('puppeteer');
const { chromium } = require('playwright');
const handlebars = require('handlebars');
const MarkdownIt = require('markdown-it');
const sass = require('sass');
const express = require('express');

const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true
});

// Paths
const srcDir = path.join(__dirname, '../src');
const outputDir = path.join(__dirname, '../output');
const pagesDir = path.join(srcDir, 'pages');
const templatesDir = path.join(srcDir, 'templates');
const stylesDir = path.join(srcDir, 'styles');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

async function compileStyles() {
    console.log('Compiling SCSS...');
    try {
        const result = sass.compile(path.join(stylesDir, 'om.scss'));
        fs.writeFileSync(path.join(outputDir, 'om.css'), result.css);
        console.log('✓ Styles compiled successfully');
    } catch (error) {
        console.error('Error compiling SCSS:', error);
        throw error;
    }
}

function loadTemplate(templateName) {
    const templatePath = path.join(templatesDir, `${templateName}.hbs`);
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    return handlebars.compile(templateContent);
}

function loadMarkdownPages() {
    const pages = [];
    const pageFiles = fs.readdirSync(pagesDir)
        .filter(file => file.endsWith('.md'))
        .sort();

    for (const file of pageFiles) {
        const filePath = path.join(pagesDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        const html = md.render(content);
        
        pages.push({
            filename: file,
            title: extractTitle(content),
            content: html
        });
    }

    return pages;
}

function extractTitle(markdown) {
    const match = markdown.match(/^#\s+(.+)$/m);
    return match ? match[1] : 'Om: The Journey';
}

async function generateHTML() {
    console.log('Generating HTML...');
    
    // Load templates
    const baseTemplate = loadTemplate('base');
    const coverTemplate = loadTemplate('cover');
    const componentSpreadTemplate = loadTemplate('component-spread');
    
    // Load pages
    const pages = loadMarkdownPages();
    
    let fullContent = '';
    
    // Add cover page
    fullContent += coverTemplate({});
    fullContent += '<div class="page-break"></div>';
    
    // Add all content pages
    for (const page of pages) {
        fullContent += page.content;
        
        // Add component spread after components page
        if (page.filename.includes('02-components')) {
            fullContent += componentSpreadTemplate({});
        }
        
        if (page !== pages[pages.length - 1]) {
            fullContent += '<div class="page-break"></div>';
        }
    }
    
    // Generate final HTML
    const finalHTML = baseTemplate({
        title: 'Om: The Journey Rulebook',
        content: fullContent
    });
    
    const htmlPath = path.join(outputDir, 'rulebook.html');
    fs.writeFileSync(htmlPath, finalHTML);
    console.log('✓ HTML generated successfully');
    
    return htmlPath;
}

async function generatePDF(htmlPath) {
    console.log('Generating PDF...');
    
    let browser;
    try {
        // Try different browser launch configurations
        const browserConfig = {
            headless: "new",
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-web-security',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection'
            ],
            timeout: 0 // Disable launch timeout
        };
        
        // Try launching browser with retries
        let retries = 3;
        while (retries > 0) {
            try {
                browser = await puppeteer.launch(browserConfig);
                break;
            } catch (launchError) {
                retries--;
                if (retries === 0) throw launchError;
                console.log(`Browser launch failed, retrying... (${retries} attempts left)`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        const page = await browser.newPage();
        
        // Increase various timeouts
        await page.setDefaultTimeout(0); // Disable timeouts
        await page.setDefaultNavigationTimeout(0);
        
        // Set viewport for consistent rendering
        await page.setViewport({ width: 1200, height: 1600 });
        
        // Enable console logging from the page for debugging
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('Browser console error:', msg.text());
            }
        });
        
        page.on('pageerror', error => {
            console.log('Page error:', error.message);
        });
        
        console.log('Loading HTML file...');
        
        // Use a data URL instead of file URL to avoid file system issues
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');
        const cssContent = fs.readFileSync(path.join(outputDir, 'om.css'), 'utf8');
        const pagedJsContent = fs.readFileSync(path.join(outputDir, 'paged.polyfill.js'), 'utf8');
        
        // Embed everything inline to avoid loading issues
        const inlineHtml = htmlContent
            .replace('<link rel="stylesheet" href="om.css">', `<style>${cssContent}</style>`)
            .replace('<script src="paged.polyfill.js"></script>', `<script>${pagedJsContent}</script>`);
        
        await page.setContent(inlineHtml, { 
            waitUntil: ['load', 'domcontentloaded'],
            timeout: 0
        });
        
        console.log('Page loaded, waiting for layout...');
        
        // Wait for fonts and resources
        await page.waitForTimeout(5000);
        
        // Try to wait for Paged.js
        try {
            await page.waitForFunction(() => {
                if (typeof window.PagedPolyfill !== 'undefined') {
                    return window.PagedPolyfill.isReady === true;
                }
                return document.readyState === 'complete';
            }, {
                timeout: 60000
            });
            console.log('✓ Layout complete');
        } catch (error) {
            console.log('⚠️  Layout timeout, proceeding with current state');
        }
        
        // Final wait for stability
        await page.waitForTimeout(3000);
        
        // Generate PDF
        const pdfPath = path.join(outputDir, 'om-journey-rulebook.pdf');
        console.log('Generating PDF file...');
        
        await page.pdf({
            path: pdfPath,
            format: 'A4',
            printBackground: true,
            margin: {
                top: '0.5in',
                bottom: '0.5in',
                left: '0.5in',
                right: '0.5in'
            }
        });
        
        console.log(`✓ PDF generated successfully: ${pdfPath}`);
        
    } catch (error) {
        console.error('PDF generation error:', error.message);
        throw error;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

async function generatePDFFallback(htmlPath) {
    console.log('Attempting fallback PDF generation (without Paged.js)...');
    
    const browser = await puppeteer.launch({ 
        headless: "new",
        args: [
            '--no-sandbox', 
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas'
        ]
    });
    
    try {
        const page = await browser.newPage();
        page.setDefaultTimeout(30000);
        
        // Read and modify HTML to remove Paged.js dependency
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');
        const simplifiedHtml = htmlContent.replace(
            '<script src="paged.polyfill.js"></script>',
            '<script>window.PagedPolyfill = { isReady: true };</script>'
        );
        
        // Create a temporary simplified HTML file
        const tempHtmlPath = path.join(outputDir, 'temp-simple-rulebook.html');
        fs.writeFileSync(tempHtmlPath, simplifiedHtml);
        
        await page.goto(`file://${tempHtmlPath}`, { 
            waitUntil: ['load', 'domcontentloaded'],
            timeout: 30000 
        });
        
        // Wait for basic loading
        await page.waitForTimeout(2000);
        
        // Generate PDF with simpler settings
        const pdfPath = path.join(outputDir, 'om-journey-rulebook-simple.pdf');
        await page.pdf({
            path: pdfPath,
            format: 'A4',
            printBackground: true,
            margin: {
                top: '0.75in',
                bottom: '0.75in',
                left: '0.75in',
                right: '0.75in'
            }
        });
        
        // Clean up temp file
        fs.unlinkSync(tempHtmlPath);
        
        console.log(`✓ Fallback PDF generated: ${pdfPath}`);
        
    } finally {
        await browser.close();
    }
}

async function copyAssets() {
    console.log('Copying assets...');
    
    // Create assets directory in output
    const assetsOutputDir = path.join(outputDir, 'assets');
    if (!fs.existsSync(assetsOutputDir)) {
        fs.mkdirSync(assetsOutputDir, { recursive: true });
    }
    
    // Copy Paged.js polyfill to output directory
    const pagedJsSource = path.join(__dirname, '../node_modules/pagedjs/dist/paged.polyfill.js');
    const pagedJsOutput = path.join(outputDir, 'paged.polyfill.js');
    
    try {
        if (fs.existsSync(pagedJsSource)) {
            fs.copyFileSync(pagedJsSource, pagedJsOutput);
            console.log('✓ Paged.js polyfill copied');
        } else {
            // Fallback: download if not in node_modules
            console.log('⚠️  Paged.js not found in node_modules, using existing copy or downloading...');
            if (!fs.existsSync(pagedJsOutput)) {
                console.log('Creating minimal Paged.js fallback...');
                const fallbackPagedJs = `
                    // Minimal Paged.js fallback
                    window.PagedPolyfill = {
                        isReady: true
                    };
                    console.log('Using Paged.js fallback');
                `;
                fs.writeFileSync(pagedJsOutput, fallbackPagedJs);
            }
        }
    } catch (error) {
        console.log('⚠️  Could not copy Paged.js, creating fallback:', error.message);
        const fallbackPagedJs = `
            // Minimal Paged.js fallback
            window.PagedPolyfill = {
                isReady: true
            };
            console.log('Using Paged.js fallback');
        `;
        fs.writeFileSync(pagedJsOutput, fallbackPagedJs);
    }
    
    // Create placeholder directories
    const assetDirs = ['images', 'icons'];
    for (const dir of assetDirs) {
        const dirPath = path.join(assetsOutputDir, dir);
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    }
    
    // Create placeholder files
    const placeholders = [
        'images/cover-art.jpg',
        'images/board-overview.jpg',
        'images/setup-board.jpg',
        'images/setup-complete.jpg',
        'images/journey-card-examples.jpg',
        'images/board-preview.jpg',
        'images/om-track.jpg',
        'images/om-track-example.jpg',
        'images/travel-card-example.jpg',
        'images/outer-journey-card.jpg',
        'images/inner-journey-card.jpg',
        'images/event-card-example.jpg'
    ];

    // Transparent 1×1 PNG (base64)
    const emptyPng = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGMAAQAABQABDQottAAAAABJRU5ErkJggg==',
        'base64'
    );

    for (const placeholder of placeholders) {
        const filePath = path.join(assetsOutputDir, placeholder);
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(filePath, emptyPng);
    }
    
    // Copy any real assets from source
    const srcAssetsDir = path.join(__dirname, '../assets');
    if (fs.existsSync(srcAssetsDir)) {
        copyRecursive(srcAssetsDir, assetsOutputDir);
    }
    
    console.log('✓ Assets copied successfully');
}

function copyRecursive(src, dest) {
    if (!fs.existsSync(src)) return;
    
    const stats = fs.statSync(src);
    if (stats.isDirectory()) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }
        const files = fs.readdirSync(src);
        for (const file of files) {
            copyRecursive(path.join(src, file), path.join(dest, file));
        }
    } else {
        fs.copyFileSync(src, dest);
    }
}

async function generatePDFViaServer(htmlPath) {
    console.log('Attempting PDF generation via HTTP server...');
    
    const app = express();
    const port = 3000;
    
    // Serve static files from output directory
    app.use(express.static(outputDir));
    
    // Start server
    const server = app.listen(port, () => {
        console.log(`Temporary server running on http://localhost:${port}`);
    });
    
    try {
        const browser = await puppeteer.launch({
            headless: "new",
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Navigate to the local server
        await page.goto(`http://localhost:${port}/rulebook.html`, {
            waitUntil: ['load', 'networkidle0'],
            timeout: 30000
        });
        
        // Wait for Paged.js
        await page.waitForTimeout(5000);
        
        try {
            await page.waitForFunction(() => {
                return window.PagedPolyfill && window.PagedPolyfill.isReady;
            }, { timeout: 30000 });
        } catch (error) {
            console.log('Paged.js timeout, proceeding anyway...');
        }
        
        // Generate PDF
        const pdfPath = path.join(outputDir, 'om-journey-rulebook-server.pdf');
        await page.pdf({
            path: pdfPath,
            format: 'A4',
            printBackground: true,
            preferCSSPageSize: true,
            margin: {
                top: '0.5in',
                bottom: '0.5in',
                left: '0.5in',
                right: '0.5in'
            }
        });
        
        console.log(`✓ Server-based PDF generated: ${pdfPath}`);
        
        await browser.close();
        
    } finally {
        // Close server
        server.close();
    }
}

async function generatePDFWithPlaywright(htmlPath) {
    console.log('Attempting PDF generation with Playwright...');
    
    try {
        const browser = await chromium.launch({
            headless: true
        });
        
        const context = await browser.newContext();
        const page = await context.newPage();
        
        // Read and inline all resources
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');
        const cssContent = fs.readFileSync(path.join(outputDir, 'om.css'), 'utf8');
        const pagedJsContent = fs.readFileSync(path.join(outputDir, 'paged.polyfill.js'), 'utf8');
        
        // Create fully self-contained HTML
        const inlineHtml = htmlContent
            .replace('<link rel="stylesheet" href="om.css">', `<style>${cssContent}</style>`)
            .replace('<script src="paged.polyfill.js"></script>', `<script>${pagedJsContent}</script>`);
        
        // Set content
        await page.setContent(inlineHtml);
        
        // Wait for page to be ready
        await page.waitForTimeout(5000);
        
        // Wait for Paged.js if available
        try {
            await page.waitForFunction(() => {
                return window.PagedPolyfill && window.PagedPolyfill.isReady;
            }, { timeout: 30000 });
            console.log('✓ Paged.js ready with Playwright');
        } catch (error) {
            console.log('⚠️  Paged.js timeout with Playwright, proceeding...');
        }
        
        // Generate PDF
        const pdfPath = path.join(outputDir, 'om-journey-rulebook.pdf');
        await page.pdf({
            path: pdfPath,
            format: 'A4',
            printBackground: true,
            margin: {
                top: '0.5in',
                bottom: '0.5in',
                left: '0.5in',
                right: '0.5in'
            }
        });
        
        await browser.close();
        
        console.log(`✓ PDF generated successfully: ${pdfPath}`);
        
    } catch (error) {
        console.error('Playwright PDF generation error:', error.message);
        throw error;
    }
}

async function main() {
    try {
        console.log('Building Om: The Journey Rulebook...\n');
        
        await compileStyles();
        await copyAssets();
        const htmlPath = await generateHTML();
        
        console.log('\n✓ HTML build complete!');
        console.log(`📖 View at: file://${htmlPath}`);
        
        // Try PDF generation with multiple approaches
        let pdfGenerated = false;
        
        // Method 1: Playwright-based generation (most reliable)
        try {
            console.log('Generating PDF with Playwright...');
            await generatePDFWithPlaywright(htmlPath);
            pdfGenerated = true;
            console.log('\n✓ PDF generation successful!');
        } catch (error) {
            console.log('\n⚠️  Playwright PDF generation failed:', error.message);
        }
        
        // Method 2: Enhanced Puppeteer file-based generation
        if (!pdfGenerated) {
            try {
                console.log('Trying enhanced Puppeteer PDF generation...');
                await generatePDF(htmlPath);
                pdfGenerated = true;
                console.log('\n✓ Puppeteer PDF generation successful!');
            } catch (error) {
                console.log('\n⚠️  Enhanced PDF generation failed:', error.message);
            }
        }
        
        // Method 3: Server-based generation
        if (!pdfGenerated) {
            try {
                console.log('Trying server-based PDF generation...');
                await generatePDFViaServer(htmlPath);
                pdfGenerated = true;
                console.log('\n✓ Server-based PDF generation successful!');
            } catch (error) {
                console.log('\n⚠️  Server-based PDF generation failed:', error.message);
            }
        }
        
        // Method 4: Simple fallback generation
        if (!pdfGenerated) {
            try {
                console.log('Trying simple fallback PDF generation...');
                await generatePDFFallback(htmlPath);
                pdfGenerated = true;
                console.log('\n✓ Fallback PDF generation successful!');
            } catch (fallbackError) {
                console.log('\n⚠️  All PDF generation methods failed.');
                console.log('Last error:', fallbackError.message);
            }
        }
        
        if (!pdfGenerated) {
            console.log('\n📄 HTML version completed successfully!');
            console.log('You can still view the HTML version or try PDF generation later.');
        }
        
        console.log(`\nOutput files in: ${outputDir}`);
        
    } catch (error) {
        console.error('\n✗ Build failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { main }; 