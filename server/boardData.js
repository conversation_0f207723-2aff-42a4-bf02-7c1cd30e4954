// server/boardData.js

/**
 * Board data for "Om: The Journey."
 * - 60 total locations (48 standard + 12 Jyotirlingas).
 * - Edges array: each edge is { from, to } (undirected).
 * - travelDeck, eventDeck, journeyDeck define the card decks.
 */

const LOCATIONS = [
  // ---------------------
  //  NORTH (8 main)
  // ---------------------
  { id: 1, name: 'Vaishno Devi', region: 'North', journeyType: 'Inner' },
  { id: 2, name: 'Chail', region: 'North', journeyType: 'Outer' },
  { id: 3, name: 'Golden Temple', region: 'North', journeyType: 'Inner' },
  { id: 4, name: 'Durgiana Temple', region: 'North', journeyType: 'Inner' },
  { id: 6, name: 'Taj Mahal', region: 'North', journeyType: 'Outer' },
  { id: 7, name: 'Haridwar', region: 'North', journeyType: 'Inner' },
  { id: 9, name: 'Valley of Flowers', region: 'North', journeyType: 'Outer' },

  //  NORTH Jyotirlingas (2)
  { id: 54, name: 'Kedarnath', region: 'North', journeyType: 'Inner' },
  { id: 56, name: 'Viswanath', region: 'North', journeyType: 'Inner' },

  //  NORTH Airport (1)
  { id: 61, name: 'North Airport', region: 'North', journeyType: 'Airport' },


  // ---------------------
  //  WEST (8 main)
  // ---------------------
  { id: 10, name: 'Ambaji Temple', region: 'West', journeyType: 'Inner' },
  { id: 11, name: 'Rann of Kutch', region: 'West', journeyType: 'Outer' },
  { id: 12, name: 'Jaisalmer Fort', region: 'West', journeyType: 'Outer' },
  { id: 13, name: 'Hawa Mahal', region: 'West', journeyType: 'Outer' },
  { id: 14, name: 'Brahma Temple, Pushkar', region: 'West', journeyType: 'Inner' },
  { id: 16, name: 'Siddhivinayak Temple', region: 'West', journeyType: 'Inner' },
  { id: 19, name: 'Basilica of Bom Jesus', region: 'West', journeyType: 'Inner' },

  //  WEST Jyotirlingas (2)
  { id: 49, name: 'Somnath', region: 'West', journeyType: 'Inner' },
  { id: 50, name: 'Nageswar', region: 'West', journeyType: 'Inner' },

  //  WEST Airport (1)
  { id: 62, name: 'West Airport', region: 'West', journeyType: 'Airport' },


  // ---------------------
  //  SOUTH (8 main)
  // ---------------------
  { id: 20, name: 'Meenakshi Temple', region: 'South', journeyType: 'Inner' },
  { id: 21, name: 'Annamalaiyar Temple', region: 'South', journeyType: 'Inner' },
  { id: 22, name: 'Nilgiri Hills', region: 'South', journeyType: 'Outer' },
  { id: 23, name: 'Sabarimala', region: 'South', journeyType: 'Inner' },
  { id: 24, name: 'Padmanabhaswamy Temple', region: 'South', journeyType: 'Inner' },
  { id: 25, name: 'Backwaters of Kerala', region: 'South', journeyType: 'Outer' },
  { id: 27, name: 'Hampi', region: 'South', journeyType: 'Outer' },

  //  SOUTH Jyotirlingas (2)
  { id: 51, name: 'Mallikarjuna', region: 'South', journeyType: 'Inner' },
  { id: 59, name: 'Rameshwar', region: 'South', journeyType: 'Inner' },

  //  SOUTH Airport (1)
  { id: 63, name: 'South Airport', region: 'South', journeyType: 'Airport' },


  // ---------------------
  //  CENTRAL (8 main)
  // ---------------------
  { id: 5, name: 'Kurukshetra', region: 'Central', journeyType: 'Outer' },
  { id: 15, name: 'Shirdi Sai Baba Temple', region: 'Central', journeyType: 'Inner' },
  { id: 18, name: 'Ajanta and Ellora Caves', region: 'Central', journeyType: 'Outer' },
  { id: 28, name: 'Mysore Palace', region: 'Central', journeyType: 'Outer' },
  { id: 29, name: 'Coorg', region: 'Central', journeyType: 'Outer' },
  { id: 33, name: 'Khajuraho Temples', region: 'Central', journeyType: 'Outer' },
  { id: 34, name: 'Pachmarhi', region: 'Central', journeyType: 'Outer' },

  //  CENTRAL Jyotirlingas (2)
  { id: 52, name: 'Mahakaleswar', region: 'Central', journeyType: 'Inner' },
  { id: 53, name: 'Omkareshwar', region: 'Central', journeyType: 'Inner' },

  //  CENTRAL Airport (1)
  { id: 64, name: 'Central Airport', region: 'Central', journeyType: 'Airport' },


  // ---------------------
  //  EAST (8 main)
  // ---------------------
  { id: 30, name: 'Charminar', region: 'East', journeyType: 'Outer' },
  { id: 31, name: 'Ramoji Film City', region: 'East', journeyType: 'Outer' },
  { id: 32, name: 'Tirumala Venkateswara', region: 'East', journeyType: 'Inner' },
  { id: 36, name: 'Jagannath Temple', region: 'East', journeyType: 'Inner' },
  { id: 37, name: 'Chilika Lake', region: 'East', journeyType: 'Outer' },
  { id: 38, name: 'Tarapith', region: 'East', journeyType: 'Inner' },
  { id: 39, name: 'Betla National Park', region: 'East', journeyType: 'Outer' },

  //  EAST Jyotirlingas (2)
  { id: 55, name: 'Bhimashankar', region: 'East', journeyType: 'Inner' },
  { id: 58, name: 'Vaidyanath', region: 'East', journeyType: 'Inner' },

  //  EAST Airport (1)
  { id: 65, name: 'East Airport', region: 'East', journeyType: 'Airport' },


  // ---------------------
  //  NORTHEAST (8 main)
  // ---------------------
  { id: 42, name: 'Kamakhya Temple', region: 'Northeast', journeyType: 'Inner' },
  { id: 43, name: 'Living Root Bridge', region: 'Northeast', journeyType: 'Outer' },
  { id: 44, name: 'Ujjayanta Palace', region: 'Northeast', journeyType: 'Outer' },
  { id: 45, name: 'Kangla Fort', region: 'Northeast', journeyType: 'Outer' },
  { id: 46, name: 'Phawngpui Blue Mountain', region: 'Northeast', journeyType: 'Outer' },
  { id: 47, name: 'Dzükou Valley', region: 'Northeast', journeyType: 'Outer' },
  { id: 48, name: 'Rumtek Monastery', region: 'Northeast', journeyType: 'Inner' },

  //  NORTHEAST Jyotirlingas (2)
  { id: 57, name: 'Triambakeshwar', region: 'Northeast', journeyType: 'Inner' },
  { id: 60, name: 'Grishneshwar', region: 'Northeast', journeyType: 'Inner' },

  //  NORTHEAST Airport (1)
  { id: 66, name: 'Northeast Airport', region: 'Northeast', journeyType: 'Airport' },
];

const EDGES = [
  //
  // ─────────────────────────────────────────────────────────────────
  //   N O R T H   R E G I O N
  //   (nodes 1,2,3,4,6,7,8,9; jyotirlingas 54,56; airport 61)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main North nodes:
  { from: 1, to: 2 },
  { from: 2, to: 3 },
  { from: 3, to: 4 },
  { from: 4, to: 6 },
  { from: 6, to: 7 },
  { from: 1, to: 9 },
  // Jyotirlingas (1 connection each):
  { from: 9, to: 54 },  // Kedarnath
  { from: 3, to: 56 },  // Viswanath
  { from: 7, to: 54 },  // New connection: Haridwar to Kedarnath
  // Airport (2 connections):
  { from: 4, to: 61 },
  { from: 6, to: 61 },
  // Inter‐regional edges:
  { from: 7, to: 11 },  // North <-> West
  // North <-> Northeast
  { from: 3, to: 47 },

  //
  // ─────────────────────────────────────────────────────────────────
  //   W E S T   R E G I O N
  //   (nodes 10,11,12,13,14,16,17,19; jyotirlingas 49,50; airport 62)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main West nodes:
  { from: 10, to: 11 },
  { from: 11, to: 12 },
  { from: 12, to: 13 },
  { from: 13, to: 14 },
  { from: 14, to: 16 },
  { from: 14, to: 19 },
  { from: 13, to: 34 },  // New connection: Hawa Mahal to Pachmarhi (West to Central)
  // Jyotirlingas (1 connection each):
  { from: 10, to: 49 }, // Somnath
  { from: 12, to: 50 }, // Nageswar
  // Airport (connected to 3 West nodes so none is left with only 1 edge):
  { from: 11, to: 62 },
  { from: 13, to: 62 },
  { from: 19, to: 62 },
  // Inter‐regional edges:
  // (North <-> West is already from 7->14)
  { from: 16, to: 27 }, // West <-> South
  // West <-> Central - removing edge with node 17

  //
  // ─────────────────────────────────────────────────────────────────
  //   S O U T H   R E G I O N
  //   (nodes 20..27; jyotirlingas 51,59; airport 63)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main South nodes:
  { from: 20, to: 21 },
  { from: 21, to: 22 },
  { from: 22, to: 23 },
  { from: 23, to: 24 },
  { from: 24, to: 25 },
  { from: 25, to: 27 },
  // Jyotirlingas (1 connection each):
  { from: 20, to: 51 }, // Mallikarjuna
  { from: 23, to: 59 }, // Rameshwar
  // Airport (2 connections):
  { from: 24, to: 63 },
  { from: 25, to: 63 },
  { from: 21, to: 63 },
  // Inter‐regional edges:
  { from: 21, to: 28 }, // South <-> Central
  // (West <-> South is 16->27 above)

  //
  // ─────────────────────────────────────────────────────────────────
  //   C E N T R A L   R E G I O N
  //   (nodes 5,15,18,28,29,33,34,35; jyotirlingas 52,53; airport 64)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main Central nodes:
  { from: 5, to: 15 },
  { from: 15, to: 18 },
  { from: 18, to: 28 },
  { from: 28, to: 29 },
  { from: 29, to: 33 },
  { from: 33, to: 34 },
  { from: 28, to: 37 },  // New connection: Mysore Palace to Chilika Lake (Central to East)
  // Jyotirlingas (1 connection each):
  { from: 5, to: 52 }, // Mahakaleswar
  { from: 18, to: 53 }, // Omkareshwar
  // Airport (3 connections so endpoints aren't stuck at 1 edge):
  { from: 15, to: 64 },
  { from: 29, to: 64 },
  // Inter‐regional edges:
  // West <-> Central - removed with node 17
  // North <-> Central - removed with node 8
  { from: 21, to: 28 }, // South <-> Central (above)
  // Central <-> East - removed with node 40

  //
  // ─────────────────────────────────────────────────────────────────
  //   E A S T   R E G I O N
  //   (nodes 30..32,36..40; jyotirlingas 55,58; airport 65)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main East nodes:
  { from: 30, to: 31 },
  { from: 31, to: 32 },
  { from: 32, to: 36 },
  { from: 65, to: 37 },
  { from: 37, to: 38 },
  { from: 38, to: 39 },
  // Jyotirlingas (1 connection each):
  { from: 30, to: 55 }, // Bhimashankar
  { from: 32, to: 58 }, // Vaidyanath
  // Airport (2 connections):
  { from: 31, to: 65 },
  { from: 39, to: 65 },
  // Inter‐regional edges:
  { from: 23, to: 38 }, // South <-> East (above)
  { from: 31, to: 45 }, // East <-> Northeast (below)

  //
  // ─────────────────────────────────────────────────────────────────
  //   N O R T H E A S T   R E G I O N
  //   (nodes 41..48; jyotirlingas 57,60; airport 66)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main Northeast nodes:
  { from: 48, to: 42 },
  { from: 42, to: 43 },
  { from: 43, to: 44 },
  { from: 44, to: 45 },
  { from: 45, to: 46 },
  { from: 46, to: 47 },
  { from: 47, to: 57 },
  { from: 57, to: 48 },  // New connection: Triambakeshwar to Rumtek Monastery
  // Jyotirlingas (1 connection each):
  // Edge with node 41 removed
  { from: 43, to: 60 }, // Grishneshwar
  // Airport (3 connections so endpoints don't get stuck at 1 edge):
  { from: 42, to: 66 },
  { from: 44, to: 66 },
  // Edge with node 41 removed
  // Inter‐regional edges:
  { from: 31, to: 45 }, // East <-> Northeast
];

// Travel cards: 8 each of values 1, 2, 3 (total 24)
const TRAVEL_DECK = [
  // First set of travel cards
  { id: 'T1', type: 'travel', value: 1, vehicle: 'camel' },
  { id: 'T2', type: 'travel', value: 1, vehicle: 'horse' },
  { id: 'T3', type: 'travel', value: 1, vehicle: 'trek' },
  { id: 'T4', type: 'travel', value: 1, vehicle: 'cycle' },
  { id: 'T5', type: 'travel', value: 2, vehicle: 'bus' },
  { id: 'T6', type: 'travel', value: 2, vehicle: 'car' },
  { id: 'T7', type: 'travel', value: 2, vehicle: 'rickshaw' },
  { id: 'T8', type: 'travel', value: 2, vehicle: 'motorbike' },
  { id: 'T9', type: 'travel', value: 3, vehicle: 'helicopter' },
  { id: 'T10', type: 'travel', value: 3, vehicle: 'boat' },
  { id: 'T11', type: 'travel', value: 3, vehicle: 'train' },
  { id: 'T12', type: 'travel', value: 3, vehicle: 'truck' },
  // Second set of travel cards (duplicates with new IDs)
  { id: 'T13', type: 'travel', value: 1, vehicle: 'camel' },
  { id: 'T14', type: 'travel', value: 1, vehicle: 'horse' },
  { id: 'T15', type: 'travel', value: 1, vehicle: 'trek' },
  { id: 'T16', type: 'travel', value: 1, vehicle: 'cycle' },
  { id: 'T17', type: 'travel', value: 2, vehicle: 'bus' },
  { id: 'T18', type: 'travel', value: 2, vehicle: 'car' },
  { id: 'T19', type: 'travel', value: 2, vehicle: 'rickshaw' },
  { id: 'T20', type: 'travel', value: 2, vehicle: 'motorbike' },
  { id: 'T21', type: 'travel', value: 3, vehicle: 'helicopter' },
  { id: 'T22', type: 'travel', value: 3, vehicle: 'boat' },
  { id: 'T23', type: 'travel', value: 3, vehicle: 'train' },
  { id: 'T24', type: 'travel', value: 3, vehicle: 'truck' },
];

// Event cards: 5 "extraHop", 4 "wildCube"
const EVENT_DECK = [
  { id: 'E1', type: 'extraHop' },
  { id: 'E2', type: 'extraHop' },
  { id: 'E3', type: 'extraHop' },
  { id: 'E4', type: 'extraHop' },
  { id: 'E5', type: 'extraHop' },
  { id: 'E6', type: 'wildCube' },
  { id: 'E7', type: 'wildCube' },
  { id: 'E8', type: 'wildCube' },
  { id: 'E9', type: 'wildCube' },
];

const JOURNEY_DECK_INNER = [
  { id: 'JI1', locationId: 1, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
  { id: 'JI2', locationId: 3, required: { bhakti: 2, gnana: 2 }, reward: { inner: 35 } },
  { id: 'JI3', locationId: 4, required: { gnana: 1 }, reward: { inner: 20 } },
  { id: 'JI4', locationId: 7, required: { bhakti: 2, gnana: 1 }, reward: { inner: 27 } },
  { id: 'JI6', locationId: 10, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
  { id: 'JI7', locationId: 14, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
  { id: 'JI8', locationId: 15, required: { bhakti: 1, gnana: 2 }, reward: { inner: 27 } },
  { id: 'JI9', locationId: 16, required: { bhakti: 2, gnana: 2 }, reward: { inner: 35 } },
  { id: 'JI10', locationId: 19, required: { bhakti: 2, gnana: 1 }, reward: { inner: 27 } },
  { id: 'JI11', locationId: 20, required: { bhakti: 2, gnana: 2 }, reward: { inner: 35 } },
  { id: 'JI12', locationId: 21, required: { bhakti: 1 }, reward: { inner: 20 } },
  { id: 'JI13', locationId: 23, required: { bhakti: 1, gnana: 2 }, reward: { inner: 27 } },
  { id: 'JI14', locationId: 24, required: { bhakti: 2, gnana: 2 }, reward: { inner: 35 } },
  { id: 'JI16', locationId: 32, required: { bhakti: 2, gnana: 2 }, reward: { inner: 35 } },
  { id: 'JI17', locationId: 36, required: { bhakti: 2, gnana: 2 }, reward: { inner: 35 } },
  { id: 'JI18', locationId: 38, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
  { id: 'JI21', locationId: 42, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
  { id: 'JI22', locationId: 48, required: { bhakti: 2, gnana: 2 }, reward: { inner: 35 } },
];

const JOURNEY_DECK_OUTER = [
  // Outer locations
  { id: 'JO1', locationId: 2, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
  { id: 'JO2', locationId: 5, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
  { id: 'JO3', locationId: 6, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
  { id: 'JO4', locationId: 9, required: { artha: 1 }, reward: { outer: 20 } },
  { id: 'JO5', locationId: 11, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
  { id: 'JO6', locationId: 12, required: { karma: 2, artha: 2 }, reward: { outer: 35 } },
  { id: 'JO7', locationId: 13, required: { artha: 1 }, reward: { outer: 20 } },
  { id: 'JO9', locationId: 18, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
  { id: 'JO10', locationId: 22, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
  { id: 'JO11', locationId: 25, required: { artha: 1 }, reward: { outer: 20 } },
  { id: 'JO12', locationId: 27, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
  { id: 'JO13', locationId: 28, required: { karma: 2, artha: 2 }, reward: { outer: 35 } },
  { id: 'JO14', locationId: 29, required: { artha: 1 }, reward: { outer: 20 } },
  { id: 'JO15', locationId: 35, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
  { id: 'JO16', locationId: 31, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
  { id: 'JO17', locationId: 33, required: { karma: 1 }, reward: { outer: 20 } },
  { id: 'JO18', locationId: 34, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
  { id: 'JO20', locationId: 37, required: { karma: 1 }, reward: { outer: 20 } },
  { id: 'JO21', locationId: 39, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
  { id: 'JO22', locationId: 43, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
  { id: 'JO23', locationId: 44, required: { karma: 2, artha: 2 }, reward: { outer: 35 } },
  { id: 'JO24', locationId: 45, required: { karma: 1 }, reward: { outer: 20 } },
  { id: 'JO25', locationId: 46, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
  { id: 'JO26', locationId: 47, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
];

module.exports = {
  locations: LOCATIONS,
  edges: EDGES,
  travelDeck: TRAVEL_DECK,
  eventDeck: EVENT_DECK,
  journeyDeckInner: JOURNEY_DECK_INNER,
  journeyDeckOuter: JOURNEY_DECK_OUTER,
};