# HTML Rulebook Migration Notes

## Completed Tasks

1. **Created HTML-focused build system**:
   - Created `build-html.js` focusing only on HTML generation
   - Removed PDF generation code and dependencies
   - Added error handling and better logging

2. **Responsive design**:
   - Created `base-html.hbs` template with proper responsive markup
   - Added navigation bar for easy section access
   - Implemented mobile-first design with breakpoints

3. **Modern styling**:
   - Created `om-web.scss` with web-optimized styles
   - Added responsive grid layouts for components
   - Implemented proper spacing and typography for web reading

4. **Development workflow**:
   - Created `watch-html.js` for live development
   - Added browser auto-opening (with ESM imports)
   - Fixed dependency issues

5. **Documentation**:
   - Created `README-HTML.md` with usage instructions
   - Added deployment guidance
   - Documented customization options

## Next Steps

1. **Content review**:
   - Review markdown files for web-specific formatting
   - Add proper image references and captions
   - Ensure links are working properly

2. **Asset management**:
   - Copy existing images into `assets/images/` directory
   - Optimize images for web (compress, resize)
   - Create SVG versions of icons for better scaling

3. **Interactive elements**:
   - Add expandable sections for complex rules
   - Create interactive examples where helpful
   - Consider adding search functionality

4. **Testing**:
   - Test across different browsers
   - Test on various mobile devices
   - Verify printing functionality

5. **Deployment**:
   - Choose hosting platform
   - Set up automated deployment
   - Consider adding analytics

## Migration Issues Fixed

1. **Removed PDF dependencies**:
   - Removed `pagedjs`, `puppeteer`, and `playwright`
   - Eliminated PDF generation code that was causing issues

2. **Fixed ESM imports**:
   - Updated `open` package import to use dynamic imports
   - Made compatible with Node.js ESM/CommonJS modules

3. **Improved error handling**:
   - Added proper checks for missing files/directories
   - Added meaningful error messages
   - Graceful handling of missing assets

## Known Issues

1. **Sass deprecation warnings**:
   - Several color functions are using deprecated syntax
   - Need to update to `color.adjust()` and `color.scale()`

2. **Missing assets**:
   - Need to set up proper asset directory structure
   - Icons and images should be copied from existing sources

## Testing

The HTML rulebook has been successfully built and renders correctly. Further testing is needed for:

1. Responsive behavior across different screen sizes
2. Navigation functionality
3. Print layout when using browser print function

## Notes for Developers

- Default dev server runs on http://localhost:3000
- Use `npm run dev` to start development server
- Use `npm run build:html` for production build
- All content is in markdown files in `src/pages/`
- Styles are in `src/styles/om-web.scss`
- Templates are in `src/templates/` 