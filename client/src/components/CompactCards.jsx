import React, { useState, useRef, useEffect } from 'react';

function CompactCards({ faceUpTravel, faceUpEvent, socket, currentGlobalEvent, energyCubePile, onCardsPicked }) {
  const [selectedTravel, setSelectedTravel] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);

  // Handle selecting a travel card
  const handleTravelCardSelect = (cardId) => {
    // Toggle selection of the travel card
    if (selectedTravel.includes(cardId)) {
      setSelectedTravel(selectedTravel.filter(id => id !== cardId));
    } else {
      setSelectedTravel([...selectedTravel, cardId]);
    }
  };

  // Handle selecting an event card
  const handleEventCardSelect = (cardId) => {
    // Toggle selection
    if (selectedEvent === cardId) {
      setSelectedEvent(null);
    } else {
      setSelectedEvent(cardId);
    }
  };

  // Refs for the travel cards to get their positions
  const cardRefs = useRef({});

  // Handle picking selected travel cards
  const handlePickTravel = () => {
    if (!selectedTravel.length) return;

    // Get the selected card objects
    const selectedCards = faceUpTravel.filter(card => selectedTravel.includes(card.id));

    // Get positions for all selected cards
    if (onCardsPicked) {
      // Create an array of card positions for each selected card
      const cardPositions = selectedCards.map(card => {
        if (cardRefs.current[card.id]) {
          const cardRect = cardRefs.current[card.id].getBoundingClientRect();
          return {
            cardId: card.id,
            position: {
              x: cardRect.left + cardRect.width / 2,
              y: cardRect.top + cardRect.height / 2
            }
          };
        }
        return null;
      }).filter(pos => pos !== null);

      // Only proceed if we have positions for at least one card
      if (cardPositions.length > 0) {
        // Trigger animation in parent component
        onCardsPicked(selectedCards, cardPositions);
      }
    }

    // Send the pick cards event to the server
    socket.emit('pickCards', {
      type: 'travel',
      pickFromFaceUp: selectedTravel,
    });

    setSelectedTravel([]);
  };

  // Handle picking travel card from top of deck
  const handlePickTravelFromTop = () => {
    socket.emit('pickCards', {
      type: 'travel',
      pickFromTop: true,
    });
  };

  // Handle picking selected event card
  const handlePickEvent = () => {
    if (!selectedEvent) return;

    socket.emit('pickCards', {
      type: 'event',
      pickFromFaceUp: [selectedEvent],
    });

    setSelectedEvent(null);
  };

  // Handle picking event card from top of deck
  const handlePickEventFromTop = () => {
    socket.emit('pickCards', {
      type: 'event',
      pickFromTop: true,
    });
  };

  // Render compact travel card
  const renderCompactTravelCard = (card, index) => {
    // Check if card is selected
    const isSelected = selectedTravel.includes(card.id);
    const vehicleImagePath = `/assets/images/vehicles/${card.value}/${card.vehicle || 'camel'}.png`;

    return (
      <div
        key={`travel-${index}`}
        ref={el => cardRefs.current[card.id] = el}
        className={`compact-card travel-card ${isSelected ? 'selected' : ''}`}
        style={{
          width: '75px',
          height: '70px',
          backgroundColor: isSelected ? '#bbdefb' : '#FFF8E1', // Cream color background
          borderRadius: '6px',
          border: isSelected ? '2px solid #1565c0' : '1px solid #1976d2',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '0 6px 6px 0',
          boxShadow: isSelected ? '0 0 8px rgba(25, 118, 210, 0.5)' : '0 2px 4px rgba(0,0,0,0.1)',
          padding: '4px',
          position: 'relative',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          overflow: 'hidden'
        }}
        onClick={() => handleTravelCardSelect(card.id)}
      >
        <img
          src={vehicleImagePath}
          alt={`${card.vehicle} (${card.value})`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain'
          }}
          onError={(e) => {
            // Fallback if image doesn't load
            e.target.style.display = 'none';
          }}
        />

        {isSelected && (
          <div style={{
            position: 'absolute',
            top: '-8px',
            right: '-8px',
            backgroundColor: '#1976d2',
            color: 'white',
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '0.7rem',
            fontWeight: 'bold',
            border: '1px solid white',
            zIndex: 2
          }}>
            ✓
          </div>
        )}
      </div>
    );
  };

  // Render compact event card
  const renderCompactEventCard = (card, index) => {
    let eventName = card.name || 'Event';
    let shortName = eventName.length > 7 ? eventName.substring(0, 6) + '...' : eventName;

    // Special handling for wild cube event
    if (card.type === 'wildCube') {
      shortName = 'Wild';
    }

    // Check if card is selected
    const isSelected = selectedEvent === card.id;

    return (
      <div
        key={`event-${index}`}
        className={`compact-card event-card ${isSelected ? 'selected' : ''}`}
        style={{
          width: '50px',
          height: '70px',
          backgroundColor: isSelected ? '#e1bee7' : '#f3e5f5',
          borderRadius: '6px',
          border: isSelected ? '2px solid #6a1b9a' : '1px solid #7b1fa2',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '0 6px 6px 0',
          boxShadow: isSelected ? '0 0 8px rgba(123, 31, 162, 0.5)' : '0 2px 4px rgba(0,0,0,0.1)',
          padding: '4px',
          position: 'relative',
          cursor: 'pointer',
          transition: 'all 0.2s ease'
        }}
        onClick={() => handleEventCardSelect(card.id)}
      >
        <div style={{
          fontWeight: 'bold',
          fontSize: '0.75rem',
          color: '#7b1fa2',
          textAlign: 'center'
        }}>
          {shortName}
        </div>
        <div style={{ fontSize: '0.6rem', marginTop: '3px', textAlign: 'center' }}>
          Event
        </div>
        {isSelected && (
          <div style={{
            position: 'absolute',
            top: '-8px',
            right: '-8px',
            backgroundColor: '#7b1fa2',
            color: 'white',
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '0.7rem',
            fontWeight: 'bold',
            border: '1px solid white'
          }}>
            ✓
          </div>
        )}
      </div>
    );
  };

  // Render global event card
  const renderGlobalEventCard = () => {
    // Debug check to see if we're receiving the global event
    console.log("Current Global Event:", currentGlobalEvent);

    if (!currentGlobalEvent) {
      // Fallback for when currentGlobalEvent is null/undefined
      return (
        <div
          className="global-event-card"
          style={{
            width: '100%',
            backgroundColor: '#fff9c4',
            borderRadius: '4px',
            border: '1px solid #fbc02d',
            padding: '3px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
            marginBottom: '5px',
            textAlign: 'center',
            color: '#757575',
            fontSize: '0.6rem'
          }}
        >
          No active global event
        </div>
      );
    }

    return (
      <div
        className="global-event-card"
        style={{
          width: '100%',
          backgroundColor: '#fff9c4', // Light yellow color
          borderRadius: '4px',
          border: '1px solid #fbc02d', // Darker yellow border
          padding: '3px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
          marginBottom: '5px'
        }}
      >
        <div style={{
          fontWeight: 'bold',
          fontSize: '0.7rem',
          color: '#e65100',
          marginBottom: '2px',
          textAlign: 'center'
        }}>
          {currentGlobalEvent.name}
        </div>
        <div style={{
          fontSize: '0.7rem',
          textAlign: 'center',
          color: '#424242'
        }}>
          {currentGlobalEvent.text}
        </div>
      </div>
    );
  };

  return (
    <div
      className="compact-cards-container"
      style={{
        padding: '8px',
        backgroundColor: '#f5f5f5',
        borderRadius: '8px',
        marginBottom: '16px'
      }}
    >
      {/* Travel Cards Section */}
      <div style={{ marginBottom: '12px' }}>
        <div style={{
          fontWeight: 'bold',
          fontSize: '0.9rem',
          marginBottom: '8px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          color: '#1976d2'
        }}>
          <span>Travel Cards</span>
          <div>
            <button
              onClick={handlePickTravel}
              disabled={selectedTravel.length === 0}
              style={{
                padding: '4px 8px',
                marginRight: '4px',
                backgroundColor: selectedTravel.length > 0 ? '#1976d2' : '#e0e0e0',
                color: selectedTravel.length > 0 ? 'white' : '#9e9e9e',
                border: 'none',
                borderRadius: '4px',
                cursor: selectedTravel.length > 0 ? 'pointer' : 'default',
                fontSize: '0.7rem'
              }}
            >
              Pick Selected ({selectedTravel.length})
            </button>
            <button
              onClick={handlePickTravelFromTop}
              style={{
                padding: '4px 8px',
                backgroundColor: '#1976d2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '0.7rem'
              }}
            >
              From Deck
            </button>
          </div>
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          {faceUpTravel && faceUpTravel.map((card, index) => renderCompactTravelCard(card, index))}
        </div>
      </div>

      {/* Event Cards Section */}
      {/* <div>
        <div style={{
          fontWeight: 'bold',
          fontSize: '0.9rem',
          marginBottom: '8px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <span>Event Cards</span>
          <div>
            <button
              onClick={handlePickEvent}
              disabled={!selectedEvent}
              style={{
                padding: '4px 8px',
                marginRight: '4px',
                backgroundColor: selectedEvent ? '#7b1fa2' : '#e0e0e0',
                color: selectedEvent ? 'white' : '#9e9e9e',
                border: 'none',
                borderRadius: '4px',
                cursor: selectedEvent ? 'pointer' : 'default',
                fontSize: '0.7rem'
              }}
            >
              Pick Selected
            </button>
            <button
              onClick={handlePickEventFromTop}
              style={{
                padding: '4px 8px',
                backgroundColor: '#7b1fa2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '0.7rem'
              }}
            >
              From Deck
            </button>
          </div>
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          {faceUpEvent && faceUpEvent.map((card, index) => renderCompactEventCard(card, index))}
        </div>
      </div> */}
    </div>
  );
}

export default CompactCards;