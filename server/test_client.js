/**
 * Test script for running simulations against a real server
 * 
 * Usage:
 *   node test_client.js [serverUrl]
 * 
 * Example:
 *   node test_client.js http://localhost:4000
 */

const SimulationClientRunner = require('./simulation/SimulationClientRunner');

// Get server URL from command line arguments or use default
const serverUrl = process.argv[2] || 'http://localhost:4000';

// Configuration for the simulation
const simulationOptions = {
  serverUrl,
  numGames: 1,
  speed: 5,
  bots: [
    { name: 'Bot-A', strategy: 'defensive' },
    { name: 'Bot-B', strategy: 'defensive' }
  ],
  logging: {
    logLevel: 'info',
    logToConsole: true,
    logToFile: true
  }
};

console.log(`Starting simulation against server at ${serverUrl}`);

// Create and run the simulation client
const simulationRunner = new SimulationClientRunner(simulationOptions);

simulationRunner.run()
  .then(results => {
    console.log('Simulation completed successfully');
    console.log(`Games played: ${results.gamesPlayed}`);
    console.log(`Games completed: ${results.gamesCompleted}`);
    
    if (Object.keys(results.botStats).length > 0) {
      console.log('\nBot statistics:');
      Object.values(results.botStats).forEach(botStat => {
        console.log(`- ${botStat.name}: Wins: ${botStat.wins}, Points: ${botStat.journeyPoints}`);
      });
    }
    
    process.exit(0);
  })
  .catch(error => {
    console.error('Simulation failed:', error.message);
    process.exit(1);
  }); 