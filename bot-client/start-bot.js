/**
 * Bot Client Runner
 *
 * Starts one or more bot clients that connect to the game server.
 * Usage: node start-bot.js [--count=N] [--strategy=STRATEGY] [--server=URL]
 */

const BotClient = require('./index');
const logger = require('../server/utils/logger');

// Parse command line arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  const match = arg.match(/--([^=]+)=?(.*)/);
  if (match) {
    acc[match[1]] = match[2] || true;
  }
  return acc;
}, {});

// Set default values
const config = {
  count: parseInt(args.count || '1', 10),
  strategy: args.strategy || 'defensive',
  serverUrl: args.server || 'http://localhost:4000',
  prioritizeOmTrack: args.prioritizeOmTrack === 'true' || false
};

// Available strategies for random assignment
const STRATEGIES = ['random', 'defensive', 'base'];

// Start bot clients
async function startBots() {
  logger.info(`Starting ${config.count} bot clients connecting to ${config.serverUrl}`);
  logger.info(`Using strategy: ${config.strategy}, Prioritize Om Track: ${config.prioritizeOmTrack}`);

  const botClients = [];

  for (let i = 0; i < config.count; i++) {
    // Use specified strategy or pick a random one
    const strategy = config.strategy === 'random-mix'
      ? STRATEGIES[Math.floor(Math.random() * STRATEGIES.length)]
      : config.strategy;

    const botClient = new BotClient({
      strategy: strategy,
      serverUrl: config.serverUrl,
      omTrackEnabled: true,
      prioritizeOmTrack: config.prioritizeOmTrack
      // name is not specified, so it will use the random name generator
    });

    await botClient.start();
    botClients.push(botClient);
  }

  logger.info(`${botClients.length} bot clients started`);

  // Handle process termination
  process.on('SIGINT', async () => {
    logger.info('Shutting down bot clients...');
    for (const botClient of botClients) {
      botClient.stop();
    }
    process.exit(0);
  });
}

// Start the bots
startBots().catch(error => {
  logger.error(`Error starting bots: ${error.message}`);
  process.exit(1);
});