# Asset Generation Scripts

This directory contains scripts for generating and manipulating game assets for the OM Journey game.

## Scripts

### 1. Vehicle Image Resizing

- **resize-vehicles.js**: Resizes vehicle images to 825x1125 pixels
- **check-dimensions.js**: Utility to check the dimensions of a resized vehicle image

Usage:
```bash
node resize-vehicles.js
```

### 2. Journey Card Image Generation

- **generate-journey-card-images.js**: Generates journey card images with location images, region indicators, and correct colors
- **check-image-dimensions.js**: Utility to check the dimensions of a generated journey card image
- **journey-card-renderer.html**: HTML template for rendering journey cards (used by the capture script)

Usage:
```bash
node generate-journey-card-images.js
```

## Output Directories

- Vehicle images: `../../client/public/assets/images/vehicles-resized/`
- Journey card images: `../../journey-card-images/`

## Colors

The scripts use colors defined in `client/src/styles/main.css` for consistency with the game's visual design:

- **Energy Colors**:
  - Artha: #F39C12 (Orange/yellow)
  - Karma: #27AE60 (Green)
  - Gnana: #2980B9 (Blue)
  - Bhakti: #8E44AD (Purple)

- **Region Colors**:
  - North: rgba(33, 150, 243, 0.95) (Vibrant blue)
  - West: rgba(76, 175, 80, 0.95) (Vibrant green)
  - South: rgba(255, 191, 102, 0.95) (Light orange)
  - Central: rgba(156, 39, 176, 0.95) (Vibrant purple)
  - East: #fd7c03ef (Orange)
  - Northeast: rgba(233, 30, 99, 0.95) (Vibrant pink)

- **Journey Types**:
  - Inner: rgba(25, 118, 210, 0.95) (Dark blue)
  - Outer: rgba(56, 142, 60, 0.95) (Dark green)
