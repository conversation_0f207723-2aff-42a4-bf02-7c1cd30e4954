// TTW-inspired Om: The Journey Rulebook Styles
// Based on specification: ½-inch outer margins, ⅜-inch inner; 12-column baseline grid (24-pt leading)

// Color Palette
$navy-blue: #205F80;
$burnt-orange: #E87E1E;
$sand: #F4EBDD;
$body-grey: #555;
$white: #FFFFFF;

// Typography
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue:wght@400&family=Lato:wght@400;700&display=swap');

// Page Setup for paged.js
@page {
  size: 8.5in 11in;
  /* uniform ¾‑inch margins all around */
  margin: 0.75in;

  /* page numbers only (bottom‑centre) */
  @bottom-center {
    content: counter(page);
    font-family: 'Lato', sans-serif;
    font-size: 10pt;
    color: $body-grey;
  }
}

// Page breaks and spreads
.page-break {
  break-after: page;
}

.spread {
  break-after: page;
  break-before: page;
}

// Base Grid System (12-column, 24pt leading)
html {
  font-size: 12pt;
  line-height: 24pt;
}

body {
  font-family: 'Lato', sans-serif;
  font-size: 10pt;
  line-height: 14pt;
  color: $body-grey;
  background: $white;
  margin: 0;
  padding: 0;
}

// Grid container
.container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 12pt;
  max-width: 100%;
}

// Typography Hierarchy
h1 {
  font-family: 'Bebas Neue', cursive;
  font-size: 36pt;
  line-height: 36pt;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 0 0 24pt 0;
  font-weight: 400;
}

h2 {
  font-family: 'Bebas Neue', cursive;
  font-size: 26pt;
  line-height: 26pt;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 24pt 0 12pt 0;
  font-weight: 400;
}

h3 {
  font-family: 'Lato', sans-serif;
  font-size: 14pt;
  line-height: 18pt;
  color: $navy-blue;
  font-weight: 700;
  margin: 18pt 0 6pt 0;
}

h4 {
  font-family: 'Lato', sans-serif;
  font-size: 12pt;
  line-height: 16pt;
  color: $burnt-orange;
  font-weight: 700;
  margin: 12pt 0 6pt 0;
}

p {
  margin: 0 0 12pt 0;
}


/* ----- List Defaults ----- */
ul {
  list-style-type: disc;
  margin-left: 18pt;
}
ol {
  list-style-type: decimal;
  margin-left: 18pt;
}

// Lists
ul, ol {
  margin: 0 0 12pt 0;
  padding-left: 18pt;
  
  li {
    margin-bottom: 6pt;
  }
}

// Boxes & Call-outs (rounded 4px, 1pt stroke, 5pt inner padding, drop-shadow)
.callout-box {
  background: $sand;
  border: 1pt solid $navy-blue;
  border-radius: 4px;
  padding: 5pt;
  margin: 12pt 0;
  filter: drop-shadow(1pt 1pt 0 rgba(0, 0, 0, 0.15));
  
  &.important {
    background: lighten($burnt-orange, 40%);
    border-color: $burnt-orange;
  }
  
  &.setup {
    background: lighten($navy-blue, 45%);
    border-color: $navy-blue;
  }
  
  p:last-child {
    margin-bottom: 0;
  }
}

// Sidebar layouts (alternating left/right)
.sidebar-left {
  grid-column: 1 / 4;
}

.sidebar-right {
  grid-column: 10 / 13;
}

.main-content {
  grid-column: 1 / 13;
  
  &.with-sidebar-left {
    grid-column: 5 / 13;
  }
  
  &.with-sidebar-right {
    grid-column: 1 / 9;
  }
}

// Tables (zebra rows 5% darker sand)
table {
  width: 100%;
  border-collapse: collapse;
  margin: 12pt 0;
  font-size: 9pt;
  line-height: 12pt;
  
  th {
    background: $navy-blue;
    color: $white;
    padding: 6pt;
    font-weight: 700;
    text-align: left;
  }
  
  td {
    padding: 6pt;
    border-bottom: 0.5pt solid lighten($navy-blue, 20%);
  }
  
  tr:nth-child(even) {
    background: darken($sand, 5%);
  }
}

// Component table (3-column for Om's components)
.component-table {
  table {
    th:first-child {
      width: 15%;
    }
    th:nth-child(2) {
      width: 20%;
    }
    th:nth-child(3) {
      width: 65%;
    }
  }
}

// Icons (32x32px, 6pt spacing from labels)
.icon {
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 6pt;
  
  &.energy-cube {
    border-radius: 3px;
    
    &.artha { background: #FFD700; }
    &.karma { background: #32CD32; }
    &.gnana { background: #4169E1; }
    &.bhakti { background: #9370DB; }
  }
  
  &.om-token {
    background: $burnt-orange;
    border-radius: 50%;
    color: $white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 10pt;
  }
  
  &.vehicle {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}

// Image treatments (borderless, 2pt white keyline on colored backgrounds)
.game-image {
  max-width: 100%;
  height: auto;
  
  &.on-color {
    border: 2pt solid $white;
  }
  
  &.board-section {
    width: 100%;
    margin: 12pt 0;
  }
  
  &.card-example {
    max-width: 200px;
    margin: 6pt;
  }
}

// Cover page (full-bleed)
.cover-page {
  grid-column: 1 / 13;
  background: linear-gradient(135deg, $navy-blue 0%, $burnt-orange 100%);
  color: $white;
  padding: 72pt 36pt;
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  h1 {
    color: $white;
    font-size: 72pt;
    line-height: 72pt;
    margin-bottom: 24pt;
  }
  
  .subtitle {
    font-size: 18pt;
    line-height: 24pt;
    margin-bottom: 36pt;
  }
  
  .game-info {
    font-size: 14pt;
    line-height: 18pt;
    
    .players, .time {
      display: inline-block;
      margin: 0 12pt;
    }
  }
}

// Component spread (2-page layout)
.component-spread {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 12pt;

  .component-category {
    grid-column: span 6;   /* two categories per row */
    margin-bottom: 24pt;

    h3 {
      color: $burnt-orange;
      border-bottom: 1pt solid $burnt-orange;
      padding-bottom: 3pt;
    }

    .component-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12pt;
      margin-top: 12pt;
    }

    .component-item {
      text-align: center;

      .component-image {
        width: 80px;
        height: 80px;
        background: $sand;
        border: 1pt solid $navy-blue;
        border-radius: 4px;
        margin: 0 auto 6pt auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8pt;
        color: $body-grey;
      }

      .component-name {
        font-weight: 700;
        font-size: 9pt;
        margin-bottom: 3pt;
      }

      .component-desc {
        font-size: 8pt;
        line-height: 10pt;
      }
    }
  }
}

// Icon legend footer
.icon-legend {
  background: $sand;
  border-top: 1pt solid $navy-blue;
  padding: 6pt 12pt;
  margin-top: 24pt;
  font-size: 8pt;
  
  .legend-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12pt;
    
    .legend-item {
      display: flex;
      align-items: center;
      
      .icon {
        width: 16px;
        height: 16px;
        margin-right: 3pt;
      }
    }
  }
}

// Game board representation
.board-diagram {
  background: $sand;
  border: 2pt solid $navy-blue;
  border-radius: 8px;
  padding: 12pt;
  margin: 18pt 0;
  
  .region {
    background: $white;
    border: 1pt solid lighten($navy-blue, 20%);
    border-radius: 4px;
    padding: 6pt;
    margin: 3pt;
    display: inline-block;
    font-size: 8pt;
    
    &.north { border-left: 3pt solid #FF6B6B; }
    &.south { border-left: 3pt solid #4ECDC4; }
    &.east { border-left: 3pt solid #45B7D1; }
    &.west { border-left: 3pt solid #96CEB4; }
    &.northeast { border-left: 3pt solid #FFEAA7; }
    &.central { border-left: 3pt solid #DDA0DD; }
  }
}

// Example gameplay
.example-turn {
  background: lighten($burnt-orange, 45%);
  border-left: 4pt solid $burnt-orange;
  padding: 12pt;
  margin: 18pt 0;
  
  .turn-header {
    font-weight: 700;
    color: $burnt-orange;
    margin-bottom: 6pt;
  }
  
  .turn-step {
    margin: 6pt 0;
    padding-left: 12pt;
    
    &:before {
      content: "→";
      color: $burnt-orange;
      font-weight: 700;
      margin-right: 6pt;
      margin-left: -12pt;
    }
  }
}

// Reference cards
.reference-card {
  background: $white;
  border: 1pt solid $navy-blue;
  border-radius: 6px;
  padding: 8pt;
  margin: 6pt;
  display: inline-block;
  vertical-align: top;
  width: 200px;
  
  .card-title {
    background: $navy-blue;
    color: $white;
    padding: 4pt 6pt;
    margin: -8pt -8pt 6pt -8pt;
    border-radius: 5px 5px 0 0;
    font-weight: 700;
    font-size: 9pt;
  }
  
  .card-cost {
    font-size: 8pt;
    margin-bottom: 4pt;
    
    .icon {
      width: 12px;
      height: 12px;
      margin-right: 2pt;
    }
  }
  
  .card-points {
    color: $burnt-orange;
    font-weight: 700;
    text-align: right;
    font-size: 14pt;
  }
}

// Responsive adjustments
@media print {
  body {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }
}

// Utility classes
.text-center { text-align: center; }
.text-right { text-align: right; }
.font-bold { font-weight: 700; }
.color-navy { color: $navy-blue; }
.color-orange { color: $burnt-orange; }
.color-sand { color: $sand; }

.margin-top { margin-top: 12pt; }
.margin-bottom { margin-bottom: 12pt; }
.padding { padding: 6pt; }

// Print-specific overrides
@media print {
  .no-print { display: none; }
  .page-break { page-break-after: always; }
  .avoid-break { page-break-inside: avoid; }
} 