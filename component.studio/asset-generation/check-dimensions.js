const sharp = require('sharp');

// Check dimensions of a sample image
async function checkDimensions() {
  try {
    const imagePath = '../../client/public/assets/images/vehicles-resized/1/camel.png';
    const metadata = await sharp(imagePath).metadata();
    console.log(`Image: ${imagePath}`);
    console.log(`Dimensions: ${metadata.width} x ${metadata.height}`);
  } catch (error) {
    console.error('Error:', error);
  }
}

checkDimensions();
