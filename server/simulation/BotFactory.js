/**
 * BotFactory.js
 * Factory class for creating bots with different strategies.
 */

const { v4: uuidv4 } = require('uuid');
const Bot = require('./Bot');
const BaseStrategy = require('./strategies/BaseStrategy');

// Import strategy implementations
const RandomStrategy = require('./strategies/RandomStrategy');
const DefensiveStrategy = require('./strategies/DefensiveStrategy');
const OptimizedDefensiveStrategy = require('./strategies/OptimizedDefensiveStrategy');
// Future strategies would be imported here:
// const BalancedStrategy = require('./strategies/BalancedStrategy');
// etc.

class BotFactory {
  /**
   * Create a new BotFactory
   * @param {object} params - Configuration parameters
   * @param {object} params.gameState - Reference to the game state
   * @param {object} [params.logger] - Logger for recording bot actions
   */
  constructor({ gameState, logger = console }) {
    this.gameState = gameState;
    this.logger = logger;
    this.strategyRegistry = new Map();

    // Register all available strategies
    this.registerStrategies();
  }

  /**
   * Register all available strategies
   * @private
   */
  registerStrategies() {
    // Register the random strategy
    this.registerStrategy('random', RandomStrategy);

    // Register the defensive strategy
    this.registerStrategy('defensive', DefensiveStrategy);

    // Register the optimized defensive strategy
    this.registerStrategy('opt-defensive', OptimizedDefensiveStrategy);

    // Register placeholder for future strategies
    this.registerPlaceholderStrategy('balanced');
    this.registerPlaceholderStrategy('aggressive');
    this.registerPlaceholderStrategy('omFocused');
    this.registerPlaceholderStrategy('scoreFocused');
  }

  /**
   * Register a placeholder strategy until a real one is implemented
   * @param {string} name - Name of the strategy
   * @private
   */
  registerPlaceholderStrategy(name) {
    this.registerStrategy(name, class PlaceholderStrategy extends BaseStrategy {
      constructor(config = {}) {
        super({ name, config });
      }

      async planTurn(bot) {
        const state = bot.getPlayerState();
        if (!state.isMyTurn) {
          return null;
        }

        bot.logger.warn(`Strategy '${name}' is a placeholder and has no implementation. Ending turn.`);

        // Simply end turn (placeholder behavior)
        return {
          action: 'endTurn',
          params: {}
        };
      }

      async analyzeMoves() { return null; }
      async evaluateCardPicks() { return null; }
      async evaluateJourneyCollection() { return null; }
    });
  }

  /**
   * Register a strategy implementation
   * @param {string} name - The name of the strategy
   * @param {class} StrategyClass - The strategy class implementation
   */
  registerStrategy(name, StrategyClass) {
    if (typeof StrategyClass !== 'function') {
      this.logger.warn(`Invalid strategy class for ${name}`);
      return;
    }

    this.strategyRegistry.set(name, StrategyClass);
    this.logger.debug(`Registered strategy: ${name}`);
  }

  /**
   * Create a bot with the specified strategy
   * @param {object} params - Bot configuration
   * @param {string} params.name - The bot's name
   * @param {string} [params.strategy] - Name of the strategy to use (for backwards compatibility)
   * @param {string} [params.strategyName] - Name of the strategy to use
   * @param {object} [params.strategyConfig] - Configuration for the strategy
   * @param {object} [params.options] - Additional bot options
   * @returns {Bot} A new bot instance
   */
  createBot({ name, strategy, strategyName, strategyConfig = {}, options = {} }) {
    // Generate a unique ID for the bot (simulating a socket ID)
    const id = `bot-${uuidv4()}`;

    // Support both strategy and strategyName parameters for backwards compatibility
    const effectiveStrategy = strategyName || strategy || 'random';

    // Get the strategy class, with fallback to random
    let StrategyClass = this.strategyRegistry.get(effectiveStrategy);

    // If strategy not found, log warning and fall back to random strategy
    if (!StrategyClass) {
      this.logger.warn(`Unknown strategy: ${effectiveStrategy}, falling back to 'random' strategy`);
      StrategyClass = this.strategyRegistry.get('random');

      // If random also not found (shouldn't happen), throw error
      if (!StrategyClass) {
        throw new Error(`Failed to find any valid strategies, even fallback 'random' strategy is missing`);
      }
    }

    // Create the strategy instance
    this.logger.info(`Creating bot ${name} with strategy '${effectiveStrategy}'`);
    const strategyInstance = new StrategyClass(strategyConfig);

    // Create and return the bot
    const bot = new Bot({
      id,
      name: name || `Bot-${id.substring(0, 6)}`,
      strategy: strategyInstance,
      gameState: this.gameState,
      logger: this.logger,
      options
    });

    this.logger.info(`Created bot ${bot.name} with ID ${bot.id} using ${strategyInstance.name} strategy`);
    return bot;
  }

  /**
   * Get a list of available strategy names
   * @returns {Array<string>} List of registered strategy names
   */
  getAvailableStrategies() {
    return Array.from(this.strategyRegistry.keys());
  }
}

module.exports = BotFactory;