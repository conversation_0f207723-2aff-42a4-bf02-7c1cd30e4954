/**
 * simulation_client.js
 * A command-line tool for running game simulations with configurable parameters.
 * Run with: node simulation_client.js [options]
 * 
 * Options:
 *  --games=N         Number of games to simulate (default: 1)
 *  --speed=N         Simulation speed multiplier (default: 10)
 *  --strategies=X,Y  Bot strategies to use (default: random,random)
 *  --save=true|false Save results to files (default: true)
 *  --memory=true|false Monitor memory usage (default: true)
 */

const fetch = require('node-fetch');
const { URLSearchParams } = require('url');
const fs = require('fs');
const path = require('path');

// Default API endpoint will be constructed based on options
let API_BASE = 'http://localhost:4000/api';

// Maximum number of events to store/process
const MAX_EVENTS = 200;

// Directory for saving simulation reports
const SIMULATION_REPORTS_DIR = path.join(__dirname, 'simulation_reports');

/**
 * Client for controlling the simulation through the API
 */
class SimulationClient {
  /**
   * Create a new simulation client
   * @param {object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      games: 1,
      speed: 10,
      strategies: ['defensive', 'defensive'],
      save: true,
      memory: true,
      enableFeatures: {
        globalEvents: true,
        playerCharacters: true,
        vehicles: true,
        handLimit: 4
      },
      host: 'localhost',
      port: 4000,
      verbose: false,
      ...options
    };
    
    // Construct API base URL from options
    API_BASE = `http://${this.options.host}:${this.options.port}/api`;
    
    console.log(`API base URL: ${API_BASE}`);
    
    // Store only summary statistics for each game, not full results
    this.gameSummaries = [];
    
    // Memory monitoring
    this.lastMemoryCheck = 0;
    this.memoryCheckInterval = 30000; // 30 seconds
    
    // Server information (filled in by checkServer)
    this.serverInfo = null;
  }
  
  /**
   * Run one or more simulations
   */
  async runSimulations() {
    try {
      console.log(`Starting simulation with options: ${JSON.stringify(this.options, null, 2)}`);
      
      // Verify the server is running
      console.log('Checking if the server is running...');
      const serverInfo = await this.checkServer();
      if (!serverInfo) {
        throw new Error('Server check failed: Server does not appear to be running');
      }
      
      // Store server info
      this.serverInfo = serverInfo;
      
      // Initialize storage for game summaries
      this.gameSummaries = [];
      
      // Configure the simulation
      try {
        await this.configureSimulation({
          autoRestart: false, // We'll manually restart for multiple games
          maxGames: 1, // Run one game at a time
          simulationSpeed: this.options.speed,
          saveResults: this.options.save,
          enableFeatures: this.options.enableFeatures,
          botThinkingSpeed: this.options.speed // Add this to explicitly set bot thinking speed
        });
      } catch (configError) {
        console.error('\n==== CONFIGURATION ERROR ====');
        console.error('Failed to configure simulation. This might be because:');
        console.error('1. The server is an older version that doesn\'t support all features');
        console.error('2. The server API endpoints have changed');
        console.error('3. There\'s a network issue');
        console.error('\nDetailed error:', configError.message);
        throw new Error(`Configuration failed: ${configError.message}`);
      }
      
      for (let gameNumber = 1; gameNumber <= this.options.games; gameNumber++) {
        console.log(`\n===== Starting Game ${gameNumber}/${this.options.games} =====`);
        
        // Make sure any previous simulation is stopped
        if (gameNumber > 1) {
          try {
            console.log('Stopping previous simulation to ensure a clean state...');
            await this.stopSimulation();
            
            // Verify the server state after stopping
            try {
              const status = await this.getStatus();
              // Check if there are still bots registered
              if (status.bots && status.bots.length > 0) {
                console.warn(`Warning: Server still has ${status.bots.length} bots registered after stopping.`);
                console.log('Attempting to force a clean slate by configuring simulation...');
                
                // Force reconfiguration to clear state
                await this.configureSimulation({
                  autoRestart: false,
                  maxGames: 1,
                  simulationSpeed: this.options.speed,
                  saveResults: this.options.save,
                  enableFeatures: this.options.enableFeatures,
                  forceReset: true  // Add a flag for force reset
                });
                
                // Wait a bit more
                await new Promise(resolve => setTimeout(resolve, 1000));
              }
            } catch (statusError) {
              console.warn(`Could not verify bot count: ${statusError.message}`);
            }
          } catch (stopError) {
            console.warn('Warning: Error stopping previous simulation:', stopError.message);
            // Continue anyway
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
        
        // Add the bots
        let bots = [];
        let botAddSuccess = true;
        
        for (let i = 0; i < this.options.strategies.length; i++) {
          const strategy = this.options.strategies[i];
          const name = `Bot-${String.fromCharCode(65 + i)}`;
          
          console.log(`Adding bot ${name} with ${strategy} strategy...`);
          
          try {
            const botInfo = await this.addBot(name, strategy);
            bots.push(botInfo);
            console.log(`Added ${name} using ${strategy} strategy (ID: ${botInfo.id})`);
            console.log('Bot response:', JSON.stringify(botInfo, null, 2));
          } catch (botError) {
            console.error(`Failed to add bot ${name}:`, botError.message);
            botAddSuccess = false;
            break;
          }
        }
        
        if (!botAddSuccess) {
          console.error('\n==== BOT CONFIGURATION ERROR ====');
          console.error('Failed to add all bots. Cannot continue with simulation.');
          throw new Error('Bot configuration failed');
        }
        
        console.log(`Successfully added ${bots.length} bots: ${bots.map(b => b.name).join(', ')}`);
        
        // Start the simulation
        try {
          console.log(`Starting game ${gameNumber}...`);
          console.log('Starting simulation...');
          const startResult = await this.startSimulation();
          console.log('Start result:', startResult);
        } catch (startError) {
          console.error('\n==== SIMULATION START ERROR ====');
          console.error('Failed to start the simulation. This might be because:');
          console.error('1. The server is busy with another simulation');
          console.error('2. There\'s a configuration issue');
          console.error('3. The server API endpoints have changed');
          console.error('\nDetailed error:', startError.message);
          throw new Error(`Simulation start failed: ${startError.message}`);
        }
        
        // Wait for the game to complete
        console.log('Waiting for game to complete...');
        let gameResult;
        try {
          gameResult = await this.waitForGameCompletion();
        } catch (waitError) {
          console.error('\n==== GAME COMPLETION ERROR ====');
          console.error('Error while waiting for game to complete:', waitError.message);
          throw new Error(`Game completion failed: ${waitError.message}`);
        }
        
        // Log the winner
        if (gameResult && gameResult.winner) {
          console.log(`Game ${gameNumber} completed! Winner: ${gameResult.winner.name} with ${gameResult.winner.totalScore} points`);
        } else {
          console.log(`Game ${gameNumber} completed with no clear winner`);
        }
        
        // Store only summarized results to save memory
        if (gameResult) {
          this.gameSummaries.push(this.summarizeGame(gameResult, gameNumber));
          
          // Generate report for this game
          this.printGameReport(gameResult, gameNumber);
          
          // Explicitly clear large objects to help garbage collection
          if (gameResult.events) gameResult.events = null;
        }
      }
      
      // Print summary of all games
      if (this.options.games > 1) {
        this.printSummaryReport();
      }
      
      // Save the simulation report as JSON
      await this.saveSimulationReportAsJson();
      
      console.log('\nSimulations completed successfully!');
      return this.gameSummaries;
    } catch (error) {
      console.error('\n===== SIMULATION ERROR =====');
      console.error('Error running simulations:', error.message);
      
      if (error.stack) {
        console.error('\nStack trace:');
        console.error(error.stack.split('\n').slice(1, 4).join('\n'));
      }
      
      // Print debug information
      console.error('\n===== DEBUG INFORMATION =====');
      console.error(`API base URL: ${API_BASE}`);
      console.error(`Host: ${this.options.host}`);
      console.error(`Port: ${this.options.port}`);
      console.error(`Server info:`, this.serverInfo ? JSON.stringify(this.serverInfo, null, 2) : 'Not available');
      
      throw error;
    }
  }
  
  /**
   * Check memory usage on the server
   */
  async checkMemoryUsage() {
    // Only check periodically
    const now = Date.now();
    if (now - this.lastMemoryCheck < this.memoryCheckInterval) {
      return;
    }
    
    this.lastMemoryCheck = now;
    
    try {
      const response = await fetch(`${API_BASE}/memory`);
      if (response.ok) {
        const memoryInfo = await response.json();
        console.log(`Server memory: Heap=${memoryInfo.heapUsed}MB, RSS=${memoryInfo.rss}MB`);
        
        if (memoryInfo.heapUsed > 700) {
          console.warn('WARNING: Server memory usage is high!');
        }
      }
    } catch (error) {
      // Don't fail the main process if memory check fails
      console.warn('Unable to check server memory:', error.message);
    }
  }
  
  /**
   * Create a memory-efficient summary of a game
   * @param {object} gameResult - The full game result
   * @param {number} gameNumber - The game number
   * @returns {object} A summarized game result
   */
  summarizeGame(gameResult, gameNumber) {
    if (!gameResult) {
      return {
        gameNumber,
        error: 'Game result is missing'
      };
    }
    
    const eventCounts = {};
    
    // Count events by type instead of storing all events
    if (gameResult.events) {
      gameResult.events.forEach(event => {
        eventCounts[event.type] = (eventCounts[event.type] || 0) + 1;
      });
    }
    
    // Make sure players property exists and is an array
    const players = Array.isArray(gameResult.players) ? gameResult.players : [];
    
    // Calculate bonus scores (event-based points) and dry turns for each player
    const playersWithExtras = players.map(p => {
      // Count bonus score events for this player
      const bonusScoreEvents = gameResult.events ? gameResult.events.filter(
        event => event.type === 'bonus_score' && event.playerId === p.id
      ) : [];
      
      const bonusScore = bonusScoreEvents.reduce((total, event) => {
        return total + (event.data?.points || 0);
      }, 0);
      
      // Count dry turns (turns where player couldn't do anything meaningful)
      const dryTurnEvents = gameResult.events ? gameResult.events.filter(
        event => event.type === 'dry_turn' && event.playerId === p.id
      ) : [];
      
      const dryTurns = dryTurnEvents.length;
      
      // Count Om track position changes
      const omTrackEvents = gameResult.events ? gameResult.events.filter(
        event => (event.type === 'placeOmToken' || event.type === 'omTrackUpdate') && event.playerId === p.id
      ) : [];
      
      // Get highest Om space reached
      let highestOmSpace = 0;
      omTrackEvents.forEach(event => {
        if (event.data?.omSpace && event.data.omSpace > highestOmSpace) {
          highestOmSpace = event.data.omSpace;
        }
      });
      
      // Process journey cards to add type information if missing
      let journeyCards = p.collectedJourneys || p.journeyCards || [];
      
      // Ensure journeyCards is an array
      if (!Array.isArray(journeyCards)) {
        if (typeof journeyCards === 'object') {
          // If it's an object but not an array, convert to array
          journeyCards = Object.values(journeyCards);
        } else {
          // If it's neither an array nor an object, make it an empty array
          journeyCards = [];
        }
      }
      
      if (Array.isArray(journeyCards)) {
        journeyCards = journeyCards.map(card => {
          if (!card) return null;
          if (card.type) return card;
          
          // Try to infer type from card id
          const id = card.id || '';
          let type = 'unknown';
          
          if (id.includes('inner')) {
            type = 'inner';
          } else if (id.includes('outer')) {
            type = 'outer';
          } else if (id.includes('special')) {
            type = 'special';
          }
          
          return { ...card, type };
        }).filter(Boolean);
      }
      
      return {
        ...p,
        bonusScore,
        dryTurns,
        journeyCards,
        highestOmSpace: highestOmSpace,
        omTrackEvents: omTrackEvents.length
      };
    });
    
    return {
      gameNumber,
      roundCount: gameResult.roundCount || 0,
      totalEvents: gameResult.events ? gameResult.events.length : 0,
      eventCounts,
      players: playersWithExtras.map(p => ({
        name: p.name || 'Unknown',
        id: p.id,
        totalScore: p.totalScore || 0,
        outerScore: p.outerScore || 0,
        innerScore: p.innerScore || 0,
        omTotal: p.omTotal || 0,
        bonusScore: p.bonusScore || 0,
        dryTurns: p.dryTurns || 0,
        isWinner: !!p.isWinner,
        journeyCards: p.journeyCards || [],
        energyCubes: p.energyCubes || { bhakti: 0, gnana: 0, karma: 0, artha: 0, total: 0 },
        totalHops: p.totalHops || 0,
        highestOmSpace: p.highestOmSpace || 0,
        omTrackEvents: p.omTrackEvents || 0
      })),
      winner: gameResult.winner ? {
        name: gameResult.winner.name || 'Unknown',
        id: gameResult.winner.id,
        totalScore: gameResult.winner.totalScore || 0,
        highestOmSpace: gameResult.winner.highestOmSpace || 0
      } : null,
      omTrack: gameResult.omTrack || []
    };
  }
  
  /**
   * Wait for a game to complete, polling the status endpoint
   * @param {number} pollingInterval - Milliseconds between status checks
   * @returns {Promise<object>} The game results
   */
  async waitForGameCompletion(pollingInterval = 1000) {
    let isComplete = false;
    let result = null;
    let maxRetries = 300; // 5 minutes at 1 second interval
    let retries = 0;
    let gameNumber = null;
    
    console.log('Waiting for game to complete...');
    
    while (!isComplete && retries < maxRetries) {
      try {
        // Check current status
        const status = await this.getStatus();
        
        // Defensive check to make sure status is valid
        if (!status) {
          console.warn('Invalid status response received');
          retries++;
          await new Promise(resolve => setTimeout(resolve, pollingInterval));
          continue;
        }
        
        // If this is our first successful status check, record the game number
        if (gameNumber === null && status.stats && status.stats.currentGame) {
          gameNumber = status.stats.currentGame.gameNumber;
          console.log(`Monitoring game #${gameNumber}`);
        }
        
        // If not running, the game has completed
        if (status.status !== 'running') {
          isComplete = true;
          
          // Make sure we have stats and currentGame before trying to access them
          if (status.stats && status.stats.currentGame) {
            result = await this.getGameResult(status.stats.currentGame.gameNumber);
          } else if (status.stats && status.stats.lastCompletedGame) {
            // Try to get the last completed game if currentGame isn't available
            result = await this.getGameResult(status.stats.lastCompletedGame.gameNumber);
          } else if (gameNumber !== null) {
            // If we had previously recorded a game number, try to use that
            console.log(`Attempting to get result for previously recorded game #${gameNumber}`);
            result = await this.getGameResult(gameNumber);
          } else {
            // If we can't get a game number from any source, create a minimal result
            console.warn('No game information found in status response');
            result = { 
              error: 'No game information found',
              gameNumber: 1, // Assume this is the first game
              roundCount: 0,
              players: [],
              events: []
            };
          }
        } else {
          // If still running, show progress
          if (status.stats && status.stats.currentGame) {
            const game = status.stats.currentGame;
            const turnCount = game.turnCount || 0;
            const roundCount = game.roundCount || 0;
            process.stdout.write(`\rGame progress: Turn ${turnCount}, Round ${roundCount}`);
            
            // Record the game number in case we need it later
            if (gameNumber === null) {
              gameNumber = game.gameNumber;
            }
            
            // Check memory periodically during long games
            if (this.options.memory && turnCount % 50 === 0) {
              await this.checkMemoryUsage();
            }
          } else {
            // If we can't get game stats, just show a basic progress indicator
            process.stdout.write('.');
          }
          
          // Wait before checking again
          await new Promise(resolve => setTimeout(resolve, pollingInterval));
          retries++;
        }
      } catch (error) {
        console.warn(`Error checking game status: ${error.message}`);
        retries++;
        await new Promise(resolve => setTimeout(resolve, pollingInterval));
      }
    }
    
    console.log(''); // New line after progress updates
    
    // If we hit the retry limit without completion
    if (!isComplete) {
      console.warn('Game did not complete within the timeout period');
      return { error: 'Game timeout', gameNumber: gameNumber || 1 };
    }
    
    return result;
  }
  
  /**
   * Get detailed results for a specific game
   * @param {number} gameNumber - The game number to fetch
   * @returns {Promise<object>} The game results
   */
  async getGameResult(gameNumber) {
    try {
      // First try to get metrics which has some game info
      const metrics = await this.getMetrics();
      
      if (!metrics || !metrics.playerStats) {
        return { gameNumber, error: 'Invalid metrics data' };
      }
      
      // Then get the events for more detailed information
      // Limit the number of events to prevent memory issues
      const events = await this.getEvents({ limit: MAX_EVENTS });
      
      // Add card event monitoring here (with defensive check)
      if (events && Array.isArray(events.events)) {
        this._monitorCardEvents(events.events.slice(-20)); // Monitor the last 20 events
      }
      
      // Process player journey cards to add type information
      const players = metrics.playerStats || [];
      
      // Track dry turns and bonus scores
      players.forEach(player => {
        // Get journey cards with types for each player
        if (player.journeyCards) {
          // Ensure journeyCards is an array
          if (!Array.isArray(player.journeyCards)) {
            if (typeof player.journeyCards === 'object') {
              // If it's an object but not an array, convert to array
              player.journeyCards = Object.values(player.journeyCards);
            } else {
              // If it's neither an array nor an object, make it an empty array
              player.journeyCards = [];
            }
          }
          
          // Ensure each journey card has a type property
          if (Array.isArray(player.journeyCards)) {
            player.journeyCards = player.journeyCards.map(card => {
              // If the card already has a type, use it
              if (card.type) return card;
              
              // Otherwise, try to infer type from card id or locationId
              const id = card.id || '';
              let type = 'unknown';
              
              // Simple classification based on card ID patterns
              if (id.includes('inner')) {
                type = 'inner';
              } else if (id.includes('outer')) {
                type = 'outer';
              } else if (id.includes('special')) {
                type = 'special';
              }
              
              return {
                ...card,
                type
              };
            });
          }
        } else {
          // Initialize journeyCards as empty array if missing
          player.journeyCards = [];
        }
        
        // Initialize bonus score and dry turns properties if not present
        player.bonusScore = player.bonusScore || 0;
        player.dryTurns = player.dryTurns || 0;
      });
      
      return {
        gameNumber,
        metrics,
        events: events && events.events ? events.events : [],
        players,
        winner: metrics.playerStats ? metrics.playerStats.find(p => p.isWinner) || null : null,
        roundCount: metrics.roundCount || 0
      };
    } catch (error) {
      console.error(`Failed to fetch game ${gameNumber} result:`, error);
      return { gameNumber, error: error.message };
    }
  }
  
  /**
   * Print a detailed report for a single game
   * @param {object} gameResult - The game result object
   * @param {number} gameNumber - The game number
   */
  printGameReport(gameResult, gameNumber) {
    if (!gameResult) {
      console.log(`\n----- Game ${gameNumber} Report -----`);
      console.log('No valid game result available');
      return;
    }
    
    console.log(`\n----- Game ${gameNumber} Report -----`);
    console.log(`Rounds: ${gameResult.roundCount || 0}`);
    console.log(`Total Events: ${gameResult.events ? gameResult.events.length : 0}`);
    
    // Player results
    console.log('\nPlayer Results:');
    if (Array.isArray(gameResult.players)) {
      gameResult.players.forEach(player => {
        if (!player) return;
        
        const winnerMark = player.isWinner ? '🏆' : '';
        console.log(`  ${player.name || 'Unknown'} ${winnerMark}:`);
        console.log(`    Total Score: ${player.totalScore || 0} (Outer: ${player.outerScore || 0}, Inner: ${player.innerScore || 0})`);
        console.log(`    OM Tokens: ${player.omTotal || 0}`);
        console.log(`    Highest Om Space: ${player.highestOmSpace || 0}`);
        console.log(`    Om Track Events: ${player.omTrackEvents || 0}`);
        
        // Energy cubes breakdown
        if (player.energyCubes) {
          const total = player.energyCubes.total || 
            ((player.energyCubes.bhakti || 0) + 
             (player.energyCubes.gnana || 0) + 
             (player.energyCubes.karma || 0) + 
             (player.energyCubes.artha || 0));
          
          console.log(`    Energy Cubes: ${total} total`);
          console.log(`      Bhakti: ${player.energyCubes.bhakti || 0}, Gnana: ${player.energyCubes.gnana || 0}, Karma: ${player.energyCubes.karma || 0}, Artha: ${player.energyCubes.artha || 0}`);
        }
        
        // Character info (new!)
        if (player.character) {
          console.log(`    Character: ${player.character.name || 'Unknown'}`);
          if (player.character.ability) {
            console.log(`      Ability: ${player.character.ability}`);
          }
        }
        
        // Vehicle info (new!)
        if (Array.isArray(player.vehicles) && player.vehicles.length > 0) {
          console.log(`    Vehicles: ${player.vehicles.length}`);
          player.vehicles.forEach((vehicle, idx) => {
            console.log(`      ${idx+1}. ${vehicle.name || 'Unknown'} (${vehicle.type || 'unknown type'})`);
          });
        }
        
        // Journey cards
        if (Array.isArray(player.journeyCards) && player.journeyCards.length > 0) {
          console.log(`    Journey Cards Collected: ${player.journeyCards.length}`);
          player.journeyCards.forEach((card, idx) => {
            if (card && card.locationId) {
              console.log(`      ${idx+1}. Location #${card.locationId}: ${card.id || 'Unknown'}`);
            }
          });
        } else {
          console.log(`    Journey Cards Collected: 0`);
        }
        
        // Total hops
        console.log(`    Total Hops: ${player.totalHops || 0}`);
      });
    } else {
      console.log('  No player data available');
    }
    
    // Event summary
    const eventCounts = {};
    if (gameResult.events && Array.isArray(gameResult.events)) {
      gameResult.events.forEach(event => {
        if (event && event.type) {
          eventCounts[event.type] = (eventCounts[event.type] || 0) + 1;
        }
      });
      
      console.log('\nEvent Summary:');
      Object.entries(eventCounts).forEach(([type, count]) => {
        console.log(`  ${type}: ${count}`);
      });
      
      // Om Track Events summary
      const omTrackEvents = gameResult.events.filter(event => 
        event && (event.type === 'placeOmToken' || event.type === 'omTrackUpdate')
      );
      
      if (omTrackEvents.length > 0) {
        console.log('\nOm Track Events:');
        omTrackEvents.forEach((event, idx) => {
          if (event.data) {
            const playerName = gameResult.players.find(p => p.id === event.playerId)?.name || 'Unknown';
            console.log(`  ${idx+1}. ${playerName}: Space ${event.data.omSpace || '?'}, Stack ${event.data.stackPosition || '?'}`);
          }
        });
      }
      
      // Global events summary (new!)
      const globalEvents = gameResult.events.filter(event => 
        event && event.type === 'globalEvent'
      );
      
      if (globalEvents.length > 0) {
        console.log('\nGlobal Events:');
        globalEvents.forEach((event, idx) => {
          if (event.data && event.data.event) {
            console.log(`  ${idx+1}. ${event.data.event.name || 'Unknown'} (${event.data.event.type || 'unknown type'})`);
          }
        });
      }
    } else {
      console.log('\nNo event data available');
    }
    
    // Om Track final state
    if (gameResult.omTrack && Array.isArray(gameResult.omTrack)) {
      console.log('\nOm Track Final State:');
      gameResult.omTrack.forEach((stack, space) => {
        if (stack && stack.length > 0) {
          const playerNames = stack.map(playerId => {
            return gameResult.players.find(p => p.id === playerId)?.name || 'Unknown';
          }).join(', ');
          console.log(`  Space ${space}: ${playerNames}`);
        }
      });
    }
  }
  
  /**
   * Print a summary report for all completed games
   */
  printSummaryReport() {
    console.log('\n===== Summary Report =====');
    
    // Strategy win rates
    const strategyWins = {};
    const totalGames = this.gameSummaries.length;
    
    this.gameSummaries.forEach(summary => {
      if (summary.winner) {
        const winningStrategy = this.options.strategies[summary.players.findIndex(p => p.id === summary.winner.id)];
        strategyWins[winningStrategy] = (strategyWins[winningStrategy] || 0) + 1;
      }
    });
    
    console.log('Strategy Performance:');
    Object.entries(strategyWins).forEach(([strategy, wins]) => {
      const winRate = (wins / totalGames * 100).toFixed(1);
      console.log(`  ${strategy}: ${wins} wins (${winRate}% win rate)`);
    });
    
    // Rounds - min, max (as requested in bot-todos.txt)
    const rounds = this.gameSummaries.map(summary => summary.roundCount);
    const minRounds = Math.min(...rounds);
    const maxRounds = Math.max(...rounds);
    const avgRounds = (rounds.reduce((sum, count) => sum + count, 0) / totalGames).toFixed(1);
    console.log(`\nRounds: Min=${minRounds}, Max=${maxRounds}, Avg=${avgRounds}`);
    
    // End game Score difference - avg, min, max (as requested in bot-todos.txt)
    const scoreDifferences = this.gameSummaries.map(summary => {
      // Sort players by total score in descending order
      const sortedPlayers = [...summary.players].sort((a, b) => b.totalScore - a.totalScore);
      // Calculate difference between winner and runner-up (if there's at least 2 players)
      return sortedPlayers.length >= 2 ? sortedPlayers[0].totalScore - sortedPlayers[1].totalScore : 0;
    });
    
    const minScoreDiff = Math.min(...scoreDifferences);
    const maxScoreDiff = Math.max(...scoreDifferences);
    const avgScoreDiff = (scoreDifferences.reduce((sum, diff) => sum + diff, 0) / totalGames).toFixed(1);
    console.log(`\nEnd Game Score Difference: Min=${minScoreDiff}, Max=${maxScoreDiff}, Avg=${avgScoreDiff}`);
    
    // Score distributions
    const scores = {
      outer: this.gameSummaries.flatMap(summary => summary.players.map(p => p.outerScore)),
      inner: this.gameSummaries.flatMap(summary => summary.players.map(p => p.innerScore)),
      total: this.gameSummaries.flatMap(summary => summary.players.map(p => p.totalScore))
    };
    
    const stats = {
      outer: this.calculateStats(scores.outer),
      inner: this.calculateStats(scores.inner),
      total: this.calculateStats(scores.total)
    };
    
    console.log('\nScore Statistics:');
    console.log(`  Outer Journey: Avg=${stats.outer.avg.toFixed(1)}, Min=${stats.outer.min}, Max=${stats.outer.max}`);
    console.log(`  Inner Journey: Avg=${stats.inner.avg.toFixed(1)}, Min=${stats.inner.min}, Max=${stats.inner.max}`);
    console.log(`  Total Score: Avg=${stats.total.avg.toFixed(1)}, Min=${stats.total.min}, Max=${stats.total.max}`);
    
    // Om track statistics
    const omSpaces = this.gameSummaries.flatMap(summary => 
      summary.players.map(p => p.highestOmSpace || 0)
    );
    const omSpaceStats = this.calculateStats(omSpaces);
    
    console.log('\nOm Track Statistics:');
    console.log(`  Highest Space: Avg=${omSpaceStats.avg.toFixed(1)}, Min=${omSpaceStats.min}, Max=${omSpaceStats.max}`);
    
    const omEvents = this.gameSummaries.flatMap(summary => 
      summary.players.map(p => p.omTrackEvents || 0)
    );
    const omEventStats = this.calculateStats(omEvents);
    console.log(`  Om Track Events: Avg=${omEventStats.avg.toFixed(1)}, Min=${omEventStats.min}, Max=${omEventStats.max}`);
    
    // Journey card count by type - avg, min, max (as requested in bot-todos.txt)
    console.log('\nJourney Card Count by Type:');
    
    // Collect journey card data from all games
    const journeyCardsByType = {};
    this.gameSummaries.forEach(summary => {
      summary.players.forEach(player => {
        // Ensure journeyCards is an array before trying to iterate over it
        const journeyCards = player.journeyCards;
        if (journeyCards && Array.isArray(journeyCards)) {
          journeyCards.forEach(card => {
            if (card && card.type) {
              const type = card.type;
              if (!journeyCardsByType[type]) {
                journeyCardsByType[type] = [];
              }
              journeyCardsByType[type].push(1);
            }
          });
        } else if (journeyCards && typeof journeyCards === 'object' && !Array.isArray(journeyCards)) {
          // If journeyCards is an object but not an array, handle it differently
          console.log(`[Warning] Player ${player.name || 'unknown'} has journeyCards as an object instead of array`);
          
          // Try to extract journey cards if it's a collection of objects
          Object.values(journeyCards).forEach(card => {
            if (card && card.type) {
              const type = card.type;
              if (!journeyCardsByType[type]) {
                journeyCardsByType[type] = [];
              }
              journeyCardsByType[type].push(1);
            }
          });
        }
      });
    });
    
    // Calculate stats for each journey card type
    Object.entries(journeyCardsByType).forEach(([type, counts]) => {
      const typeStats = this.calculateStats(counts);
      console.log(`  ${type}: Avg=${typeStats.avg.toFixed(1)}, Min=${typeStats.min}, Max=${typeStats.max}`);
    });
    
    // Total Bonus scores (i.e event based) collected - avg, min, max (as requested in bot-todos.txt)
    const bonusScores = this.gameSummaries.flatMap(summary => {
      return summary.players.map(player => {
        return player.bonusScore || 0;
      });
    });
    
    const bonusStats = this.calculateStats(bonusScores);
    console.log('\nBonus Scores (Event Based):');
    console.log(`  Avg=${bonusStats.avg.toFixed(1)}, Min=${bonusStats.min}, Max=${bonusStats.max}`);
    
    // Dry turns - avg, min, max (as requested in bot-todos.txt)
    const dryTurns = this.gameSummaries.flatMap(summary => {
      return summary.players.map(player => {
        return player.dryTurns || 0;
      });
    });
    
    const dryTurnStats = this.calculateStats(dryTurns);
    console.log('\nDry Turns (No Meaningful Move):');
    console.log(`  Avg=${dryTurnStats.avg.toFixed(1)}, Min=${dryTurnStats.min}, Max=${dryTurnStats.max}`);
    
    // Energy cubes statistics
    const energyCubes = {
      bhakti: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.bhakti || 0)),
      gnana: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.gnana || 0)),
      karma: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.karma || 0)),
      artha: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.artha || 0)),
      total: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.total || 0))
    };
    
    const energyStats = {
      bhakti: this.calculateStats(energyCubes.bhakti),
      gnana: this.calculateStats(energyCubes.gnana),
      karma: this.calculateStats(energyCubes.karma),
      artha: this.calculateStats(energyCubes.artha),
      total: this.calculateStats(energyCubes.total)
    };
    
    console.log('\nEnergy Cubes Collected:');
    console.log(`  Total: Avg=${energyStats.total.avg.toFixed(1)}, Min=${energyStats.total.min}, Max=${energyStats.total.max}`);
    console.log(`  Bhakti: Avg=${energyStats.bhakti.avg.toFixed(1)}, Min=${energyStats.bhakti.min}, Max=${energyStats.bhakti.max}`);
    console.log(`  Gnana: Avg=${energyStats.gnana.avg.toFixed(1)}, Min=${energyStats.gnana.min}, Max=${energyStats.gnana.max}`);
    console.log(`  Karma: Avg=${energyStats.karma.avg.toFixed(1)}, Min=${energyStats.karma.min}, Max=${energyStats.karma.max}`);
    console.log(`  Artha: Avg=${energyStats.artha.avg.toFixed(1)}, Min=${energyStats.artha.min}, Max=${energyStats.artha.max}`);
    
    // Journey cards collected
    const journeyCardsCount = this.gameSummaries.flatMap(summary => 
      summary.players.map(p => {
        // Ensure journeyCards is an array before accessing length
        if (!p.journeyCards) return 0;
        if (Array.isArray(p.journeyCards)) return p.journeyCards.length;
        if (typeof p.journeyCards === 'object') return Object.keys(p.journeyCards).length;
        return 0;
      })
    );
    const journeyStats = this.calculateStats(journeyCardsCount);
    
    console.log('\nJourney Cards Collected:');
    console.log(`  Total: Avg=${journeyStats.avg.toFixed(1)}, Min=${journeyStats.min}, Max=${journeyStats.max}`);
    
    // Hops statistics
    const hops = this.gameSummaries.flatMap(summary => 
      summary.players.map(p => p.totalHops || 0)
    );
    const hopStats = this.calculateStats(hops);
    
    console.log('\nTotal Hops (Movement):');
    console.log(`  Avg=${hopStats.avg.toFixed(1)}, Min=${hopStats.min}, Max=${hopStats.max}`);
    
    // Per-strategy statistics
    console.log('\nPer-Strategy Statistics:');
    this.options.strategies.forEach((strategy, index) => {
      const strategyPlayers = this.gameSummaries.flatMap(summary => 
        summary.players.filter((_, playerIndex) => playerIndex === index)
      );
      
      if (strategyPlayers.length > 0) {
        const strategyHops = strategyPlayers.map(p => p.totalHops || 0);
        const strategyJourneyCards = strategyPlayers.map(p => p.journeyCards?.length || 0);
        const strategyEnergyCubes = strategyPlayers.map(p => p.energyCubes?.total || 0);
        const strategyBonusScores = strategyPlayers.map(p => p.bonusScore || 0);
        const strategyDryTurns = strategyPlayers.map(p => p.dryTurns || 0);
        
        const avgHops = strategyHops.reduce((sum, h) => sum + h, 0) / strategyPlayers.length;
        const avgJourneyCards = strategyJourneyCards.reduce((sum, c) => sum + c, 0) / strategyPlayers.length;
        const avgEnergyCubes = strategyEnergyCubes.reduce((sum, c) => sum + c, 0) / strategyPlayers.length;
        const avgBonusScores = strategyBonusScores.reduce((sum, c) => sum + c, 0) / strategyPlayers.length;
        const avgDryTurns = strategyDryTurns.reduce((sum, c) => sum + c, 0) / strategyPlayers.length;
        
        console.log(`  ${strategy}:`);
        console.log(`    Avg Hops: ${avgHops.toFixed(1)}`);
        console.log(`    Avg Journey Cards: ${avgJourneyCards.toFixed(1)}`);
        console.log(`    Avg Energy Cubes: ${avgEnergyCubes.toFixed(1)}`);
        console.log(`    Avg Bonus Scores: ${avgBonusScores.toFixed(1)}`);
        console.log(`    Avg Dry Turns: ${avgDryTurns.toFixed(1)}`);
      }
    });
  }
  
  /**
   * Calculate basic statistics for an array of numbers
   * @param {Array<number>} values - The values to analyze
   * @returns {object} Statistics object with min, max, avg
   */
  calculateStats(values) {
    const min = Math.min(...values);
    const max = Math.max(...values);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    
    return { min, max, avg };
  }
  
  /**
   * Check if the server is running and gather version information
   * @returns {Promise<object>} Server status information
   */
  async checkServer() {
    try {
      console.log('Checking if the server is running...');
      
      let serverInfo = {
        running: false,
        version: 'unknown',
        features: {
          globalEvents: false,
          playerCharacters: false,
          vehicles: false
        },
        endpoints: {
          configure: '/api/simulation/configure',
          addBot: '/api/simulation/add-bot', 
          start: '/api/simulation/start',
          status: '/api/simulation/status',
          metrics: '/api/metrics',
          events: '/api/events'
        }
      };
      
      // Try to get health information
      try {
        const healthResponse = await fetch(`${API_BASE}/health`, {
          method: 'GET',
          headers: { 'Accept': 'application/json' }
        });
        
        if (healthResponse.ok) {
          const healthData = await healthResponse.json();
          serverInfo.running = true;
          serverInfo.version = healthData.version || 'unknown';
          console.log(`Server is running (version: ${serverInfo.version})`);
        } else {
          console.warn(`Server health check failed with status: ${healthResponse.status}`);
        }
      } catch (error) {
        // Try a fallback endpoint if health fails
        try {
          const statusResponse = await fetch(`${API_BASE}/status`, { 
            method: 'GET',
            headers: { 'Accept': 'application/json' }
          });
          
          if (statusResponse.ok) {
            serverInfo.running = true;
            console.log('Server is running (status endpoint)');
          }
        } catch (statusError) {
          console.warn('Server status check also failed');
        }
      }
      
      if (!serverInfo.running) {
        throw new Error('Server does not appear to be running');
      }
      
      // Detect available routes with OPTIONS request
      try {
        const routeResponse = await fetch(`${API_BASE}/simulation`, { 
          method: 'OPTIONS',
          headers: { 'Accept': 'application/json' }
        });
        
        if (routeResponse.ok) {
          // Some servers support OPTIONS to check available endpoints
          const allowHeader = routeResponse.headers.get('Allow');
          if (allowHeader) {
            console.log(`Available methods: ${allowHeader}`);
          }
        }
      } catch (error) {
        // Ignore OPTIONS errors
      }
      
      // Detect server API version
      if (serverInfo.version === 'unknown') {
        // Try to infer version from what endpoints are available
        try {
          const featureCheckResponse = await fetch(`${API_BASE}/features`, { 
            method: 'GET',
            headers: { 'Accept': 'application/json' }
          });
          
          if (featureCheckResponse.ok) {
            const featureData = await featureCheckResponse.json();
            serverInfo.features = {
              ...serverInfo.features,
              ...featureData
            };
            console.log('Server supports feature detection');
          }
        } catch (error) {
          // Older server without feature detection
          console.log('Server does not support feature detection, using defaults');
        }
      }
      
      // Store the server information for later use
      this.serverInfo = serverInfo;
      
      return serverInfo;
    } catch (error) {
      console.error(`Server check failed: ${error.message}`);
      throw new Error(`Server check failed: ${error.message}`);
    }
  }
  
  /**
   * Get available strategies
   */
  async getStrategies() {
    const response = await fetch(`${API_BASE}/simulation/strategies`);
    const data = await response.json();
    return data.strategies;
  }
  
  /**
   * Configure the simulation settings
   * @param {object} config - Configuration options
   * @returns {Promise<object>} The updated configuration
   */
  async configureSimulation(config) {
    try {
      console.log(`Configuring simulation with options:`, JSON.stringify(config, null, 2));
      
      // Use a more robust fetch approach with retries
      let response;
      let retryCount = 0;
      const maxRetries = 3;
      
      while (retryCount < maxRetries) {
        try {
          response = await fetch(`${API_BASE}/simulation/configure`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(config),
          });
          
          // Check if the response is ok
          if (!response.ok) {
            const statusText = response.statusText;
            console.warn(`Server returned ${response.status} ${statusText} when configuring simulation. Retry ${retryCount + 1}/${maxRetries}`);
            
            // Try to get the response body to give more information
            let errorBody;
            try {
              const contentType = response.headers.get('content-type');
              if (contentType && contentType.includes('application/json')) {
                errorBody = await response.json();
              } else {
                errorBody = await response.text();
                if (errorBody.length > 100) {
                  // Truncate long error responses (like HTML pages)
                  errorBody = errorBody.substring(0, 100) + '... [truncated]';
                }
              }
              console.error(`Error response body: ${typeof errorBody === 'string' ? errorBody : JSON.stringify(errorBody)}`);
            } catch (parseError) {
              console.error(`Error parsing error response: ${parseError.message}`);
            }
            
            // For 5xx errors, retry
            if (response.status >= 500) {
              retryCount++;
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
              continue;
            }
            
            // For 4xx errors, fail immediately
            throw new Error(`Server returned ${response.status} ${statusText}`);
          }
          
          // We got a successful response, break out of the retry loop
          break;
        } catch (fetchError) {
          // Network or other fetch errors
          console.error(`Fetch error (attempt ${retryCount + 1}/${maxRetries}): ${fetchError.message}`);
          
          // Check if server is running
          try {
            const healthResponse = await fetch(`${API_BASE}/health`, { method: 'GET' });
            if (healthResponse.ok) {
              console.log('Server is running but configure endpoint failed');
            }
          } catch (healthError) {
            console.error(`Server health check failed: ${healthError.message}`);
            console.log('Server may not be running. Please make sure server is started on port 4000.');
          }
          
          retryCount++;
          if (retryCount >= maxRetries) {
            throw fetchError;
          }
          
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
        }
      }
      
      // Parse the response
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Failed to configure simulation: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Add a bot to the simulation
   * @param {string} name - Bot name
   * @param {string} strategy - Bot strategy
   * @returns {Promise<object>} Bot configuration
   */
  async addBot(name, strategy) {
    try {
      console.log(`Adding bot ${name} with ${strategy} strategy...`);
      
      // Try the add-bot endpoint first, then fall back to the older endpoint if needed
      let response = null;
      let endpointTried = '';
      
      // Try the first endpoint path (newer)
      try {
        endpointTried = `${API_BASE}/simulation/add-bot`;
        response = await fetch(endpointTried, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ name, strategy }),
        });
      } catch (error) {
        console.warn(`Error using endpoint ${endpointTried}: ${error.message}`);
        
        // If the first endpoint fails, try the older endpoint
        endpointTried = `${API_BASE}/simulation/addBot`;
        response = await fetch(endpointTried, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ name, strategy: strategy }), // Use the older property name too
        });
      }
      
      if (!response || !response.ok) {
        // Try one last endpoint variation
        if (!response || response.status === 404) {
          endpointTried = `${API_BASE}/simulation/bot`;
          response = await fetch(endpointTried, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name, strategyName: strategy }), // Match the expected property name
          });
        }
      }
      
      if (!response || !response.ok) {
        // Try to get more detailed error information
        let errorMessage = response ? `Server returned ${response.status} ${response.statusText}` : 'No response received';
        try {
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json();
            errorMessage = errorData.message || errorData.error || errorMessage;
          }
        } catch (e) {
          // Ignore error parsing errors
        }
        
        throw new Error(`Failed to add bot: ${errorMessage}`);
      }
      
      // Process the response
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        
        // Handle different response formats
        if (data.bot) {
          // Legacy format
          return data.bot;
        } else if (data.id) {
          // Direct bot object return
          return data;
        } else {
          // Generic success response
          return { 
            name, 
            strategy, 
            id: `bot-${Math.floor(Math.random() * 10000)}`,
            ...data 
          };
        }
      } else {
        // Unexpected content type
        console.warn(`Unexpected content type from add bot endpoint: ${contentType}`);
        return { 
          name, 
          strategy, 
          id: `bot-${Math.floor(Math.random() * 10000)}`
        };
      }
    } catch (error) {
      console.error(`Failed to add bot ${name}: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Start the simulation
   * @returns {Promise<object>} Start result
   */
  async startSimulation() {
    try {
      console.log('Starting simulation...');
      
      // Try each possible endpoint path until one works
      const possibleEndpoints = [
        `${API_BASE}/simulation/start`,      // Primary endpoint
        `${API_BASE}/simulation/start-game`, // Alternative endpoint
        `${API_BASE}/start-simulation`       // Legacy endpoint
      ];
      
      let response = null;
      let endpointTried = '';
      let error = null;
      
      // Try each endpoint in turn
      for (const endpoint of possibleEndpoints) {
        try {
          endpointTried = endpoint;
          response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({}), // Empty body - all config should have been done already
          });
          
          // If we got a response, break out of the loop
          if (response && response.ok) {
            console.log(`Successfully started simulation using endpoint: ${endpoint}`);
            break;
          } else if (response) {
            // If we got a response but it wasn't OK, log it and try the next endpoint
            console.warn(`Endpoint ${endpoint} returned ${response.status}`);
          }
        } catch (endpointError) {
          // Store the error but keep trying other endpoints
          console.warn(`Error with endpoint ${endpoint}: ${endpointError.message}`);
          error = endpointError;
        }
      }
      
      // If we didn't get a successful response from any endpoint, throw an error
      if (!response || !response.ok) {
        let errorMessage = 'Failed to start simulation. Tried endpoints:';
        possibleEndpoints.forEach(ep => {
          errorMessage += `\n  - ${ep}`;
        });
        
        if (error) {
          errorMessage += `\nLast error: ${error.message}`;
        }
        
        throw new Error(errorMessage);
      }
      
      // Parse the response
      try {
        const data = await response.json();
        return data;
      } catch (parseError) {
        console.warn(`Error parsing start response: ${parseError.message}`);
        // Return a generic success response if we can't parse JSON
        return { status: 'ok', message: 'Simulation started' };
      }
    } catch (error) {
      console.error(`Failed to start simulation: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get the current simulation status
   */
  async getStatus() {
    const response = await fetch(`${API_BASE}/simulation/status`);
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.error || 'Failed to get simulation status');
    }
    return data;
  }
  
  /**
   * Get game metrics
   */
  async getMetrics() {
    const response = await fetch(`${API_BASE}/metrics`);
    const data = await response.json();
    if (!response.ok) {
      throw new Error(data.error || 'Failed to get metrics');
    }
    return data;
  }
  
  /**
   * Get game events
   * @param {object} options - Filter options
   * @returns {Promise<object>} Event data
   */
  async getEvents(options = {}) {
    try {
      // Build query string from options
      let queryString = '';
      if (options) {
        const params = new URLSearchParams();
        
        // Ensure limit is always set to prevent memory issues
        params.set('limit', options.limit || MAX_EVENTS);
        
        if (options.type) params.set('type', options.type);
        if (options.playerId) params.set('playerId', options.playerId);
        
        queryString = `?${params.toString()}`;
      }
      
      const response = await fetch(`${API_BASE}/events${queryString}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch events: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to get events:', error);
      return { events: [] };
    }
  }
  
  /**
   * Stop the simulation
   */
  async stopSimulation() {
    try {
      console.log('Stopping simulation and waiting for cleanup...');
      const response = await fetch(`${API_BASE}/simulation/stop`, {
        method: 'POST'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to stop simulation: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      // Wait a bit for the server to fully clean up resources
      console.log('Waiting for server cleanup...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Verify the simulation is actually stopped
      try {
        const status = await this.getStatus();
        console.log(`Simulation status after stop: ${status.status}`);
        if (status.status === 'running') {
          console.warn('Warning: Simulation reports still running after stop command');
        }
      } catch (statusError) {
        console.warn(`Could not verify simulation status: ${statusError.message}`);
      }
      
      return data.results;
    } catch (error) {
      console.warn(`Warning: Error stopping simulation: ${error.message}`);
      // We still want to continue even if stop fails
      return null;
    }
  }
  
  /**
   * Monitor events for card-related actions
   * @param {Array} events - The game events
   * @private
   */
  _monitorCardEvents(events) {
    if (!Array.isArray(events)) return;
    
    // Filter for card-related events
    const cardEvents = events.filter(event => {
      return event && (
        event.type === 'pickCards' || 
        event.type === 'cardsPicked' || 
        event.type === 'handUpdated' ||
        event.type.includes('card')
      );
    });
    
    // Log the card events
    if (cardEvents.length > 0) {
      console.log(`[CARD-DEBUG] Found ${cardEvents.length} card-related events:`);
      cardEvents.forEach(event => {
        console.log(`[CARD-DEBUG] Event: ${event.type}, Player: ${event.player}, Details:`, 
          JSON.stringify(event.details || {}).substring(0, 200));
      });
    }
  }

  /**
   * Save the simulation report as a JSON file
   */
  async saveSimulationReportAsJson() {
    try {
      // Create the simulation_reports directory if it doesn't exist
      if (!fs.existsSync(SIMULATION_REPORTS_DIR)) {
        console.log(`Creating simulation reports directory: ${SIMULATION_REPORTS_DIR}`);
        fs.mkdirSync(SIMULATION_REPORTS_DIR, { recursive: true });
      }

      // Create report object
      const report = {
        timestamp: new Date().toISOString(),
        options: this.options,
        gameSummaries: this.gameSummaries,
        statistics: this.generateStatisticsReport()
      };

      // Generate a filename based on timestamp and strategies
      const strategiesStr = this.options.strategies.join('-');
      const numBots = this.options.strategies.length;
      const numGames = this.options.games;
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `sim_${numBots}bots_${strategiesStr}_${numGames}games_${timestamp}.json`;
      const filepath = path.join(SIMULATION_REPORTS_DIR, filename);

      // Write the file
      console.log(`Saving simulation report to: ${filepath}`);
      fs.writeFileSync(filepath, JSON.stringify(report, null, 2), 'utf8');
      console.log('Simulation report saved successfully!');

      return filepath;
    } catch (error) {
      console.error('Error saving simulation report:', error.message);
      return null;
    }
  }

  /**
   * Generate a statistics report object for JSON serialization
   */
  generateStatisticsReport() {
    const totalGames = this.gameSummaries.length;
    if (totalGames === 0) {
      return { error: 'No completed games' };
    }

    // Strategy win rates
    const strategyWins = {};
    this.gameSummaries.forEach(summary => {
      if (summary.winner) {
        const winningStrategy = this.options.strategies[summary.players.findIndex(p => p.id === summary.winner.id)];
        strategyWins[winningStrategy] = (strategyWins[winningStrategy] || 0) + 1;
      }
    });

    const strategyPerformance = Object.entries(strategyWins).map(([strategy, wins]) => {
      const winRate = (wins / totalGames * 100).toFixed(1);
      return {
        strategy,
        wins,
        winRate: parseFloat(winRate)
      };
    });

    // Rounds statistics
    const rounds = this.gameSummaries.map(summary => summary.roundCount);
    const roundStats = this.calculateStats(rounds);

    // Score difference statistics
    const scoreDifferences = this.gameSummaries.map(summary => {
      const sortedPlayers = [...summary.players].sort((a, b) => b.totalScore - a.totalScore);
      return sortedPlayers.length >= 2 ? sortedPlayers[0].totalScore - sortedPlayers[1].totalScore : 0;
    });
    const scoreDiffStats = this.calculateStats(scoreDifferences);

    // Score distributions
    const scores = {
      outer: this.gameSummaries.flatMap(summary => summary.players.map(p => p.outerScore)),
      inner: this.gameSummaries.flatMap(summary => summary.players.map(p => p.innerScore)),
      total: this.gameSummaries.flatMap(summary => summary.players.map(p => p.totalScore))
    };

    const scoreStats = {
      outer: this.calculateStats(scores.outer),
      inner: this.calculateStats(scores.inner),
      total: this.calculateStats(scores.total)
    };

    // Journey card statistics
    const journeyCardsByType = {};
    this.gameSummaries.forEach(summary => {
      summary.players.forEach(player => {
        // Ensure journeyCards is an array
        const journeyCards = player.journeyCards;
        if (journeyCards && Array.isArray(journeyCards)) {
          journeyCards.forEach(card => {
            if (card && card.type) {
              const type = card.type;
              if (!journeyCardsByType[type]) {
                journeyCardsByType[type] = [];
              }
              journeyCardsByType[type].push(1);
            }
          });
        } else if (journeyCards && typeof journeyCards === 'object' && !Array.isArray(journeyCards)) {
          Object.values(journeyCards).forEach(card => {
            if (card && card.type) {
              const type = card.type;
              if (!journeyCardsByType[type]) {
                journeyCardsByType[type] = [];
              }
              journeyCardsByType[type].push(1);
            }
          });
        }
      });
    });

    const journeyCardStats = {};
    Object.entries(journeyCardsByType).forEach(([type, counts]) => {
      journeyCardStats[type] = this.calculateStats(counts);
    });

    // Bonus scores
    const bonusScores = this.gameSummaries.flatMap(summary => {
      return summary.players.map(player => player.bonusScore || 0);
    });
    const bonusScoreStats = this.calculateStats(bonusScores);

    // Dry turns
    const dryTurns = this.gameSummaries.flatMap(summary => {
      return summary.players.map(player => player.dryTurns || 0);
    });
    const dryTurnStats = this.calculateStats(dryTurns);

    // Energy cubes
    const energyCubes = {
      bhakti: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.bhakti || 0)),
      gnana: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.gnana || 0)),
      karma: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.karma || 0)),
      artha: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.artha || 0)),
      total: this.gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.total || 0))
    };

    const energyStats = {
      bhakti: this.calculateStats(energyCubes.bhakti),
      gnana: this.calculateStats(energyCubes.gnana),
      karma: this.calculateStats(energyCubes.karma),
      artha: this.calculateStats(energyCubes.artha),
      total: this.calculateStats(energyCubes.total)
    };

    // Journey cards collected
    const journeyCardsCount = this.gameSummaries.flatMap(summary => 
      summary.players.map(p => {
        if (!p.journeyCards) return 0;
        if (Array.isArray(p.journeyCards)) return p.journeyCards.length;
        if (typeof p.journeyCards === 'object') return Object.keys(p.journeyCards).length;
        return 0;
      })
    );
    const journeyCollectionStats = this.calculateStats(journeyCardsCount);

    // Total hops
    const hops = this.gameSummaries.flatMap(summary => 
      summary.players.map(p => p.totalHops || 0)
    );
    const hopStats = this.calculateStats(hops);

    // Per-strategy statistics
    const perStrategyStats = {};
    this.options.strategies.forEach((strategy, index) => {
      const strategyPlayers = this.gameSummaries.flatMap(summary => 
        summary.players.filter((_, playerIndex) => playerIndex === index)
      );
      
      if (strategyPlayers.length > 0) {
        const strategyHops = strategyPlayers.map(p => p.totalHops || 0);
        const strategyJourneyCards = strategyPlayers.map(p => {
          if (!p.journeyCards) return 0;
          if (Array.isArray(p.journeyCards)) return p.journeyCards.length;
          if (typeof p.journeyCards === 'object') return Object.keys(p.journeyCards).length;
          return 0;
        });
        const strategyEnergyCubes = strategyPlayers.map(p => p.energyCubes?.total || 0);
        const strategyBonusScores = strategyPlayers.map(p => p.bonusScore || 0);
        const strategyDryTurns = strategyPlayers.map(p => p.dryTurns || 0);
        
        perStrategyStats[strategy] = {
          hops: this.calculateStats(strategyHops),
          journeyCards: this.calculateStats(strategyJourneyCards),
          energyCubes: this.calculateStats(strategyEnergyCubes),
          bonusScores: this.calculateStats(strategyBonusScores),
          dryTurns: this.calculateStats(strategyDryTurns)
        };
      }
    });

    return {
      strategyPerformance,
      rounds: roundStats,
      scoreDifference: scoreDiffStats,
      scores: scoreStats,
      journeyCardsByType: journeyCardStats,
      bonusScores: bonusScoreStats,
      dryTurns: dryTurnStats,
      energyCubes: energyStats,
      journeyCardsCollected: journeyCollectionStats,
      totalHops: hopStats,
      perStrategyStats
    };
  }
}

/**
 * Parse command line arguments
 * @returns {object} Parsed options
 */
function parseCommandLineArgs() {
  const options = {
    games: 1,
    speed: 10,
    strategies: ['defensive', 'defensive'],
    save: true,
    memory: true
  };
  
  // Process each argument
  process.argv.slice(2).forEach(arg => {
    if (arg.startsWith('--games=')) {
      options.games = parseInt(arg.split('=')[1], 10);
    } else if (arg.startsWith('--speed=')) {
      options.speed = parseFloat(arg.split('=')[1]);
    } else if (arg.startsWith('--strategies=')) {
      options.strategies = arg.split('=')[1].split(',');
    } else if (arg.startsWith('--save=')) {
      options.save = arg.split('=')[1].toLowerCase() === 'true';
    } else if (arg.startsWith('--memory=')) {
      options.memory = arg.split('=')[1].toLowerCase() === 'true';
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
Usage: node simulation_client.js [options]

Options:
  --games=N         Number of games to simulate (default: 1)
  --speed=N         Simulation speed multiplier (default: 10)
  --strategies=X,Y  Bot strategies to use (default: random,random)
  --save=true|false Save results to files (default: true)
  --memory=true|false Monitor memory usage (default: true)
  --help, -h        Show this help message
      `);
      process.exit(0);
    }
  });
  
  return options;
}

// If this script is run directly, run the simulation
if (require.main === module) {
  const options = parseCommandLineArgs();
  const client = new SimulationClient(options);
  client.runSimulations().catch(err => {
    console.error('Fatal error:', err);
    process.exit(1);
  });
}

module.exports = SimulationClient;