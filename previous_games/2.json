{"players": [{"id": "hdP-Cl85MRj9P8KsAAAD", "name": "<PERSON><PERSON>", "position": 29, "hand": [{"id": "T12", "type": "travel", "value": 3}], "energyCubes": ["bhakti", "gnana", "bhakti"], "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "outerScore": 54, "innerScore": 30, "collectedJourneys": [{"id": "JI9", "locationId": 16, "reward": {"inner": 30}}, {"id": "JO12", "locationId": 27, "reward": {"outer": 27}}, {"id": "JO3", "locationId": 6, "reward": {"outer": 27}}], "didMoveThisTurn": true, "didSelectionActionThisTurn": true}, {"id": "brUCucMwdc9B4shFAAAB", "name": "<PERSON><PERSON>", "position": 7, "hand": [{"id": "T13", "type": "travel", "value": 3}, {"id": "T5", "type": "travel", "value": 1}], "energyCubes": ["gnana"], "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 2, 0], "outerScore": 24, "innerScore": 77, "collectedJourneys": [{"id": "JI12", "locationId": 21, "reward": {"inner": 20}}, {"id": "JO2", "locationId": 5, "reward": {"outer": 24}}, {"id": "JI17", "locationId": 36, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "reward": {"inner": 27}}], "didMoveThisTurn": true, "didSelectionActionThisTurn": true}], "started": true, "turnIndex": 1, "roundCount": 19, "travelDeck": [{"id": "T15", "type": "travel", "value": 3}, {"id": "T3", "type": "travel", "value": 1}, {"id": "T8", "type": "travel", "value": 2}, {"id": "T6", "type": "travel", "value": 2}, {"id": "T11", "type": "travel", "value": 3}], "eventDeck": [{"id": "E6", "type": "wildCube"}, {"id": "E1", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "journeyDeckOuter": [{"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}], "travelDiscard": [{"id": "T14", "type": "travel", "value": 3}, {"id": "T10", "type": "travel", "value": 2}, {"id": "T7", "type": "travel", "value": 2}], "eventDiscard": [], "journeyInnerDiscard": [{"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "journeyOuterDiscard": [{"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "faceUpTravel": [{"id": "T9", "type": "travel", "value": 2}, {"id": "T2", "type": "travel", "value": 1}, {"id": "T4", "type": "travel", "value": 1}, {"id": "T1", "type": "travel", "value": 1}], "faceUpEvent": [{"id": "E4", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "faceUpJourneyOuter": [{"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "locationCubes": {"1": null, "2": null, "3": "gnana", "4": null, "5": null, "6": null, "7": null, "8": null, "9": null, "10": "artha", "11": "artha", "12": "gnana", "13": null, "14": "artha", "15": "karma", "16": null, "17": "artha", "18": "gnana", "19": null, "20": null, "21": null, "22": null, "23": "artha", "24": "bhakti", "25": null, "26": "bhakti", "27": null, "28": "karma", "29": null, "30": "karma", "31": null, "32": "gnana", "33": "gnana", "34": null, "35": "artha", "36": null, "37": null, "38": "gnana", "39": "artha", "40": "karma", "41": "karma", "42": "artha", "43": "artha", "44": "karma", "45": "bhakti", "46": "artha", "47": "bhakti", "48": "bhakti", "undefined": "artha"}, "locationOm": {"49": false, "50": false, "51": false, "52": false, "53": false, "54": false, "55": false, "56": false, "57": true, "58": false, "59": false, "60": true}, "finalRound": true, "finalRoundStarter": 1, "finalRoundEnd": 0, "gameEvents": [{"type": "DEAL_INITIAL_CARD", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743087762697, "data": {"cardType": "travel", "card": {"id": "T3", "type": "travel", "value": 1}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743087762697, "data": {"cardType": "travel", "card": {"id": "T8", "type": "travel", "value": 2}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743087762697, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743087762697, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743087792581, "data": {"cardType": "event", "pickedCards": [{"id": "E8", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743087810898, "data": {"location": 50}}, {"type": "move", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743087810898, "data": {"path": [62, 11, 12, 50], "travelCardIds": ["T3", "T8"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743087813722, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743087825804, "data": {"cardType": "event", "pickedCards": [{"id": "E9", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743088078188, "data": {"path": [65, 63, 20], "travelCardIds": ["T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743088081206, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743088081207, "data": {"roundNumber": 1, "playerCount": 2, "playerStats": [{"id": "Dd33QsDyeKQN3fRQAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 1, "energyCubesCount": 0, "collectedJourneysCount": 0}, {"id": "VyCbTNbtvsyg8hcvAAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743088162142, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2}, {"id": "T4", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743088166423, "data": {"path": [50, 12, 13], "travelCardIds": ["T10"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743088168520, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743088189996, "data": {"cardType": "event", "pickedCards": [{"id": "E7", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743088210624, "data": {"path": [20, 21, 22], "travelCardIds": ["T7"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743088213189, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743088213189, "data": {"roundNumber": 2, "playerCount": 2, "playerStats": [{"id": "Dd33QsDyeKQN3fRQAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 0}, {"id": "VyCbTNbtvsyg8hcvAAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 2, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743088307455, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}, {"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743088323818, "data": {"path": [13, 62, 61, 6], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743088326764, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743088347217, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1}, {"id": "T15", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743088354156, "data": {"path": [22, 21], "travelCardIds": ["T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "VyCbTNbtvsyg8hcvAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743088355875, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743088355875, "data": {"roundNumber": 3, "playerCount": 2, "playerStats": [{"id": "Dd33QsDyeKQN3fRQAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 0}, {"id": "VyCbTNbtvsyg8hcvAAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 0}]}}, {"type": "move", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743088375869, "data": {"path": [6, 61, 62, 19], "travelCardIds": ["T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Dd33QsDyeKQN3fRQAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743088377574, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 1, "timestamp": 1743090677911, "data": {"timestamp": 1743090677911, "loadedStateRoundCount": 3, "playerCount": 2}}, {"type": "collectOm", "playerId": "wb2cWAQD89v4WYb7AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743090687358, "data": {"location": 59}}, {"type": "move", "playerId": "wb2cWAQD89v4WYb7AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743090687358, "data": {"path": [21, 22, 23, 59], "travelCardIds": ["T15"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "wb2cWAQD89v4WYb7AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743090689315, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743090689315, "data": {"roundNumber": 4, "playerCount": 2, "playerStats": [{"id": "Zly3No2huvY8eC9aAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 0}, {"id": "wb2cWAQD89v4WYb7AAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Zly3No2huvY8eC9aAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743090698912, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 1}, {"id": "T12", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Zly3No2huvY8eC9aAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743090703834, "data": {"path": [19, 14, 16], "travelCardIds": ["T4", "T5"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Zly3No2huvY8eC9aAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743090707381, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 1, "timestamp": 1743090750053, "data": {"timestamp": 1743090750053, "loadedStateRoundCount": 4, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "zvlK-U4MTeOcsqFtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743090810858, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3}, {"id": "T9", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "zvlK-U4MTeOcsqFtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743090824761, "data": {"path": [59, 23, 22, 21], "travelCardIds": ["T14"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "zvlK-U4MTeOcsqFtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743090831781, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743090831781, "data": {"roundNumber": 5, "playerCount": 2, "playerStats": [{"id": "lwS_TRgzvJKbfL5cAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 4, "collectedJourneysCount": 0}, {"id": "zvlK-U4MTeOcsqFtAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 0}]}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743090870614, "data": {"timestamp": 1743090870614, "loadedStateRoundCount": 5, "playerCount": 2}}, {"type": "COLLECT_JOURNEY", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743090878971, "data": {"journeyCard": {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, "journeyType": "inner", "location": {"id": 16, "name": "Siddhivinayak Temple", "region": "West", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["gnana", "bhakti", "gnana", "artha"], "energyCubesAfter": ["artha", "karma"], "wildCubesUsed": 1, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 0, "innerScore": 30}}}}, {"type": "END_TURN", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743097114664, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "COLLECT_JOURNEY", "playerId": "UFZJGXeKGEZmzuv6AAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743097385674, "data": {"journeyCard": {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, "journeyType": "inner", "location": {"id": 21, "name": "Annamalaiyar Temple", "region": "South", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["bhakti", "bhakti", "karma"], "energyCubesAfter": ["bhakti", "karma"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 0, "innerScore": 20}}}}, {"type": "END_TURN", "playerId": "UFZJGXeKGEZmzuv6AAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743097387822, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743097387822, "data": {"roundNumber": 6, "playerCount": 2, "playerStats": [{"id": "ji2FK1i-LS5amI7UAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 1}, {"id": "UFZJGXeKGEZmzuv6AAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 20, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743097465828, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3}, {"id": "T7", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743097511053, "data": {"location": 51}}, {"type": "move", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743097511053, "data": {"path": [16, 14, 13, 62, 63, 20, 51], "travelCardIds": ["T12", "T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743097512845, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UFZJGXeKGEZmzuv6AAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743097606030, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 3}, {"id": "T2", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "UFZJGXeKGEZmzuv6AAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743097609555, "data": {"location": 52}}, {"type": "move", "playerId": "UFZJGXeKGEZmzuv6AAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743097609555, "data": {"path": [21, 28, 18, 15, 5, 52], "travelCardIds": ["T9", "T15"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "UFZJGXeKGEZmzuv6AAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743097611138, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743097611138, "data": {"roundNumber": 7, "playerCount": 2, "playerStats": [{"id": "ji2FK1i-LS5amI7UAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 1}, {"id": "UFZJGXeKGEZmzuv6AAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 20, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743097631192, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2}, {"id": "T1", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743097634484, "data": {"path": [51, 20, 63, 25], "travelCardIds": ["T7", "T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "ji2FK1i-LS5amI7UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743097636631, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 1, "timestamp": 1743097733511, "data": {"timestamp": 1743097733510, "loadedStateRoundCount": 7, "playerCount": 2}}, {"type": "move", "playerId": "Qcl179YF50c15dnJAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743097866646, "data": {"path": [52, 5], "travelCardIds": ["T2"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "Qcl179YF50c15dnJAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743097877319, "data": {"journeyCard": {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, "journeyType": "outer", "location": {"id": 5, "name": "Kurukshetra", "region": "Central", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["bhakti", "karma", "bhakti"], "energyCubesAfter": ["bhakti", "bhakti"], "wildCubesUsed": 1, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 20}, "newScore": {"outerScore": 24, "innerScore": 20}}}}, {"type": "END_TURN", "playerId": "Qcl179YF50c15dnJAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743097883896, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743097883896, "data": {"roundNumber": 8, "playerCount": 2, "playerStats": [{"id": "dtx2ccSmqUfGrr7EAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 2, "collectedJourneysCount": 1}, {"id": "Qcl179YF50c15dnJAAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 20, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 2, "collectedJourneysCount": 2}]}}, {"type": "move", "playerId": "dtx2ccSmqUfGrr7EAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743097917829, "data": {"path": [25, 26, 27], "travelCardIds": ["T10"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "dtx2ccSmqUfGrr7EAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743097960299, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Qcl179YF50c15dnJAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743098012491, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2}, {"id": "T4", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 1, "timestamp": 1743098271811, "data": {"timestamp": 1743098271811, "loadedStateRoundCount": 8, "playerCount": 2}}, {"type": "END_TURN", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743098640813, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743098640813, "data": {"roundNumber": 9, "playerCount": 2, "playerStats": [{"id": "rEvlNFxOLkhjEHp-AAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 0, "energyCubesCount": 3, "collectedJourneysCount": 1}, {"id": "G_-G_XFgPMkTUHWVAAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 20, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743098685059, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}, {"id": "T6", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743098691565, "data": {"path": [27, 16, 17, 34], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743098693319, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743098702525, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 3}, {"id": "T10", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743098727072, "data": {"location": 53}}, {"type": "move", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743098727072, "data": {"path": [5, 15, 18, 53], "travelCardIds": ["T15"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743098729227, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743098729227, "data": {"roundNumber": 10, "playerCount": 2, "playerStats": [{"id": "rEvlNFxOLkhjEHp-AAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 4, "collectedJourneysCount": 1}, {"id": "G_-G_XFgPMkTUHWVAAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 20, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743098745181, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3}, {"id": "T3", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743098750784, "data": {"path": [34, 17, 16, 27], "travelCardIds": ["T12"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743098753317, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743098811974, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743098823277, "data": {"path": [53, 18, 15, 64, 65, 31, 32, 36], "travelCardIds": ["T8", "T10", "T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743098825102, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743098825102, "data": {"roundNumber": 11, "playerCount": 2, "playerStats": [{"id": "rEvlNFxOLkhjEHp-AAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 2, "energyCubesCount": 4, "collectedJourneysCount": 1}, {"id": "G_-G_XFgPMkTUHWVAAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 20, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 2}]}}, {"type": "COLLECT_JOURNEY", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743098902839, "data": {"journeyCard": {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, "journeyType": "outer", "location": {"id": 27, "name": "<PERSON><PERSON>", "region": "South", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["artha", "karma", "artha", "karma"], "energyCubesAfter": ["artha"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 30}, "newScore": {"outerScore": 27, "innerScore": 30}}}}, {"type": "END_TURN", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743098937429, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "COLLECT_JOURNEY", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743098961689, "data": {"journeyCard": {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, "journeyType": "inner", "location": {"id": 36, "name": "Jagannath Temple", "region": "East", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["bhakti", "bhakti", "gnana"], "energyCubesAfter": [], "wildCubesUsed": 1, "scoreChange": {"previousScore": {"outerScore": 24, "innerScore": 20}, "newScore": {"outerScore": 24, "innerScore": 50}}}}, {"type": "END_TURN", "playerId": "G_-G_XFgPMkTUHWVAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743098970913, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743098970913, "data": {"roundNumber": 12, "playerCount": 2, "playerStats": [{"id": "rEvlNFxOLkhjEHp-AAAC", "name": "<PERSON><PERSON>", "outerScore": 27, "innerScore": 30, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 2}, {"id": "G_-G_XFgPMkTUHWVAAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 0, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rEvlNFxOLkhjEHp-AAAC", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743099021693, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3}, {"id": "T1", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743099137969, "data": {"timestamp": 1743099137969, "loadedStateRoundCount": 12, "playerCount": 2}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743099459675, "data": {"path": [27, 26, 25, 63, 61, 4, 3, 2], "travelCardIds": ["T6", "T3", "T14", "T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743099475994, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743100492670, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2}, {"id": "T9", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743100749496, "data": {"location": 58}}, {"type": "move", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743100749496, "data": {"path": [36, 32, 58], "travelCardIds": ["T7"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743100752076, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1743100752077, "data": {"roundNumber": 13, "playerCount": 2, "playerStats": [{"id": "5UwZAtJJogYACXgOAAAB", "name": "<PERSON><PERSON>", "outerScore": 27, "innerScore": 30, "omTemp": 0, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 0, "energyCubesCount": 2, "collectedJourneysCount": 2}, {"id": "EPUXlglnYr-RshN8AAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 0, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743100766235, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 1}, {"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743100776414, "data": {"path": [2, 1], "travelCardIds": ["T5"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743100777934, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743100785714, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}, {"id": "T12", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743100795524, "data": {"location": 55}}, {"type": "move", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743100795524, "data": {"path": [58, 32, 31, 30, 55], "travelCardIds": ["T4", "T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743100798012, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1743100798012, "data": {"roundNumber": 14, "playerCount": 2, "playerStats": [{"id": "5UwZAtJJogYACXgOAAAB", "name": "<PERSON><PERSON>", "outerScore": 27, "innerScore": 30, "omTemp": 0, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 3, "collectedJourneysCount": 2}, {"id": "EPUXlglnYr-RshN8AAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 2, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 0, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1743100804572, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3}, {"id": "T3", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1743100822894, "data": {"location": 56}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1743100822894, "data": {"path": [1, 2, 3, 56], "travelCardIds": ["T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1743100824402, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1743100886609, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 3}, {"id": "T10", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1743100918744, "data": {"path": [55, 30, 31], "travelCardIds": ["T9"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1743100920449, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 15, "turnIndex": 0, "timestamp": 1743100920449, "data": {"roundNumber": 15, "playerCount": 2, "playerStats": [{"id": "5UwZAtJJogYACXgOAAAB", "name": "<PERSON><PERSON>", "outerScore": 27, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 2, "energyCubesCount": 3, "collectedJourneysCount": 2}, {"id": "EPUXlglnYr-RshN8AAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 2, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 3}]}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1743100925181, "data": {"path": [56, 3, 4, 6], "travelCardIds": ["T14"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1743100928200, "data": {"journeyCard": {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, "journeyType": "outer", "location": {"id": 6, "name": "<PERSON><PERSON>", "region": "North", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["artha", "karma", "karma"], "energyCubesAfter": ["bhakti", "gnana", "bhakti"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 27, "innerScore": 30}, "newScore": {"outerScore": 54, "innerScore": 30}}}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 15, "turnIndex": 1, "timestamp": 1743100931153, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "collectOm", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 15, "turnIndex": 1, "timestamp": 1743101534678, "data": {"location": 49}}, {"type": "move", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 15, "turnIndex": 1, "timestamp": 1743101534678, "data": {"path": [31, 65, 62, 11, 10, 49], "travelCardIds": ["T12", "T10"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1743101536756, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 16, "turnIndex": 0, "timestamp": 1743101536756, "data": {"roundNumber": 16, "playerCount": 2, "playerStats": [{"id": "5UwZAtJJogYACXgOAAAB", "name": "<PERSON><PERSON>", "outerScore": 54, "innerScore": 30, "omTemp": 0, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 0, "collectedJourneysCount": 3}, {"id": "EPUXlglnYr-RshN8AAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 3, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 16, "turnIndex": 0, "timestamp": 1743101729134, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2}, {"id": "T6", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 16, "turnIndex": 0, "timestamp": 1743101734574, "data": {"location": 54}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 16, "turnIndex": 0, "timestamp": 1743101734574, "data": {"path": [6, 7, 8, 54], "travelCardIds": ["T3", "T8"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 16, "turnIndex": 1, "timestamp": 1743101736233, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 16, "turnIndex": 1, "timestamp": 1743101874005, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1}, {"id": "T1", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 16, "turnIndex": 1, "timestamp": 1743101916888, "data": {"path": [49, 10, 11, 62, 65, 37], "travelCardIds": ["T15", "T2", "T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 16, "turnIndex": 0, "timestamp": 1743101919784, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 17, "turnIndex": 0, "timestamp": 1743101919784, "data": {"roundNumber": 17, "playerCount": 2, "playerStats": [{"id": "5UwZAtJJogYACXgOAAAB", "name": "<PERSON><PERSON>", "outerScore": 54, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 0, "collectedJourneysCount": 3}, {"id": "EPUXlglnYr-RshN8AAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 3, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 0, "energyCubesCount": 2, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 17, "turnIndex": 0, "timestamp": 1743101959995, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1}, {"id": "T14", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 17, "turnIndex": 0, "timestamp": 1743101964528, "data": {"path": [54, 9], "travelCardIds": ["T4"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 17, "turnIndex": 1, "timestamp": 1743101965798, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 17, "turnIndex": 1, "timestamp": 1743103299982, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}, {"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 17, "turnIndex": 1, "timestamp": 1743103316858, "data": {"path": [37, 65, 61, 4], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 17, "turnIndex": 0, "timestamp": 1743103320829, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 18, "turnIndex": 0, "timestamp": 1743103320829, "data": {"roundNumber": 18, "playerCount": 2, "playerStats": [{"id": "5UwZAtJJogYACXgOAAAB", "name": "<PERSON><PERSON>", "outerScore": 54, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 3}, {"id": "EPUXlglnYr-RshN8AAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 3, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 3, "collectedJourneysCount": 3}]}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 18, "turnIndex": 0, "timestamp": 1743103361449, "data": {"path": [9, 54, 8], "travelCardIds": ["T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 18, "turnIndex": 1, "timestamp": 1743103366576, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 18, "turnIndex": 1, "timestamp": 1743103390553, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2}, {"id": "T5", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "END_TURN", "playerId": "EPUXlglnYr-RshN8AAAD", "playerName": "<PERSON><PERSON>", "roundCount": 18, "turnIndex": 0, "timestamp": 1743103391943, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 19, "turnIndex": 0, "timestamp": 1743103391943, "data": {"roundNumber": 19, "playerCount": 2, "playerStats": [{"id": "5UwZAtJJogYACXgOAAAB", "name": "<PERSON><PERSON>", "outerScore": 54, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 1, "energyCubesCount": 2, "collectedJourneysCount": 3}, {"id": "EPUXlglnYr-RshN8AAAD", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 50, "omTemp": 3, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 19, "turnIndex": 0, "timestamp": 1743103409889, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2}, {"id": "T12", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 19, "turnIndex": 0, "timestamp": 1743103416391, "data": {"path": [8, 7, 6, 61, 64, 29], "travelCardIds": ["T14", "T10"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "5UwZAtJJogYACXgOAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 19, "turnIndex": 1, "timestamp": 1743103417810, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 19, "turnIndex": 1, "timestamp": 1743103435315, "data": {"timestamp": 1743103435315, "loadedStateRoundCount": 19, "playerCount": 2}}, {"type": "move", "playerId": "brUCucMwdc9B4shFAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 19, "turnIndex": 1, "timestamp": 1743103445827, "data": {"path": [4, 6, 7], "travelCardIds": ["T7"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "brUCucMwdc9B4shFAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 19, "turnIndex": 1, "timestamp": 1743103449485, "data": {"journeyCard": {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, "journeyType": "inner", "location": {"id": 7, "name": "<PERSON><PERSON><PERSON>", "region": "North", "journeyType": "Inner"}, "omSpent": 2, "energyCubesBefore": ["bhakti", "bhakti", "gnana", "gnana"], "energyCubesAfter": ["gnana"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 24, "innerScore": 50}, "newScore": {"outerScore": 24, "innerScore": 77}}}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "brUCucMwdc9B4shFAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 19, "turnIndex": 1, "timestamp": 1743103449485, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 101, "playerOmTotal": 6, "finalRoundStarter": 1, "finalRoundEnd": 0}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 19, "turnIndex": 1, "timestamp": 1743103453286, "data": {"winner": {"id": "brUCucMwdc9B4shFAAAB", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 77, "totalScore": 101, "omTotal": 6}, "totalRounds": 19, "players": [{"id": "hdP-Cl85MRj9P8KsAAAD", "name": "<PERSON><PERSON>", "outerScore": 54, "innerScore": 30, "totalScore": 84, "omTotal": 4}, {"id": "brUCucMwdc9B4shFAAAB", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 77, "totalScore": 101, "omTotal": 6}]}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 19, "turnIndex": 1, "timestamp": 1743103453287, "data": {"winner": {"id": "brUCucMwdc9B4shFAAAB", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 77, "totalScore": 101, "omTotal": 6}, "totalRounds": 19, "players": [{"id": "hdP-Cl85MRj9P8KsAAAD", "name": "<PERSON><PERSON>", "outerScore": 54, "innerScore": 30, "totalScore": 84, "omTotal": 4}, {"id": "brUCucMwdc9B4shFAAAB", "name": "<PERSON><PERSON>", "outerScore": 24, "innerScore": 77, "totalScore": 101, "omTotal": 6}]}}]}