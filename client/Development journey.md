Development journey:
- We played Trekking the World last night on board game arena. Quite enjoyed the whole game. Collecting journey cards was pretty decent, being able to get the required cards a lot of the times, at other times we’d use the Skip xxx Journey log to use 1 fewer icon by generally being able to have 2 matching icon cards and able to keep with the need to travel at every turn. I was unable to focus on collecting specific color cubes until latter stages of the game, and started focus on regional bonus as well halfway through the game. Made only 1 mistake of activating Journey log on an incorrect location (non journey location).
- 8 Apr 25: 
    - Contemplating on board design and how its not an exact map of India: https://chatgpt.com/c/67f44e7c-a4e4-800d-beca-abcfce64b998 
        - Immortals Of Meluha has fictional Meluha North East
        - Ticket to Ride India has modified India map
    - Pivot:
        - As a race from south to north with final victory in Pashupatinath (13th Jyotirlinga)
        - Players start from south choosing to go via West or East with different trade offs. No airport. 
        - There are some board games in this realm in some past chat with chatgpt
        - Player Concepts of Vehicle, Bagpack; Food, Money, Knowledge, etc
        - Journey Game concepts: Weather, People
        - 
- 9 Apr 25:
    - Travel at the beginning of turn may be powerful
        - Prevent first player advantage during event cards relating to vehicles 
        - Will increase number of turns so needs to be measured
    - Need to adjust characters since we didnt use trade a single time in last ~4 games. 
        - Change trade to 1:1
        - Other characters that can trade a travel card for 1 energy
        - Characters that get +5 for each outer/inner JC
    - Food for thought: If the location board does not pan out due to reasons such as not a true India representative or too crowded, then explore a mythical narrative like immortals of meluha map, as Ancient Bharat, ancient characters vehicles locations and events
- 10 Apr 25:
    - Reimplemented app with boardgame.io for OOTB bots. Had to abandon it since pretty time consuming to mold it into our current UI
- Different forks of the game:
    - Engine building like Wingspan??
    - Character evolution: Wealth (businessman, CEO) , Education (doctor, professor, scientist), Karma (philanthropist, social worker, NGO chief), Bhakti (Sage, Devotee, Monk.
- 17 Apr 25:
    - Player’s Faceup hand cards remains to be a good choice: https://chatgpt.com/share/6800fafd-7fbc-800d-94af-87baf63c7528 
    - There is still a fair amount of 1st player advantage when it comes to event cards: https://chatgpt.com/share/6800fb89-7d20-800d-b788-94ff803fe8b3 
        - Most number of om tokens OR journey cards collected to determine turn order seems like a good design to try, at the cost of +0.2 game complexity
    - Need to try the 3 player variant now
        - May need to increase travel cards to 24 and analyze all event cards wrt 3 players
    - We played the 3 player variant last night with a bot
        - Board spacing seemed to be smooth with not too much congestion
        - Travel cards were getting refreshed at a decent speed even with 12, a few times when there were none in deck with faceup full and others in players hands, will need to run more tests to guage this
        - Kims reached the om token count of 5 much quickly since bot wasn’t collecting enough and thus I wouldn’t be able to catchup after a point; may need to see how often a smart player would do this when there is a weaker player player and another average-smart player
        - Not enough strict 2-3 player games on the market per o3 with notable comparison being TTR: Nordic
- 21 Apr 25
    - Also consider reducing hand travel cards from 4 to 3?
        - Reduces chance of 1st player advantage?
        - More travel cards in deck?
        - More route planning? Turns increases?
    - How does the Om token victory play out with 3 players? Are they able to each grab 4 tokens in time once things heat up?
    - During human playtesting, need to check how many times do people forget om token calculations
- 22 Apr 25
          -  Try 24 travel cards
		- Another motivation is that 12 cards require too much reshuffling of discard pile to main pile in 3 player game, works fine in 2 player setting
          -  Different om collection strategies to try during playtesting
              - Collect 3 om tokens right away in temp slot, collect 1st journey card (I.e use 1 om token) and then attempt to collect 2 more om tokens to win. May have to travel far continuously and needs bigger travel cards 
          	- Collect 2 journey cards and then attempt to collect 3 om tokens
    - The latest 3 player match with Kims and bot involved 2 small move mistakes from Kims and prevented her from convergence due to token depletion and maxxing on 5 energy cubes with her character trade not helping with the cube limits.
- 23 Apr 25
    - Global event card images generated!
- 28 Apr 25
    - Consolidated travel card related event cards
    - Added Excess baggage and Heritage Site restoration cards
- 29 Apr 25
    - Shifted starting in region event cards to ending in
    - Runaway leader rubber banding ideas:
        - Added: Spirit of Seva - Leader on each track donates 3 points to player with lowest score on that track
    - Parikrama in the clouds event card to encourage airport travel
- 2 May 25
    - Board layout is ready for prototype print - jyotirlinga and airport icons and region signs
    - Planning way to generate cards for print; component.studio free trial or automate
    - Target be ready for Same day order before noon tmrw
- 3 May 25
    - Created the event and journey cards in tarot size for printing next week (shoutout to Kims for her ppt skills)
    - Need to plan how to create a scoring track along side the board. A bit worried about too many physical elements in the game. Created an image of standalone scoring tracks 
    - Need to plan how players manage their artifacts; a player mat would get tricky with 2 rows of 4 tarot card slots for journey plus om collection, character and energy cubes.
    - Now it does seem like the game complexity is 3 - 3.5 BGG
- 4 May 25
    - Analysis of player mat wrt other board games like Terraforming Mars and Spirit Island that have a larger player mat
    - Skeleton of our player mat: https://chatgpt.com/share/6818c755-1a68-800d-a5aa-f2ecbcbc3662 
- 5 May 25
    - Analyzing trekking the world gameplays to understand item management , player mat
        - https://www.youtube.com/watch?v=Fp-D-JOqnck&pp=ygUbdHJla2tpbmcgdGhlIHdvcmxkIGdhbWVwbGF5 
        - https://www.youtube.com/watch?v=DAjlyiC2dIQ&pp=ygUbdHJla2tpbmcgdGhlIHdvcmxkIGdhbWVwbGF5 
        - https://www.youtube.com/watch?v=fvyNeUjmUt0&pp=ygUbdHJla2tpbmcgdGhlIHdvcmxkIGdhbWVwbGF5
        - https://www.youtube.com/watch?v=3Zawd8g9bQ8&ab_channel=BoardGameEmpire -> 3 player
        - https://www.youtube.com/watch?v=n_VvP-ByGlM&ab_channel=TheDiceTower -> 4 player, very wide table with 1 player managing faceup trek, 1 managing journey cards
        - Rough player mat: https://docs.google.com/presentation/d/1cWPfJ3eRc4P7kgzeo0WU8XqLHI6uOmc-WAvk4CWyS74/edit?slide=id.p#slide=id.p , will upgrade with Kims help 
        - Very painful to create precise score tracks in google slides in drive: https://docs.google.com/presentation/d/1DO-JgvFz9zjumYilpf8WMoW9XPMkwfHC3dqmu9tTPJU/edit?slide=id.p#slide=id.p , handling precise width/offsets
        - First time I’ve considered a possibility of dropping convergence altogether and replacing with max score as winner OR max om tokens to cut out scoring math and managing score track. Would hand out bonus chips for event cards. May then have to drop inner journey notion altogether and replace it with the om collection itself as an inner journey
        - Everdell gives out cards in increasing count, prob to counter first player advantage? Should we consider/experiment that too?
    - 6 May 25
        - Happy birthday to me.
        - First full print of all cards on 100-lb cardstock and the first full physical gameplay 🎉
        - Noted rule clarifications
        - Was very fulfilling to play physically and look at the journey cards
        - Challenges: Board is too big for non facing players to reach all ends. Scoring tracks are hard to reach especially the top tracks
    - 7 May 25
        - Trekking the world is 28 x 19
        - They might’ve been limited by some standard sizing from manufacturers since https://printninja.com/custom-board-game-quote/ says 20 x 30 as biggest size
        - 
        - 500 copies demo quote to 17k USD ~34.5 per copy . Trekking the world kickstarter was 40 USD and as of today sells at 50 USD
        - The Game Crafter sells max 27 x 18 size: https://www.thegamecrafter.com/make/games/D9128EDC-2B77-11F0-9941-45126CB82320/customizable-components?hierarchicalMenu%5Bcategories%5D%5B0%5D=Boards priced at 20
        - Ticket to Ride size ~ 30 x 20: https://boardgamegeek.com/thread/2326160/ticket-to-ride-europe-board-size 
    - 8 May 25
        - Got really pulled into a need for creating beautiful art for the game especially since looking at Everdell intro video.
        - BGG thread on fan made cards https://boardgamegeek.com/thread/3262786/fan-made-new-cards-new-paths and they shared psd , ai templates! Explored it in Photopea
        - Contemplating on 7 day plan of adobe creative cloud + chatgpt to create everdell quality cards (event cards)
        - Generated some front layers tree style similar to Everdell
        - Created 4 cutout location board for printing on 17 x 11 to make a new 28 x 18 (30 x 20 with scoretracks) board 
        - Cutout at Staples failed since I had to quickly whip up 4 cropped images using Mac preview that threw off accuracy and mainly pasting the bottom left image in pptx messed up image scaling completely to not fit
    - 9 May 25
        - Tried putting together the 30 x 20 location board in online google slides which pixelates images when resizing, tried learning GIMP but hard to offset to x,y and port over score tracks from google slides. Tried KeyNote but limited features such as no custom page setup in inches/cm
        - Planning to explore approaches for first player advantage:
            - https://chatgpt.com/c/68197dd7-77d8-800d-a4aa-06e3edc9c2a3 
            - 2 player setting:
                - 2nd player gets 3 cards at start
                - 4 cards at start
                - 1 energy cube
            - 3 player setting
                - 2nd gets 3 cards, 3rd gets 4
                - 2nd gets 3 cards, 3rd gets 3 cards and 1 energy
        - Playtested with 1st player starting with 1 travel card (idea being 2nd with 2, 3rd with 3). Didn’t have desirable effect as 1st player caught up quickly with max travel cards probably at points like no travel.
        - Feedback from Kims: she is not going for 30 pointer cards as they’re not worth the additional turns. She doesn’t recollect winning the game when collecting 30 pointer cards. Need to study why trekking the world has bigger pointer cards, ask gpt for some analysis and possibly run bot simulation with this strategy 
        - My feedback: players have stopped trying for 7 on tokens due to quicker point based convergence caused by journey cards. explore updating event cards for motivating om token strategy, or update journey card scoring like distribution of points
    - 10 May 25
        - Studying TTW revealed that their journey cards had max requirement of 3 trek cards for destination cards score ranging from 16 - 18 - 20, 2 trek cards at 10 and 12 with difference being 16 required all unique, 18 with 2 same and 20 with all same. The must-see token +3/+5 gives further incentives to choose certain destination cards.
        - Analyzing LOTR duel for middle earth for how game motivates players to advance on doom track and 
        - Goals: 
            - 1.⁠ ⁠to increase motivation for Om token based convergence 
            - 2.⁠ ⁠Reduce first player advantage 
            - Approaches:
                - reduce score of lowest card from 20 to say 18 
                - reduce event card bonuses from 5 to 3, 7 to 5, 10 to 7
                - Separate Om track position based on Om tokens collected with notion of stacking on top based on last player to reach an occupied spot. Turn order based on Om track
        - Playtesting plan for today:
            - Second player to get 1 energy cube before revealing journey cards
            - Journey scores from 20 -> 18, 24 -> 20, 27 -> 24, 30 -> 30
            - Event bonus 5 -> 3, 7 -> 5, 10 -> 7
        - Google Image FX experience on Triathlon card: https://labs.google/fx/tools/image-fx/0rorgei9b0000 
    - 11 May 25
        - Got into a bit of time-wasting/experimentation mode with board map generation using Image FX and asking Chatgpt to emulate it (to no luck)
        - First game of the day with scores from 20 -> 16, 24 -> 18, 27 -> 24, 30 -> 30 , 4 om tokens allowed in slot. I tried strategy of win by om tokens and Kims went with usual strategy of cheapest cards first and then heavier, ended up at a point where she collected 6 om tokens as well as I did at round 13. Thus I would have to also now go for score convergence. Kims went for 3 outer cards i.e 4 tokens slotted and would’ve likely not converged now with 2 tokens left. I would’ve likely won using 3 unspotted tokens and collecting 1 heavier card.
        - Second game of the day with:  1. no om token convergence win condition 2. Separate om track for turn order based on om tokens collected 3. Scores 20 -> 18, 24 -> 20, 27 -> 24, 30 -> 30 with event scoring unchanged yielded in a second player victory who went slightly ahead on the om token track early on and bagged a few event bonuses. First player did lose out on trades due to lack of seed energy cubes while second player did not.
    - 12 May 25
        - Again experimenting with board maps, to little avail
        - Reading bits and pieces of Joe Slack’s books
        - Next phase is to keep play testing the removed Om victory condition based version with Om turn track, analyze 
        - Helpful analysis from o3:
            - Solo / Co-op Pilgrimage Automate rival “monk” using Travel deck to claim tokens & cards on scripted priority—matching trend seen in Trekking through History solo system
            - Character Quests - collect 5 om tokens, a 30 point card, 5 energy cubes to get 7 points for further strategy changes
            - Keep an eye on play testing feedback for perception of too many negative event cards (esp when back to back)
        - Brainstorming new strategies based on om turn track:
            - Single cube card grab: In a 2 player setting, 2nd player gets an om token to be on top, becomes the first player next round and collects a journey card with a single cube requirement either where required energy cube type is on the location or via character trade.
        - Brainstorming mechanics to encourage players to collect 30 point cards
        - Need to also look out for people’s ease in doing math involving the 100 convergence 
    - 13 May 25
        - Fixed bot sims to run multiple games to eventually analyze impact of JC score changes and then om turn track
        - Major pending decision analysis: convergence of dual tracks vs single score track to 100 and over. Dual track is more uncommon and fresh, but may pose more math challenges?? Single track may simplify math element of scoring?? And may be more simpler view of a race. Would it lose the charm of a dual journey and thematic richness?