import React, { useCallback } from 'react';

function CompactJourneyCards({
  faceUpJourneyOuter,
  faceUpJourneyInner,
  gameState,
  currentPlayer,
  socket,
  setJourneyModal
}) {
  // Helper function to check if player can collect a journey card
  const canCollectJourneyCard = (card, player) => {
    if (!player) return false;

    // Check if player is on the correct location
    const onLocation = player.position === card.locationId;

    // Check if inner journey cards are blocked by global event (Drought of Spirits)
    if (gameState?.currentGlobalEvent?.effect === 'no_inner_journey_cards' && card.reward.inner !== undefined) {
      return false;
    }

    // Check if outer journey cards are blocked by global event (Heritage Site Renovations)
    if (gameState?.currentGlobalEvent?.effect === 'no_outer_journey_cards' && card.reward.outer !== undefined) {
      return false;
    }

    // Check if player meets energy requirements
    const meetsEnergy = meetsEnergyRequirement(card.required, player);

    // Check if player has enough OM
    const meetsOm = meetsOmRequirement(card, player);

    return onLocation && meetsEnergy && meetsOm;
  };

  // Helper function to check if player meets energy requirements
  const meetsEnergyRequirement = (required, player) => {
    if (!required || !player) return true;

    // Count player's energy cubes by type
    const playerCubes = {};
    (player.energyCubes || []).forEach(cube => {
      const type = cube.toLowerCase();
      playerCubes[type] = (playerCubes[type] || 0) + 1;
    });

    // Count wild cubes separately
    const wildCubes = player.wildCubes || 0;

    // Check each required energy type
    let remainingWildCubes = wildCubes;
    for (const [type, count] of Object.entries(required)) {
      const available = playerCubes[type.toLowerCase()] || 0;
      if (available >= count) continue;  // Player has enough of this type

      const deficit = count - available;
      if (deficit <= remainingWildCubes) {
        remainingWildCubes -= deficit; // Use wild cubes to cover deficit
      } else {
        return false; // Not enough cubes even with wild cubes
      }
    }

    return true;
  };

  // Helper function to check if player has enough OM tokens
  const meetsOmRequirement = (card, player) => {
    if (!player) return false;

    const costArray = [1, 1, 2, 3];
    const count = getJourneyCount(player, card);
    const cost = count < costArray.length ? costArray[count] : Infinity;

    return player.omTemp.length >= cost;
  };

  // Helper function to get journey count for a player
  const getJourneyCount = (player, card) => {
    if (!player) return 0;

    // Check if it's inner or outer journey
    const journeyType = card.reward.inner !== undefined ? 'inner' : 'outer';
    const locationId = card.locationId;

    // Count completed journeys of the same type to the same location
    const completedCards = journeyType === 'inner'
      ? player.journeyInner || []
      : player.journeyOuter || [];

    return completedCards.filter(c => c.locationId === locationId).length;
  };

  // Helper function to render energy requirements in a compact way
  const renderCompactEnergyReq = (required) => {
    if (!required) return null;

    return (
      <div style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'center', marginBottom: '2px' }}>
        {Object.entries(required).map(([color, count]) => (
          count > 0 && (
            <div key={color} style={{ display: 'flex', alignItems: 'center', margin: '0 2px' }}>
              <div
                className={`energy-cube ${color.toLowerCase()}`}
                style={{ width: '12px', height: '12px', margin: '0 2px 0 0' }}
              />
              <span style={{ fontSize: '0.7rem' }}>{count}</span>
            </div>
          )
        ))}
      </div>
    );
  };

  // New function to render global event card
  const renderGlobalEventCard = () => {
    const currentGlobalEvent = gameState?.currentGlobalEvent;

    if (!currentGlobalEvent) return null;

    // Get the index from the effect name to find the corresponding image
    const eventEffectToIndex = {
      'max_moves_2_and_cost_artha_north_east': 0,
      'gain_5_inner_no_cube_pickup': 1,
      'jyotirlinga_7_inner_or_bonus_cube': 2,
      'no_inner_journey_cards': 3,
      'draw_2_cubes_bonus_5_outer': 4,
      'no_airport_travel': 5,
      'double_trade_no_travel': 6,
      'triathlon_bonus': 7,
      'riots_discard': 8,
      'om_meditation': 9,
      'heavy_haul_reward': 20,
      'merchants_midas_reward': 22,
      'professors_insight_reward': 23,
      'pilgrims_grace_reward': 24,
      'engineers_precision_reward': 25,
      'frozen_north': 26,
      'solar_south': 28,
      'himalayan_ne': 30,
      'central_heart': 31,
      'eco_trail_reward': 32,
      'rajput_caravans_reward': 33,
      'urban_ride_reward': 34,
      'road_warriors_reward': 35,
      'rails_and_sails_reward': 36,
      'excess_baggage': 37,
      'no_outer_journey_cards': 38,
      'spirit_of_seva': 39,
      'pushkar_holy_dip_end_turn_reward': 40,
      'parikrama_in_clouds_reward': 41,
      'cultural_exchange': 42
    };

    const imageIndex = eventEffectToIndex[currentGlobalEvent.effect];
    const imagePath = `${process.env.PUBLIC_URL}/assets/images/global_event_cards/${imageIndex}.png`;

    return (
      <div className="global-event-card-container" style={{
        width: '100%',
        marginBottom: '12px'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '6px'
        }}>
          <h3 style={{
            margin: 0,
            fontSize: '0.9rem',
            color: '#e65100',
            fontWeight: 'bold'
          }}>
            Global Event
          </h3>
          <div style={{
            fontSize: '0.7rem',
            color: '#757575',
            fontStyle: 'italic'
          }}>
            {/* Empty for now, could include turn counter or other info */}
          </div>
        </div>

        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          backgroundColor: '#fff8e1',
          border: '2px solid #ffb74d',
          borderRadius: '8px',
          padding: '8px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          position: 'relative',
          overflow: 'hidden',
          minHeight: '240px'
        }}>
          <div style={{
            width: '100%',
            textAlign: 'center',
            marginBottom: '6px',
            color: '#d84315',
            fontWeight: 'bold',
            fontSize: '0.9rem'
          }}>
            {currentGlobalEvent.name}
          </div>

          <div style={{
            width: '100%',
            height: '160px',
            borderRadius: '4px',
            overflow: 'hidden',
            marginBottom: '6px',
            border: '1px solid #ffe0b2'
          }}>
            <img
              src={imagePath}
              alt={currentGlobalEvent.name}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                objectPosition: 'center'
              }}
              onError={(e) => {
                console.error(`Failed to load global event image: ${imagePath}`);
                e.target.style.display = 'none';
                e.target.parentNode.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; text-align: center; padding: 10px; color: #d84315; font-style: italic;">Event Image<br/>Not Available</div>';
              }}
            />
          </div>

          <div style={{
            width: '100%',
            textAlign: 'center',
            fontSize: '0.75rem',
            color: '#5d4037',
            fontStyle: 'italic',
            padding: '0 4px',
            maxHeight: '60px',
            overflow: 'auto',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {currentGlobalEvent.text}
          </div>
        </div>
      </div>
    );
  };

  // Add a more reliable check for current player
  const isCurrentPlayer = useCallback(() => {
    return Boolean(
      socket && 
      gameState.players && 
      gameState.turnIndex !== undefined && 
      gameState.turnIndex >= 0 && 
      gameState.turnIndex < gameState.players.length && 
      gameState.players[gameState.turnIndex]?.id === socket.id
    );
  }, [socket, gameState]);

  // Update the calculateWildCubesNeeded function to use the currentPlayer parameter directly
  const calculateWildCubesNeeded = (card, player) => {
    if (!player || !card.required) return 0;
    
    const energyCubes = player.energyCubes || [];
    
    // Count available energy cubes by type
    const available = {
      artha: energyCubes.filter(cube => cube === 'artha').length,
      karma: energyCubes.filter(cube => cube === 'karma').length,
      gnana: energyCubes.filter(cube => cube === 'gnana').length,
      bhakti: energyCubes.filter(cube => cube === 'bhakti').length
    };
    
    // Calculate total shortfall across all energy types
    let totalWildNeeded = 0;
    for (const [color, req] of Object.entries(card.required)) {
      const shortfall = Math.max(0, req - available[color]);
      totalWildNeeded += shortfall;
    }
    
    return totalWildNeeded;
  };

  // Render compact journey card
  const renderCompactJourneyCard = (card, index, type) => {
    const isOuter = type === 'outer';
    const bgColor = isOuter ? '#fff3e0' : '#e8eaf6';
    const borderColor = isOuter ? '#f57c00' : '#3f51b5';
    const textColor = isOuter ? '#e65100' : '#283593';

    // Generate a short name for location
    const location = card.locationName || `Loc ${card.locationId}`;
    // Get full location name for enhanced display
    const locationInfo = gameState.locations?.find(loc => loc.id === card.locationId);
    const fullLocationName = locationInfo?.name || location;
    const region = locationInfo?.region || 'Unknown';

    // Use CSS variables instead of hardcoded colors to maintain consistency
    const regionColor = region ? `var(--${region.toLowerCase()}-color)` : '#9e9e9e';

    // Get short name for compact display
    const shortLocation = location.length > 12 ? location.substring(0, 10) + '...' : location;

    // Extract reward value
    const rewardValue = isOuter
      ? card.reward?.outer
      : card.reward?.inner;

    // Image path based on location ID - using correct path format from FaceUpCards
    const imagePath = `${process.env.PUBLIC_URL}/assets/images/cards/${card.locationId}.png`;

    // Check if card can be collected
    const canCollect = isCurrentPlayer() && canCollectJourneyCard(card, currentPlayer);

    // Check if journey cards are blocked by global event
    const isInnerJourneyBlocked =
      gameState?.currentGlobalEvent?.effect === 'no_inner_journey_cards' &&
      !isOuter;

    const isOuterJourneyBlocked =
      gameState?.currentGlobalEvent?.effect === 'no_outer_journey_cards' &&
      isOuter;

    return (
      <div
        key={`journey-${type}-${index}`}
        className={`compact-card journey-card ${isOuter ? 'outer' : 'inner'} ${canCollect ? 'collectable' : ''}`}
        style={{
          width: '110px',  // Reduced from 120px to fit more cards
          height: '160px', // Reduced from 180px for better vertical fit
          backgroundColor: bgColor,
          borderRadius: '8px',
          border: canCollect ? `2px solid #4caf50` : `1px solid ${borderColor}`,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'center',
          margin: '0 6px 6px 0', // Reduced margin
          boxShadow: canCollect ? '0 0 8px rgba(76, 175, 80, 0.5)' : '0 2px 4px rgba(0,0,0,0.2)',
          padding: '4px', // Reduced padding
          fontSize: '0.7rem',
          overflow: 'hidden',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          position: 'relative'
        }}
        onClick={(e) => {
          e.stopPropagation(); // Prevent event bubbling
          if (isCurrentPlayer()) {
            // Only set localStorage flag if we're in fullscreen mode
            const isFullScreen = document.querySelector('.board-fullscreen') !== null;
            if (isFullScreen) {
              localStorage.setItem('pendingJourneyModal', JSON.stringify(card));
            }
            setJourneyModal(card);
          }
        }}
      >
        {/* Enhanced header with location name and region circle */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          marginBottom: '2px' // Reduced margin
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            width: '100%'
          }}>
            <div style={{
              width: '10px', // Reduced from 12px
              height: '10px', // Reduced from 12px
              borderRadius: '50%',
              backgroundColor: regionColor,
              border: '1px solid #fff',
              boxShadow: '0 1px 2px rgba(0,0,0,0.2)'
            }} />
            <div style={{
              fontSize: '0.7rem', // Reduced from 0.8rem
              fontWeight: 'bold',
              color: textColor,
              textAlign: 'left',
              flex: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              display: 'flex',
              alignItems: 'center',
              gap: '2px' // Reduced from 4px
            }}>
              <span style={{
                fontSize: '0.6rem', // Reduced from 0.65rem
                backgroundColor: isOuter ? 'rgba(245, 124, 0, 0.2)' : 'rgba(63, 81, 181, 0.2)',
                color: textColor,
                borderRadius: '4px',
                padding: '1px 2px', // Reduced padding
                fontWeight: 'bold',
                minWidth: '14px', // Reduced from 16px
                textAlign: 'center',
                border: `1px solid ${borderColor}`
              }}>
                {card.locationId}
              </span>
              <span>{fullLocationName}</span>
            </div>
          </div>
        </div>

        {/* Journey image section */}
        <div
          style={{
            width: '95%',
            height: '60px', // Reduced from 65px
            borderRadius: '4px',
            position: 'relative',
            marginBottom: '2px', // Reduced from 4px
            overflow: 'hidden'
          }}
        >
          <img
            key={`card-image-${card.locationId}-${index}`}
            src={imagePath}
            alt={fullLocationName}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center'
            }}
            onError={(e) => {
              // If image fails to load, set a placeholder background
              e.target.style.backgroundColor = '#f5f5f5';
            }}
          />

          {/* Reward value badge */}
          <div style={{
            position: 'absolute',
            bottom: '2px',
            right: '2px',
            backgroundColor: isOuter ? '#ff9800' : '#3f51b5',
            color: 'white',
            width: '22px', // Reduced from 24px
            height: '22px', // Reduced from 24px
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '0.8rem', // Reduced from 0.9rem
            fontWeight: 'bold',
            border: '1px solid white',
            boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
          }}>
            {rewardValue}
          </div>

          {/* Block overlay for inner journey cards during Drought of Spirits */}
          {isInnerJourneyBlocked && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'column',
              color: 'white',
              padding: '5px',
              textAlign: 'center',
              borderRadius: '4px'
            }}>
              <div style={{ fontSize: '0.9rem', fontWeight: 'bold', marginBottom: '2px' }}>
                BLOCKED
              </div>
              <div style={{ fontSize: '0.6rem' }}>
                Drought of Spirits
              </div>
            </div>
          )}

          {/* Block overlay for outer journey cards during Heritage Site Renovations */}
          {isOuterJourneyBlocked && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'column',
              color: 'white',
              padding: '5px',
              textAlign: 'center',
              borderRadius: '4px'
            }}>
              <div style={{ fontSize: '0.9rem', fontWeight: 'bold', marginBottom: '2px' }}>
                BLOCKED
              </div>
              <div style={{ fontSize: '0.6rem' }}>
                Heritage Site Renovations
              </div>
            </div>
          )}
        </div>

        {/* Middle section with requirements */}
        <div style={{ marginBottom: '2px' }}> {/* Reduced from 4px */}
          {renderCompactEnergyReq(card.required)}
        </div>

        {/* Available ribbon for cards that can be collected */}
        {canCollect && (
          <div style={{
            position: 'absolute',
            top: '25px',
            right: '-25px',
            backgroundColor: '#4caf50',
            color: 'white',
            padding: '2px 25px',
            transform: 'rotate(45deg)',
            boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
            fontSize: '0.65rem',
            fontWeight: 'bold',
            zIndex: 1
          }}>
            Available
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="compact-journey-cards-container" style={{
      backgroundColor: 'rgba(245, 245, 245, 0.85)',
      borderRadius: '8px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      padding: '8px',
      width: '100%',
      maxWidth: '450px',
      margin: '0',
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      maxHeight: '95vh', // Added max height to prevent overflow
      overflowY: 'auto' // Added scrolling for the container
    }}>
      {/* Add Global Event Card at the top */}
      {renderGlobalEventCard()}

      {/* Outer Journey Cards Section */}
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '0.9rem' }}>Outer Journey Cards</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          {faceUpJourneyOuter && faceUpJourneyOuter.map((card, index) => (
            renderCompactJourneyCard(card, index, 'outer')
          ))}
        </div>
      </div>

      {/* Inner Journey Cards Section */}
      <div style={{ marginTop: '15px' }}>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '0.9rem' }}>Inner Journey Cards</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          {faceUpJourneyInner && faceUpJourneyInner.map((card, index) => (
            renderCompactJourneyCard(card, index, 'inner')
          ))}
        </div>
      </div>
    </div>
  );
}

export default CompactJourneyCards;