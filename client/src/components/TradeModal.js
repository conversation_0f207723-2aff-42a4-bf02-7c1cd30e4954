import React, { useState, useEffect } from 'react';

function TradeModal({ player, onClose, onTrade, socket }) {
  const [selectedCubes, setSelectedCubes] = useState([]);
  const [error, setError] = useState(null);
  
  // Count energy cubes by type
  const cubesByType = {
    artha: player.energyCubes.filter(cube => cube.toLowerCase() === 'artha'),
    bhakti: player.energyCubes.filter(cube => cube.toLowerCase() === 'bhakti'),
    gnana: player.energyCubes.filter(cube => cube.toLowerCase() === 'gnana'),
    karma: player.energyCubes.filter(cube => cube.toLowerCase() === 'karma')
  };
  
  // Get all valid cube types for trading from player's character
  const validCubeTypes = player.character ? player.character.ability.takes : [];
  const resultCubeType = player.character ? player.character.ability.gives : '';

  // Handle cube selection
  const handleCubeClick = (cube, index) => {
    // Create a unique ID for the cube
    const cubeId = `${cube}-${index}`;
    
    // Toggle selection
    if (selectedCubes.some(sc => sc.id === cubeId)) {
      setSelectedCubes(selectedCubes.filter(sc => sc.id !== cubeId));
    } else {
      // Only allow selection of valid cube types and limit to 1
      if (validCubeTypes.includes(cube.toLowerCase()) && selectedCubes.length < 1) {
        setSelectedCubes([...selectedCubes, { id: cubeId, type: cube }]);
      } else if (!validCubeTypes.includes(cube.toLowerCase())) {
        setError(`You can't trade ${cube} cubes with your ${player.character.type}`);
      } else if (selectedCubes.length >= 1) {
        setError('You can only select 1 energy cube');
      }
    }
  };
  
  // Execute the trade
  const handleTrade = () => {
    if (selectedCubes.length !== 1) {
      setError('Select exactly 1 energy cube');
      return;
    }
    
    // Extract just the cube types from the selected cubes
    const cubeTypes = selectedCubes.map(c => c.type.toLowerCase());
    
    // Emit trade event to server
    socket.emit('tradeEnergyCubes', cubeTypes);
    onClose();
  };
  
  // Reset error when selection changes
  useEffect(() => {
    setError(null);
  }, [selectedCubes]);
  
  // Listen for trade errors from server
  useEffect(() => {
    if (socket) {
      const handleTradeError = (errorMsg) => {
        setError(errorMsg);
        // Don't close modal on error
      };
      
      socket.on('tradeError', handleTradeError);
      
      return () => {
        socket.off('tradeError', handleTradeError);
      };
    }
  }, [socket]);
  
  // Render individual cube with selection state
  const renderCube = (cube, index) => {
    const cubeId = `${cube}-${index}`;
    const isSelected = selectedCubes.some(sc => sc.id === cubeId);
    const isValid = validCubeTypes.includes(cube.toLowerCase());
    
    return (
      <div 
        key={cubeId}
        className={`energy-cube ${cube.toLowerCase()} ${isValid ? 'selectable' : ''} ${isSelected ? 'selected' : ''}`}
        style={{ 
          width: '30px', 
          height: '30px',
          margin: '5px',
          opacity: isValid ? 1 : 0.5,
          cursor: isValid ? 'pointer' : 'not-allowed'
        }}
        onClick={() => handleCubeClick(cube, index)}
        title={isValid ? `Select this ${cube} cube` : `Can't trade ${cube} cubes`}
      />
    );
  };
  
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: '#fff',
        borderRadius: '8px',
        padding: '20px',
        width: '90%',
        maxWidth: '400px',
        boxShadow: '0 5px 15px rgba(0, 0, 0, 0.3)'
      }}>
        <h3 style={{ marginTop: 0, borderBottom: '1px solid #eee', paddingBottom: '10px' }}>
          Trade Energy Cubes
        </h3>
        
        <div style={{ marginBottom: '15px' }}>
          <div style={{ 
            backgroundColor: '#f8f8f8', 
            padding: '10px',
            borderRadius: '6px',
            marginBottom: '10px'
          }}>
            <p style={{ marginTop: 0, fontWeight: 'bold' }}>
              {player.character.type}'s Ability:
            </p>
            <p style={{ margin: 0 }}>
              {player.character.description}
            </p>
          </div>
          
          <p style={{ fontWeight: 'bold', marginBottom: '5px' }}>
            Select 1 energy cube to trade:
          </p>
          
          <div style={{ 
            display: 'flex', 
            flexWrap: 'wrap',
            justifyContent: 'center'
          }}>
            {player.energyCubes.map((cube, index) => renderCube(cube, index))}
          </div>
          
          {selectedCubes.length > 0 && (
            <div style={{ 
              marginTop: '15px', 
              backgroundColor: '#f0f9ff', 
              padding: '10px',
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <p>You will receive:</p>
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: '5px'
              }}>
                <div 
                  className={`energy-cube ${resultCubeType.toLowerCase()}`}
                  style={{ 
                    width: '40px', 
                    height: '40px',
                  }}
                />
              </div>
              <p style={{ 
                marginTop: '5px', 
                fontWeight: 'bold',
                textTransform: 'capitalize' 
              }}>
                1 {resultCubeType} cube
              </p>
            </div>
          )}
          
          {error && (
            <div style={{ 
              color: 'red', 
              marginTop: '10px',
              padding: '8px',
              backgroundColor: '#fff0f0',
              borderRadius: '4px',
              textAlign: 'center'
            }}>
              {error}
            </div>
          )}
        </div>
        
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between',
          marginTop: '15px'
        }}>
          <button
            onClick={onClose}
            style={{ 
              padding: '8px 15px',
              backgroundColor: '#f0f0f0',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
          
          <button
            onClick={handleTrade}
            disabled={selectedCubes.length !== 1}
            style={{ 
              padding: '8px 15px',
              backgroundColor: selectedCubes.length === 1 ? '#4caf50' : '#cccccc',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: selectedCubes.length === 1 ? 'pointer' : 'not-allowed'
            }}
          >
            Trade
          </button>
        </div>
      </div>
    </div>
  );
}

export default TradeModal; 