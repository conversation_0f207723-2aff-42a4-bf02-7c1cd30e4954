/**
 * run_simulation_test.js
 * 
 * Script to start the server and run the simulation in a single command
 * 
 * Usage:
 *   node run_simulation_test.js
 */

const { fork } = require('child_process');
const path = require('path');
const fs = require('fs');
const net = require('net');
const Logger = require('./simulation/Logger');

// Create a logger
const logger = new Logger({
  logLevel: 'info',
  logToConsole: true,
  logToFile: true,
  logFilename: `test_run_${Date.now()}.log`
});

// Configuration
const BASE_PORT = 8000; // Start checking from this port
const MAX_PORT_ATTEMPTS = 100; // Maximum number of ports to try
const SERVER_STARTUP_DELAY = 3000; // Wait 3 seconds for server to initialize
const CLIENT_TIMEOUT = 60000; // 1 minute timeout for client
const RESULTS_FILE = path.join(__dirname, 'simulation_results.json');

// Paths to scripts
const SERVER_SCRIPT = path.join(__dirname, 'start_test_server.js');
const CLIENT_SCRIPT = path.join(__dirname, 'test_client.js');

/**
 * Find an available port to use
 * @returns {Promise<number>} An available port
 */
function findAvailablePort() {
  return new Promise((resolve, reject) => {
    let currentPort = BASE_PORT;
    let attemptCount = 0;

    function tryPort() {
      if (attemptCount >= MAX_PORT_ATTEMPTS) {
        reject(new Error('Could not find an available port after multiple attempts'));
        return;
      }

      attemptCount++;
      logger.info(`Trying port ${currentPort}...`);

      const server = net.createServer();
      
      server.once('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          logger.info(`Port ${currentPort} is in use, trying next port`);
          currentPort++;
          tryPort();
        } else {
          reject(err);
        }
      });

      server.once('listening', () => {
        const foundPort = server.address().port;
        server.close(() => {
          logger.info(`Found available port: ${foundPort}`);
          resolve(foundPort);
        });
      });

      server.listen(currentPort);
    }

    tryPort();
  });
}

/**
 * Start the server process
 * @param {number} port - The port to use
 * @returns {Promise<ChildProcess>} The server process
 */
function startServer(port) {
  logger.info(`Starting server on port ${port}`);
  
  return new Promise((resolve) => {
    // Start the server process
    const serverProcess = fork(SERVER_SCRIPT, [], {
      env: { ...process.env, PORT: port },
      stdio: 'pipe'
    });
    
    // Handle server output
    serverProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      logger.info(`[SERVER] ${output}`);
    });
    
    serverProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      logger.error(`[SERVER ERROR] ${output}`);
    });
    
    // Handle server exit
    serverProcess.on('exit', (code) => {
      logger.info(`Server process exited with code ${code}`);
    });
    
    // Wait for server to start
    setTimeout(() => {
      logger.info('Server should be ready now');
      resolve(serverProcess);
    }, SERVER_STARTUP_DELAY);
  });
}

/**
 * Run the client process
 * @param {string} serverUrl - The URL of the server
 * @returns {Promise<Object>} The simulation results
 */
async function runClient(serverUrl) {
  logger.info(`Starting client to connect to ${serverUrl}`);
  
  return new Promise((resolve, reject) => {
    let clientOutput = '';
    let clientError = '';
    
    // Start the client process
    const clientProcess = fork(CLIENT_SCRIPT, [serverUrl], { stdio: 'pipe' });
    
    // Handle client output
    clientProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      clientOutput += output + '\n';
      logger.info(`[CLIENT] ${output}`);
    });
    
    clientProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      clientError += output + '\n';
      logger.error(`[CLIENT ERROR] ${output}`);
    });
    
    // Handle client completion
    clientProcess.on('exit', (code) => {
      logger.info(`Client process exited with code ${code}`);
      
      if (code === 0) {
        // Try to extract JSON results from output
        try {
          // Look for simulation results in output
          const resultsMatch = clientOutput.match(/Bot statistics:([\s\S]*)/);
          const results = {
            success: true,
            output: clientOutput,
            error: clientError,
            stats: resultsMatch ? resultsMatch[1].trim() : 'No statistics found'
          };
          
          resolve(results);
        } catch (error) {
          logger.error(`Failed to parse client results: ${error.message}`);
          resolve({
            success: true,
            output: clientOutput,
            error: clientError,
            stats: 'Unable to parse statistics'
          });
        }
      } else {
        reject(new Error(`Client failed with exit code ${code}`));
      }
    });
    
    // Set timeout
    const timeout = setTimeout(() => {
      logger.error(`Client timed out after ${CLIENT_TIMEOUT}ms`);
      clientProcess.kill();
      reject(new Error('Client process timed out'));
    }, CLIENT_TIMEOUT);
    
    // Clear timeout on exit
    clientProcess.on('exit', () => clearTimeout(timeout));
  });
}

/**
 * Save results to file
 * @param {Object} results - The simulation results
 */
function saveResults(results) {
  logger.info(`Saving results to ${RESULTS_FILE}`);
  
  try {
    fs.writeFileSync(RESULTS_FILE, JSON.stringify(results, null, 2));
    logger.info('Results saved successfully');
  } catch (error) {
    logger.error(`Failed to save results: ${error.message}`);
  }
}

/**
 * Run the complete test
 */
async function runTest() {
  let serverProcess = null;
  
  try {
    logger.info('Starting simulation test');
    
    // Find an available port
    const port = await findAvailablePort();
    const serverUrl = `http://localhost:${port}`;
    
    // Start the server
    serverProcess = await startServer(port);
    
    // Run the client
    const results = await runClient(serverUrl);
    
    // Save results
    saveResults(results);
    
    logger.info('Test completed successfully');
    return results;
  } catch (error) {
    logger.error(`Test failed: ${error.message}`);
    throw error;
  } finally {
    // Clean up server process
    if (serverProcess) {
      logger.info('Shutting down server');
      serverProcess.kill();
    }
  }
}

// Run the test
runTest()
  .then((results) => {
    console.log('\nTest completed successfully!');
    console.log('Results summary:');
    console.log(results.stats);
    process.exit(0);
  })
  .catch((error) => {
    console.error('\nTest failed:', error.message);
    process.exit(1);
  }); 