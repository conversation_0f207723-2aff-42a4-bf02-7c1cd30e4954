/**
 * Simulation.js
 * Main class that manages the simulation of games with bots.
 */

const BotFactory = require('./BotFactory');
const { EventEmitter } = require('events');
const fs = require('fs');
const path = require('path');

/**
 * Class that manages bot simulations
 */
class Simulation extends EventEmitter {
  /**
   * Create a new simulation manager
   * @param {object} params - Configuration parameters
   * @param {object} params.gameState - Reference to the game state
   * @param {object} params.io - Socket.io instance for broadcasting updates
   * @param {object} [params.logger] - Logger for recording simulation actions
   */
  constructor({ gameState, io, logger = console }) {
    super();
    this.gameState = gameState;
    this.io = io;
    this.logger = logger;
    this.botFactory = new BotFactory({ gameState, logger });

    this.bots = [];
    this.running = false;
    this.paused = false;
    this.simulationSpeed = 1.0; // Normal speed multiplier
    this.currentSimulation = null;
    this.totalSimulations = 0;
    this.statistics = {
      gamesPlayed: 0,
      gamesInProgress: 0,
      averageRounds: 0,
      averageTurns: 0,
      winsByStrategy: {},
      totalEvents: 0,
      lastGameRoundCount: 0,
      lastGameCompleted: null
    };

    // Track bots for regular game mode
    this.gameBots = [];
    this.isProcessingBotTurn = false;
    // Initialize a queue to capture all turnChanged events
    this._turnQueue = [];

    // Default simulation configuration
    this.config = {
      autoRestart: false,
      maxGames: 1,
      saveResults: true,
      resultsDirectory: path.join(__dirname, '../simulation_results'),
      simulationSpeed: 1.0,
      maxMemoryUsageMB: 1024, // Default 1GB memory limit
      memoryCheckFrequency: 10 // Check memory every N turns
    };

    // Listen for turn changes and queue them to ensure all turn notifications are handled
    this.gameState.on('turnChanged', (currentPlayerId) => {
      this._turnQueue.push(currentPlayerId);
      this._processTurnQueue();
    });

    // Ensure results directory exists
    this._ensureResultsDirectory();
  }

  /**
   * Configure a bot for use in regular game mode (not simulation)
   * @param {Bot} bot - The bot to configure
   */
  configureBotForGameMode(bot) {
    // Set bot's game state reference
    bot.setGameState(this.gameState);

    // Add to game bots list for turn processing
    if (!this.gameBots.includes(bot)) {
      this.gameBots.push(bot);
      this.logger.info(`Configured bot ${bot.name} for game mode`);
    }
  }

  /**
   * Process bot turn if the current player is a bot
   * @param {string} currentPlayerId - ID of the current player
   * @private
   */
  async _processBotTurnIfNeeded(currentPlayerId) {
    // Skip if already processing a bot turn or no game bots
    if (this.isProcessingBotTurn || this.gameBots.length === 0) {
      return;
    }

    // Find if current player is a bot
    const currentBot = this.gameBots.find(bot => bot.id === currentPlayerId);
    if (!currentBot) {
      return; // Not a bot's turn
    }

    try {
      this.isProcessingBotTurn = true;
      this.logger.info(`Processing turn for bot ${currentBot.name}`);

      // Wait a short delay to give UI time to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Ensure bot has the latest game state reference
      if (currentBot.gameState !== this.gameState) {
        this.logger.warn(`Bot ${currentBot.name} had outdated game state reference. Updating...`);
        currentBot.setGameState(this.gameState);
      }

      // Prepare player state for the bot
      const playerState = this._preparePlayerStateForBot(currentBot);
      if (!playerState || !playerState.player) {
        this.logger.error(`Failed to prepare valid player state for bot ${currentBot.name}`);
        this.isProcessingBotTurn = false;
        return;
      }
      
      currentBot.updatePlayerState(playerState);

      // Plan and execute the bot's turn
      const actions = await currentBot.planTurn();
      if (actions && actions.length > 0) {
        for (const action of actions) {
          await this._executeBotAction(currentBot, action);
          // Small delay between actions
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } else {
        // If no actions planned, end the turn
        this.gameState.endTurn(currentBot.id);
      }
    } catch (error) {
      this.logger.error(`Error processing bot turn: ${error.message}`);
      // End turn if there's an error
      try {
        this.gameState.endTurn(currentBot.id);
      } catch (endTurnError) {
        this.logger.error(`Failed to end bot turn: ${endTurnError.message}`);
      }
    } finally {
      this.isProcessingBotTurn = false;
      // Broadcast game state after bot turn
      this._broadcastGameState();
    }
  }

  /**
   * Execute a bot action in game mode
   * @param {Bot} bot - The bot taking the action
   * @param {object} action - The action to execute
   * @private
   */
  async _executeBotAction(bot, action) {
    if (!action || !action.action) {
      return;
    }

    try {
      switch (action.action) {
        case 'move':
          this.gameState.movePlayer(
            bot.id,
            action.params.path,
            action.params.travelCardIds,
            action.params.extraHopCount || 0,
            action.params.isTriathlon || false
          );
          break;

        case 'pickCards':
          this.gameState.pickCards(bot.id, action.params);
          break;

        case 'collectJourney':
          this.gameState.collectJourney(
            bot.id,
            action.params.journeyCardId,
            action.params.journeyType
          );
          break;

        case 'trade':
          this.gameState.tradeEnergyCubes(bot.id, action.params.selectedCubes);
          break;

        case 'selectTravelCards':
          this.gameState.handleTravelCardSelection(bot.id, action.params.selectedCardIds);
          break;

        case 'selectEnergyCubes':
          this.gameState.handleEnergyCubeSelection(bot.id, action.params.selectedCubes);
          break;

        case 'endTurn':
          this.gameState.endTurn(bot.id);
          break;

        default:
          this.logger.warn(`Unknown bot action: ${action.action}`);
          break;
      }
    } catch (error) {
      this.logger.error(`Error executing bot action (${action.action}): ${error.message}`);
      throw error;
    }
  }

  /**
   * Ensure the results directory exists
   * @private
   */
  _ensureResultsDirectory() {
    try {
      if (!fs.existsSync(this.config.resultsDirectory)) {
        fs.mkdirSync(this.config.resultsDirectory, { recursive: true });
      }
    } catch (error) {
      this.logger.error('Failed to create results directory:', error);
    }
  }

  /**
   * Set simulation configuration
   * @param {object} config - Configuration options
   * @returns {object} The updated configuration
   */
  configure(config = {}) {
    // Update the simulation configuration with provided values
    this.config = {
      ...this.config,
      ...config
    };

    // Log the updated configuration
    this.logger.info(`Simulation configuration updated: ${JSON.stringify(this.config)}`);

    // Update simulation speed
    if (config.simulationSpeed) {
      this.simulationSpeed = config.simulationSpeed;
    }

    // Update bot thinking speed separately if provided
    const botThinkingSpeed = config.botThinkingSpeed || config.simulationSpeed;
    if (botThinkingSpeed) {
      // Update thinking speed for all bots
      for (const bot of this.bots) {
        bot.setThinkingSpeed(botThinkingSpeed);
      }
      this.logger.info(`Updated bot thinking speed to ${botThinkingSpeed}x`);
    }

    return this.config;
  }

  /**
   * Add a bot to the simulation
   * @param {object} botConfig - Bot configuration (name, strategy, etc.)
   * @returns {object} The created bot
   */
  addBot(botConfig) {
    if (this.running && !this.paused) {
      throw new Error('Cannot add bots while simulation is running');
    }

    // Normalize the bot configuration to handle both strategy and strategyName
    // This ensures backwards compatibility with older code
    const normalizedConfig = {
      ...botConfig,
      // If strategyName isn't provided but strategy is, use that as strategyName
      strategyName: botConfig.strategyName || botConfig.strategy || 'random'
    };

    this.logger.info(`Adding bot ${normalizedConfig.name} with ${normalizedConfig.strategyName} strategy`);

    // Add the bot with current simulation speed
    const bot = this.botFactory.createBot({
      ...normalizedConfig,
      options: {
        thinkingSpeed: this.simulationSpeed,
        maxDecisionsLogSize: 50 // Limit decision log size for memory efficiency
      }
    });

    this.bots.push(bot);
    this.logger.info(`Added bot ${bot.name} with ${bot.strategy.name} strategy`);

    return bot;
  }

  /**
   * Remove a bot from the simulation
   * @param {string} botId - ID of the bot to remove
   * @returns {boolean} Whether the bot was removed
   */
  removeBot(botId) {
    if (this.running && !this.paused) {
      throw new Error('Cannot remove bots while simulation is running');
    }

    const initialLength = this.bots.length;
    this.bots = this.bots.filter(bot => bot.id !== botId);

    return this.bots.length < initialLength;
  }

  /**
   * Check if the process is using too much memory
   * @returns {boolean} True if memory usage exceeds limits
   * @private
   */
  _isMemoryUsageTooHigh() {
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const rssMemoryMB = Math.round(memoryUsage.rss / 1024 / 1024);

    // Log memory usage periodically
    if (this.currentSimulation &&
        this.currentSimulation.turnCount % this.config.memoryCheckFrequency === 0) {
      this.logger.info(`Memory usage - Heap: ${heapUsedMB}MB, RSS: ${rssMemoryMB}MB`);
    }

    // Check if memory usage exceeds the configured limit
    if (heapUsedMB > this.config.maxMemoryUsageMB || rssMemoryMB > this.config.maxMemoryUsageMB * 1.2) {
      this.logger.warn(`Memory usage too high: Heap: ${heapUsedMB}MB, RSS: ${rssMemoryMB}MB`);
      return true;
    }

    return false;
  }

  /**
   * Attempt to free memory by clearing caches and running garbage collection
   * @private
   */
  _attemptMemoryRecovery() {
    // Clear any temporary caches
    this.bots.forEach(bot => {
      if (bot.decisionsLog && bot.decisionsLog.length > 10) {
        bot.decisionsLog = bot.decisionsLog.slice(-10);
      }
    });

    // Suggest garbage collection if available
    if (global.gc) {
      this.logger.info('Attempting to run garbage collection');
      global.gc();
    }
  }

  /**
   * Start the simulation
   * @param {object} [params] - Start parameters
   * @param {number} [params.maxGames] - Maximum number of games to simulate
   * @param {boolean} [params.autoRestart] - Whether to auto-restart when a game ends
   * @returns {Promise} Resolves when the simulation is started
   */
  async start(params = {}) {
    if (this.running) {
      if (this.paused) {
        return this.resume();
      }
      throw new Error('Simulation is already running');
    }

    // Update config with any provided parameters
    if (params.maxGames !== undefined) this.config.maxGames = params.maxGames;
    if (params.autoRestart !== undefined) this.config.autoRestart = params.autoRestart;

    // Debug: Log the number of bots and their details
    this.logger.info(`Starting simulation with ${this.bots.length} bots`);
    this.bots.forEach(bot => {
      this.logger.info(`Bot found: id=${bot.id}, name=${bot.name}, strategy=${bot.strategy.name}`);
    });

    // Check if we have enough bots
    if (this.bots.length < 2) {
      this.logger.error(`Not enough bots to start simulation. Found ${this.bots.length} bots, need at least 2.`);
      throw new Error('Need at least 2 bots to start simulation');
    }

    // Record the previous 'started' state in case we need to restore it
    const wasStarted = this.gameState.started;

    // Reset the game state, but only if we're starting a simulation
    if (!wasStarted) {
      // Normal user game isn't started, so we can reset the gameState
      this.logger.info('Resetting game state before starting simulation');
      this.gameState.reset();
    } else {
      // If a regular player game is already in progress, we shouldn't disturb it
      this.logger.warn('A player game is already in progress. Simulation cannot start until that game is finished.');
      throw new Error('Cannot start simulation while a player game is in progress');
    }

    // Add bots to the game
    this.logger.info(`Adding ${this.bots.length} bots to game state`);
    for (const bot of this.bots) {
      const success = this.gameState.addPlayer(bot.id, bot.name);
      this.logger.info(`Added bot ${bot.name} to game state: ${success ? 'success' : 'failed'}`);
    }

    // Start the game
    const success = this.gameState.startGame();
    if (!success) {
      this.logger.error('Failed to start the game');
      throw new Error('Failed to start the game');
    }

    // Mark simulation as running
    this.running = true;
    this.paused = false;
    this.currentSimulation = {
      startTime: Date.now(),
      gameNumber: ++this.totalSimulations,
      turnCount: 0,
      botPerformance: {}
    };
    this.statistics.gamesInProgress++;

    // Emit event
    this.emit('simulationStarted', {
      gameNumber: this.currentSimulation.gameNumber,
      botsCount: this.bots.length,
      botNames: this.bots.map(b => b.name),
      strategies: this.bots.map(b => b.strategy.name)
    });

    // Broadcast game state update
    this._broadcastGameState();

    // Start the simulation loop
    this._loop();

    return {
      started: true,
      gameNumber: this.currentSimulation.gameNumber
    };
  }

  /**
   * Stop the current simulation
   * @returns {object} Simulation results
   */
  stop() {
    if (!this.running) {
      this.logger.info('Simulation is not running');
      return;
    }

    this.logger.info('Stopping simulation');
    
    // If we're stopping in the middle of a game, record it as completed
    if (this.running && this.currentSimulation) {
      // Update statistics to indicate the game was stopped
      this.statistics.gamesCompleted = this.statistics.gamesPlayed + 1;
      
      // Store the round count of the stopped game
      this.statistics.lastGameRoundCount = this.gameState.roundCount;
      
      // Collect any results that might be available
      const results = this._collectResults();
      
      // Emit game completed event
      this.emit('gameCompleted', results);
    }
    
    this.running = false;
    this.paused = false;

    // Clear any pending timeouts
    if (this.currentTimeout) {
      clearTimeout(this.currentTimeout);
      this.currentTimeout = null;
    }
    
    // Clear the bots array to prepare for the next game
    // Store the count before clearing for logging
    const botCount = this.bots.length;
    this.bots = [];
    this.logger.info(`Cleared ${botCount} bots for next game`);
    
    // Reset the game state
    this.gameState.reset();
    
    // Explicitly ensure the started flag is set to false
    this.gameState.started = false;
    
    this.logger.info('Game state reset for next game');

    // Signal completion
    this._handleAllSimulationsCompleted();
  }

  /**
   * Pause the simulation
   * @returns {boolean} Whether the simulation was paused
   */
  pause() {
    if (!this.running || this.paused) {
      return false;
    }

    this.paused = true;
    this.emit('simulationPaused', {
      gameNumber: this.currentSimulation.gameNumber,
      turnCount: this.currentSimulation.turnCount
    });

    return true;
  }

  /**
   * Resume a paused simulation
   * @returns {boolean} Whether the simulation was resumed
   */
  resume() {
    if (!this.running || !this.paused) {
      return false;
    }

    this.paused = false;
    this.emit('simulationResumed', {
      gameNumber: this.currentSimulation.gameNumber,
      turnCount: this.currentSimulation.turnCount
    });

    // Restart the simulation loop
    this._loop();

    return true;
  }

  /**
   * Get the current simulation stats
   * @returns {object} Current simulation statistics
   */
  getStats() {
    const currentGame = this.running ? {
      gameNumber: this.currentSimulation.gameNumber,
      turnCount: this.currentSimulation.turnCount,
      roundCount: this.gameState.roundCount,
      elapsedTime: Date.now() - this.currentSimulation.startTime,
      status: this.paused ? 'paused' : 'running'
    } : null;

    // Get memory usage information
    const memoryUsage = process.memoryUsage();
    const memoryStats = {
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      rss: Math.round(memoryUsage.rss / 1024 / 1024)
    };

    return {
      ...this.statistics,
      currentGame,
      // Get simplified bot stats to reduce memory usage
      botStats: this.bots.map(bot => ({
        id: bot.id,
        name: bot.name,
        strategy: bot.strategy?.name,
        turns: bot.turnsPlayed,
        actions: bot.actionsTaken
      })),
      running: this.running,
      paused: this.paused,
      simulationSpeed: this.simulationSpeed,
      config: this.config,
      memory: memoryStats
    };
  }

  /**
   * Get available bot strategies
   * @returns {Array<string>} List of available strategy names
   */
  getAvailableStrategies() {
    return this.botFactory.getAvailableStrategies();
  }

  /**
   * Main simulation loop
   * @private
   */
  async _loop() {
    if (!this.running || this.paused) {
      return;
    }

    // Check memory usage periodically
    if (this.currentSimulation.turnCount % this.config.memoryCheckFrequency === 0) {
      if (this._isMemoryUsageTooHigh()) {
        // Try to recover memory
        this._attemptMemoryRecovery();

        // If still too high after recovery attempt, stop simulation
        if (this._isMemoryUsageTooHigh()) {
          this.logger.error('Memory usage exceeded limits. Stopping simulation to prevent crash.');
          this.stop();
          return;
        }
      }
    }

    // Enhanced infinite loop detection
    // Track how many times each bot has taken its turn
    if (!this.currentSimulation.botTurnCounts) {
      this.currentSimulation.botTurnCounts = {};
      this.currentSimulation.lastAction = {};
      this.currentSimulation.repeatedActions = {};
      this.currentSimulation.stuckTurns = {};
    }

    // Get the current player's turn
    const currentPlayerIndex = this.gameState.turnIndex;
    const currentPlayerId = this.gameState.players[currentPlayerIndex]?.id;

    // Track turns per bot
    if (currentPlayerId) {
      this.currentSimulation.botTurnCounts[currentPlayerId] =
        (this.currentSimulation.botTurnCounts[currentPlayerId] || 0) + 1;

      // Check for very high turn counts for a specific bot (potential stuck)
      if (this.currentSimulation.botTurnCounts[currentPlayerId] > 30) {
        const botName = this.bots.find(b => b.id === currentPlayerId)?.name || 'Unknown';
        this.logger.warn(`Bot ${botName} has taken ${this.currentSimulation.botTurnCounts[currentPlayerId]} turns - possible stuck condition`);

        // If a bot has taken way too many turns, abort
        if (this.currentSimulation.botTurnCounts[currentPlayerId] > 50) {
          this.logger.error(`Bot ${botName} appears to be stuck in a loop. Forcing simulation to stop.`);
          this.stop();
          return;
        }
      }
    }

    // Original code for checking excessive turns per player
    const turnsPerPlayer = this.currentSimulation.turnCount / this.bots.length;
    if (turnsPerPlayer > 50) {
      this.logger.warn(`Detected potential infinite loop: ${turnsPerPlayer.toFixed(1)} turns per player`);

      // Log detailed memory usage
      const memUsage = process.memoryUsage();
      this.logger.warn(`Memory usage during potential infinite loop: Heap=${Math.round(memUsage.heapUsed/1024/1024)}MB, RSS=${Math.round(memUsage.rss/1024/1024)}MB`);

      // Check if we're in an abnormal infinite loop and not just a long game
      if (turnsPerPlayer > 100) {
        this.logger.error(`Simulation aborted: Likely infinite loop detected (${turnsPerPlayer.toFixed(1)} turns per player)`);
        this.stop();
        return;
      }
    }

    // Check if the game is over
    if (this.gameState._isGameOver()) {
      this.logger.info('Game over!');

      // Get the results
      const results = this._collectResults();

      // Update statistics
      this.statistics.gamesPlayed++;
      this.statistics.gamesInProgress--;
      this.statistics.averageRounds = (
        (this.statistics.averageRounds * (this.statistics.gamesPlayed - 1)) +
        this.gameState.roundCount
      ) / this.statistics.gamesPlayed;
      
      // Store the last game's round count for status queries
      this.statistics.lastGameRoundCount = this.gameState.roundCount;

      // Update wins by strategy
      const winner = this.gameState.getGameResult();
      if (winner) {
        const winnerBot = this.bots.find(b => b.id === winner.id);
        if (winnerBot) {
          const strategyName = winnerBot.strategy.name;
          this.statistics.winsByStrategy[strategyName] =
            (this.statistics.winsByStrategy[strategyName] || 0) + 1;
        }
      }

      // Track the total number of events
      if (this.gameState.gameEvents) {
        this.statistics.totalEvents += this.gameState.gameEvents.length;
      }

      // Emit event
      this.emit('gameCompleted', results);

      // Save results if configured
      if (this.config.saveResults) {
        this._saveResults(results);
      }

      // Attempt memory recovery before potentially starting a new game
      this._attemptMemoryRecovery();

      // Check if we should restart or stop
      if (
        this.config.autoRestart &&
        this.statistics.gamesPlayed < this.config.maxGames
      ) {
        this.logger.info('Auto-restarting simulation...');

        // Reset the simulation
        this.running = false;

        // Start a new simulation after a short delay
        setTimeout(() => {
          this.start()
            .catch(error => {
              this.logger.error('Failed to auto-restart simulation:', error);
            });
        }, 1000);
      } else {
        this.running = false;
        this.emit('allSimulationsCompleted', this.statistics);
      }

      return;
    }

    // Initialize error tracking if not already present
    if (!this.currentSimulation.consecutiveErrors) {
      this.currentSimulation.consecutiveErrors = 0;
      this.currentSimulation.errorsByBot = {};
    }

    // Find the bot for the current player
    const currentBot = this.bots.find(bot => bot.id === currentPlayerId);

    if (currentBot) {
      try {
        // Check if we've exceeded the maximum allowed consecutive errors
        if (this.currentSimulation.consecutiveErrors > 10) {
          throw new Error(`Simulation aborted: Maximum consecutive errors (10) exceeded`);
        }

        // Check if this bot has had too many errors in a row
        const botErrorCount = this.currentSimulation.errorsByBot[currentBot.id] || 0;
        if (botErrorCount > 5) {
          throw new Error(`Simulation aborted: Bot ${currentBot.name} has had more than 5 consecutive errors`);
        }

        // Increment turn count
        this.currentSimulation.turnCount++;

        // Track if this is more than 100 turns per player, which might indicate an infinite loop
        const turnsPerPlayer = this.currentSimulation.turnCount / this.bots.length;
        if (turnsPerPlayer > 100) {
          this.logger.warn(`Detected potential infinite loop: ${turnsPerPlayer.toFixed(1)} turns per player`);

          // If we've clearly gone too far, abort the simulation
          if (turnsPerPlayer > 150) {
            throw new Error(`Simulation aborted: Potential infinite loop detected (${turnsPerPlayer.toFixed(1)} turns per player)`);
          }
        }

        // Prepare and set the player state for the bot before it takes its turn
        this._preparePlayerStateForBot(currentBot);

        // Verify it's actually this bot's turn before proceeding
        const playerState = currentBot.getPlayerState();
        if (!playerState || !playerState.isMyTurn) {
          this.logger.error(`Bot ${currentBot.name} was asked to take a turn when it's not their turn. Current player: ${this.gameState.players[this.gameState.turnIndex]?.name}`);

          // Skip this bot's turn and continue the simulation
          setTimeout(() => this._loop(), this._getDelayTime());
          return;
        }

        // Take the bot's turn
        await currentBot.takeTurn();

        // Reset consecutive error count for this bot since turn was successful
        this.currentSimulation.errorsByBot[currentBot.id] = 0;
        this.currentSimulation.consecutiveErrors = 0;

        // Track bot performance (with minimal data)
        if (!this.currentSimulation.botPerformance[currentBot.id]) {
          this.currentSimulation.botPerformance[currentBot.id] = {
            turnsPlayed: 0,
            actionsTaken: 0,
            errors: 0
          };
        }
        this.currentSimulation.botPerformance[currentBot.id].turnsPlayed++;
        this.currentSimulation.botPerformance[currentBot.id].actionsTaken++;

        // Broadcast game state update
        this._broadcastGameState();

        // Continue the loop after a delay based on simulation speed
        setTimeout(() => this._loop(), this._getDelayTime());
      } catch (error) {
        this.logger.error(`Error during bot ${currentBot.name}'s turn: ${error.message}`);

        // Increment consecutive error counts
        this.currentSimulation.consecutiveErrors++;
        this.currentSimulation.errorsByBot[currentBot.id] =
          (this.currentSimulation.errorsByBot[currentBot.id] || 0) + 1;

        // Track the error
        if (this.currentSimulation.botPerformance[currentBot.id]) {
          this.currentSimulation.botPerformance[currentBot.id].errors++;
        }

        // If we've had too many consecutive errors, stop the simulation
        if (this.currentSimulation.consecutiveErrors > 10 ||
            this.currentSimulation.errorsByBot[currentBot.id] > 5 ||
            error.message.includes('Simulation aborted')) {
          this.logger.error(`Too many errors in simulation, stopping: ${this.currentSimulation.consecutiveErrors} consecutive errors`);
          this.stop();
          return;
        }

        // Check memory usage
        if (this._isMemoryUsageTooHigh()) {
          this.logger.error('Memory usage too high after error. Stopping simulation.');
          this.stop();
          return;
        }

        // Try to end the turn and continue
        try {
          this.gameState.endTurn(currentBot.id);

          // Broadcast game state update
          this._broadcastGameState();

          // Continue the loop after a longer delay to allow for recovery
          setTimeout(() => this._loop(), this._getDelayTime() * 2);
        } catch (endTurnError) {
          this.logger.error(`Failed to recover from bot error: ${endTurnError.message}`);
          this.stop();
        }
      }
    } else {
      this.logger.error(`No bot found for player ID: ${currentPlayerId}`);
      this.stop();
    }
  }

  /**
   * Calculate delay time based on simulation speed
   * @returns {number} Milliseconds to delay
   * @private
   */
  _getDelayTime() {
    return Math.max(10, Math.floor(50 / this.simulationSpeed));
  }

  /**
   * Broadcast the current game state to all connected clients
   * @private
   */
  _broadcastGameState() {
    if (!this.io) return;

    try {
      // Get the current game state
      const gameState = this.gameState.getState();

      // Add simulation status information
      const stateWithSimInfo = {
        ...gameState,
        simulation: {
          running: this.running,
          paused: this.paused,
          simulationSpeed: this.simulationSpeed,
          serverTime: Date.now()
        }
      };

      // Sanitize the state for broadcast
      const sanitizedState = this._sanitizeStateForBroadcast(stateWithSimInfo);

      // Send the state to all connected clients
      this.io.emit('gameState', sanitizedState);
    } catch (error) {
      this.logger.error(`Error broadcasting game state: ${error.message}`);
    }
  }

  /**
   * Collect results from the current simulation
   * @returns {object} Results of the simulation
   * @private
   */
  _collectResults() {
    // Get the game result
    const winner = this.gameState.getGameResult();
    const botWinner = winner ? this.bots.find(b => b.id === winner.id) : null;
    
    // Create results object
    const results = {
      gameNumber: this.currentSimulation.gameNumber,
      startTime: this.currentSimulation.startTime,
      endTime: Date.now(),
      duration: Date.now() - this.currentSimulation.startTime,
      roundCount: this.gameState.roundCount,
      turnCount: this.currentSimulation.turnCount,
      winner: winner ? {
        id: winner.id,
        name: winner.name,
        strategy: botWinner ? botWinner.strategy.name : 'unknown',
        outerScore: winner.outerScore || 0,
        innerScore: winner.innerScore || 0,
        totalScore: (winner.outerScore || 0) + (winner.innerScore || 0)
      } : null,
      players: this.gameState.players.map(player => {
        const bot = this.bots.find(b => b.id === player.id);
        return {
          id: player.id,
          name: player.name,
          strategy: bot ? bot.strategy.name : 'unknown',
          outerScore: player.outerScore || 0,
          innerScore: player.innerScore || 0,
          totalScore: (player.outerScore || 0) + (player.innerScore || 0),
          isWinner: winner ? player.id === winner.id : false
        };
      }),
      eventCounts: this._countEventsByType()
    };
    
    // Store the last game's round count for status endpoint
    this.statistics.lastGameRoundCount = this.gameState.roundCount;
    
    return results;
  }

  /**
   * Count events by type
   * @returns {object} Event counts by type
   * @private
   */
  _countEventsByType() {
    const counts = {};

    if (this.gameState.gameEvents) {
      this.gameState.gameEvents.forEach(event => {
        counts[event.type] = (counts[event.type] || 0) + 1;
      });
    }

    return counts;
  }

  /**
   * Save simulation results to a file
   * @param {object} results - Results to save
   * @private
   */
  _saveResults(results) {
    try {
      const filename = `game_${results.gameNumber}_${Date.now()}.json`;
      const filePath = path.join(this.config.resultsDirectory, filename);

      // Create a complete results object with limited data to save memory
      const completeResults = {
        ...results,
        // Only include essential game events - limit to last 100 events
        gameEvents: this.gameState.gameEvents.length > 100 ?
          this.gameState.gameEvents.slice(-100) : this.gameState.gameEvents,
        timestamp: Date.now(),
        bots: this.bots.map(bot => ({
          id: bot.id,
          name: bot.name,
          strategy: bot.strategy.name,
          turnsPlayed: bot.turnsPlayed,
          actionsTaken: bot.actionsTaken
        })),
        simulationSpeed: this.simulationSpeed,
        roundSummaries: this.gameState.roundSummaries
      };

      // Write to file
      fs.writeFileSync(
        filePath,
        JSON.stringify(completeResults, null, 2),
        'utf8'
      );

      this.logger.info(`Saved simulation results to ${filePath}`);

      // Return the path for reference
      return filePath;
    } catch (error) {
      this.logger.error(`Failed to save simulation results: ${error.message}`);
      return null;
    }
  }

  /**
   * Get a list of all saved simulation files
   * @returns {Promise<Array<object>>} Array of simulation file info
   */
  async getSavedSimulations() {
    try {
      const files = fs.readdirSync(this.config.resultsDirectory);

      // Filter for only JSON files that match our naming pattern
      const simulationFiles = files
        .filter(file => file.match(/^game_\d+_\d+\.json$/))
        .map(file => {
          const filePath = path.join(this.config.resultsDirectory, file);
          const stats = fs.statSync(filePath);

          // Extract game number from the filename
          const gameNumber = parseInt(file.match(/^game_(\d+)_/)[1], 10);

          return {
            fileName: file,
            filePath,
            gameNumber,
            createdAt: stats.birthtime,
            size: stats.size
          };
        })
        .sort((a, b) => b.createdAt - a.createdAt); // Sort newest first

      return simulationFiles;
    } catch (error) {
      this.logger.error(`Failed to list saved simulations: ${error.message}`);
      return [];
    }
  }

  /**
   * Load a saved simulation file
   * @param {string} fileName - Name of the file to load
   * @returns {Promise<object>} The loaded simulation data
   */
  async loadSavedSimulation(fileName) {
    try {
      const filePath = path.join(this.config.resultsDirectory, fileName);
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      this.logger.error(`Failed to load simulation ${fileName}: ${error.message}`);
      throw new Error(`Failed to load simulation: ${error.message}`);
    }
  }

  /**
   * Run a single simulation iteration (bot turn)
   * @private
   */
  async _runSimulationTick() {
    if (!this.running || this.paused) {
      return;
    }

    try {
      // Get current bot turn
      const currentBot = this._getCurrentBot();
      if (!currentBot) {
        this.logger.warn('No current bot found for simulation tick');
        return;
      }

      // Track memory usage
      if (this.config.memoryCheckFrequency > 0 &&
          this.turnCount % this.config.memoryCheckFrequency === 0) {
        this._checkMemoryUsage(currentBot);
      }

      // Log current turn info
      this.logger.info(`Bot ${currentBot.name} is taking turn ${currentBot.turnsPlayed + 1} - Memory: Heap=${this._getHeapUsageMB()}MB, RSS=${this._getRSSUsageMB()}MB`);

      // Prepare the player state for the bot
      this._preparePlayerStateForBot(currentBot);

      // Verify it's actually this bot's turn before proceeding
      const playerState = currentBot.getPlayerState();
      if (!playerState || !playerState.isMyTurn) {
        this.logger.error(`Bot ${currentBot.name} was asked to take a turn when it's not their turn. Current player: ${this.gameState.players[this.gameState.turnIndex]?.name}`);

        // Skip this bot's turn and continue the simulation
        this._advanceTurn();
        const tickDelay = this._calculateTickDelay();
        this.tickTimeout = setTimeout(() => this._runSimulationTick(), tickDelay);
        return;
      }

      // Execute the bot's turn
      try {
        await currentBot.takeTurn();

        // Broadcast game state after each bot turn
        this._broadcastGameState();

      } catch (error) {
        this.logger.error(`Bot ${currentBot.name} encountered an error during turn ${currentBot.turnsPlayed + 1}: ${error.message}`);
        // Continue the simulation despite the error
      }

      // Move to next bot
      this._advanceTurn();

      // Check if the game is over
      if (this._isGameOver()) {
        this._endGame();
        return;
      }

      // Schedule next tick
      const tickDelay = this._calculateTickDelay();
      this.tickTimeout = setTimeout(() => this._runSimulationTick(), tickDelay);
    } catch (error) {
      this.logger.error(`Error in simulation tick: ${error.message}`);
      this.stop();
    }
  }

  /**
   * Prepare player state for a bot to take its turn
   * @param {Bot} bot - The bot to prepare state for
   * @private
   */
  _preparePlayerStateForBot(bot) {
    try {
      // Get the current player index
      const currentPlayerIndex = this.gameState.turnIndex;

      // Find the matching player from the game state
      const player = this.gameState.players.find(p => p.id === bot.id);

      if (!player) {
        this.logger.error(`Could not find player state for bot ${bot.id}`);
        // Instead of returning nothing, return a default state
        const defaultState = {
          isMyTurn: bot.id === this.gameState.players[currentPlayerIndex]?.id,
          player: {
            id: bot.id,
            name: bot.name,
            position: null,
            hand: [],
            journeyCards: [],
            collectedJourneys: [],
            energyCubes: { bhakti: 0, gnana: 0, karma: 0, artha: 0 },
            omTokens: { inner: 0, outer: 0 },
            omSlotsInner: [],
            omSlotsOuter: [],
            omTemp: 0
          },
          // Get locations and edges from game state if available
          locations: this.gameState.getState()?.locations || [],
          edges: this.gameState.getState()?.edges || [],
          faceUpTravel: this.gameState.faceUpTravel || [],
          faceUpEvent: this.gameState.faceUpEvent || [],
          faceUpJourneyInner: this.gameState.faceUpJourneyInner || [],
          faceUpJourneyOuter: this.gameState.faceUpJourneyOuter || [],
          locationOm: this.gameState.locationOm || {},
          locationCubes: this.gameState.locationCubes || {},
          // Include global event information
          currentGlobalEvent: this.gameState.currentGlobalEvent || null,
          roundCount: this.gameState.roundCount || 0,
          opponents: []
        };
        
        this.logger.warn(`Created default player state for bot ${bot.name} since player not found in game state`);
        return defaultState;
      }

      // Get full game state to ensure we have locations and edges
      const fullGameState = this.gameState.getState();

      // Create a sanitized state with just what the bot needs to know
      const playerState = {
        isMyTurn: bot.id === this.gameState.players[currentPlayerIndex]?.id,
        player: {
          id: player.id,
          name: player.name,
          position: player.position, // Use position directly from player object
          hand: player.hand || [],
          journeyCards: player.collectedJourneys || [],
          collectedJourneys: player.collectedJourneys || [], // Ensure collectedJourneys is properly passed
          energyCubes: player.energyCubes || { bhakti: 0, gnana: 0, karma: 0, artha: 0 },
          omTokens: {
            inner: (player.omSlotsInner || []).filter(x => x > 0).length,
            outer: (player.omSlotsOuter || []).filter(x => x > 0).length
          },
          omSlotsInner: player.omSlotsInner || [],
          omSlotsOuter: player.omSlotsOuter || [],
          omTemp: player.omTemp || 0
        },
        // Use the data from the full game state to ensure we have locations and edges
        locations: fullGameState.locations || [],
        edges: fullGameState.edges || [],
        faceUpTravel: this.gameState.faceUpTravel || [],
        faceUpEvent: this.gameState.faceUpEvent || [],
        faceUpJourneyInner: this.gameState.faceUpJourneyInner || [],
        faceUpJourneyOuter: this.gameState.faceUpJourneyOuter || [],
        locationOm: this.gameState.locationOm || {},
        locationCubes: this.gameState.locationCubes || {},
        // Include global event information
        currentGlobalEvent: this.gameState.currentGlobalEvent || null,
        roundCount: this.gameState.roundCount || 0,
        opponents: this.gameState.players
          .filter(p => p.id !== bot.id)
          .map(p => ({
            id: p.id,
            name: p.name,
            position: p.position,
            handSize: (p.hand || []).length,
            journeyCards: (p.collectedJourneys || []).length,
            omTokens: {
              inner: (p.omSlotsInner || []).filter(x => x > 0).length,
              outer: (p.omSlotsOuter || []).filter(x => x > 0).length
            }
          }))
      };

      // Set the player state on the bot
      bot.setPlayerState(playerState);

      // Log more detailed information about the player state we're setting
      this.logger.info(`Set player state for bot ${bot.name} - position=${playerState.player.position}, hand size=${playerState.player.hand.length}, collected journeys=${(playerState.player.collectedJourneys || []).length}, locations=${playerState.locations.length}, edges=${playerState.edges.length}`);
    } catch (error) {
      this.logger.error(`Error preparing player state for bot ${bot.name}: ${error.message}`);
    }
  }

  /**
   * Update the simulation to handle end turn only once per bot
   * Not after each action in a multi-action turn
   * @private
   */
  _updateAfterMultiActionBotTurn() {
    try {
      // Broadcast updated game state after the bot has taken multiple actions
      this._broadcastGameState();

      // Check if the game is over after the bot's turn
      if (this.gameState._isGameOver()) {
        this._handleGameOver();
      }
    } catch (error) {
      this.logger.error(`Error updating after bot multi-action turn: ${error.message}`);
    }
  }

  /**
   * Signal that all simulations have completed
   * @private
   */
  _handleAllSimulationsCompleted() {
    // Add timestamp to statistics
    this.statistics.endTime = Date.now();
    this.statistics.totalDuration = this.statistics.endTime - this.statistics.startTime;
    
    // If the last game wasn't properly recorded, update the counter
    if (this.currentSimulation) {
      // Record the final game's details if it wasn't already captured
      this.statistics.lastGameRoundCount = this.gameState.roundCount || 0;
      this.statistics.lastGameCompleted = Date.now();
    }
    
    // Log statistics
    this.logger.info(`All simulations completed: ${this.statistics.gamesPlayed} games played`);
    this.logger.info(`Average rounds per game: ${this.statistics.averageRounds.toFixed(1)}`);
    
    if (this.statistics.gamesPlayed > 0) {
      this.logger.info('Wins by strategy:');
      Object.entries(this.statistics.winsByStrategy).forEach(([strategy, wins]) => {
        const percentage = (wins / this.statistics.gamesPlayed * 100).toFixed(1);
        this.logger.info(`  ${strategy}: ${wins} wins (${percentage}%)`);
      });
    }
    
    // Emit completion event
    this.emit('allSimulationsCompleted', this.statistics);
  }

  async createBot(name, strategy) {
    try {
      this.logger.info(`Creating bot ${name} with ${strategy} strategy`);
      
      // Check if strategy exists
      const strategyModule = this.getStrategy(strategy);
      
      if (!strategyModule) {
        throw new Error(`Strategy ${strategy} not found`);
      }
      
      const id = `bot-${this.nextBotId++}`;
      
      // Create bot instance
      const bot = new Bot({
        id,
        name,
        strategy: strategyModule,
        gameState: this.gameState,
        logger: this.logger,
        options: {
          thinkingSpeed: this.simulationSpeed,
        }
      });
      
      // Set bot thinking speed
      bot.setThinkingSpeed(this.simulationSpeed);
      
      // Add bot to game through server API
      const botData = {
        name,
        id,
        strategy,
        thinkingSpeed: this.simulationSpeed,
      };
      
      // Register bot with server to initiate client connection
      await this.server.addBot(botData);
      
      // Store bot in our list
      this.bots.push(bot);
      
      this.logger.info(`Bot ${name} (${id}) created successfully`);
      
      // Return bot data
      return {
        id,
        name,
        strategy
      };
    } catch (error) {
      this.logger.error(`Error creating bot ${name}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Sanitize game state for broadcasting
   * @param {object} state - The game state to sanitize
   * @returns {object} - The sanitized state
   * @private
   */
  _sanitizeStateForBroadcast(state) {
    // If we receive a falsy state, return an empty object
    if (!state) return {};

    try {
      // Create a sanitized version of the state
      const sanitizedState = {
        started: state.started,
        roundCount: state.roundCount,
        turnCount: state.turnCount,
        turnIndex: state.turnIndex,
        // Include simulation info if available
        simulation: state.simulation,
        // Only include essential player information
        players: state.players?.map(player => ({
          id: player.id,
          name: player.name,
          position: player.position || player.currentNodeId,
          outerScore: player.outerScore || 0,
          innerScore: player.innerScore || 0,
          handSize: (player.hand || []).length,
          energyCubes: player.energyCubes || {},
          collectedJourneys: (player.collectedJourneys || []).length,
        })) || [],
        // Include game board information
        faceUpTravel: state.faceUpTravel || [],
        faceUpJourneyInner: state.faceUpJourneyInner || [],
        faceUpJourneyOuter: state.faceUpJourneyOuter || [],
        currentGlobalEvent: state.currentGlobalEvent,
        locations: state.locations,
        edges: state.edges,
        locationOm: state.locationOm || {},
        locationCubes: state.locationCubes || {}
      };

      return sanitizedState;
    } catch (error) {
      this.logger.error(`Error sanitizing state: ${error.message}`);
      // Return a minimal state if there was an error
      return {
        error: 'Error sanitizing state',
        started: state.started,
        simulation: state.simulation
      };
    }
  }

  /**
   * Process all queued turnChanged events sequentially
   * @private
   */
  async _processTurnQueue() {
    // If already processing a bot turn, defer processing
    if (this.isProcessingBotTurn) return;
    // Process each queued turn notification in order
    while (this._turnQueue.length > 0) {
      const currentPlayerId = this._turnQueue.shift();
      await this._processBotTurnIfNeeded(currentPlayerId);
    }
  }
}

module.exports = Simulation;