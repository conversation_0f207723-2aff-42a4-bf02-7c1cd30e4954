1. prevent multiple travel in single turn
2. prevent multiple choose actions in single turn (pick travel OR event OR journey)
3. disable end turn and actions for non-current player
4. prevent travel after choose-action completion
5. summarize energy color cubes
6. update event cards: extra hops +2
7. blank locations as airport locations.
8. fix UI of player holding event card WildCube
9. Resolve all tiles without energy cubes
10. Create regions and specify region of Journey cards
11. strategic location score allocation. for instance in trekking the world banff is furthest from airport and inter asia connection i.e least connected and awards more points.
12. improve airport to location connectivity; should be max distance of 3 hops. currently 63 to 20 is too many hops.

13. special effect for jyotirlinga without om token
14. locations at distance of 4 getting highlighted, refer gamestate_bug1.json
15. discard pile should get shuffled back into main deck as soon as there are less than 2 cards in main deck
16. end turn auto on collecting journey card, and if travel card are picked and travelled
17. largen player circles or use another icon
18. effect for which travel cards were picked by opponent
19. om sound when collecting om token
20. om meditation did not work on first turn, apply global effect may not be happening?
21. journey card images in compactjourneymat break on collecting a journey card
22. frozen north effect did not work; move failed:
Applying global event effect: Frozen North (frozen_north)
[2025-04-03T00:42:41.314Z] Player 1nhl4g4wVG4tlos9AAAB attempting to move: {
  path: [ 4, 61, 6 ],
  travelCardIds: [ 'T7' ],
  extraHopCount: 0,
  isTriathlon: false
}
Player 1nhl4g4wVG4tlos9AAAB attempting to move along path: 4 -> 61 -> 6
Applied Frozen North effect: Movement cost doubled for starting in North region
Travel values: sum=2, hops=2, extraHops=0
Invalid path length: 2 with multiplier 2 requires 1 but got 2
[2025-04-03T00:42:41.316Z] Player move failed