/* client/src/styles/main.css */

:root {
  --primary-color: #6C63FF;
  --secondary-color: #FF6584;
  --accent-color: #43C59E;
  --dark-color: #333333;
  --light-color: #FFFFFF;
  --background-color: #F8F9FA;
  --card-background: #FFFFFF;
  --border-radius: 8px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;

  /* Energy Colors */
  --artha-color: #F39C12;
  --karma-color: #27AE60;
  --gnana-color: #2980B9;
  --bhakti-color: #8E44AD;

  /* Region Colors - More vibrant and distinct */
  --north-color: rgba(33, 150, 243, 0.95);      /* Vibrant blue */
  --west-color: rgba(76, 175, 80, 0.95);        /* Vibrant green */
  --south-color: rgba(255, 191, 102, 0.95);     /* Light orange */
  --central-color: rgba(156, 39, 176, 0.95);    /* Vibrant purple */
  --east-color: #fd7c03ef;        /* Orange */
  --northeast-color: rgba(233, 30, 99, 0.95);   /* Vibrant pink */
  --jyotirlinga-color: rgba(255, 236, 130, 0.95); /* Light yellow for jyotirlingas */
  --airport-color: rgba(103, 58, 183, 0.95);    /* Vibrant indigo for airports */

  /* Journey Types */
  --inner-color: rgba(25, 118, 210, 0.95);     /* Dark blue for inner journey */
  --outer-color: rgba(56, 142, 60, 0.95);      /* Dark green for outer journey */
}

/* Base Styles */
body {
  margin: 0;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background: var(--background-color);
  color: var(--dark-color);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0.5rem 0;
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
  color: var(--primary-color);
}

h2 {
  font-size: 1.8rem;
  color: var(--dark-color);
}

h3 {
  font-size: 1.5rem;
}

/* Button Styles */
button {
  cursor: pointer;
  padding: 0.6rem 1.2rem;
  background: var(--primary-color);
  color: var(--light-color);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

button:hover {
  background: #5A52D5;
  transform: translateY(-2px);
}

button:active {
  transform: translateY(0);
}

button.secondary {
  background: var(--secondary-color);
}

button.secondary:hover {
  background: #E55A78;
}

button.accent {
  background: var(--accent-color);
}

button.accent:hover {
  background: #3AB08D;
}

/* Form Elements */
input {
  padding: 0.8rem 1rem;
  border: 1px solid #DDD;
  border-radius: var(--border-radius);
  font-size: 1rem;
  width: 100%;
  transition: var(--transition);
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(108, 99, 255, 0.2);
}

/* Card Styles */
.card {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

/* Player Mat Styles */
.player-mat {
  border-radius: var(--border-radius);
  padding: 1rem;
  background: var(--card-background);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.player-mat.active {
  border: 2px solid var(--accent-color);
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Game Board Layout */
.game-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.board-container {
  margin: 2rem 0;
}

.face-up-cards {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 1.5rem 0;
}

.card-item {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 1rem;
  width: 150px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.card-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Energy Cube Visual Styles */
.energy-cube {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 5px;
}

.energy-cube.artha {
  background-color: var(--artha-color);
}

.energy-cube.karma {
  background-color: var(--karma-color);
}

.energy-cube.gnana {
  background-color: var(--gnana-color);
}

.energy-cube.bhakti {
  background-color: var(--bhakti-color);
}

/* Selectable Energy Cubes */
.energy-cube.selectable {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid transparent;
}

.energy-cube.selectable:hover {
  transform: translateY(-3px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}

.energy-cube.selected {
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #333;
  transform: translateY(-3px);
}

/* Character Card Styles */
.character-card {
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.character-card-image {
  width: 120px;
  height: 120px;
  background-color: #eee;
  border-radius: 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  color: #555;
  border: 1px solid #ccc;
  overflow: hidden;
  object-fit: cover;
  position: relative;
}

.character-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.character-card-title {
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 4px;
  text-align: center;
}

.character-card-description {
  font-size: 0.8rem;
  text-align: center;
  color: #555;
}

.trade-btn {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  margin-top: 8px;
}

/* SVG Board Styling */
.board-svg {
  width: 100%;
  height: auto;
  max-height: 70vh;
  display: block;
  margin: 0 auto;
  background-color: transparent;
}

/* Full-screen board styles */
.board-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background-color: #ecf5fe; /* Slightly darker sky blue for fullscreen */
  overflow: hidden;
}

.board-fullscreen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.board-fullscreen-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: 0 180px; /* Adjust padding to match sidebar width */
}

.board-fullscreen-svg {
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: none;
}

/* Location labels with improved visibility */
.location-label {
  pointer-events: none;
  user-select: none;
}

.location-label-bg {
  fill: rgba(255, 255, 255, 0.98);
  stroke: #222;
  stroke-width: 1.5;
  rx: 8;
  ry: 8;
  filter: drop-shadow(0px 2px 4px rgba(0, 0, 0, 0.4));
}

.location-label-text {
  font-size: 12px;
  font-weight: 700;
  text-anchor: middle;
  fill: #000;
  dominant-baseline: middle;
}

/* Improved contrast for node region colors - all white with different border colors */
.node-region-north,
.node-region-west,
.node-region-south,
.node-region-central,
.node-region-east,
.node-region-northeast,
.node-region-jyotirlinga,
.node-region-airport {
  fill: #ffffff;
  stroke: #333;
  stroke-width: 2px;
}

.node-region-jyotirlinga {
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.3));
}

.node-region-airport {
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.3));
}

.node-region-jyotirlinga:hover,
.node-region-airport:hover,
.node:hover {
  fill: rgba(255, 255, 255, 0.9);
  filter: brightness(1.1) drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.5));
  transform: scale(1.05);
  z-index: 10;
}

/* Enhanced styles for node hover effects */
.node {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.3));
  transform-origin: center center;
}

.node-group {
  transform-box: fill-box;
  transform-origin: center center;
}

.node-group:hover .location-label-background {
  fill: rgba(255, 255, 255, 1);
  stroke: #000;
  stroke-width: 2;
  filter: drop-shadow(0px 3px 6px rgba(0, 0, 0, 0.4));
  transform: scale(1.02);
}

/* Enhanced location labels with improved visibility */
.node-label-container {
  pointer-events: none;
  user-select: none;
}

.hide-labels .node-label-container {
  display: none;
}

.node-group:hover .enhanced-location-label {
  font-weight: 800;
  fill: #000;
}

/* Enhanced labels for full-screen mode */
.enhanced-location-label {
  font-size: 12px;
  font-weight: 700;
  text-anchor: middle;
  fill: #000;
  dominant-baseline: middle;
  text-shadow: 0px 0px 3px rgba(255, 255, 255, 1);
}

.location-label-background {
  fill: rgba(255, 255, 255, 0.98);
  stroke: #333;
  stroke-width: 1.5;
  rx: 8;
  ry: 8;
  filter: drop-shadow(0px 3px 5px rgba(0, 0, 0, 0.4));
  transition: all 0.2s ease;
}

/* Line styling - now applied to path elements */
path {
  stroke: #ffffff;
  stroke-width: 2.5;
  stroke-linecap: round;
  opacity: 0.85;
  fill: none;
  filter: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.2));
  transition: all 0.2s ease;
}

path:hover {
  opacity: 1;
  stroke-width: 3.5;
  filter: drop-shadow(0px 0px 5px rgba(255, 255, 255, 0.6));
}

/* Board controls styling */
.board-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 100;
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Layout Helpers */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.gap-sm {
  gap: 0.5rem;
}

.gap-md {
  gap: 1rem;
}

.gap-lg {
  gap: 2rem;
}

.mt-sm {
  margin-top: 0.5rem;
}

.mt-md {
  margin-top: 1rem;
}

.mt-lg {
  margin-top: 2rem;
}

/* Game Over Screen */
.game-over {
  text-align: center;
  max-width: 600px;
  margin: 3rem auto;
  padding: 2rem;
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.game-over h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

/* Loading State */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  font-size: 1.5rem;
  color: var(--primary-color);
}

.loading::after {
  content: "...";
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80%, 100% { content: ''; }
}

/* App Navigation */
.app-navigation {
  display: flex;
  background-color: #2c3e50;
  padding: 0 20px;
}

.app-navigation a {
  padding: 15px 20px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  position: relative;
  text-decoration: none;
  display: inline-block;
}

.app-navigation a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.app-navigation a.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.15);
}

.app-navigation a.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #3498db;
}

/* Styles for compact components in fullscreen mode */
.compact-player-mat.active {
  box-shadow: 0 0 8px var(--accent-color);
}

.energy-cube.artha, .energy-cube.karma, .energy-cube.gnana, .energy-cube.bhakti {
  border-radius: 3px;
}

/* Scrollbars for fullscreen sidebars */
.board-fullscreen ::-webkit-scrollbar {
  width: 6px;
}

.board-fullscreen ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.board-fullscreen ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.board-fullscreen ::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Highlighted node animation */
.node-highlighted {
  animation: pulse 1.5s infinite alternate;
  filter: drop-shadow(0 0 8px rgba(255, 87, 34, 0.8)) !important;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));
  }
  100% {
    transform: scale(1.08);
    filter: drop-shadow(0 0 10px rgba(255, 87, 34, 0.9));
  }
}

/* Player movement animation */
@keyframes moveAnimation {
  0% {
    transform: scale(0.95);
    filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 15px rgba(255, 87, 34, 0.9));
  }
  100% {
    transform: scale(0.95);
    filter: drop-shadow(0 0 5px rgba(255, 87, 34, 0.7));
  }
}

/* Animation for card picking */
.card-animation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.animated-card {
  position: absolute;
  transition: all 1.8s cubic-bezier(0.2, 0.8, 0.2, 1.0); /* Slowed down by 20% */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  animation: cardGlow 1.8s infinite alternate; /* Slowed down by 20% */
}

@keyframes cardGlow {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow: 0 4px 20px rgba(25, 118, 210, 0.7);
  }
}

/* Make sure the animation is visible for a longer time */
.card-animation-container .animated-card {
  opacity: 1 !important;
  transition-duration: 1.8s !important;
}