/* OmJourney Game Styles */

/* =============== General Game Styles =============== */
.game-board {
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.game-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.game-header h2 {
  margin: 10px 0;
  color: #333;
}

.game-info {
  display: flex;
  gap: 20px;
  font-weight: bold;
}

.global-event {
  margin-top: 15px;
  padding: 10px;
  background-color: #fff5e6;
  border: 1px solid #ffcc80;
  border-radius: 5px;
  text-align: center;
}

.global-event h3 {
  margin-top: 0;
  color: #e65100;
}

/* =============== Game Setup Phase =============== */
.setup-phase {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
}

.setup-phase button {
  margin-top: 20px;
  padding: 10px 30px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.setup-phase button:hover {
  background-color: #388E3C;
}

/* =============== Game Content Layout =============== */
.game-content {
  display: flex;
  gap: 20px;
}

.board-container {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.game-sidebar {
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* =============== Player Info Styles =============== */
.player-info {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.player-info h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.player-stats {
  margin-bottom: 15px;
}

.character-info {
  background-color: #f0f7ff;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
}

.character-info h4 {
  margin: 0 0 5px 0;
  color: #0d47a1;
}

/* =============== Resources Styles =============== */
.resources {
  margin: 15px 0;
}

.resources h4 {
  margin: 10px 0 5px 0;
}

.cubes {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.cube {
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 14px;
  color: white;
}

.cube.artha {
  background-color: #F44336; /* Red */
}

.cube.karma {
  background-color: #2196F3; /* Blue */
}

.cube.gnana {
  background-color: #FFC107; /* Yellow */
  color: #333;
}

.cube.bhakti {
  background-color: #4CAF50; /* Green */
}

.om-tokens {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.om-token {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #9c27b0;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

/* =============== Card Styles =============== */
.cards {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
}

.card {
  width: calc(50% - 10px);
  padding: 8px;
  border-radius: 5px;
  font-size: 12px;
  background-color: #f5f5f5;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.card.selected {
  outline: 3px solid #4CAF50;
  background-color: #e8f5e9;
}

.card.travel {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
}

.journey-card {
  width: calc(50% - 10px);
  padding: 8px;
  border-radius: 5px;
  margin-bottom: 10px;
  font-size: 12px;
}

.journey-card.inner {
  background-color: #f3e5f5;
  border: 1px solid #e1bee7;
}

.journey-card.outer {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
}

/* =============== Actions Styles =============== */
.actions {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.actions h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-buttons button {
  padding: 10px;
  border: none;
  border-radius: 5px;
  background-color: #3f51b5;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-buttons button:hover {
  background-color: #303f9f;
}

.action-buttons button:disabled {
  background-color: #bdbdbd;
  cursor: not-allowed;
}

.action-buttons button.end-turn {
  background-color: #f44336;
  margin-top: 20px;
}

.action-buttons button.end-turn:hover {
  background-color: #e53935;
}

/* =============== Modal Styles =============== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  max-width: 600px;
  width: 90%;
  max-height: 80%;
  overflow-y: auto;
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.modal-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.modal-buttons button:first-child {
  background-color: #4caf50;
  color: white;
}

.modal-buttons button:last-child {
  background-color: #f5f5f5;
  color: #333;
}

/* =============== Game Over Styles =============== */
.game-over {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  z-index: 2000;
}

.game-over h2 {
  font-size: 36px;
  margin-bottom: 20px;
}

/* =============== Player Pieces Styles =============== */
.player-piece {
  transition: transform 0.3s ease;
}

.player-piece.current-player {
  transform: scale(1.2);
}

/* =============== Responsive Design =============== */
@media (max-width: 1200px) {
  .game-content {
    flex-direction: column;
  }
  
  .game-sidebar {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .game-info {
    flex-direction: column;
    gap: 5px;
  }
  
  .card, .journey-card {
    width: 100%;
  }
} 