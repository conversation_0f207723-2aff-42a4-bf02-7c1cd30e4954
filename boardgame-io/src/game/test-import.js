// Test all possible import methods
const core1 = require('boardgame.io/core');
console.log('core1:', Object.keys(core1));

try {
  const { Game } = require('boardgame.io/core');
  console.log('Direct Game import:', typeof Game);
} catch (e) {
  console.log('Direct Game import error:', e.message);
}

try {
  const importedGame = require('boardgame.io/core').Game;
  console.log('Chained import:', typeof importedGame);
} catch (e) {
  console.log('Chained import error:', e.message);
}

// Also test the Client
try {
  const { Client } = require('boardgame.io/react');
  console.log('Client import:', typeof Client);
} catch (e) {
  console.log('Client import error:', e.message);
} 