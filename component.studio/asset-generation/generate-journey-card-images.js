const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const { createCanvas, registerFont } = require('canvas');

// Get journey card data
const boardData = require('../../server/boardData');
const journeyDeckInner = boardData.journeyDeckInner;
const journeyDeckOuter = boardData.journeyDeckOuter;
const locations = boardData.locations;

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../../journey-card-images');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Color definitions from client/src/styles/main.css
const colors = {
  // Energy Colors
  arthaColor: '#F39C12', // Orange/yellow for artha
  karmaColor: '#27AE60', // Green for karma
  gnanaColor: '#2980B9', // Blue for gnana
  bhaktiColor: '#8E44AD', // Purple for bhakti

  // Region Colors
  northColor: 'rgba(33, 150, 243, 0.95)',      // Vibrant blue
  westColor: 'rgba(76, 175, 80, 0.95)',        // Vibrant green
  southColor: 'rgba(255, 191, 102, 0.95)',     // Light orange
  centralColor: 'rgba(156, 39, 176, 0.95)',    // Vibrant purple
  eastColor: '#fd7c03ef',                      // Orange
  northeastColor: 'rgba(233, 30, 99, 0.95)',   // Vibrant pink

  // Journey Types
  innerColor: 'rgba(25, 118, 210, 0.95)',     // Dark blue for inner journey
  outerColor: 'rgba(56, 142, 60, 0.95)',      // Dark green for outer journey
};

// Read Caveat font files for data-URI embedding
const caveatRegularBase64 = fs.readFileSync(path.join(__dirname, 'fonts', 'Caveat-Regular.ttf')).toString('base64');
const caveatBoldBase64 = fs.readFileSync(path.join(__dirname, 'fonts', 'Caveat-Bold.ttf')).toString('base64');

// Register Caveat fonts for canvas
registerFont(path.join(__dirname, 'fonts', 'Caveat-Regular.ttf'), { family: 'Caveat', weight: 'normal' });
registerFont(path.join(__dirname, 'fonts', 'Caveat-Bold.ttf'), { family: 'Caveat', weight: 'bold' });

// Function to create a card image with location image
async function createCardImage(card) {
  try {
    const location = locations.find(loc => loc.id === card.locationId);
    if (!location) {
      console.error(`Location not found for card ${card.id} with locationId ${card.locationId}`);
      return false;
    }

    const isInnerJourney = card.reward && card.reward.inner !== undefined;
    const journeyType = isInnerJourney ? 'Inner' : 'Outer';
    const journeyValue = isInnerJourney ? card.reward.inner : card.reward.outer;
    const accentColor = isInnerJourney ? colors.innerColor : colors.outerColor;
    const locationName = location.name;
    const region = location.region;
    const regionColor = colors[`${region.toLowerCase()}Color`];

    // Map region to abbreviation
    const regionAbbreviations = {
      'North': 'NW',
      'Northeast': 'NE',
      'East': 'E',
      'Central': 'C',
      'West': 'W',
      'South': 'S'
    };
    const regionAbbr = regionAbbreviations[region] || region.charAt(0);
    
    // Use shared color map for region text colors
    const regionTextColors = {
      'North': colors.northColor,
      'Northeast': colors.northeastColor,
      'East': colors.eastColor,
      'Central': colors.centralColor,
      'West': colors.westColor,
      'South': colors.southColor
    };
    const regionTextColor = regionTextColors[region] || '#000000'; // Fallback to black if not found
    
    // Handle multiline location names
    let locationLine1 = locationName;
    let locationLine2 = '';
    
    // Special cases for splitting location names
    const splitLocations = {
      'Brahma Temple, Pushkar': ['Brahma Temple,', 'Pushkar'],
      'Shirdi Sai Baba Temple': ['Shirdi Sai Baba', 'Temple'],
      'Siddhivinayak Temple': ['Siddhivinayak', 'Temple'],
      'Basilica of Bom Jesus': ['Basilica of', 'Bom Jesus'],
      'Annamalaiyar Temple': ['Annamalaiyar', 'Temple'],
      'Padmanabhaswamy Temple': ['Padmanabhaswamy', 'Temple'],
      'Tirumala Venkateswara': ['Tirumala', 'Venkateswara'],
      'Ajanta and Ellora Caves': ['Ajanta and', 'Ellora Caves'],
      'Backwaters of Kerala': ['Backwaters', 'of Kerala'],
      'Betla National Park': ['Betla', 'National Park'],
      'Phawngpui Blue Mountain': ['Phawngpui', 'Blue Mountain']
    };
    
    if (splitLocations[locationName]) {
      [locationLine1, locationLine2] = splitLocations[locationName];
    }

    // Determine journey text font size (20% larger for Outer Journey)
    const journeyFontSize = isInnerJourney ? 40 : 48;

    // Define dimensions
    const width = 900;
    const height = 1500;

    // Define larger image area dimensions
    const imageHeight = 1100;
    // Increase header height by 20%
    const headerHeight = 160;
    
    // Position region compass at consistent height
    const regionCircleY = Math.floor(headerHeight / 2);

    // First, create a base card with white background
    const baseCard = sharp({
      create: {
        width,
        height,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      }
    });

    // Try to load the location image
    const locationImagePath = path.join(__dirname, '../../client/public/assets/images/cards', `${card.locationId}.png`);
    let hasLocationImage = false;

    try {
      if (fs.existsSync(locationImagePath)) {
        hasLocationImage = true;
        console.log(`Found location image for card ${card.id}: ${locationImagePath}`);
      } else {
        console.log(`Location image not found for card ${card.id}: ${locationImagePath}`);
      }
    } catch (err) {
      console.log(`Error checking location image for card ${card.id}: ${err.message}`);
    }

    // Gather energy requirements for a single line
    let energyCubes = [];
    if (card.required) {
      Object.entries(card.required).forEach(([type, count]) => {
        for (let i = 0; i < count; i++) {
          energyCubes.push(type.toLowerCase());
        }
      });
    }

    // Create SVG for the card content
    const svgHeader = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
          <style type="text/css">
            @font-face {
              font-family: 'Caveat';
              font-weight: 400;
              src: url('data:font/ttf;base64,${caveatRegularBase64}') format('truetype');
            }
            @font-face {
              font-family: 'Caveat';
              font-weight: 700;
              src: url('data:font/ttf;base64,${caveatBoldBase64}') format('truetype');
            }
            text {
              font-family: 'Arial', sans-serif;
            }
            .location-name {
              font-family: 'Caveat', cursive;
              font-weight: 700;
            }
          </style>
        </defs>

        <!-- Card background -->
        <rect x="0" y="0" width="${width}" height="${height}" fill="white" rx="20" ry="20" />

        <!-- White header area - increased height by 20% -->
        <rect x="0" y="0" width="${width}" height="${headerHeight}" fill="white" />

        <!-- Location name drawn via canvas using Caveat font -->

        <!-- Region placeholder; will draw dynamically -->

        <!-- Larger image area -->
        <rect x="0" y="${headerHeight}" width="${width}" height="${imageHeight}" fill="#f0f0f0" />
    `;

    // Journey type - bigger text below image
    const journeySvg = `
        <!-- Card type - bigger text -->
        <text x="30" y="${headerHeight + imageHeight + 80}" font-size="${journeyFontSize}" fill="${accentColor}" font-weight="bold">${journeyType} Journey</text>
    `;

    // Energy requirements on a single line
    let energySvg = '';
    if (energyCubes.length > 0) {
      energyCubes.forEach((type, index) => {
        const colorKey = `${type}Color`;
        const cubeColor = colors[colorKey] || '#999';

        // Draw cubes on left side, 86px square with 17px spacing (increased by another 20% from 72px)
        energySvg += `
          <rect x="${30 + index * 103}" y="${headerHeight + imageHeight + 100}" width="86" height="86" fill="${cubeColor}" stroke="#333" stroke-width="1" rx="8" ry="8" />
        `;
      });
    }

    // Value badge directly on the image - make sure it's visible and positioned properly
    const valueBadgeSvg = `
      <!-- Journey value badge - larger, on the image -->
      <circle cx="${width - 90}" cy="${headerHeight + imageHeight - 100}" r="90" fill="${accentColor}" stroke="white" stroke-width="4" />
      <text x="${width - 90}" y="${headerHeight + imageHeight - 80}" font-size="84" fill="white" text-anchor="middle" font-weight="bold">+${journeyValue}</text>
    `;

    const svgFooter = `</svg>`;
    const svgImage = svgHeader + journeySvg + energySvg + valueBadgeSvg + svgFooter;

    // Save the output path
    const outputPath = path.join(outputDir, `${card.id}.png`);

    // Create the SVG file first for debugging
    const svgOutputPath = path.join(outputDir, `${card.id}.svg`);
    fs.writeFileSync(svgOutputPath, svgImage);
    console.log(`Created SVG for card ${card.id} at ${svgOutputPath}`);

    // Prepare footer score badge overlay buffer (below the image and near energy section)
    const badgeFooterSvg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <circle cx="${width - 90}" cy="${headerHeight + imageHeight + 110}" r="90" fill="beige" stroke="white" stroke-width="4" />
        <text x="${width - 90}" y="${headerHeight + imageHeight + 110}" font-size="112" fill="brown" text-anchor="middle" dominant-baseline="middle" font-weight="bold">${journeyValue}</text>
      </svg>
    `;
    const badgeFooterBuffer = Buffer.from(badgeFooterSvg);

    // Load the appropriate compass image based on the region
    const regionCompassMap = {
      'North': 'north_west.png',
      'Northeast': 'north_east.png',
      'East': 'east.png',
      'Central': 'central.png',
      'West': 'west.png',
      'South': 'south.png'
    };
    const compassImagePath = path.join(__dirname, 'compass/' + regionCompassMap[region]);

    let compassImageBuffer;
    try {
      compassImageBuffer = await sharp(compassImagePath)
        .resize({ width: 300, height: 300 }) // Size to match the region circle
        .toBuffer();
    } catch (error) {
      console.error(`Error loading compass image: ${error.message}`);
      // Fallback to circle if compass image can't be loaded
      const fallbackRegionSvg = `
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
          <circle cx="${width - 100}" cy="${regionCircleY}" r="70" fill="${regionColor}" stroke="#333" stroke-width="1" />
        </svg>
      `;
      compassImageBuffer = Buffer.from(fallbackRegionSvg);
    }

    // Prepare header overlay buffer (location name and region)
    const headerCanvas = createCanvas(width, headerHeight);
    const headerCtx = headerCanvas.getContext('2d');
    headerCtx.fillStyle = 'black';
    headerCtx.textBaseline = 'middle';
    
    // Draw location name - handle multiline text
    if (locationLine2) {
      headerCtx.font = `bold 80px Caveat`;
      headerCtx.fillText(locationLine1, 30, headerHeight / 2 - 30); // First line slightly higher
      headerCtx.fillText(locationLine2, 30, headerHeight / 2 + 30); // Second line slightly lower
    } else {
      headerCtx.font = `bold 96px Caveat`;
      headerCtx.fillText(locationName, 30, headerHeight / 2); // Single line, centered vertically
    }
    
    const headerBuffer = headerCanvas.toBuffer();

    // If we have a location image, composite it with the SVG
    if (hasLocationImage) {
      try {
        // Create the SVG buffer
        const svgBuffer = Buffer.from(svgImage);
        // Load and resize the location image to fit the larger image area
        const locationImageBuffer = await sharp(locationImagePath)
          .resize({ width: width, height: imageHeight, fit: 'cover', position: 'center' })
          .toBuffer();
        // Composite layers: header, location image, compass image, and badge for all journey types
        const composites = [
          { input: headerBuffer, top: 0, left: 0 },
          { input: locationImageBuffer, top: headerHeight, left: 0 },
          { input: compassImageBuffer, top: regionCircleY - 100, left: width - 270 }, // Position compass so center aligns with text
          { input: badgeFooterBuffer, top: 0, left: 0 }
        ];
        await sharp(svgBuffer)
          .png()
          .composite(composites)
          .toFile(outputPath);

        console.log(`Created image for card ${card.id} at ${outputPath}`);
        return true;
      } catch (error) {
        console.error(`Error creating composite image for card ${card.id}:`, error);

        // Fallback to just using the SVG without a location image
        try {
          // Fallback: composite header, compass image, and badge for all journey types
          const fbComposites = [
            { input: headerBuffer, top: 0, left: 0 },
            { input: compassImageBuffer, top: regionCircleY - 70, left: width - 170 }, // Position compass so center aligns with text
            { input: badgeFooterBuffer, top: 0, left: 0 }
          ];
          const svgBuf = Buffer.from(svgImage);
          await sharp(svgBuf)
            .png()
            .composite(fbComposites)
            .toFile(outputPath);

          console.log(`Created fallback image for card ${card.id} at ${outputPath}`);
          return true;
        } catch (fallbackError) {
          console.error(`Fallback failed for card ${card.id}:`, fallbackError);
          return false;
        }
      }
    } else {
      // Just use the SVG without a location image
      try {
        // Composite header, compass image, and badge for all journey types
        const noimgComposites = [
          { input: headerBuffer, top: 0, left: 0 },
          { input: compassImageBuffer, top: regionCircleY - 70, left: width - 170 }, // Position compass so center aligns with text
          { input: badgeFooterBuffer, top: 0, left: 0 }
        ];
        const svgBuf = Buffer.from(svgImage);
        await sharp(svgBuf)
          .png()
          .composite(noimgComposites)
          .toFile(outputPath);

        console.log(`Created image for card ${card.id} at ${outputPath}`);
        return true;
      } catch (error) {
        console.error(`Error creating image for card ${card.id}:`, error);
        return false;
      }
    }
  } catch (error) {
    console.error(`Error creating image for card ${card.id}:`, error);
    return false;
  }
}

// Process all journey cards
async function processAllCards() {
  const allCards = [...journeyDeckInner, ...journeyDeckOuter];

  console.log(`Processing ${allCards.length} journey cards...`);

  let successCount = 0;
  let failCount = 0;

  for (const card of allCards) {
    const success = await createCardImage(card);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  console.log(`\nProcessing complete!`);
  console.log(`Successfully created: ${successCount} images`);
  console.log(`Failed: ${failCount} images`);
  console.log(`Output directory: ${outputDir}`);
}

// Start processing
processAllCards();
