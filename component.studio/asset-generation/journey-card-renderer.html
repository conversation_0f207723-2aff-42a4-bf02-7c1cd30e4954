<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Journey Card Renderer</title>
  <style>
    :root {
      --primary-color: #4a6741;
      --accent-color: #375a60;
      --north-color: #375a60;
      --south-color: #c17817;
      --east-color: #9c3e3e;
      --west-color: #7a4e8c;
      --central-color: #4a6741;
      --northeast-color: #1e88e5;
      --artha-color: #ffc107;
      --karma-color: #9c3e3e;
      --gnana-color: #1e88e5;
      --bhakti-color: #7a4e8c;
    }
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
    }
    #card-container {
      width: 825px;
      height: 1125px;
      overflow: hidden;
      position: relative;
    }
    button {
      background-color: #4a6741;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    button.secondary {
      background-color: #f0f0f0;
      color: #333;
    }
  </style>
</head>
<body>
  <div id="card-container"></div>

  <script src="https://unpkg.com/react@17/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

  <script type="text/babel">
    // JourneyCardModal Component
    function JourneyCardModal({ card, gameState }) {
      if (!card) return null;

      // Determine card type (inner or outer journey)
      const isInnerJourney = card.reward && card.reward.inner !== undefined;
      const journeyType = isInnerJourney ? 'Inner' : 'Outer';
      const journeyValue = isInnerJourney ? card.reward.inner : card.reward.outer;
      const accentColor = isInnerJourney ? 'var(--primary-color)' : 'var(--accent-color)';

      // Find the location info based on locationId
      const location = gameState?.locations?.find(loc => loc.id === card.locationId);
      const locationName = location ? location.name : 'Unknown Location';
      const region = location ? location.region : 'unknown';
      const journeySubType = location ? location.journeyType : '';

      // Get energy requirements for display
      const energyRequirements = [];
      if (card.required) {
        Object.entries(card.required).forEach(([type, count]) => {
          for (let i = 0; i < count; i++) {
            energyRequirements.push(type.toLowerCase());
          }
        });
      }

      return (
        <div style={{
          width: '825px',
          height: '1125px',
          background: 'white',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
          display: 'flex',
          flexDirection: 'column',
        }}>
          {/* Header with location name */}
          <div style={{
            background: accentColor,
            color: 'white',
            padding: '16px 20px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <h2 style={{ margin: 0, fontSize: '1.5rem', fontWeight: 'bold' }}>
                {locationName}
              </h2>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                fontSize: '0.9rem',
                marginTop: '4px'
              }}>
                <span>#{card.locationId}</span>
                <div style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  background: `var(--${region.toLowerCase()}-color)`,
                  border: '1px solid white'
                }} />
                <span>{region}</span>
              </div>
            </div>
          </div>

          {/* Main content with image and details */}
          <div style={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
            {/* Location image */}
            <div style={{
              height: '500px',
              backgroundImage: `url(../../client/public/assets/images/cards/${card.locationId}.png)`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              position: 'relative'
            }}>
              {/* Journey type badge */}
              <div style={{
                position: 'absolute',
                bottom: '16px',
                right: '16px',
                background: accentColor,
                color: 'white',
                borderRadius: '50%',
                width: '48px',
                height: '48px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                fontSize: '1.4rem',
                fontWeight: 'bold',
                border: '3px solid white',
                boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
              }}>
                {journeyValue}
              </div>
            </div>

            {/* Card details */}
            <div style={{ padding: '24px' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
                <div style={{
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  color: accentColor,
                  marginRight: '10px',
                  textTransform: 'uppercase'
                }}>
                  {journeyType} Journey
                </div>
                <div style={{
                  fontSize: '0.9rem',
                  color: '#666',
                  fontStyle: 'italic'
                }}>
                  {journeySubType}
                </div>
              </div>

              {/* Requirements section */}
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{
                  fontSize: '1rem',
                  marginBottom: '10px',
                  color: '#333',
                  borderBottom: '1px solid #eee',
                  paddingBottom: '8px'
                }}>
                  Required Resources
                </h3>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    {energyRequirements.map((cubeType, i) => (
                      <div
                        key={i}
                        style={{
                          width: '36px',
                          height: '36px',
                          borderRadius: '6px',
                          background: `var(--${cubeType}-color)`,
                          border: '1px solid #666',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                        }}
                      />
                    ))}
                    {energyRequirements.length === 0 && (
                      <div style={{ color: '#666', fontStyle: 'italic' }}>No resources required</div>
                    )}
                  </div>
                </div>
              </div>

              {/* Reward section */}
              <div>
                <h3 style={{
                  fontSize: '1rem',
                  marginBottom: '10px',
                  color: '#333',
                  borderBottom: '1px solid #eee',
                  paddingBottom: '8px'
                }}>
                  Reward
                </h3>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}>
                  <div style={{
                    fontSize: '1.1rem',
                    fontWeight: 'bold',
                    color: accentColor,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    <span style={{
                      background: accentColor,
                      color: 'white',
                      width: '36px',
                      height: '36px',
                      borderRadius: '50%',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}>
                      +{journeyValue}
                    </span>
                    <span>{journeyType} OM Points</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer with card ID */}
          <div style={{
            padding: '16px',
            borderTop: '1px solid #eee',
            display: 'flex',
            justifyContent: 'flex-end',
            gap: '12px'
          }}>
            <div style={{ color: '#999', fontSize: '0.8rem' }}>
              Card ID: {card.id}
            </div>
          </div>
        </div>
      );
    }

    // Location data
    const LOCATIONS = [
      // NORTH
      { id: 1,  name: 'Vaishno Devi',           region: 'North',     journeyType: 'Inner' },
      { id: 2,  name: 'Chail',                  region: 'North',     journeyType: 'Outer' },
      { id: 3,  name: 'Golden Temple',          region: 'North',     journeyType: 'Inner' },
      { id: 4,  name: 'Durgiana Temple',        region: 'North',     journeyType: 'Inner' },
      { id: 6,  name: 'Taj Mahal',              region: 'North',     journeyType: 'Outer' },
      { id: 7,  name: 'Haridwar',               region: 'North',     journeyType: 'Inner' },
      { id: 9,  name: 'Valley of Flowers',      region: 'North',     journeyType: 'Outer' },
      // WEST
      { id: 10, name: 'Ambaji Temple',          region: 'West',      journeyType: 'Inner' },
      { id: 11, name: 'Rann of Kutch',          region: 'West',      journeyType: 'Outer' },
      { id: 12, name: 'Jaisalmer Fort',         region: 'West',      journeyType: 'Outer' },
      { id: 13, name: 'Hawa Mahal',             region: 'West',      journeyType: 'Outer' },
      { id: 14, name: 'Brahma Temple, Pushkar', region: 'West',      journeyType: 'Inner' },
      { id: 16, name: 'Siddhivinayak Temple',   region: 'West',      journeyType: 'Inner' },
      { id: 19, name: 'Basilica of Bom Jesus',  region: 'West',      journeyType: 'Inner' },
      // SOUTH
      { id: 20, name: 'Meenakshi Temple',       region: 'South',     journeyType: 'Inner' },
      { id: 21, name: 'Annamalaiyar Temple',    region: 'South',     journeyType: 'Inner' },
      { id: 22, name: 'Nilgiri Hills',          region: 'South',     journeyType: 'Outer' },
      { id: 23, name: 'Sabarimala',             region: 'South',     journeyType: 'Inner' },
      { id: 24, name: 'Padmanabhaswamy Temple', region: 'South',     journeyType: 'Inner' },
      { id: 25, name: 'Backwaters of Kerala',   region: 'South',     journeyType: 'Outer' },
      { id: 27, name: 'Hampi',                  region: 'South',     journeyType: 'Outer' },
      // CENTRAL
      { id: 5,  name: 'Kurukshetra',            region: 'Central',   journeyType: 'Outer' },
      { id: 15, name: 'Shirdi Sai Baba Temple', region: 'Central',   journeyType: 'Inner' },
      { id: 18, name: 'Ajanta and Ellora Caves',region: 'Central',   journeyType: 'Outer' },
      { id: 28, name: 'Mysore Palace',          region: 'Central',   journeyType: 'Outer' },
      { id: 29, name: 'Coorg',                  region: 'Central',   journeyType: 'Outer' },
      { id: 33, name: 'Khajuraho Temples',      region: 'Central',   journeyType: 'Outer' },
      { id: 34, name: 'Pachmarhi',              region: 'Central',   journeyType: 'Outer' },
      // EAST
      { id: 30, name: 'Charminar',              region: 'East',      journeyType: 'Outer' },
      { id: 31, name: 'Ramoji Film City',       region: 'East',      journeyType: 'Outer' },
      { id: 32, name: 'Tirumala Venkateswara',  region: 'East',      journeyType: 'Inner' },
      { id: 36, name: 'Jagannath Temple',       region: 'East',      journeyType: 'Inner' },
      { id: 37, name: 'Chilika Lake',           region: 'East',      journeyType: 'Outer' },
      { id: 38, name: 'Tarapith',               region: 'East',      journeyType: 'Inner' },
      { id: 39, name: 'Betla National Park',    region: 'East',      journeyType: 'Outer' },
      // NORTHEAST
      { id: 42, name: 'Kamakhya Temple',        region: 'Northeast', journeyType: 'Inner' },
      { id: 43, name: 'Living Root Bridge',     region: 'Northeast', journeyType: 'Outer' },
      { id: 44, name: 'Ujjayanta Palace',       region: 'Northeast', journeyType: 'Outer' },
      { id: 45, name: 'Kangla Fort',            region: 'Northeast', journeyType: 'Outer' },
      { id: 46, name: 'Phawngpui Blue Mountain',region: 'Northeast', journeyType: 'Outer' },
      { id: 47, name: 'Dzükou Valley',          region: 'Northeast', journeyType: 'Outer' },
      { id: 48, name: 'Rumtek Monastery',       region: 'Northeast', journeyType: 'Inner' },
    ];

    // Journey card data
    const JOURNEY_DECK_INNER = [
      { id: 'JI1',  locationId: 1,  required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
      { id: 'JI2',  locationId: 3,  required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
      { id: 'JI3',  locationId: 4,  required: { gnana: 1 },            reward: { inner: 20 } },
      { id: 'JI4',  locationId: 7,  required: { bhakti: 2, gnana: 1 }, reward: { inner: 27 } },
      { id: 'JI6',  locationId: 10, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
      { id: 'JI7',  locationId: 14, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
      { id: 'JI8',  locationId: 15, required: { bhakti: 1, gnana: 2 }, reward: { inner: 27 } },
      { id: 'JI9',  locationId: 16, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
      { id: 'JI10', locationId: 19, required: { bhakti: 2, gnana: 1 }, reward: { inner: 27 } },
      { id: 'JI11', locationId: 20, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
      { id: 'JI12', locationId: 21, required: { bhakti: 1 },            reward: { inner: 20 } },
      { id: 'JI13', locationId: 23, required: { bhakti: 1, gnana: 2 }, reward: { inner: 27 } },
      { id: 'JI14', locationId: 24, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
      { id: 'JI16', locationId: 32, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
      { id: 'JI17', locationId: 36, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
      { id: 'JI18', locationId: 38, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
      { id: 'JI21', locationId: 42, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
      { id: 'JI22', locationId: 48, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
    ];

    const JOURNEY_DECK_OUTER = [
      { id: 'JO1',  locationId: 2,  required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
      { id: 'JO2',  locationId: 5,  required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
      { id: 'JO3',  locationId: 6,  required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
      { id: 'JO4',  locationId: 9,  required: { artha: 1 },           reward: { outer: 20 } },
      { id: 'JO5',  locationId: 11, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
      { id: 'JO6',  locationId: 12, required: { karma: 2, artha: 2 }, reward: { outer: 30 } },
      { id: 'JO7',  locationId: 13, required: { artha: 1 },           reward: { outer: 20 } },
      { id: 'JO9',  locationId: 18, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
      { id: 'JO10', locationId: 22, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
      { id: 'JO11', locationId: 25, required: { artha: 1 },           reward: { outer: 20 } },
      { id: 'JO12', locationId: 27, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
      { id: 'JO13', locationId: 28, required: { karma: 2, artha: 2 }, reward: { outer: 30 } },
      { id: 'JO14', locationId: 29, required: { artha: 1 },           reward: { outer: 20 } },
      { id: 'JO15', locationId: 30, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
      { id: 'JO16', locationId: 31, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
      { id: 'JO17', locationId: 33, required: { karma: 1 },           reward: { outer: 20 } },
      { id: 'JO18', locationId: 34, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
      { id: 'JO20', locationId: 37, required: { karma: 1 },           reward: { outer: 20 } },
      { id: 'JO21', locationId: 39, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
      { id: 'JO22', locationId: 43, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
      { id: 'JO23', locationId: 44, required: { karma: 2, artha: 2 }, reward: { outer: 30 } },
      { id: 'JO24', locationId: 45, required: { karma: 1 },           reward: { outer: 20 } },
      { id: 'JO25', locationId: 46, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
      { id: 'JO26', locationId: 47, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
    ];

    // Combine all journey cards
    const allJourneyCards = [...JOURNEY_DECK_INNER, ...JOURNEY_DECK_OUTER];

    // Function to render a specific card
    function renderCard(cardId) {
      const card = allJourneyCards.find(c => c.id === cardId);
      if (!card) {
        console.error(`Card with ID ${cardId} not found`);
        return;
      }

      const gameState = {
        locations: LOCATIONS
      };

      ReactDOM.render(
        <JourneyCardModal card={card} gameState={gameState} />,
        document.getElementById('card-container')
      );

      // Signal that rendering is complete
      window.cardRendered = true;
    }

    // Expose the render function to the global scope
    window.renderCard = renderCard;

    // Expose the card data to the global scope
    window.journeyCards = {
      inner: JOURNEY_DECK_INNER,
      outer: JOURNEY_DECK_OUTER,
      all: allJourneyCards
    };
  </script>
</body>
</html>
