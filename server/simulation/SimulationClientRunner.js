/**
 * SimulationClientRunner.js
 * 
 * This class manages running a simulation against a standalone server via REST API and Socket.IO
 */

const io = require('socket.io-client');
const fetch = require('node-fetch');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs');
const { EventEmitter } = require('events');

class SimulationClientRunner extends EventEmitter {
  /**
   * Create a new SimulationClientRunner
   * @param {Object} options - Configuration options
   * @param {string} options.serverUrl - URL of the server to connect to
   * @param {number} options.numGames - Number of games to play
   * @param {number} options.speed - Simulation speed (1-10)
   * @param {Array} options.bots - Array of bot configurations
   * @param {Object} options.logging - Logging configuration
   */
  constructor(options) {
    super();
    
    this.options = {
      serverUrl: 'http://localhost:4000',
      numGames: 1,
      speed: 5,
      bots: [],
      logging: {
        logLevel: 'info',
        logToConsole: true,
        logToFile: true
      },
      ...options
    };
    
    this.socket = null;
    this.connected = false;
    this.simulationRunning = false;
    
    // Create a timestamp for this simulation run
    this.timestamp = Date.now();
    
    // Store simulation results
    this.results = {
      gamesPlayed: 0,
      gamesCompleted: 0,
      gameResults: [],
      startTime: null,
      endTime: null,
      botStats: {},
      errors: []
    };
    
    // Bind methods so they maintain context when used as callbacks
    this._onConnect = this._onConnect.bind(this);
    this._onDisconnect = this._onDisconnect.bind(this);
    this._onGameState = this._onGameState.bind(this);
    this._onGameEvent = this._onGameEvent.bind(this);
    this._onGameComplete = this._onGameComplete.bind(this);
  }
  
  /**
   * Connect to the server and initialize the simulation
   * @returns {Promise} Promise that resolves when connected
   */
  async connect() {
    logger.info(`Connecting to server at ${this.options.serverUrl}`);
    
    try {
      // First check if the server is reachable
      const healthResponse = await fetch(`${this.options.serverUrl}/api/health`);
      if (!healthResponse.ok) {
        throw new Error(`Server health check failed: ${healthResponse.status} ${healthResponse.statusText}`);
      }
      
      const healthData = await healthResponse.json();
      logger.info(`Server health check successful: ${JSON.stringify(healthData)}`);
      
      // Connect to Socket.IO
      this.socket = io(this.options.serverUrl, {
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000
      });
      
      return new Promise((resolve, reject) => {
        this.socket.on('connect', () => {
          this.connected = true;
          logger.info(`Connected to server with socket ID: ${this.socket.id}`);
          this._setupSocketListeners();
          resolve();
        });
        
        this.socket.on('connect_error', (error) => {
          logger.error(`Socket connection error: ${error.message}`);
          reject(error);
        });
        
        // Set a timeout for the connection
        setTimeout(() => {
          if (!this.connected) {
            reject(new Error('Connection timed out after 10 seconds'));
          }
        }, 10000);
      });
    } catch (error) {
      logger.error(`Failed to connect to server: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Set up Socket.IO event listeners
   * @private
   */
  _setupSocketListeners() {
    this.socket.on('gameState', (state) => {
      // Handle game state updates
      logger.debug(`Received game state update: Round ${state.roundCount}`);
      
      // Update bot stats if the game has been completed
      if (state.gameCompleted) {
        this.results.gamesCompleted++;
        this._updateBotStats(state);
      }
    });
    
    this.socket.on('gameEvent', (event) => {
      // Handle game events
      logger.debug(`Game event: ${event.type} - ${event.message}`);
    });
    
    this.socket.on('simulationCompleted', (stats) => {
      // Handle simulation completion
      logger.info(`Simulation completed: ${stats.gamesPlayed} games played`);
      this.simulationRunning = false;
      this.results.gamesPlayed = stats.gamesPlayed;
    });
    
    this.socket.on('disconnect', () => {
      logger.warn('Disconnected from server');
      this.connected = false;
    });
    
    this.socket.on('error', (error) => {
      logger.error(`Socket error: ${error.message}`);
      this.results.errors.push(error.message);
    });
  }
  
  /**
   * Handle socket connect event
   * @private
   */
  _onConnect() {
    logger.info('Socket reconnected to server');
  }
  
  /**
   * Handle socket disconnect event
   * @private
   */
  _onDisconnect(reason) {
    logger.warn(`Socket disconnected: ${reason}`);
  }
  
  /**
   * Handle game state updates
   * @private
   * @param {object} state - Game state update
   */
  _onGameState(state) {
    if (!this.currentGame.inProgress && state.started) {
      this.currentGame.inProgress = true;
      this.currentGame.startTime = Date.now();
      logger.info('Game started');
    }
    
    this.currentGame.roundCount = state.roundCount || 0;
    this.currentGame.turnCount = state.turnCount || 0;
    this.currentGame.players = state.players || [];
    
    // Update bot stats and check for starting positions
    this.currentGame.players.forEach(player => {
      if (player.isBot) {
        // Update bot stats
        if (this.results.botStats[player.id]) {
          this.results.botStats[player.id].gamesPlayed++;
          if (player.score) {
            this.results.botStats[player.id].totalScore += player.score;
            this.results.botStats[player.id].averageScore = 
              this.results.botStats[player.id].totalScore / this.results.botStats[player.id].gamesPlayed;
          }
        }
        
        // Log bot state if it's their turn
        if (state.currentPlayerId === player.id) {
          logger.info(`Bot ${player.name} (${player.id}) turn:`, {
            position: player.position || 'No starting position',
            hand: player.hand?.length || 0,
            omTokens: player.omTokens || 0,
            energyCubes: player.energyCubes || 0
          });
        }
      }
    });
    
    if (this.options.verbose) {
      logger.info(`Game state updated: Round ${this.currentGame.roundCount}, Turn ${this.currentGame.turnCount}`);
    }
  }
  
  /**
   * Handle game events
   * @private
   * @param {object} event - Game event
   */
  _onGameEvent(event) {
    this.currentGame.events.push(event);
    
    if (this.options.verbose) {
      logger.info(`Game event: ${event.type} - ${event.description || ''}`);
    }
  }
  
  /**
   * Handle game completion
   * @private
   * @param {object} result - Game result
   */
  _onGameComplete(result) {
    this.currentGame.inProgress = false;
    this.currentGame.endTime = Date.now();
    
    const gameSummary = {
      gameNumber: this.currentGame.gameNumber,
      roundCount: this.currentGame.roundCount,
      turnCount: this.currentGame.turnCount,
      duration: this.currentGame.endTime - this.currentGame.startTime,
      winner: result.winner,
      players: this.currentGame.players.map(p => ({
        id: p.id,
        name: p.name,
        strategy: p.strategy,
        score: p.score || 0
      })),
      eventCount: this.currentGame.events.length
    };
    
    this.results.gameResults.push(gameSummary);
    this.results.gamesCompleted++;
    
    logger.info(`Game ${this.currentGame.gameNumber} completed in ${gameSummary.duration}ms`);
    logger.info(`Winner: ${result.winner ? result.winner.name : 'None'}`);
    
    if (this.options.saveOutput) {
      this._saveGameResult(gameSummary);
    }
    
    // Reset current game state for next game
    this.currentGame = {
      inProgress: false,
      gameNumber: this.currentGame.gameNumber + 1,
      startTime: null,
      roundCount: 0,
      turnCount: 0,
      players: [],
      events: []
    };
    
    // Emit game complete event
    this.emit('gameComplete', gameSummary);
    
    // Check if we've completed all games
    if (this.results.gamesCompleted >= this.options.numGames) {
      logger.info('All games completed');
      this.emit('simulationComplete', this.results);
    }
  }
  
  /**
   * Configure the simulation on the server
   * @returns {Promise} Promise that resolves when configured
   */
  async configureSimulation() {
    logger.info('Configuring simulation');
    
    try {
      const response = await fetch(`${this.options.serverUrl}/api/simulation/configure`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          numGames: this.options.numGames,
          speed: this.options.speed,
          outputStats: true
        })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to configure simulation: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      logger.info(`Simulation configured: ${data.message}`);
      return data;
    } catch (error) {
      logger.error(`Error configuring simulation: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Add bots to the simulation
   * @returns {Promise} Promise that resolves when bots are added
   */
  async addBots() {
    logger.info(`Adding ${this.options.bots.length} bots to the simulation`);
    
    if (!this.options.bots || this.options.bots.length === 0) {
      logger.warn('No bots configured for simulation');
      return;
    }
    
    try {
      const promises = this.options.bots.map(async (botConfig) => {
        const { name, strategy, color } = botConfig;
        
        logger.info(`Adding bot ${name} with ${strategy} strategy`);
        
        const response = await fetch(`${this.options.serverUrl}/api/simulation/bot`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name,
            strategyName: strategy,
            color: color || this._getRandomColor()
          })
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to add bot: ${response.status} ${response.statusText} - ${errorText}`);
        }
        
        const responseData = await response.json();
        if (!responseData.success || !responseData.bot || !responseData.bot.id) {
          throw new Error('Invalid bot response from server');
        }
        
        logger.info(`Bot added successfully: ${responseData.bot.id}`);
        
        // Initialize bot stats for this bot
        this.results.botStats[responseData.bot.id] = {
          name,
          strategy,
          gamesPlayed: 0,
          wins: 0,
          totalScore: 0,
          averageScore: 0
        };
        
        return responseData.bot;
      });
      
      const results = await Promise.all(promises);
      logger.info(`Successfully added ${results.length} bots`);
      
      return results;
    } catch (error) {
      logger.error(`Error adding bots: ${error.message}`);
      this.results.errors.push(`Failed to add bots: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Generate a random color for bots
   * @returns {string} Random color in hex format
   * @private
   */
  _getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }
  
  /**
   * Start the simulation
   * @returns {Promise} Promise that resolves when the simulation starts
   */
  async startSimulation() {
    logger.info('Starting simulation');
    
    try {
      const response = await fetch(`${this.options.serverUrl}/api/simulation/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to start simulation: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      logger.info(`Simulation started: ${data.message}`);
      this.simulationRunning = true;
      return data;
    } catch (error) {
      logger.error(`Error starting simulation: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Stop the simulation
   * @returns {Promise} Promise that resolves when the simulation stops
   */
  async stopSimulation() {
    if (!this.simulationRunning) {
      logger.info('Simulation is not running, no need to stop');
      return { status: 'ok', message: 'Simulation was not running' };
    }
    
    logger.info('Stopping simulation');
    
    try {
      const response = await fetch(`${this.options.serverUrl}/api/simulation/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to stop simulation: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      logger.info(`Simulation stopped: ${data.message}`);
      this.simulationRunning = false;
      return data;
    } catch (error) {
      logger.error(`Error stopping simulation: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Get the simulation status
   * @returns {Promise<Object>} Promise that resolves with the simulation status
   */
  async getStatus() {
    try {
      const response = await fetch(`${this.options.serverUrl}/api/simulation/status`);
      
      if (!response.ok) {
        throw new Error(`Failed to get simulation status: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      logger.debug(`Simulation status: ${JSON.stringify(data)}`);
      return data;
    } catch (error) {
      logger.error(`Error getting simulation status: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Run the complete simulation workflow
   * @returns {Promise<Object>} Promise that resolves with the simulation results
   */
  async run() {
    try {
      logger.info('Starting simulation workflow');
      logger.info(`Configuration: ${JSON.stringify(this.options, null, 2)}`);
      
      // Connect to the server
      await this.connect();
      
      // Configure the simulation
      await this.configureSimulation();
      
      // Add bots to the simulation
      await this.addBots();
      
      // Start the simulation
      await this.startSimulation();
      
      // Wait for simulation completion
      await this._waitForCompletion();
      
      // Get final status
      const finalStatus = await this.getStatus();
      
      // Disconnect and return results
      this.disconnect();
      
      logger.info('Simulation workflow completed');
      return {
        ...this.results,
        finalStatus
      };
    } catch (error) {
      logger.error(`Simulation workflow failed: ${error.message}`);
      
      // Try to stop the simulation if it's running
      if (this.simulationRunning) {
        try {
          await this.stopSimulation();
        } catch (stopError) {
          logger.error(`Failed to stop simulation: ${stopError.message}`);
        }
      }
      
      // Disconnect
      this.disconnect();
      
      throw error;
    }
  }
  
  /**
   * Wait for the simulation to complete
   * @returns {Promise} Promise that resolves when the simulation completes
   * @private
   */
  _waitForCompletion() {
    return new Promise((resolve, reject) => {
      // Set up a timeout for the maximum simulation duration
      const maxDuration = Math.max(60000, this.options.numGames * 30000); // At least 1 minute or 30s per game
      const timeout = setTimeout(() => {
        logger.warn(`Simulation timeout reached after ${maxDuration}ms`);
        resolve(); // Resolve anyway to allow cleanup
      }, maxDuration);
      
      // Set up a completion listener
      const completionListener = (stats) => {
        clearTimeout(timeout);
        resolve(stats);
      };
      
      // Add the one-time listener
      this.socket.once('simulationCompleted', completionListener);
      
      // Check the status periodically
      const interval = setInterval(async () => {
        try {
          const status = await this.getStatus();
          if (!status.running) {
            clearInterval(interval);
            clearTimeout(timeout);
            this.socket.off('simulationCompleted', completionListener); // Remove the listener
            resolve(status);
          }
        } catch (error) {
          logger.error(`Error checking simulation status: ${error.message}`);
          // Don't reject, keep trying
        }
      }, 5000); // Check every 5 seconds
    });
  }
  
  /**
   * Update the bot statistics from game state
   * @param {Object} state - The game state
   * @private
   */
  _updateBotStats(state) {
    if (!state.players) return;
    
    for (const player of state.players) {
      // Only track bots
      if (!player.isBot) continue;
      
      if (!this.results.botStats[player.id]) {
        this.results.botStats[player.id] = {
          name: player.name,
          wins: 0,
          journeyPoints: 0,
          meditations: 0,
          movementCount: 0,
          travelCardCount: 0,
          journeyCardCount: 0
        };
      }
      
      const stats = this.results.botStats[player.id];
      
      // Update stats
      if (state.winner && state.winner.id === player.id) {
        stats.wins++;
      }
      
      stats.journeyPoints += player.journeyPoints || 0;
      stats.meditations += player.meditationCount || 0;
      stats.movementCount += player.movementCount || 0;
      stats.travelCardCount += (player.hand?.travelCards?.length || 0);
      stats.journeyCardCount += (player.hand?.journeyCards?.length || 0);
    }
  }
  
  /**
   * Disconnect from the server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.connected = false;
      logger.info('Disconnected from server');
    }
  }
  
  /**
   * Save a game result to file
   * @private
   * @param {object} result - Game result to save
   */
  _saveGameResult(result) {
    try {
      const resultsDir = path.join(__dirname, '..', 'simulation_results');
      
      // Ensure directory exists
      if (!fs.existsSync(resultsDir)) {
        fs.mkdirSync(resultsDir, { recursive: true });
      }
      
      const filename = path.join(
        resultsDir, 
        `game_${this.timestamp}_${result.gameNumber}.json`
      );
      
      fs.writeFileSync(filename, JSON.stringify(result, null, 2));
      logger.info(`Saved game result to ${filename}`);
    } catch (error) {
      logger.error(`Error saving game result: ${error.message}`);
    }
  }
}

module.exports = SimulationClientRunner; 