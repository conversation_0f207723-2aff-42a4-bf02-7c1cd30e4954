import React, { useState, useEffect } from 'react';

function EnergyCubeSelection({ player, onCancel, onSelectCubes, numberOfCubesToSelect = 2, title, description }) {
  const [selectedCubes, setSelectedCubes] = useState([]);
  const [error, setError] = useState(null);
  
  // Ensure that numberOfCubesToSelect is valid based on player's cube count
  const actualNumberToSelect = Math.min(numberOfCubesToSelect, player.energyCubes.length);
  
  // Reset selection when the component is first shown
  useEffect(() => {
    setSelectedCubes([]);
    setError(null);
  }, []);
  
  const handleCubeToggle = (cube, index) => {
    // Create a unique ID for this cube using its type and index
    const cubeId = `${cube}-${index}`;
    
    // If already selected, remove it
    if (selectedCubes.some(sc => sc.id === cubeId)) {
      setSelectedCubes(selectedCubes.filter(sc => sc.id !== cubeId));
      setError(null);
    } else {
      // If we haven't reached the selection limit, add it
      if (selectedCubes.length < actualNumberToSelect) {
        setSelectedCubes([...selectedCubes, { type: cube, id: cubeId, index }]);
        setError(null);
      } else {
        setError(`You can only select ${actualNumberToSelect} cubes`);
      }
    }
  };
  
  const handleConfirm = () => {
    if (selectedCubes.length === actualNumberToSelect) {
      console.log("Confirming cube selection:", selectedCubes);
      onSelectCubes(selectedCubes);
    } else {
      setError(`Please select exactly ${actualNumberToSelect} cubes`);
    }
  };
  
  // Handle cancel button
  const handleCancel = () => {
    console.log("Cancelling cube selection");
    if (onCancel) {
      onCancel();
    }
  };
  
  // Group cubes by type for better display
  const groupedCubes = {};
  player.energyCubes.forEach((cube, index) => {
    if (!groupedCubes[cube]) {
      groupedCubes[cube] = [];
    }
    groupedCubes[cube].push({ type: cube, index });
  });
  
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 9999
    }}>
      <div style={{
        backgroundColor: '#fff',
        borderRadius: '8px',
        padding: '20px',
        width: '90%',
        maxWidth: '450px',
        boxShadow: '0 5px 15px rgba(0, 0, 0, 0.3)'
      }}>
        <h2 style={{ marginTop: 0, textAlign: 'center' }}>{title || 'Select Energy Cubes'}</h2>
        
        <p style={{ textAlign: 'center', marginBottom: '20px' }}>
          {description || `Select ${actualNumberToSelect} energy cubes to discard.`}
        </p>
        
        {error && (
          <p style={{ color: 'red', textAlign: 'center', marginBottom: '15px' }}>
            {error}
          </p>
        )}
        
        <div style={{ marginBottom: '20px' }}>
          {Object.entries(groupedCubes).map(([cubeType, cubes]) => (
            <div key={cubeType} style={{ marginBottom: '15px' }}>
              <h3 style={{ 
                textTransform: 'capitalize', 
                margin: '0 0 8px 0',
                fontSize: '1rem'
              }}>
                {cubeType} ({cubes.length})
              </h3>
              
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                {cubes.map((cube) => {
                  const isSelected = selectedCubes.some(sc => sc.id === `${cube.type}-${cube.index}`);
                  const cubeColor = getCubeColor(cube.type);
                  
                  return (
                    <div 
                      key={`${cube.type}-${cube.index}`}
                      onClick={() => handleCubeToggle(cube.type, cube.index)}
                      style={{
                        width: '40px',
                        height: '40px',
                        backgroundColor: cubeColor,
                        borderRadius: '4px',
                        cursor: 'pointer',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        border: isSelected ? '3px solid #2196F3' : '1px solid #0000001a',
                        boxShadow: isSelected ? '0 0 8px rgba(33, 150, 243, 0.7)' : 'none'
                      }}
                    >
                      {isSelected && (
                        <div style={{
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          backgroundColor: 'white',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          fontWeight: 'bold',
                          color: '#2196F3'
                        }}>
                          ✓
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
        
        {Object.keys(groupedCubes).length === 0 && (
          <p style={{ textAlign: 'center', color: '#666', marginBottom: '20px' }}>
            You don't have any energy cubes.
          </p>
        )}
        
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          gap: '10px',
          marginTop: '20px' 
        }}>
          <button 
            onClick={handleCancel}
            style={{
              padding: '8px 16px',
              backgroundColor: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
          
          <button 
            onClick={handleConfirm}
            disabled={selectedCubes.length !== actualNumberToSelect || Object.keys(groupedCubes).length === 0}
            style={{
              padding: '8px 16px',
              backgroundColor: selectedCubes.length === actualNumberToSelect && Object.keys(groupedCubes).length > 0 
                ? '#4CAF50' 
                : '#ccc',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: selectedCubes.length === actualNumberToSelect && Object.keys(groupedCubes).length > 0
                ? 'pointer' 
                : 'not-allowed'
            }}
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
}

// Helper function to get cube colors
function getCubeColor(cubeType) {
  const colors = {
    artha: '#FF9800',  // orange
    karma: '#4CAF50',  // green
    gnana: '#2196F3',  // blue
    bhakti: '#9C27B0'  // purple
  };
  
  return colors[cubeType.toLowerCase()] || '#ccc';
}

export default EnergyCubeSelection; 