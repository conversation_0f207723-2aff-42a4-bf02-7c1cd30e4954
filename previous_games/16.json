{"description": "Realized a lot of good insights: 1. Need to be very smart about journey card selection with only 4 om tokens; either need good amount of bonuses or choose mix of heavy and lighr journeys", "players": [{"id": "dnNzx9hJBld4pVMjAAAB", "name": "WiseNomad", "position": 44, "hand": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "energyCubes": ["gnana", "bhakti", "artha"], "omTemp": [], "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "outerScore": 75, "innerScore": 5, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "position": 46, "hand": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "energyCubes": ["artha"], "omTemp": [], "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "outerScore": 90, "innerScore": 29, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "GqWJZS1nDhE37_cWAAAF", "name": "<PERSON><PERSON>", "position": 58, "hand": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "energyCubes": ["gnana", "gnana", "bhakti", "karma", "artha"], "omTemp": [1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "outerScore": 37, "innerScore": 39, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}}], "started": true, "turnIndex": 2, "roundCount": 14, "travelDeck": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "eventDeck": [{"id": "E5", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}, {"id": "E8", "type": "wildCube"}, {"id": "E1", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "travelDiscard": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "faceUpEvent": [{"id": "E3", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}, {"id": "E4", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "faceUpJourneyOuter": [{"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}], "locationCubes": {"1": "karma", "5": "karma", "6": "gnana", "9": "karma", "10": "gnana", "11": "gnana", "15": "karma", "21": "bhakti", "22": "gnana", "24": "artha", "30": "gnana", "31": "bhakti", "32": "bhakti", "36": "karma", "38": "artha", "39": "artha", "43": "gnana", "undefined": "gnana"}, "locationOm": {}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "omTokenVictory": true, "omTokenVictor": "LgaefFf3khrfoKsdAAAD", "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025238190, "data": {"eventId": "global-event-22", "eventName": "Up and Over", "eventEffect": "up_and_over_reward"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025253055, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025253055, "data": {"cardType": "travel", "card": {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025253055, "data": {"cardType": "travel", "card": {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025253055, "data": {"cardType": "travel", "card": {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025253055, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025253055, "data": {"cardType": "travel", "card": {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "move", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025255154, "data": {"path": [63, 21, 20, 51], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 0, "turnIndex": 0, "timestamp": 1745025258159, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 0, "turnIndex": 1, "timestamp": 1745025261159, "data": {"nextPlayerId": "l9u_GOzrfgLSpM7lAAAB", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745025290412, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745025299366, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745025328812, "data": {"vehicle": "helicopter", "effect": "up_and_over_reward", "outerPoints": 5}}, {"type": "move", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745025328813, "data": {"path": [61, 4, 3, 56], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745025333424, "data": {"nextPlayerId": "RbMOChcZ3lAXZWWbAAAD", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745025397119, "data": {"path": [62, 11, 12, 50], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745025403389, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745025411124, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1745025446428, "data": {"eventId": "global-event-20", "eventName": "Scenic Cruise", "eventEffect": "scenic_cruise_reward"}}, {"type": "endTurn", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745025446428, "data": {"nextPlayerId": "NmzRbhTW2ipA4qWKAAAF", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 1, "turnIndex": 0, "timestamp": 1745025448447, "data": {"path": [51, 20], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 1, "turnIndex": 0, "timestamp": 1745025451450, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 1, "turnIndex": 1, "timestamp": 1745025454451, "data": {"nextPlayerId": "l9u_GOzrfgLSpM7lAAAB", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745025575337, "data": {"path": [56, 3, 4], "travelCards": ["T1", "T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745025586737, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745025593258, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745025595717, "data": {"nextPlayerId": "RbMOChcZ3lAXZWWbAAAD", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745025605794, "data": {"path": [50, 12, 13, 14, 16], "travelCards": ["T12", "T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745025612273, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745025620658, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1745025624524, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "endTurn", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745025624525, "data": {"nextPlayerId": "NmzRbhTW2ipA4qWKAAAF", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 2, "turnIndex": 0, "timestamp": 1745025626380, "data": {"path": [20, 21, 28, 29, 33], "travelCards": ["T7", "T6"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 2, "turnIndex": 0, "timestamp": 1745025629382, "data": {"journeyType": "outer", "journeyCardId": "JO17", "omRequirement": 1}}, {"type": "endTurn", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 2, "turnIndex": 1, "timestamp": 1745025632384, "data": {"nextPlayerId": "l9u_GOzrfgLSpM7lAAAB", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745025678561, "data": {"path": [4, 61, 62, 13, 14], "travelCards": ["T4", "T9"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745025719243, "data": {"journeyType": "inner", "journeyCardId": "JI7", "omRequirement": 1}}, {"type": "endTurn", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1745025724287, "data": {"nextPlayerId": "RbMOChcZ3lAXZWWbAAAD", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1745025732761, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "region_based_reward", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1745025768876, "data": {"region": "West", "vehicle": "camel", "effect": "sandy_west", "innerPoints": 7}}, {"type": "move", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1745025768876, "data": {"path": [16, 14, 13, 34], "travelCards": ["T1", "T5"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1745025771683, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "endTurn", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745025771683, "data": {"nextPlayerId": "NmzRbhTW2ipA4qWKAAAF", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 3, "turnIndex": 0, "timestamp": 1745025773819, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 3, "turnIndex": 0, "timestamp": 1745025776821, "data": {"path": [33, 29, 28, 18, 53], "travelCards": ["T10", "T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 3, "turnIndex": 1, "timestamp": 1745025779823, "data": {"nextPlayerId": "l9u_GOzrfgLSpM7lAAAB", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745025809429, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745025867695, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745025889102, "data": {"path": [14, 13, 12, 11, 10, 49], "travelCards": ["T9", "T7"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745025898519, "data": {"nextPlayerId": "RbMOChcZ3lAXZWWbAAAD", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745025904188, "data": {"path": [34, 13, 14, 19], "travelCards": ["T8", "T2"], "extraHopCards": []}}, {"type": "trade", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745025908349, "data": {"cubesTraded": ["artha"], "cubeReceived": "gnana", "count": 1}}, {"type": "collectJourney", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745025912308, "data": {"journeyType": "inner", "journeyCardId": "JI10", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1745025914729, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "endTurn", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745025914729, "data": {"nextPlayerId": "NmzRbhTW2ipA4qWKAAAF", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 4, "turnIndex": 0, "timestamp": 1745025916814, "data": {"path": [53, 18, 15, 5], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 4, "turnIndex": 0, "timestamp": 1745025919817, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 4, "turnIndex": 1, "timestamp": 1745025922817, "data": {"nextPlayerId": "l9u_GOzrfgLSpM7lAAAB", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745025958833, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745025988942, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745025997953, "data": {"path": [49, 10, 11, 7, 54], "travelCards": ["T11", "T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1745026002282, "data": {"nextPlayerId": "RbMOChcZ3lAXZWWbAAAD", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1745026014717, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1745026022284, "data": {"path": [19, 62, 63, 24, 23, 59], "travelCards": ["T10", "T7"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1745026025153, "data": {"eventId": "global-event-29", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "endTurn", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745026025153, "data": {"nextPlayerId": "NmzRbhTW2ipA4qWKAAAF", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 5, "turnIndex": 0, "timestamp": 1745026027149, "data": {"path": [5, 15, 64, 63, 25], "travelCards": ["T6", "T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 5, "turnIndex": 0, "timestamp": 1745026030152, "data": {"journeyType": "outer", "journeyCardId": "JO11", "omRequirement": 1}}, {"type": "endTurn", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 5, "turnIndex": 1, "timestamp": 1745026033154, "data": {"nextPlayerId": "l9u_GOzrfgLSpM7lAAAB", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745026111820, "data": {"path": [54, 7], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745026127331, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "l9u_GOzrfgLSpM7lAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1745026130252, "data": {"nextPlayerId": "RbMOChcZ3lAXZWWbAAAD", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1745026214939, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1745026222228, "data": {"path": [59, 23], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1745026224049, "data": {"eventId": "global-event-19", "eventName": "Bullet Train", "eventEffect": "bullet_train_reward"}}, {"type": "endTurn", "playerId": "RbMOChcZ3lAXZWWbAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1745026224049, "data": {"nextPlayerId": "NmzRbhTW2ipA4qWKAAAF", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 6, "turnIndex": 0, "timestamp": 1745026225867, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "NmzRbhTW2ipA4qWKAAAF", "playerName": "WiseNomad", "roundCount": 6, "turnIndex": 1, "timestamp": 1745026228869, "data": {"nextPlayerId": "l9u_GOzrfgLSpM7lAAAB", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 1, "timestamp": 1745026309428, "data": {"timestamp": 1745026309428, "loadedStateRoundCount": 6, "playerCount": 3}}, {"type": "move", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745026344524, "data": {"path": [7, 6, 61, 64, 15, 18], "travelCards": ["T12", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745026349928, "data": {"journeyType": "outer", "journeyCardId": "JO9", "omRequirement": 1}}, {"type": "endTurn", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1745026355334, "data": {"nextPlayerId": "xP3RWcvDXjUASQsuAAAF", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1745026428884, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1745026436157, "data": {"vehicle": "train", "effect": "bullet_train_reward", "outerPoints": 5}}, {"type": "move", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1745026436157, "data": {"path": [23, 22, 21, 28, 29], "travelCards": ["T11", "T2"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1745026439954, "data": {"eventId": "global-event-8", "eventName": "Triathlon", "eventEffect": "triathlon_bonus"}}, {"type": "endTurn", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1745026439954, "data": {"nextPlayerId": "yxTfod1ZdtdRF7zdAAAB", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 7, "turnIndex": 0, "timestamp": 1745026441883, "data": {"path": [25, 63, 64, 15, 5, 52], "travelCards": ["T9", "T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 7, "turnIndex": 0, "timestamp": 1745026444888, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 7, "turnIndex": 1, "timestamp": 1745026447889, "data": {"nextPlayerId": "rBPcsxoXLunGrP7mAAAD", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745026535048, "data": {"cardType": "travel", "drawnCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745026541713, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745026556862, "data": {"path": [18, 28, 37], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1745026568325, "data": {"nextPlayerId": "xP3RWcvDXjUASQsuAAAF", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1745026608156, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1745026612201, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickFromTop": true}}, {"type": "move", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1745026646664, "data": {"path": [29, 33, 34, 13, 12], "travelCards": ["T4", "T11"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1745026648958, "data": {"eventId": "global-event-9", "eventName": "Riots", "eventEffect": "riots_discard"}}, {"type": "endTurn", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745026648958, "data": {"nextPlayerId": "yxTfod1ZdtdRF7zdAAAB", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 8, "turnIndex": 0, "timestamp": 1745026651327, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 8, "turnIndex": 0, "timestamp": 1745026654333, "data": {"path": [52, 5, 15, 64, 65, 31, 30, 55], "travelCards": ["T5", "T7", "T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 8, "turnIndex": 1, "timestamp": 1745026657332, "data": {"nextPlayerId": "rBPcsxoXLunGrP7mAAAD", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745026743757, "data": {"path": [37, 28], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745026793080, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1745026796374, "data": {"nextPlayerId": "xP3RWcvDXjUASQsuAAAF", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1745026820869, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1745026830573, "data": {"path": [12, 13], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1745026833467, "data": {"eventId": "global-event-32", "eventName": "Central Heart", "eventEffect": "central_heart"}}, {"type": "endTurn", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745026833467, "data": {"nextPlayerId": "yxTfod1ZdtdRF7zdAAAB", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 9, "turnIndex": 0, "timestamp": 1745026835730, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 9, "turnIndex": 0, "timestamp": 1745026838736, "data": {"path": [55, 30, 31, 45], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 9, "turnIndex": 1, "timestamp": 1745026841736, "data": {"nextPlayerId": "rBPcsxoXLunGrP7mAAAD", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "region_based_reward", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745026969640, "data": {"region": "Central", "effect": "central_heart", "outerPoints": 5}}, {"type": "move", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745026969640, "data": {"path": [28, 21, 63, 24, 25, 27], "travelCards": ["T11", "T6"], "extraHopCards": []}}, {"type": "trade", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745026992854, "data": {"cubesTraded": ["bhakti"], "cubeReceived": "karma", "count": 1}}, {"type": "collectJourney", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745026997202, "data": {"journeyType": "outer", "journeyCardId": "JO12", "omRequirement": 1}}, {"type": "endTurn", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1745027003721, "data": {"nextPlayerId": "xP3RWcvDXjUASQsuAAAF", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1745027011569, "data": {"path": [13, 62, 61, 6, 4, 3, 47], "travelCards": ["T10", "T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1745027070270, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1745027075213, "data": {"eventId": "global-event-25", "eventName": "<PERSON><PERSON><PERSON>'s Grace", "eventEffect": "pilgrims_grace_reward"}}, {"type": "endTurn", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745027075213, "data": {"nextPlayerId": "yxTfod1ZdtdRF7zdAAAB", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 10, "turnIndex": 0, "timestamp": 1745027076933, "data": {"path": [45, 44], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 10, "turnIndex": 0, "timestamp": 1745027079936, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 10, "turnIndex": 1, "timestamp": 1745027082937, "data": {"nextPlayerId": "rBPcsxoXLunGrP7mAAAD", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745027144447, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745027165021, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745027185911, "data": {"path": [27, 25, 63, 66, 42, 48, 57], "travelCards": ["T11", "T10"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1745027192874, "data": {"nextPlayerId": "xP3RWcvDXjUASQsuAAAF", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1745027232541, "data": {"path": [47, 3], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1745027249309, "data": {"eventId": "global-event-10", "eventName": "Om Meditation", "eventEffect": "om_meditation"}}, {"type": "om_meditation_gain", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 11, "turnIndex": 0, "timestamp": 1745027249310, "data": {"jyotirlinga": 60, "distance": 2}}, {"type": "endTurn", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745027249310, "data": {"nextPlayerId": "yxTfod1ZdtdRF7zdAAAB", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 11, "turnIndex": 0, "timestamp": 1745027251738, "data": {"path": [44, 43, 42], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 11, "turnIndex": 0, "timestamp": 1745027254741, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "yxTfod1ZdtdRF7zdAAAB", "playerName": "WiseNomad", "roundCount": 11, "turnIndex": 1, "timestamp": 1745027257741, "data": {"nextPlayerId": "rBPcsxoXLunGrP7mAAAD", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1745027320468, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1745027329171, "data": {"cardType": "travel", "drawnCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "rBPcsxoXLunGrP7mAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1745027346273, "data": {"nextPlayerId": "xP3RWcvDXjUASQsuAAAF", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "xP3RWcvDXjUASQsuAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1745027440875, "data": {"path": [3, 47], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 2, "timestamp": 1745028026534, "data": {"timestamp": 1745028026534, "loadedStateRoundCount": 11, "playerCount": 3}}, {"type": "collectJourney", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1745028050540, "data": {"journeyType": "outer", "journeyCardId": "JO26", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1745028054496, "data": {"eventId": "global-event-16", "eventName": "<PERSON><PERSON> Rhapsody", "eventEffect": "rickshaw_rhapsody_reward"}}, {"type": "endTurn", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1745028054496, "data": {"nextPlayerId": "dnNzx9hJBld4pVMjAAAB", "newRound": true, "roundCount": 12, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 12, "turnIndex": 0, "timestamp": 1745028056869, "data": {"vehicle": "rickshaw", "effect": "rickshaw_rhapsody_reward", "outerPoints": 5}}, {"type": "move", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 12, "turnIndex": 0, "timestamp": 1745028056869, "data": {"path": [42, 48, 57, 47, 3, 2], "travelCards": ["T9", "T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 12, "turnIndex": 0, "timestamp": 1745028059872, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 12, "turnIndex": 1, "timestamp": 1745028062873, "data": {"nextPlayerId": "LgaefFf3khrfoKsdAAAD", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1745028100975, "data": {"path": [57, 48], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1745028122200, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1745028134873, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745028137943, "data": {"nextPlayerId": "GqWJZS1nDhE37_cWAAAF", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745028173407, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745028187927, "data": {"path": [47, 46], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1745028191986, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "endTurn", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1745028191987, "data": {"nextPlayerId": "dnNzx9hJBld4pVMjAAAB", "newRound": true, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 13, "turnIndex": 0, "timestamp": 1745028193542, "data": {"path": [2, 3, 47, 46, 45, 44], "travelCards": ["T12", "T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 13, "turnIndex": 0, "timestamp": 1745028196544, "data": {"journeyType": "outer", "journeyCardId": "JO23", "omRequirement": 2}}, {"type": "endTurn", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 13, "turnIndex": 1, "timestamp": 1745028199546, "data": {"nextPlayerId": "LgaefFf3khrfoKsdAAAD", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1745028373642, "data": {"path": [48, 42, 43, 60], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1745028475627, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745028478220, "data": {"nextPlayerId": "GqWJZS1nDhE37_cWAAAF", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745028500851, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745028504137, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "travel_card_reward", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745028512441, "data": {"vehicle": "camel", "effect": "desert_caravan_reward", "outerPoints": 5}}, {"type": "move", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745028512442, "data": {"path": [46, 45, 31, 32, 58], "travelCards": ["T9", "T1"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1745028537217, "data": {"eventId": "global-event-5", "eventName": "Bountiful Bhandara", "eventEffect": "draw_2_cubes_bonus_5_outer"}}, {"type": "endTurn", "playerId": "GqWJZS1nDhE37_cWAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1745028537222, "data": {"nextPlayerId": "dnNzx9hJBld4pVMjAAAB", "newRound": true, "roundCount": 14, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 14, "turnIndex": 0, "timestamp": 1745028539438, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "dnNzx9hJBld4pVMjAAAB", "playerName": "WiseNomad", "roundCount": 14, "turnIndex": 1, "timestamp": 1745028542440, "data": {"nextPlayerId": "LgaefFf3khrfoKsdAAAD", "newRound": false, "roundCount": 14, "turnCount": null}}, {"type": "move", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1745028586917, "data": {"path": [60, 43, 44, 45, 46], "travelCards": ["T10", "T4"], "extraHopCards": []}}, {"type": "trade", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1745028607176, "data": {"cubesTraded": ["bhakti"], "cubeReceived": "karma", "count": 1}}, {"type": "collectJourney", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1745028613389, "data": {"journeyType": "outer", "journeyCardId": "JO25", "omRequirement": 2}}, {"type": "OM_TOKEN_VICTORY", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1745028613389, "data": {"playerOmTotal": 5, "omTokenThreshold": 5}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 2, "timestamp": 1745028618099, "data": {"winner": {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "outerScore": 90, "innerScore": 29, "totalScore": 119, "omTotal": 5, "winByOm": true, "winByScore": false}, "winCondition": "OM_THRESHOLD", "totalRounds": 14, "players": [{"id": "dnNzx9hJBld4pVMjAAAB", "name": "WiseNomad", "outerScore": 75, "innerScore": 5, "totalScore": 80, "omTotal": 4}, {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "outerScore": 90, "innerScore": 29, "totalScore": 119, "omTotal": 5}, {"id": "GqWJZS1nDhE37_cWAAAF", "name": "<PERSON><PERSON>", "outerScore": 37, "innerScore": 39, "totalScore": 76, "omTotal": 3}]}}, {"type": "endTurn", "playerId": "LgaefFf3khrfoKsdAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 2, "timestamp": 1745028618099, "data": {"nextPlayerId": "GqWJZS1nDhE37_cWAAAF", "newRound": false, "roundCount": 14, "turnCount": null}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 2, "timestamp": 1745028618101, "data": {"winner": {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "outerScore": 90, "innerScore": 29, "totalScore": 119, "omTotal": 5, "winByOm": true, "winByScore": false}, "winCondition": "OM_THRESHOLD", "totalRounds": 14, "players": [{"id": "dnNzx9hJBld4pVMjAAAB", "name": "WiseNomad", "outerScore": 75, "innerScore": 5, "totalScore": 80, "omTotal": 4}, {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "outerScore": 90, "innerScore": 29, "totalScore": 119, "omTotal": 5}, {"id": "GqWJZS1nDhE37_cWAAAF", "name": "<PERSON><PERSON>", "outerScore": 37, "innerScore": 39, "totalScore": 76, "omTotal": 3}]}}], "roundSummaries": [{"roundNumber": 1, "timestamp": 1745025446427, "players": [{"id": "NmzRbhTW2ipA4qWKAAAF", "name": "WiseNomad", "position": 51, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}}, {"id": "l9u_GOzrfgLSpM7lAAAB", "name": "<PERSON><PERSON>", "position": 56, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 0, "total": 5}}, {"id": "RbMOChcZ3lAXZWWbAAAD", "name": "<PERSON><PERSON>", "position": 50, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}}], "faceUpCards": {"travel": [{"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}]}, "globalEvent": {"id": "global-event-22", "name": "Up and Over", "effect": "up_and_over_reward"}}, {"roundNumber": 2, "timestamp": 1745025624524, "players": [{"id": "NmzRbhTW2ipA4qWKAAAF", "name": "WiseNomad", "position": 20, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}}, {"id": "l9u_GOzrfgLSpM7lAAAB", "name": "<PERSON><PERSON>", "position": 4, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 0, "total": 5}}, {"id": "RbMOChcZ3lAXZWWbAAAD", "name": "<PERSON><PERSON>", "position": 16, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}}], "faceUpCards": {"travel": [{"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T1", "value": 1, "vehicle": "camel"}, {"id": "T12", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}]}, "globalEvent": {"id": "global-event-20", "name": "Scenic Cruise", "effect": "scenic_cruise_reward"}}, {"roundNumber": 3, "timestamp": 1745025771682, "players": [{"id": "NmzRbhTW2ipA4qWKAAAF", "name": "WiseNomad", "position": 33, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 20, "inner": 0, "total": 20}}, {"id": "l9u_GOzrfgLSpM7lAAAB", "name": "<PERSON><PERSON>", "position": 14, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 24, "total": 29}}, {"id": "RbMOChcZ3lAXZWWbAAAD", "name": "<PERSON><PERSON>", "position": 34, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 2, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 7, "total": 7}}], "faceUpCards": {"travel": [{"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T12", "value": 3, "vehicle": "truck"}, {"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO11", "locationId": 25, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI10", "locationId": 19, "requiredCubes": {}, "points": 27}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-28", "name": "<PERSON>", "effect": "sandy_west"}}, {"roundNumber": 4, "timestamp": 1745025914729, "players": [{"id": "NmzRbhTW2ipA4qWKAAAF", "name": "WiseNomad", "position": 53, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 20, "inner": 0, "total": 20}}, {"id": "l9u_GOzrfgLSpM7lAAAB", "name": "<PERSON><PERSON>", "position": 49, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 24, "total": 29}}, {"id": "RbMOChcZ3lAXZWWbAAAD", "name": "<PERSON><PERSON>", "position": 19, "hand": {"total": 0, "travel": 0}, "omTemp": 0, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 34, "total": 34}}], "faceUpCards": {"travel": [{"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T1", "value": 1, "vehicle": "camel"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO11", "locationId": 25, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-6", "name": "Turbulent Skies", "effect": "no_airport_travel"}}, {"roundNumber": 5, "timestamp": 1745026025152, "players": [{"id": "NmzRbhTW2ipA4qWKAAAF", "name": "WiseNomad", "position": 5, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 20, "inner": 5, "total": 25}}, {"id": "l9u_GOzrfgLSpM7lAAAB", "name": "<PERSON><PERSON>", "position": 54, "hand": {"total": 1, "travel": 1}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 29, "total": 34}}, {"id": "RbMOChcZ3lAXZWWbAAAD", "name": "<PERSON><PERSON>", "position": 59, "hand": {"total": 0, "travel": 0}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 39, "total": 39}}], "faceUpCards": {"travel": [{"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T8", "value": 2, "vehicle": "motorbike"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T12", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO11", "locationId": 25, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "effect": "gain_5_inner_no_cube_pickup"}}, {"roundNumber": 6, "timestamp": 1745026224048, "players": [{"id": "NmzRbhTW2ipA4qWKAAAF", "name": "WiseNomad", "position": 25, "hand": {"total": 0, "travel": 0}, "omTemp": 0, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 40, "inner": 5, "total": 45}}, {"id": "l9u_GOzrfgLSpM7lAAAB", "name": "<PERSON><PERSON>", "position": 7, "hand": {"total": 2, "travel": 2}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 5, "inner": 29, "total": 34}}, {"id": "RbMOChcZ3lAXZWWbAAAD", "name": "<PERSON><PERSON>", "position": 23, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 39, "total": 39}}], "faceUpCards": {"travel": [{"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T1", "value": 1, "vehicle": "camel"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-29", "name": "Solar South", "effect": "solar_south"}}, {"roundNumber": 7, "timestamp": 1745026439954, "players": [{"id": "yxTfod1ZdtdRF7zdAAAB", "name": "WiseNomad", "position": 25, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 40, "inner": 5, "total": 45}}, {"id": "rBPcsxoXLunGrP7mAAAD", "name": "<PERSON><PERSON>", "position": 18, "hand": {"total": 0, "travel": 0}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 29, "inner": 29, "total": 58}}, {"id": "xP3RWcvDXjUASQsuAAAF", "name": "<PERSON><PERSON>", "position": 29, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 5, "inner": 39, "total": 44}}], "faceUpCards": {"travel": [{"id": "T1", "value": 1, "vehicle": "camel"}, {"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-19", "name": "Bullet Train", "effect": "bullet_train_reward"}}, {"roundNumber": 8, "timestamp": 1745026648957, "players": [{"id": "yxTfod1ZdtdRF7zdAAAB", "name": "WiseNomad", "position": 52, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 40, "inner": 5, "total": 45}}, {"id": "rBPcsxoXLunGrP7mAAAD", "name": "<PERSON><PERSON>", "position": 37, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 29, "inner": 29, "total": 58}}, {"id": "xP3RWcvDXjUASQsuAAAF", "name": "<PERSON><PERSON>", "position": 12, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 5, "inner": 39, "total": 44}}], "faceUpCards": {"travel": [{"id": "T1", "value": 1, "vehicle": "camel"}, {"id": "T12", "value": 3, "vehicle": "truck"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T2", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-8", "name": "Triathlon", "effect": "triathlon_bonus"}}, {"roundNumber": 9, "timestamp": 1745026833464, "players": [{"id": "yxTfod1ZdtdRF7zdAAAB", "name": "WiseNomad", "position": 55, "hand": {"total": 1, "travel": 1}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 40, "inner": 5, "total": 45}}, {"id": "rBPcsxoXLunGrP7mAAAD", "name": "<PERSON><PERSON>", "position": 28, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 29, "inner": 29, "total": 58}}, {"id": "xP3RWcvDXjUASQsuAAAF", "name": "<PERSON><PERSON>", "position": 13, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 3, "bhakti": 0, "gnana": 1, "karma": 1, "artha": 1}, "scores": {"outer": 5, "inner": 39, "total": 44}}], "faceUpCards": {"travel": [{"id": "T8", "value": 2, "vehicle": "motorbike"}, {"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T12", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-9", "name": "Riots", "effect": "riots_discard"}}, {"roundNumber": 10, "timestamp": 1745027075212, "players": [{"id": "yxTfod1ZdtdRF7zdAAAB", "name": "WiseNomad", "position": 45, "hand": {"total": 2, "travel": 2}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 1, "karma": 1, "artha": 0}, "scores": {"outer": 40, "inner": 5, "total": 45}}, {"id": "rBPcsxoXLunGrP7mAAAD", "name": "<PERSON><PERSON>", "position": 27, "hand": {"total": 0, "travel": 0}, "omTemp": 0, "omSlots": {"outer": 2, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 61, "inner": 29, "total": 90}}, {"id": "xP3RWcvDXjUASQsuAAAF", "name": "<PERSON><PERSON>", "position": 47, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 4, "bhakti": 0, "gnana": 2, "karma": 1, "artha": 1}, "scores": {"outer": 5, "inner": 39, "total": 44}}], "faceUpCards": {"travel": [{"id": "T7", "value": 2, "vehicle": "rickshaw"}, {"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}, {"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-32", "name": "Central Heart", "effect": "central_heart"}}, {"roundNumber": 11, "timestamp": 1745027249308, "players": [{"id": "yxTfod1ZdtdRF7zdAAAB", "name": "WiseNomad", "position": 44, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 0, "gnana": 1, "karma": 1, "artha": 1}, "scores": {"outer": 40, "inner": 5, "total": 45}}, {"id": "rBPcsxoXLunGrP7mAAAD", "name": "<PERSON><PERSON>", "position": 57, "hand": {"total": 0, "travel": 0}, "omTemp": 1, "omSlots": {"outer": 2, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 61, "inner": 29, "total": 90}}, {"id": "xP3RWcvDXjUASQsuAAAF", "name": "<PERSON><PERSON>", "position": 3, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 5, "bhakti": 0, "gnana": 2, "karma": 2, "artha": 1}, "scores": {"outer": 5, "inner": 39, "total": 44}}], "faceUpCards": {"travel": [{"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T12", "value": 3, "vehicle": "truck"}, {"id": "T6", "value": 2, "vehicle": "car"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO26", "locationId": 47, "requiredCubes": {}, "points": 27}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}, {"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "effect": "pilgrims_grace_reward"}}, {"roundNumber": 12, "timestamp": 1745028054495, "players": [{"id": "dnNzx9hJBld4pVMjAAAB", "name": "WiseNomad", "position": 42, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 0, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 40, "inner": 5, "total": 45}}, {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "position": 57, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 2, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 61, "inner": 29, "total": 90}}, {"id": "GqWJZS1nDhE37_cWAAAF", "name": "<PERSON><PERSON>", "position": 47, "hand": {"total": 0, "travel": 0}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 39, "total": 71}}], "faceUpCards": {"travel": [{"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T1", "value": 1, "vehicle": "camel"}, {"id": "T8", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}, {"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-10", "name": "Om Meditation", "effect": "om_meditation"}}, {"roundNumber": 13, "timestamp": 1745028191986, "players": [{"id": "dnNzx9hJBld4pVMjAAAB", "name": "WiseNomad", "position": 2, "hand": {"total": 2, "travel": 2}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 0, "gnana": 1, "karma": 2, "artha": 2}, "scores": {"outer": 45, "inner": 5, "total": 50}}, {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "position": 48, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 2, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 61, "inner": 29, "total": 90}}, {"id": "GqWJZS1nDhE37_cWAAAF", "name": "<PERSON><PERSON>", "position": 46, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 39, "total": 71}}], "faceUpCards": {"travel": [{"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}, {"id": "T3", "value": 1, "vehicle": "trek"}], "journeyOuter": [{"id": "JO23", "locationId": 44, "requiredCubes": {}, "points": 30}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}, {"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "effect": "rickshaw_rhapsody_reward"}}, {"roundNumber": 14, "timestamp": 1745028537216, "players": [{"id": "dnNzx9hJBld4pVMjAAAB", "name": "WiseNomad", "position": 44, "hand": {"total": 0, "travel": 0}, "omTemp": 0, "omSlots": {"outer": 3, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 75, "inner": 5, "total": 80}}, {"id": "LgaefFf3khrfoKsdAAAD", "name": "<PERSON><PERSON>", "position": 60, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 61, "inner": 29, "total": 90}}, {"id": "GqWJZS1nDhE37_cWAAAF", "name": "<PERSON><PERSON>", "position": 58, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 37, "inner": 39, "total": 76}}], "faceUpCards": {"travel": [{"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T12", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}, {"id": "JO25", "locationId": 46, "requiredCubes": {}, "points": 24}, {"id": "JO21", "locationId": 39, "requiredCubes": {}, "points": 27}, {"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-14", "name": "Desert Caravan", "effect": "desert_caravan_reward"}}], "characterDeck": [{"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}], "energyCubePile": {"artha": 7, "karma": 8, "gnana": 3, "bhakti": 5}, "currentGlobalEvent": {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, "globalEventDeck": [{"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}], "globalEventDiscard": [{"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}], "nameMode": false}