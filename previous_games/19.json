{"description": "<PERSON><PERSON><PERSON><PERSON><PERSON> won by very close margin getting many event card benefits", "players": [{"id": "cueVKiFQWyFQ4gWrAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 52, "hand": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "energyCubes": [], "omTemp": [1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "outerScore": 32, "innerScore": 66, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}}, {"id": "iVaqQQrlYNMTTfFSAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 6, "hand": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "energyCubes": [], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "outerScore": 59, "innerScore": 41, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}], "started": true, "turnIndex": 1, "roundCount": 11, "travelDeck": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}], "eventDeck": [{"id": "E5", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}, {"id": "E6", "type": "wildCube"}, {"id": "E8", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}], "travelDiscard": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "faceUpEvent": [{"id": "E9", "type": "wildCube"}, {"id": "E2", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}], "faceUpJourneyOuter": [{"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}], "locationCubes": {"1": "gnana", "2": "artha", "4": "bhakti", "5": "artha", "11": "artha", "13": "karma", "14": "karma", "15": "artha", "16": "karma", "18": "karma", "19": "bhakti", "22": "artha", "23": "artha", "24": "karma", "27": "bhakti", "28": "gnana", "29": "gnana", "30": "bhakti", "32": "gnana", "33": "artha", "34": "bhakti", "37": "artha", "38": "bhakti", "39": "bhakti", "42": "karma", "43": "artha", "46": "gnana", "48": "gnana", "undefined": "artha"}, "locationOm": {"49": true, "53": true, "56": true, "57": true, "60": true}, "finalRound": true, "finalRoundStarter": 1, "finalRoundEnd": 0, "omTokenVictory": false, "omTokenVictor": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972586929, "data": {"eventId": "global-event-8", "eventName": "Triathlon", "eventEffect": "triathlon_bonus"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972648534, "data": {"cardType": "travel", "card": {"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972648534, "data": {"cardType": "travel", "card": {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "cvSAcr8QuG3GuJkRAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972648534, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "cvSAcr8QuG3GuJkRAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972648534, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972757359, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972759962, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972799721, "data": {"path": [64, 61, 62, 65, 31, 32, 58], "travelCards": ["T23", "T8", "T3"], "extraHopCards": []}}, {"type": "triathlon_bonus", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745972810065, "data": {"bonus": 7, "usedValues": [3, 2, 1]}}, {"type": "endTurn", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745972810065, "data": {"nextPlayerId": "cvSAcr8QuG3GuJkRAAAB", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "cvSAcr8QuG3GuJkRAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745972832796, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "cvSAcr8QuG3GuJkRAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745972862104, "data": {"cardType": "travel", "pickedCards": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "cvSAcr8QuG3GuJkRAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745972893812, "data": {"path": [62, 61, 63, 25, 24, 23, 59], "travelCards": ["T6", "T10", "T4"], "extraHopCards": []}}, {"type": "triathlon_bonus", "playerId": "cvSAcr8QuG3GuJkRAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745972901221, "data": {"bonus": 7, "usedValues": [2, 3, 1]}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1745972901223, "data": {"eventId": "global-event-33", "eventName": "<PERSON><PERSON> Caravan<PERSON>", "eventEffect": "rajput_caravans_reward"}}, {"type": "endTurn", "playerId": "cvSAcr8QuG3GuJkRAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745972901224, "data": {"nextPlayerId": "UVPgs866jDjeIibGAAAD", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745972955866, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745972966283, "data": {"cardType": "travel", "pickedCards": [{"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745972972993, "data": {"vehicle": "camel", "effect": "rajput_caravans_reward", "outerPoints": 5}}, {"type": "travel_card_reward", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745972972993, "data": {"vehicle": "horse", "effect": "rajput_caravans_reward", "outerPoints": 5}}, {"type": "move", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745972972993, "data": {"path": [58, 32, 36], "travelCards": ["T13", "T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "UVPgs866jDjeIibGAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745972978006, "data": {"nextPlayerId": "cvSAcr8QuG3GuJkRAAAB", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 1, "timestamp": 1745973107701, "data": {"timestamp": 1745973107701, "loadedStateRoundCount": 1, "playerCount": 2}}, {"type": "PICK_DECK_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745973121579, "data": {"cardType": "travel", "drawnCards": [{"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745973126652, "data": {"cardType": "travel", "drawnCards": [{"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}], "pickFromTop": true}}, {"type": "travel_card_reward", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745973138061, "data": {"vehicle": "horse", "effect": "rajput_caravans_reward", "outerPoints": 5}}, {"type": "move", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745973138061, "data": {"path": [59, 23, 24, 25], "travelCards": ["T18", "T14"], "extraHopCards": []}}, {"type": "trade", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745973145597, "data": {"cubesTraded": ["gnana"], "cubeReceived": "artha", "count": 1}}, {"type": "collectJourney", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745973148152, "data": {"journeyType": "outer", "journeyCardId": "JO11", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1745973151467, "data": {"eventId": "global-event-3", "eventName": "<PERSON><PERSON>", "eventEffect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"type": "endTurn", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745973151471, "data": {"nextPlayerId": "ErZrWueiDz-hSr3KAAAD", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745973186747, "data": {"cardType": "travel", "pickedCards": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745973189677, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745973201971, "data": {"path": [36, 32, 31, 30, 55], "travelCards": ["T16", "T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745973205215, "data": {"nextPlayerId": "wGlhOOkox1JkCzIzAAAB", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745973215336, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745973238582, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745973249495, "data": {"path": [25, 63, 21, 20, 51], "travelCards": ["T15", "T9"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1745973253901, "data": {"eventId": "global-event-32", "eventName": "Eco Trail", "eventEffect": "eco_trail_reward"}}, {"type": "endTurn", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745973253902, "data": {"nextPlayerId": "ErZrWueiDz-hSr3KAAAD", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745973296071, "data": {"path": [55, 30, 31, 45], "travelCards": ["T21"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745973299269, "data": {"journeyType": "outer", "journeyCardId": "JO24", "omRequirement": 1}}, {"type": "endTurn", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745973302081, "data": {"nextPlayerId": "wGlhOOkox1JkCzIzAAAB", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745973357574, "data": {"cardType": "travel", "drawnCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745973358990, "data": {"cardType": "travel", "drawnCards": [{"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "move", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745973373777, "data": {"path": [51, 20, 21], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1745973376147, "data": {"eventId": "global-event-40", "eventName": "Spirit of Seva", "eventEffect": "spirit_of_seva"}}, {"type": "endTurn", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745973376149, "data": {"nextPlayerId": "ErZrWueiDz-hSr3KAAAD", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745973473714, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745973487762, "data": {"cardType": "travel", "pickedCards": [{"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745973615781, "data": {"path": [45, 44], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745973621052, "data": {"nextPlayerId": "wGlhOOkox1JkCzIzAAAB", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745973687312, "data": {"cardType": "travel", "pickedCards": [{"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745973689816, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745973721487, "data": {"path": [21, 63, 61, 6, 7, 54, 9], "travelCards": ["T22", "T24"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1745973727714, "data": {"eventId": "global-event-38", "eventName": "Excess Baggage", "eventEffect": "excess_baggage"}}, {"type": "endTurn", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745973727715, "data": {"nextPlayerId": "ErZrWueiDz-hSr3KAAAD", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745973819636, "data": {"path": [44, 45, 31], "travelCards": ["T19"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745973834188, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745973852987, "data": {"cardType": "travel", "pickedCards": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745973856260, "data": {"nextPlayerId": "wGlhOOkox1JkCzIzAAAB", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745973895311, "data": {"path": [9, 54, 7], "travelCards": ["T20"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745973899083, "data": {"journeyType": "inner", "journeyCardId": "JI4", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1745973901927, "data": {"eventId": "global-event-7", "eventName": "Election Campaigns", "eventEffect": "double_trade_no_travel"}}, {"type": "endTurn", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1745973901929, "data": {"nextPlayerId": "ErZrWueiDz-hSr3KAAAD", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1745973958422, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745973968957, "data": {"nextPlayerId": "wGlhOOkox1JkCzIzAAAB", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745974016794, "data": {"cardType": "travel", "drawnCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745974030518, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1745974032319, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "endTurn", "playerId": "wGlhOOkox1JkCzIzAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1745974032320, "data": {"nextPlayerId": "ErZrWueiDz-hSr3KAAAD", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1745974060903, "data": {"path": [31, 65, 63, 21, 20], "travelCards": ["T12", "T16"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1745974065737, "data": {"journeyType": "inner", "journeyCardId": "JI11", "omRequirement": 1}}, {"type": "endTurn", "playerId": "ErZrWueiDz-hSr3KAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745974074912, "data": {"nextPlayerId": "wGlhOOkox1JkCzIzAAAB", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 1, "timestamp": 1745974237831, "data": {"timestamp": 1745974237831, "loadedStateRoundCount": 7, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "gIpchG2jYa6n5yzuAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745974325944, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "gIpchG2jYa6n5yzuAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745974335013, "data": {"cardType": "travel", "pickedCards": [{"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1745974337388, "data": {"eventId": "global-event-28", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "endTurn", "playerId": "gIpchG2jYa6n5yzuAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745974337390, "data": {"nextPlayerId": "lYbLZ233TEemdPm0AAAD", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lYbLZ233TEemdPm0AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745974483588, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lYbLZ233TEemdPm0AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745974512003, "data": {"cardType": "travel", "pickedCards": [{"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "lYbLZ233TEemdPm0AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745974530492, "data": {"path": [20, 21, 63, 62, 11, 12], "travelCards": ["T11", "T18"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "lYbLZ233TEemdPm0AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745974534243, "data": {"nextPlayerId": "gIpchG2jYa6n5yzuAAAB", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "gIpchG2jYa6n5yzuAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745974550345, "data": {"path": [54, 9, 1, 2, 3, 47], "travelCards": ["T9", "T17"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "gIpchG2jYa6n5yzuAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745974562770, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1745974568126, "data": {"eventId": "global-event-9", "eventName": "Riots", "eventEffect": "riots_discard"}}, {"type": "endTurn", "playerId": "gIpchG2jYa6n5yzuAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745974568129, "data": {"nextPlayerId": "lYbLZ233TEemdPm0AAAD", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lYbLZ233TEemdPm0AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745974860712, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "lYbLZ233TEemdPm0AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745974864858, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "lYbLZ233TEemdPm0AAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745974866626, "data": {"nextPlayerId": "gIpchG2jYa6n5yzuAAAB", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1745974908432, "data": {"timestamp": 1745974908432, "loadedStateRoundCount": 9, "playerCount": 2}}, {"type": "move", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745974932646, "data": {"path": [12, 50], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745974934323, "data": {"nextPlayerId": "iVaqQQrlYNMTTfFSAAAB", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "iVaqQQrlYNMTTfFSAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745974949891, "data": {"path": [47, 3], "travelCards": ["T15"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "iVaqQQrlYNMTTfFSAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745974956949, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1745974967297, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "endTurn", "playerId": "iVaqQQrlYNMTTfFSAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745974967305, "data": {"nextPlayerId": "cueVKiFQWyFQ4gWrAAAD", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745975003297, "data": {"path": [50, 12, 11, 10], "travelCards": ["T23"], "extraHopCards": []}}, {"type": "trade", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745975007947, "data": {"cubesTraded": ["artha"], "cubeReceived": "gnana", "count": 1}}, {"type": "collectJourney", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745975011001, "data": {"journeyType": "inner", "journeyCardId": "JI6", "omRequirement": 1}}, {"type": "endTurn", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745975017596, "data": {"nextPlayerId": "iVaqQQrlYNMTTfFSAAAB", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "iVaqQQrlYNMTTfFSAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745975028389, "data": {"path": [3, 4, 6], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "iVaqQQrlYNMTTfFSAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745975061639, "data": {"journeyType": "outer", "journeyCardId": "JO3", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "iVaqQQrlYNMTTfFSAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745975061639, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 100, "finalRoundStarter": 1, "finalRoundEnd": 0}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1745975072969, "data": {"eventId": "global-event-42", "eventName": "<PERSON><PERSON><PERSON> in the Clouds", "eventEffect": "parikrama_in_clouds_reward"}}, {"type": "endTurn", "playerId": "iVaqQQrlYNMTTfFSAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745975072970, "data": {"nextPlayerId": "cueVKiFQWyFQ4gWrAAAD", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745975162333, "data": {"cardType": "travel", "pickedCards": [{"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745975165778, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745975171537, "data": {"path": [10, 11, 62, 64, 15, 5, 52], "travelCards": ["T10", "T21"], "extraHopCards": []}}, {"type": "parikrama_in_clouds_reward", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745975176567, "data": {"previousInnerScore": 61, "newInnerScore": 66, "airportsVisited": [62, 64], "bonus": 5}}, {"type": "endTurn", "playerId": "cueVKiFQWyFQ4gWrAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1745975176567, "data": {"nextPlayerId": "iVaqQQrlYNMTTfFSAAAB", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 1, "timestamp": 1745975176568, "data": {"winner": {"id": "iVaqQQrlYNMTTfFSAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 59, "innerScore": 41, "totalScore": 100, "omTotal": 3, "winByOm": false, "winByScore": true}, "winCondition": "SCORE_THRESHOLD", "totalRounds": 11, "players": [{"id": "cueVKiFQWyFQ4gWrAAAD", "name": "<PERSON><PERSON><PERSON>", "outerScore": 32, "innerScore": 66, "totalScore": 98, "omTotal": 4}, {"id": "iVaqQQrlYNMTTfFSAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 59, "innerScore": 41, "totalScore": 100, "omTotal": 3}]}}], "roundSummaries": [{"roundNumber": 1, "timestamp": 1745972901222, "players": [{"id": "UVPgs866jDjeIibGAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 58, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 7, "inner": 0, "total": 7}, "collectedJourneys": 0, "gained": null}, {"id": "cvSAcr8QuG3GuJkRAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 59, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 7, "inner": 0, "total": 7}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T17", "value": 2, "vehicle": "bus"}], "journeyOuter": [{"id": "JO11", "locationId": 25, "requiredCubes": {}, "points": 20}, {"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-8", "name": "Triathlon", "effect": "triathlon_bonus"}}, {"roundNumber": 2, "timestamp": 1745973151466, "players": [{"id": "ErZrWueiDz-hSr3KAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 36, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 12, "inner": 0, "total": 12}, "collectedJourneys": 0, "gained": null}, {"id": "wGlhOOkox1JkCzIzAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 25, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 0, "total": 32}, "collectedJourneys": 1, "gained": null}], "faceUpCards": {"travel": [{"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T19", "value": 2, "vehicle": "rickshaw"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-33", "name": "<PERSON><PERSON> Caravan<PERSON>", "effect": "rajput_caravans_reward"}}, {"roundNumber": 3, "timestamp": 1745973253900, "players": [{"id": "ErZrWueiDz-hSr3KAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 55, "hand": {"total": 1, "travel": 1}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 12, "inner": 7, "total": 19}, "collectedJourneys": 0, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "wGlhOOkox1JkCzIzAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 51, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 7, "total": 39}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T19", "value": 2, "vehicle": "rickshaw"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}, {"id": "T20", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO24", "locationId": 45, "requiredCubes": {}, "points": 20}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-3", "name": "<PERSON><PERSON>", "effect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"roundNumber": 4, "timestamp": 1745973376147, "players": [{"id": "ErZrWueiDz-hSr3KAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 45, "hand": {"total": 0, "travel": 0}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 7, "total": 39}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": 1}}, {"id": "wGlhOOkox1JkCzIzAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 21, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 7, "total": 39}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T19", "value": 2, "vehicle": "rickshaw"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}, {"id": "T20", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-32", "name": "Eco Trail", "effect": "eco_trail_reward"}}, {"roundNumber": 5, "timestamp": 1745973727713, "players": [{"id": "ErZrWueiDz-hSr3KAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 44, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 7, "total": 39}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "wGlhOOkox1JkCzIzAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 9, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 7, "total": 39}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T12", "value": 3, "vehicle": "truck"}, {"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T16", "value": 1, "vehicle": "cycle"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI4", "locationId": 7, "requiredCubes": {}, "points": 27}]}, "globalEvent": {"id": "global-event-40", "name": "Spirit of Seva", "effect": "spirit_of_seva"}}, {"roundNumber": 6, "timestamp": 1745973901926, "players": [{"id": "ErZrWueiDz-hSr3KAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 31, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 7, "total": 39}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "wGlhOOkox1JkCzIzAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 7, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 34, "total": 66}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T11", "value": 3, "vehicle": "train"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-38", "name": "Excess Baggage", "effect": "excess_baggage"}}, {"roundNumber": 7, "timestamp": 1745974032318, "players": [{"id": "ErZrWueiDz-hSr3KAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 31, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 7, "total": 39}, "collectedJourneys": 1, "gained": null}, {"id": "wGlhOOkox1JkCzIzAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 7, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 34, "total": 66}, "collectedJourneys": 2, "gained": null}], "faceUpCards": {"travel": [{"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T18", "value": 2, "vehicle": "car"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI11", "locationId": 20, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-7", "name": "Election Campaigns", "effect": "double_trade_no_travel"}}, {"roundNumber": 8, "timestamp": 1745974337387, "players": [{"id": "lYbLZ233TEemdPm0AAAD", "name": "<PERSON><PERSON><PERSON>", "position": 20, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 37, "total": 69}, "collectedJourneys": 2, "gained": null}, {"id": "gIpchG2jYa6n5yzuAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 54, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 41, "total": 73}, "collectedJourneys": 2, "gained": null}], "faceUpCards": {"travel": [{"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T18", "value": 2, "vehicle": "car"}, {"id": "T23", "value": 3, "vehicle": "train"}, {"id": "T4", "value": 1, "vehicle": "cycle"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-27", "name": "Frozen North", "effect": "frozen_north"}}, {"roundNumber": 9, "timestamp": 1745974568125, "players": [{"id": "lYbLZ233TEemdPm0AAAD", "name": "<PERSON><PERSON><PERSON>", "position": 12, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 37, "total": 69}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "gIpchG2jYa6n5yzuAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 47, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 32, "inner": 41, "total": 73}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T23", "value": 3, "vehicle": "train"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T10", "value": 3, "vehicle": "boat"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-28", "name": "Solar South", "effect": "solar_south"}}, {"roundNumber": 10, "timestamp": 1745974967296, "players": [{"id": "cueVKiFQWyFQ4gWrAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 50, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 37, "total": 69}, "collectedJourneys": 2, "gained": null}, {"id": "iVaqQQrlYNMTTfFSAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 3, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 1}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 32, "inner": 41, "total": 73}, "collectedJourneys": 2, "gained": null}], "faceUpCards": {"travel": [{"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T21", "value": 3, "vehicle": "helicopter"}, {"id": "T3", "value": 1, "vehicle": "trek"}], "journeyOuter": [{"id": "JO3", "locationId": 6, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-9", "name": "Riots", "effect": "riots_discard"}}, {"roundNumber": 11, "timestamp": 1745975072968, "players": [{"id": "cueVKiFQWyFQ4gWrAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 10, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 1, "inner": 2}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 32, "inner": 61, "total": 93}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "iVaqQQrlYNMTTfFSAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 6, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 2, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 59, "inner": 41, "total": 100}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T21", "value": 3, "vehicle": "helicopter"}, {"id": "T3", "value": 1, "vehicle": "trek"}], "journeyOuter": [{"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO7", "locationId": 13, "requiredCubes": {}, "points": 20}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}, {"id": "JO4", "locationId": 9, "requiredCubes": {}, "points": 20}], "journeyInner": [{"id": "JI16", "locationId": 32, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}, {"id": "JI9", "locationId": 16, "requiredCubes": {}, "points": 30}, {"id": "JI12", "locationId": 21, "requiredCubes": {}, "points": 20}]}, "globalEvent": {"id": "global-event-6", "name": "Turbulent Skies", "effect": "no_airport_travel"}}], "characterDeck": [{"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}], "energyCubePile": {"artha": 4, "karma": 5, "gnana": 6, "bhakti": 7}, "currentGlobalEvent": {"id": "global-event-42", "name": "<PERSON><PERSON><PERSON> in the Clouds", "text": "<PERSON><PERSON> 5 inner points if you visit > 1 Airports this round", "effect": "parikrama_in_clouds_reward"}, "globalEventDeck": [{"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-35", "name": "Road Warriors", "text": "Car or Bus: gain +5 Outer points", "effect": "road_warriors_reward"}, {"id": "global-event-34", "name": "Urban Ride", "text": "Motorbike or Rickshaw: gain +5 Outer points", "effect": "urban_ride_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-36", "name": "Rails and Sails", "text": "Train or Boat: gain +5 Outer points", "effect": "rails_and_sails_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-41", "name": "<PERSON><PERSON><PERSON> Dip", "text": "<PERSON><PERSON> 5 inner points if you are in West at the end of turn", "effect": "pushkar_holy_dip_end_turn_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-43", "name": "Cultural Exchange", "text": "At the start of turn, you may swap locations with another player who agrees. Both gain 5 Inner points.", "effect": "cultural_exchange"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-37", "name": "Central Heart", "text": "<PERSON>ain 5 inner points if you are in Central at the end of turn", "effect": "central_heart_end_turn_reward"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-30", "name": "Himalayan NE", "text": "Gain 5 inner points if you are in North East at the end of turn", "effect": "himalayan_ne_end_turn_reward"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-39", "name": "Heritage Site Renovations", "text": "No outer journey cards can be collected this round", "effect": "no_outer_journey_cards"}], "globalEventDiscard": [{"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-33", "name": "<PERSON><PERSON> Caravan<PERSON>", "text": "Horse or Camel: gain +5 Outer points", "effect": "rajput_caravans_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-32", "name": "Eco Trail", "text": "Cycle or Trek: gain +5 Inner points", "effect": "eco_trail_reward"}, {"id": "global-event-40", "name": "Spirit of Seva", "text": "Leader on each track donates 3 points to player with lowest score on that track", "effect": "spirit_of_seva"}, {"id": "global-event-38", "name": "Excess Baggage", "text": "Hand limit 2 this round, discard down immediately", "effect": "excess_baggage"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-28", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}], "nameMode": false}