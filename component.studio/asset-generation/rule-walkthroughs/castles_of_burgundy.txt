0:00
[Music]
0:07
hi
0:07
and welcome to watch it played my name
0:09
is rodney smith and in this video we're
0:11
going to learn the one to four player
0:13
game the castles of burgundy designed by
0:15
stephen feld
0:16
and published by a<PERSON><PERSON> and
0:17
raven<PERSON><PERSON> who helped sponsor this
0:19
video
0:20
as an influential 15th century duke or
0:23
duchess you'll be leading your estate to
0:25
prosperity in this classic title
0:27
where you'll be earning points at every
0:29
turn so many points
0:31
but can you earn the most the castles of
0:33
burgundy is a beloved game from 2011
0:36
and this is a newer printing that
0:37
contains several expansions and optional
0:39
materials including rules for team and
0:41
solo play
0:42
but here we'll be focusing on the core
0:44
game using the basic components for two
0:46
to four players
0:47
so join me at the table and let's learn
0:50
how to play
0:51
first set the double-sided game board in
0:53
the center of the play
0:54
area and the side with a triangle shape
0:56
in the middle is for two to three
0:58
players but in this video
1:00
we'll be setting up a four player game
1:02
which uses this side
1:03
nearby create a pool of these silver
1:05
coins and worker chips
1:08
now organize the other hex tiles by
1:10
color
1:11
some of these are double-sided showing
1:13
the same image on both sides and
1:15
others have a blank side the ones with a
1:17
blank side should be
1:18
mixed up and kept face down since i'm
1:21
going to have to move these tokens
1:22
around quite a bit in this video i'm
1:24
putting them into some trays that i have
1:26
and if you'd like some of your own
1:27
you'll find links in the description
1:29
below the tiles with this back
1:31
are the goods tiles which you'll mix
1:33
face down and then you set
1:35
five of them onto each of these spaces
1:38
it'll look like this when you're done
1:40
any leftovers you set aside and we'll
1:43
use them later these uniquely shaped
1:45
pieces are the bonus tiles
1:47
and they come in two sizes small and big
1:50
and you'll set each one on the matching
1:52
colored spaces for them here putting the
1:54
small
1:54
tile on the bottom and the larger one on
1:57
top
1:57
so again when you're done it will look
1:59
like this now look for the player boards
2:02
these will have numbers in their bottom
2:04
corners so look for the ones labeled as
2:06
either one or two
2:08
and give each player one that shares the
2:10
same number
2:11
in this case i'll give all the players a
2:14
number one board but in other games you
2:16
can use the number two boards for
2:17
additional variety
2:18
or as it suggests here in later games
2:21
each player can start with a different
2:22
board
2:23
but either way everyone now also
2:25
collects one of these overview boards
2:28
each player now takes one of these dark
2:30
green hexes from the piles that we
2:32
created
2:32
earlier this is a castle and you'll set
2:35
it into the matching colored space found
2:37
in the center of your player board
2:39
then from the 17 goods tiles that we set
2:41
aside earlier
2:42
every player takes three of these
2:44
randomly and puts them face up
2:46
into these storage spaces here now if
2:49
you have
2:50
any that share the same color then stack
2:52
them together
2:53
into the same space once everyone has
2:56
their goods tiles
2:57
any leftover ones are returned to the
2:59
box
3:00
now have each player collect a silver
3:02
coin from the supply and set
3:04
it into this area of their player board
3:06
and also have each player take
3:08
two dice two of these playing pieces and
3:11
one victory point token
3:12
in their chosen color everyone now sets
3:15
one of their colored pieces
3:17
into this starting space of the board
3:19
here
3:20
then each player rolls a die and the
3:22
player with the highest result
3:24
after re-rolling any ties wins and will
3:26
be the starting player for the game
3:28
so in this case blue they receive one
3:32
worker token from the supply
3:34
and set it into this area of their
3:36
player board
3:37
the next player seated in clockwise
3:39
order now gains
3:40
two workers and continuing around the
3:42
table in this way
3:44
the next player gains three workers and
3:46
our fourth player here
3:48
gains four workers the starting player
3:51
now puts their
3:52
other player piece on this first space
3:55
of the bridge here on the board and the
3:57
other players
3:58
put theirs underneath in player order so
4:01
that the
4:02
last player is on the bottom the first
4:04
player also collects this
4:06
white die and that's the setup
4:09
in the castles of burgundy players will
4:11
acquire a variety of structures
4:13
farmlands and ships to build into their
4:15
duchies and by cleverly placing them
4:17
you'll gain new abilities opportunities
4:20
and best of all
4:21
points at the end of the game the player
4:23
with the most points will have earned
4:24
the most prestige
4:25
and wins the game is played over five
4:28
phases lettered from a
4:30
to e as you can see here in the top left
4:32
hand corner of the board
4:34
at the start of every phase you first
4:36
remove
4:37
any hex tiles that might still be on the
4:39
board now at the beginning of the game
4:41
there won't be any to remove yet
4:43
so we can just move on next take the
4:45
goods tiles from the stack
4:47
that matches your current phase a in
4:50
this case
4:51
and one by one you'll place them face up
4:54
into the spaces for them here in this
4:57
column
4:58
filling each one of them in now let's
5:00
take a look at the board
5:02
in the center you'll find a group of
5:03
black colored hexes
5:05
and around it are six large squares
5:07
known as depots and each of these
5:10
are numbered with a die and beside each
5:12
depot
5:13
you'll find four colored hexes from the
5:16
hex supplies that we created earlier
5:18
you'll fill in
5:19
every depot hex space with the matching
5:21
colored hex
5:23
some of these will have a blank side
5:25
which should always be drawn randomly
5:27
and then flipped over as they're set
5:29
into their matching colored spaces
5:31
but it doesn't matter which hexes you
5:33
take when you're adding the double-sided
5:35
ones
5:35
as they're all the same it will look
5:38
something like this when you're all done
5:40
filling in all of the outer depot spaces
5:43
in the center add eight random black
5:46
backed hexes again
5:47
only revealing them as they're being
5:49
placed so that it looks
5:51
something like this when you're finished
5:52
filling in all those spaces
5:54
if you have two or three players
5:57
remember you'll use the other side of
5:59
the board which has fewer hexes
6:01
and the rules for filling them in are
6:02
just slightly different but they're
6:04
outlined here in the rule book and
6:06
on screen if you want to pause and take
6:08
a read but other than that change
6:10
the two three and four player games are
6:13
played the exact same way
6:15
and that's how you set up a brand new
6:17
phase now each phase
6:19
is broken into five rounds and a round
6:21
starts with all the players rolling
6:23
their two dice and the first player also
6:26
rolls
6:27
the white die everyone is allowed to see
6:29
everyone else's
6:30
results and once all the dice have been
6:33
rolled the starting player
6:35
now resolves the white die to do this
6:38
they take the topmost goods tile from
6:40
this column
6:41
and move it to the depot space that
6:43
matches the value of the white die
6:45
so in this case it would go here we're
6:48
now done with the white die for this
6:49
round and can be returned to the first
6:51
player
6:52
then the first player takes their turn
6:55
on a turn you'll perform an action
6:56
with each of your dice so most of the
6:59
time you'll be performing
7:00
two actions on your turn as you perform
7:02
an action with a die
7:04
you move it to your portrait here to
7:06
remind you that it's been used
7:07
however if you have any of these worker
7:10
chips you can use any number of them at
7:12
any point during your turn
7:13
to change the values of your dice and
7:16
for each worker you return back to the
7:18
supply
7:18
you can increase or decrease a die value
7:21
by
7:22
one turning it to the new side so i
7:24
could spend this worker
7:25
to say turn this three into a four
7:29
also a worker can change a one into a
7:32
six
7:33
or a six into a one kind of loops around
7:36
in that way
7:37
however a worker can never be used to
7:40
change the values
7:41
on the white die now as i mentioned you
7:43
can take
7:44
one action per die and you can use them
7:47
in any order
7:48
and even use them both on the same type
7:50
of action and there are a variety of
7:52
different action types so let's go
7:53
through them together
7:54
right now one way to use a die is to
7:57
take a hex tile from the game board and
8:00
the value of the die
8:01
tells you which depot area to look at
8:04
and then you can collect any one hex of
8:06
your choice
8:07
from that area now just keep in mind
8:09
this central area
8:10
is separate so you can't take a hex tile
8:13
from here
8:14
using a die but as an example if we use
8:17
this three
8:18
we could then take any one of these
8:20
hexes and maybe
8:21
i'll collect this one after taking a hex
8:24
you must place it into any
8:26
empty storage space of your player board
8:28
here and these are labeled with a key
8:30
and can also be referred to
8:31
as your key spaces now if you didn't
8:34
have an
8:34
empty space here then you'd have to
8:37
first discard a hex from this area
8:39
back to the box to make room for the new
8:41
one you're adding
8:42
so that's one of the options taking a
8:44
hex tile and adding it to your storage
8:46
area
8:47
and this side of your overview board
8:49
shows a reminder of that action here
8:51
using these symbols
8:53
but now let's take a look at another
8:54
type of action also shown here
8:56
known as placing a hex tile into your
8:59
duchy
9:00
to do this take any one hex tile from
9:02
the storage area of your player board
9:05
and set it into an empty hex space of
9:07
your duchy
9:08
that matches both the value of the die
9:11
you're using
9:12
and the tile color of the one you're
9:14
placing
9:16
also the tile you place must share at
9:18
least
9:19
one full side with another tile already
9:22
in your duchy so
9:23
i could put my ship here every hex tile
9:26
has
9:26
some kind of effect and after it's
9:28
placed into your duchy
9:30
you resolve that effect right away so
9:32
let's go through each of those effects
9:34
right now after placing a ship in your
9:36
duchy you then pick
9:37
any one depot on the game board and
9:40
collect all of the goods tiles from it
9:42
in this case there's only one depot that
9:45
has goods so we'll pick this one but you
9:46
really can choose
9:48
any depot at all it doesn't have to
9:50
match the value
9:51
of the die that you used with that
9:53
action so
9:54
we'll take this good then add those
9:56
collected goods
9:57
to these three storage spaces on your
9:59
board stacking any colors that match
10:02
in this way you can have at most three
10:04
different colors of goods
10:05
but any number of them now if all your
10:08
storage spaces are full
10:09
and you gain more goods than you have
10:11
room for you cannot collect those extra
10:14
tiles and you would leave them in the
10:15
depot
10:16
only taking the ones you can place
10:18
you're not allowed to dump goods you
10:20
already have
10:21
to make room so if i just gained these
10:23
three tiles
10:24
i would stack this one and then i would
10:26
pick either one of these to keep putting
10:28
it in my available space
10:30
and the remaining one would have to be
10:31
returned to the depot it was taken from
10:34
after collecting goods from placing a
10:36
ship in your dutch you then
10:37
also move your playing piece on the
10:39
bridge forward
10:40
by one space the player who is furthest
10:43
forward on the bridge
10:44
immediately becomes the new first player
10:46
and collects the white die
10:48
as a reminder of this when a piece moves
10:50
forward
10:51
if the space you enter is already
10:53
occupied let's say for example
10:54
green was moving forward now you stack
10:57
your piece
10:58
on top of any other pieces there the
11:00
player on top is always considered
11:02
further ahead than any pieces underneath
11:05
so in this case green would be the new
11:07
first player
11:08
so that's what happens when you place a
11:10
ship into your duchy and you'll find
11:11
reminders
11:12
of the effects of the various hexes here
11:14
on your overview board
11:16
but now let's look at what happens if
11:18
you place one of these light green
11:20
livestock hexes first of all for the
11:22
rest of these examples please assume
11:24
that i'm using the correct die
11:26
value in order to place the tiles that i
11:28
want to show you into my duchy
11:30
with these livestock hexes they'll show
11:32
a certain type
11:33
and number of livestock and after
11:35
placing the tile you immediately gain a
11:37
number of points
11:39
equal to the amount of livestock shown
11:41
on it so here we have three cows and
11:42
that means we'd gain
11:43
three points when you gain points in the
11:46
game advance your marker on the track
11:48
here
11:49
and if you ever loop all the way around
11:51
and pass this code of arms space
11:53
place your victory point chip there with
11:56
this 100 point side face up to show
11:58
you've banked 100 points so far
12:00
and then you keep going and if you cross
12:02
here again
12:03
flip it over to its 200 point side when
12:06
adding livestock to your board
12:08
if you already have livestock hexes with
12:11
that same type of animal
12:13
in that pasture you also store each of
12:16
those tiles again
12:17
right away as well now a pasture is made
12:20
up of
12:21
all of the light green hexes that are
12:23
adjacent to each other
12:24
so this is a pasture of five spaces and
12:27
this is a separate pasture of just one
12:29
space
12:30
so let's say it was later in the game
12:31
and we had some hexes on the board
12:33
that had been placed like this if i then
12:36
added a hex with cows
12:37
into this pasture i'd get two points for
12:40
it
12:40
and three points for this cow tile as
12:43
well
12:44
i don't get anything for the cows over
12:45
here because they're in a different
12:47
pasture
12:48
i also don't get anything for this tile
12:50
because these are pigs
12:51
but now let's move on to yellow hexes
12:54
known as
12:54
monasteries each is unique and many will
12:57
provide you with an ongoing ability for
12:59
the rest of the game
13:00
once you've added them to your duchy and
13:03
others will resolve their effects only
13:05
during the final scoring
13:06
as an example once you've added this
13:09
monastery
13:10
from now on anytime you add a ship tile
13:13
to your estate
13:14
in addition to collecting all the tiles
13:16
from a depot of your choice
13:17
you also gain all the goods from any one
13:20
adjacent depot
13:22
as well we're not going to go through
13:24
all the effects of each individual
13:26
different type of monastery but you'll
13:28
find them all explained clearly in the
13:30
rule book
13:30
so just look them up as you play now
13:33
let's move on to these gray hexes
13:35
known as mines placing one into your
13:37
duchy provides no immediate benefit
13:39
they're instead resolved at the end of
13:41
each phase which we'll see later
13:43
a dark green hex is a castle after
13:46
adding one to your duchy you immediately
13:48
take another action of any type for free
13:51
you can think of the castle as providing
13:53
you with a temporary imaginary die
13:55
that you now get to use and you can set
13:58
that die
13:59
to any side next let's discuss these
14:02
beige hexes known as the buildings and
14:04
there are eight different types
14:06
each with their own effect that resolves
14:08
once as soon as you add it to your duchy
14:10
and you'll find reminders of all these
14:12
effects here on your overview board
14:14
within a town you can never have more
14:16
than one of each type of building and a
14:18
town
14:19
is a group of beige spaces connected to
14:21
each other so
14:22
in this duchy we have a town here here
14:26
here and here once i add this type of
14:29
building known as a market
14:30
into this town it means i can't add
14:32
another market here
14:34
but i could add one to this separate
14:37
town
14:37
with that understood now let's go
14:39
through each of the different types of
14:41
buildings
14:42
when you add one of these markets to
14:43
your duchy you may immediately take
14:46
one ship or one livestock from any of
14:49
the six depots on the main board
14:51
and add it to an empty key space in your
14:53
player board
14:54
this carpenter's workshop instead lets
14:57
you collect and add
14:58
a beige building from any of the six
15:01
depots to a key space
15:02
while this church lets you take any one
15:05
mine
15:06
monastery or castle tile from any of the
15:10
six depots
15:11
and add it to your key storage if you
15:13
add this bank building to your duchy
15:16
then you immediately gain two silver
15:18
coins from the supply which you store
15:20
here while placing this boarding house
15:23
building
15:24
causes you to immediately collect four
15:26
worker chips
15:27
from the supply which you keep in this
15:30
area
15:30
by the way silver coins and worker chips
15:33
are meant to be unlimited
15:34
so if you ever run out just use a
15:37
suitable replacement
15:38
this is the town hall and after placing
15:40
it in your duchy you may immediately
15:42
place any one
15:43
other hex tile from your key spaces into
15:45
your duchy as well
15:46
in other words you don't need to use a
15:48
die and it can go onto any valid space
15:50
no matter the value
15:52
you then gain whatever effect it would
15:54
normally provide when placed
15:56
after adding one of these watch towers
15:58
to your duchy you gain
16:00
four victory points finally we have the
16:02
warehouse building
16:04
after adding it to your dutch you may
16:05
immediately sell all of
16:07
one type of good you have without using
16:09
a die
16:10
but we'll discuss how selling goods
16:12
works a little later
16:14
and those are all the buildings now i
16:16
should mention that if you place a
16:17
building
16:18
but can't perform the effect it provides
16:21
you can still add it to your dutchie you
16:22
just don't gain that additional benefit
16:24
from it
16:25
also as a general rule for all tiles
16:28
once they're placed
16:29
you must leave them where they are you
16:30
can't move them around later
16:33
and it's also worth mentioning getting a
16:35
hex into your duchy
16:37
is usually a two-step process and you've
16:39
probably noticed that
16:40
see when you first collect a hex from
16:42
the main board
16:43
it goes into one of your key storage
16:44
spaces and then with a separate action
16:47
you can move it from your storage
16:49
to your duchy and that covers all the
16:52
hexes that you can add to your duchy but
16:54
there are a few other benefits to adding
16:56
them that we should go over
16:58
as we've seen your duchy is divided up
17:00
by hexes and as we also discussed
17:02
hexes that are grouped together sharing
17:04
the same color are each their own
17:06
separate areas
17:07
so this is an area or pasture of light
17:10
green hexes
17:11
this is an area or river of blue hexes
17:13
and here's another separate river
17:15
and so on as soon as you cover the final
17:18
space
17:18
within an area it's considered complete
17:21
and you score some points
17:22
first count how many spaces made up that
17:25
area
17:26
and then check this chart on the game
17:28
board and you'll also find this
17:30
on the back of your overview board based
17:32
on the size of the area you then gain
17:34
the related number of points
17:36
so our area of three hexes means that we
17:38
score
17:39
six points you then gain additional
17:41
points based on what phase of the game
17:43
you're in which you're reminded of
17:45
here the rightmost empty space is your
17:47
current phase
17:48
so phase a right now and this means we
17:50
gain an additional
17:52
10 points and as you can see the earlier
17:54
you complete an area
17:56
the more points you'll gain you also
17:58
score points when placing a hex if
18:00
you've covered
18:01
every hex of a color within your duchy
18:04
for example by placing this blue ship
18:06
all the blue spaces are covered now i
18:09
would first
18:09
gain the points for finishing this
18:11
particular blue
18:13
area but then after i come to this area
18:15
of the board
18:16
and collect the token on top that
18:19
matches
18:19
the color of the area i just completed
18:22
then i immediately score the points it
18:24
shows based on the number of players in
18:26
the game which is ordered from left to
18:28
right in other words in a two player
18:29
game i'd score five points
18:32
six in a three player game and seven
18:34
with four players
18:35
now this means that the second player to
18:37
fill in all of the blue spaces on their
18:39
board
18:40
would then take this token scoring its
18:43
related points
18:44
and anyone completing all the blue
18:46
spaces after that point
18:48
will gain no bonus points but still get
18:50
the regular points for completing a
18:52
colored area
18:53
so with that we now know everything that
18:54
can happen when you use a die action to
18:56
add a hex to your duchy
18:58
but there are two other actions you can
19:00
use a die for instead
19:01
and let's go over those starting with
19:04
selling goods
19:05
to do this pick a stack of goods you
19:07
have in your storage
19:08
that shares the same die value as the
19:11
one you're using
19:12
and then sell all of them by placing
19:14
them face down into this area here
19:17
after a sale you gain exactly one silver
19:20
coin
19:21
no matter how many tiles you sold and
19:23
you then get a number of victory points
19:25
equal to the number of players in your
19:27
game for each tile you just sold so here
19:30
i sold one
19:31
two three tiles and that means in this
19:34
four player game
19:35
i'd get four points for each of them for
19:37
a total of 12 points
19:40
the final type of action you can take
19:41
with a die is to gain workers
19:43
by using a die of any value and then
19:46
collecting
19:46
two workers from the supply that covers
19:49
everything on the board
19:51
except for one area all of the hexes in
19:54
the center
19:55
these you might remember are the blacked
19:57
back ones
19:58
but on their faces they're made up of
19:59
hexes from all the various colored types
20:01
we've seen already
20:02
and during your turn in addition to your
20:04
two dice actions
20:06
you may buy a single tile of your choice
20:09
from this black depot area
20:10
by spending two silver coins returning
20:13
them to the supply
20:14
and then placing the collected hex into
20:16
one of your key storage spaces
20:19
again you can only buy a single black
20:21
depot tile per turn but
20:23
you can do this at any point before
20:26
between
20:27
or after your two dice actions you can
20:29
even do it during a die
20:31
action so that's how you conduct a turn
20:34
you use your
20:35
two dice to take actions and then the
20:37
next player takes their turn
20:39
the next player is whoever hasn't taken
20:41
a turn yet this round
20:43
but is next closest to the center of the
20:45
board on the bridge
20:47
and again if you have a stack it's the
20:48
person closest to the top so in this
20:50
example
20:51
blue is closest to the middle of the
20:53
board but they already took a turn
20:55
so we'd go to green next when all of the
20:58
players have finished taking their turns
21:00
the next round begins and remember at
21:02
the start of a new round
21:03
everyone collects and rolls their own
21:06
dice
21:06
then the current first player also rolls
21:08
the white die and moves the next
21:10
topmost good here into the matching
21:13
depot
21:14
in this way you can always see what
21:16
round you are currently on
21:17
and at the end of the fifth round all of
21:19
these tokens will be gone
21:20
and the current phase will be over when
21:23
a phase ends
21:24
each player now collects one silver coin
21:27
for every mine they currently have in
21:29
their duchy and also check for any
21:31
yellow tiles with effects that resolve
21:33
at the end of a phase
21:35
you then start a new phase and remember
21:37
at the start of a phase
21:39
you first remove all the remaining hex
21:41
tiles on the board
21:43
including from the center and all these
21:44
you can return to the box they won't be
21:46
used in this game again
21:48
then you refill all the empty hex spaces
21:50
with new ones
21:52
then you'll refill the spaces here with
21:54
goods from the new phases stack and then
21:56
you can start the first round
21:58
of the new phase the game ends after the
22:01
fifth phase is over which you'll be able
22:04
to identify
22:04
because all of the goods tiles will be
22:06
gone from this column
22:08
and this row then it's time for final
22:10
scoring
22:11
first you gain one victory point for
22:13
every goods tile you have but didn't
22:15
manage to sell
22:16
you also get one victory point for every
22:18
silver coin you have
22:20
and one point for every two worker chips
22:23
so here i have three and that means i'd
22:24
gain
22:25
one extra point also check all of your
22:27
yellow monastery tiles located in your
22:29
duchy
22:30
for any points they may provide at the
22:32
end of the game
22:33
the player with the most points wins and
22:36
if there's a tie the tied player with
22:38
the fewest empty hexes in their duchy
22:40
wins
22:40
and if there's still a tie the tied
22:42
player furthest behind
22:44
on the bridge wins and don't forget if
22:46
you have this edition of the game you'll
22:48
find additional expansions included that
22:50
add new hex tiles to the game
22:52
as well as rules for team and solo games
22:55
and a variant that includes the use of
22:57
these shield tokens
22:59
but those rules i'll leave for you to
23:00
discover on your own
23:02
otherwise that's everything you need to
23:04
know to play the castles of burgundy
23:06
if you have any questions at all about
23:08
anything you saw here
23:09
feel free to put them in the comments
23:11
below and i'll gladly answer them
23:13
as soon as i get a chance you'll also
23:15
find fours for discussion pictures
23:17
other videos and lots more over on the
23:19
games page at board game geek and i'll
23:21
put a link to that
23:22
in the description below and if you
23:24
found this video helpful please consider
23:25
giving it a like subscribing
23:27
and clicking that little bell icon so
23:29
you get notification anytime we post a
23:30
new video
23:31
and if you'd like to support the channel
23:33
directly you'll find our patreon which
23:35
i'll also put
23:36
link below but until next time
23:39
thanks for watching
23:49
[Applause]
23:53
[Music]
24:00
you