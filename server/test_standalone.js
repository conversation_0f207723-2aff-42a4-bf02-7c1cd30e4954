/**
 * test_standalone.js
 * Run a simulation test against a real server
 */
const SimulationClientRunner = require('./simulation/SimulationClientRunner');

// Configuration for the simulation
const options = {
  serverUrl: 'http://localhost:4000', // Connect to a real server
  numGames: 1,
  speed: 0.5,
  bots: [
    { name: 'Bot-A', strategy: 'defensive' },
    { name: '<PERSON><PERSON>-<PERSON>', strategy: 'defensive' }
  ],
  logging: {
    logLevel: 'info',
    logToConsole: true,
    logToFile: true
  }
};

console.log(`Starting simulation against server at ${options.serverUrl}...`);
console.log('Using DefensiveStrategy for all bots');

// Create and run the simulation client
const simulationRunner = new SimulationClientRunner(options);

simulationRunner.run()
  .then(results => {
    console.log('Simulation completed successfully');
    console.log(`Games played: ${results.gamesPlayed}`);
    console.log(`Games completed: ${results.gamesCompleted}`);
    
    if (Object.keys(results.botStats).length > 0) {
      console.log('\nBot statistics:');
      Object.values(results.botStats).forEach(botStat => {
        console.log(`- ${botStat.name}: Wins: ${botStat.wins}, Points: ${botStat.journeyPoints}`);
      });
    }
    
    process.exit(0);
  })
  .catch(error => {
    console.error('Simulation failed:', error.message);
    process.exit(1);
  }); 