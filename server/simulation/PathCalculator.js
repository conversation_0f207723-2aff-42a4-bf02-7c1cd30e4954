/**
 * PathCalculator.js
 * Utility for calculating valid paths on the game board.
 */

const { edges, locations } = require('../boardData');

// Maximum number of paths to cache
const MAX_CACHE_SIZE = 50;
// Maximum number of paths to calculate and return
const MAX_PATHS_TO_RETURN = 100;

class PathCalculator {
  /**
   * Create a new path calculator
   */
  constructor() {
    // Build an adjacency map for quicker lookups
    this.adjacencyMap = this._buildAdjacencyMap();
    
    // Cache for calculated paths to improve performance in repeated calculations
    this.pathCache = new Map();
    // Keep track of cache usage to implement LRU eviction
    this.cacheAccessOrder = [];
  }
  
  /**
   * Build an adjacency map from the game's edge data
   * @returns {Map<number, Set<number>>} Map of location IDs to their adjacent locations
   * @private
   */
  _buildAdjacencyMap() {
    const adjMap = new Map();
    
    // Initialize sets for each location
    for (const location of locations) {
      adjMap.set(location.id, new Set());
    }
    
    // Add edges to the adjacency map
    for (const edge of edges) {
      const fromSet = adjMap.get(edge.from);
      const toSet = adjMap.get(edge.to);
      
      if (fromSet) fromSet.add(edge.to);
      if (toSet) toSet.add(edge.from);
    }
    
    // Add airport connections (airports can travel directly to any other airport)
    const airports = locations
      .filter(loc => loc.id >= 61 && loc.id <= 65)
      .map(loc => loc.id);
    
    for (const airport1 of airports) {
      for (const airport2 of airports) {
        if (airport1 !== airport2) {
          const set = adjMap.get(airport1);
          if (set) set.add(airport2);
        }
      }
    }
    
    return adjMap;
  }
  
  /**
   * Check if two locations are adjacent (share an edge)
   * @param {number} locationId1 - First location ID
   * @param {number} locationId2 - Second location ID
   * @returns {boolean} Whether the locations are adjacent
   */
  areAdjacent(locationId1, locationId2) {
    const adjacentToLoc1 = this.adjacencyMap.get(locationId1);
    return adjacentToLoc1 ? adjacentToLoc1.has(locationId2) : false;
  }

  /**
   * Calculate the shortest number of hops between two locations using BFS
   * @param {number} start - Starting location ID
   * @param {number} end - Target location ID
   * @returns {number} Minimum hops required, or Infinity if unreachable
   */
  calculateHops(start, end) {
    if (start === end) return 0;
    const visited = new Set([start]);
    const queue = [{ loc: start, dist: 0 }];
    while (queue.length) {
      const { loc, dist } = queue.shift();
      const neighbors = this.adjacencyMap.get(loc) || new Set();
      for (const neigh of neighbors) {
        if (neigh === end) {
          return dist + 1;
        }
        if (!visited.has(neigh)) {
          visited.add(neigh);
          queue.push({ loc: neigh, dist: dist + 1 });
        }
      }
    }
    return Infinity;
  }
  
  /**
   * Find all possible paths from a starting location
   * @param {number} startLocationId - The starting location ID
   * @param {Array<object>} travelCards - Array of travel cards available
   * @param {number} extraHops - Number of extra hops available
   * @returns {Array<Array<number>>} Array of possible paths (each an array of location IDs)
   */
  findPossiblePaths(startLocationId, travelCards, extraHops = 0) {
    // Memory usage logging when called from turn 4
    const memUsage = process.memoryUsage();
    const shouldLogMemory = this._isRequestFromTurn4();
    
    if (shouldLogMemory) {
      console.log(`[MEMORY-DEBUG] PathCalculator.findPossiblePaths - Start - Heap: ${Math.round(memUsage.heapUsed/1024/1024)}MB, RSS: ${Math.round(memUsage.rss/1024/1024)}MB`);
      console.log(`[MEMORY-DEBUG] Finding paths from location ${startLocationId} with ${travelCards.length} travel cards and ${extraHops} extra hops`);
    }
    
    // Simplify travel cards to just their IDs and values for the cache key
    const simplifiedCards = travelCards.map(c => ({ id: c.id, value: c.value }));
    
    // Create cache key based on inputs
    const cacheKey = `${startLocationId}:${JSON.stringify(simplifiedCards)}:${extraHops}`;
    
    // Check if we have this calculation cached
    if (this.pathCache.has(cacheKey)) {
      // Update cache access order (move to end of array = most recently used)
      this._updateCacheOrder(cacheKey);
      if (shouldLogMemory) {
        console.log(`[MEMORY-DEBUG] PathCalculator - Using cached paths for ${cacheKey}`);
      }
      return this.pathCache.get(cacheKey);
    }
    
    // Calculate all possible travel distances using single or multiple travel cards
    const possibleDistances = this._calculatePossibleDistances(travelCards, extraHops);
    
    // If we have no movement options, just return the current location
    if (possibleDistances.length === 0) {
      const result = [[startLocationId]];
      this._cacheResult(cacheKey, result);
      return result;
    }
    
    // Find all possible paths for each distance
    const allPossiblePaths = [];
    
    // For each possible travel distance, find all valid paths
    for (const totalValue of possibleDistances) {
      const visited = new Set();
      const pathsForThisDistance = [];
      
      this._dfsPath(
        startLocationId, 
        [startLocationId], 
        totalValue, 
        visited, 
        pathsForThisDistance,
        0, // Current depth
        MAX_PATHS_TO_RETURN / possibleDistances.length // Limit paths per distance for performance
      );
      
      allPossiblePaths.push(...pathsForThisDistance);
      
      // If we've found too many paths already, stop searching
      if (allPossiblePaths.length >= MAX_PATHS_TO_RETURN) {
        break;
      }
    }
    
    // Cache the result for future use
    this._cacheResult(cacheKey, allPossiblePaths);
    
    return allPossiblePaths;
  }
  
  /**
   * Calculate all possible travel distances by combining travel cards
   * @param {Array<object>} travelCards - Available travel cards
   * @param {number} extraHops - Number of extra hops available
   * @returns {Array<number>} Array of possible travel distances
   * @private
   */
  _calculatePossibleDistances(travelCards, extraHops = 0) {
    const possibleDistances = new Set();
    
    // First, add individual card values
    for (const card of travelCards) {
      possibleDistances.add(card.value + extraHops);
    }
    
    // Then calculate combinations of up to 3 cards (for performance reasons)
    // This allows bot to consider combinations like using 1+2+3 to travel 6 hops
    if (travelCards.length > 1) {
      const usedCardIndices = new Set();
      
      const generateCombinations = (startIdx, sum, depth) => {
        if (depth > 0) {
          possibleDistances.add(sum + extraHops);
        }
        
        if (depth >= 3) return; // Limit combinations to 3 cards for performance
        
        for (let i = startIdx; i < travelCards.length; i++) {
          if (!usedCardIndices.has(i)) {
            usedCardIndices.add(i);
            generateCombinations(i + 1, sum + travelCards[i].value, depth + 1);
            usedCardIndices.delete(i);
          }
        }
      };
      
      generateCombinations(0, 0, 0);
    }
    
    // Remove 0 as it's not valid for movement
    possibleDistances.delete(0);
    possibleDistances.delete(extraHops); // Remove just the extra hops value without travel cards
    
    return Array.from(possibleDistances).sort((a, b) => a - b);
  }
  
  /**
   * Update the cache access order
   * @param {string} key - Cache key to update
   * @private
   */
  _updateCacheOrder(key) {
    // Remove the key from its current position
    const index = this.cacheAccessOrder.indexOf(key);
    if (index > -1) {
      this.cacheAccessOrder.splice(index, 1);
    }
    // Add to the end (most recently used)
    this.cacheAccessOrder.push(key);
  }
  
  /**
   * Cache a result and manage cache size
   * @param {string} key - Cache key
   * @param {Array} result - Result to cache
   * @private
   */
  _cacheResult(key, result) {
    // If cache is full, remove least recently used item
    if (this.pathCache.size >= MAX_CACHE_SIZE && this.cacheAccessOrder.length > 0) {
      const oldestKey = this.cacheAccessOrder.shift();
      this.pathCache.delete(oldestKey);
    }
    
    // Add the new result to the cache
    this.pathCache.set(key, result);
    this.cacheAccessOrder.push(key);
  }
  
  /**
   * Depth-first search to find all possible paths within a given travel value
   * @param {number} currentLocationId - Current location in the search
   * @param {Array<number>} currentPath - Current path being explored
   * @param {number} remainingValue - Remaining travel value
   * @param {Set<number>} visited - Set of already visited locations
   * @param {Array<Array<number>>} possiblePaths - Result collection for valid paths
   * @param {number} depth - Current search depth
   * @param {number} maxPaths - Maximum number of paths to collect
   * @private
   */
  _dfsPath(currentLocationId, currentPath, remainingValue, visited, possiblePaths, depth, maxPaths) {
    // Stop if we've found enough paths
    if (possiblePaths.length >= maxPaths) {
      return;
    }
    
    // Mark current location as visited
    visited.add(currentLocationId);
    
    // If we've used all our movement, this is a valid path
    if (remainingValue === 0) {
      possiblePaths.push([...currentPath]);
      visited.delete(currentLocationId);
      return;
    }
    
    // Get adjacent locations
    const adjacentLocations = this.adjacencyMap.get(currentLocationId) || new Set();
    
    // Try each adjacent location
    for (const nextLocationId of adjacentLocations) {
      if (!visited.has(nextLocationId)) {
        // Add location to current path
        currentPath.push(nextLocationId);
        
        // Continue search from this location
        this._dfsPath(
          nextLocationId,
          currentPath,
          remainingValue - 1,
          visited,
          possiblePaths,
          depth + 1,
          maxPaths
        );
        
        // Backtrack
        currentPath.pop();
        
        // Break if we've found enough paths
        if (possiblePaths.length >= maxPaths) {
          break;
        }
      }
    }
    
    // Unmark the current location as visited to explore other paths
    visited.delete(currentLocationId);
  }
  
  /**
   * Find the optimal paths to a specific destination
   * @param {number} startLocationId - Starting location ID
   * @param {number} targetLocationId - Target location ID
   * @param {Array<object>} travelCards - Available travel cards
   * @param {number} extraHops - Number of extra hops available
   * @returns {Array<Array<number>>} Array of possible paths to the destination
   */
  findPathsToDestination(startLocationId, targetLocationId, travelCards, extraHops = 0) {
    // If already at the destination, return single-element path
    if (startLocationId === targetLocationId) {
      return [[startLocationId]];
    }
    
    // Try to find the shortest path first
    const shortestPath = this._findShortestPath(startLocationId, targetLocationId);
    
    // If no path exists, return empty array
    if (!shortestPath) {
      console.log(`No path found from ${startLocationId} to ${targetLocationId}`);
      return [];
    }
    
    // Return array with the shortest path
    return [shortestPath];
  }
  
  /**
   * Get all possible combinations of travel cards
   * @param {Array<object>} cards - Available travel cards
   * @returns {Array<Array<object>>} All possible combinations of cards
   * @private
   */
  _getAllCardCombinations(cards) {
    const combinations = [];
    
    // Add individual cards
    for (let i = 0; i < cards.length; i++) {
      combinations.push([cards[i]]);
    }
    
    // Add pairs of cards
    if (cards.length >= 2) {
      for (let i = 0; i < cards.length - 1; i++) {
        for (let j = i + 1; j < cards.length; j++) {
          combinations.push([cards[i], cards[j]]);
        }
      }
    }
    
    // Add triplets of cards (limit to 3 cards for performance)
    if (cards.length >= 3) {
      for (let i = 0; i < cards.length - 2; i++) {
        for (let j = i + 1; j < cards.length - 1; j++) {
          for (let k = j + 1; k < cards.length; k++) {
            combinations.push([cards[i], cards[j], cards[k]]);
          }
        }
      }
    }
    
    // Sort combinations by total value (ascending)
    combinations.sort((a, b) => {
      const sumA = a.reduce((sum, card) => sum + card.value, 0);
      const sumB = b.reduce((sum, card) => sum + card.value, 0);
      return sumA - sumB;
    });
    
    return combinations;
  }
  
  /**
   * Find the shortest path between two locations using breadth-first search
   * @param {number} startLocationId - Starting location ID
   * @param {number} targetLocationId - Target location ID
   * @returns {Array<number>|null} Shortest path or null if no path exists
   * @private
   */
  _findShortestPath(startLocationId, targetLocationId) {
    // Special case for airport-to-airport travel
    const isStartAirport = startLocationId >= 61 && startLocationId <= 65;
    const isTargetAirport = targetLocationId >= 61 && targetLocationId <= 65;
    
    // If both are airports, return direct path (airports connect directly)
    if (isStartAirport && isTargetAirport) {
      return [startLocationId, targetLocationId];
    }
    
    // Use BFS to find shortest path
    const queue = [{ location: startLocationId, path: [startLocationId] }];
    const visited = new Set([startLocationId]);
    
    while (queue.length > 0) {
      const { location, path } = queue.shift();
      
      // If we've reached the target, return the path
      if (location === targetLocationId) {
        return path;
      }
      
      // Get adjacent locations
      const adjacentLocations = this.adjacencyMap.get(location) || new Set();
      
      // Process airport connections with higher priority (they count as 1 hop regardless of real distance)
      const isCurrentAirport = location >= 61 && location <= 65;
      const nextLocations = Array.from(adjacentLocations);
      
      // Sort next locations to prioritize airports if current location is an airport
      if (isCurrentAirport) {
        nextLocations.sort((a, b) => {
          const aIsAirport = a >= 61 && a <= 65;
          const bIsAirport = b >= 61 && b <= 65;
          if (aIsAirport && !bIsAirport) return -1;
          if (!aIsAirport && bIsAirport) return 1;
          return 0;
        });
      }
      
      // Try each adjacent location
      for (const nextLocation of nextLocations) {
        if (!visited.has(nextLocation)) {
          visited.add(nextLocation);
          queue.push({
            location: nextLocation,
            path: [...path, nextLocation]
          });
        }
      }
    }
    
    // If no path was found
    return null;
  }
  
  /**
   * Clear the path cache
   */
  clearCache() {
    this.pathCache.clear();
    this.cacheAccessOrder = [];
  }
  
  /**
   * Check if this request is coming from a bot's 4th turn
   * @returns {boolean} Whether the current call stack suggests this is being called from turn 4
   * @private
   */
  _isRequestFromTurn4() {
    // Get stack trace
    const stackTrace = new Error().stack || '';
    
    // Check if stack contains indicators of turn 4
    if (stackTrace.includes('turn 4') || 
        stackTrace.includes('turnsPlayed: 4') || 
        stackTrace.includes('turn=4')) {
      return true;
    }
    
    // If we have a global flag indicating turn 4, check it
    return global.isTurn4 === true;
  }
}

module.exports = PathCalculator; 