{"description": "first trial of event cards favoured <PERSON><PERSON><PERSON>", "players": [{"id": "y7kL8MLazxpxKxSJAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 13, "hand": [{"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "energyCubes": ["gnana", "karma"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}], "outerScore": 66, "innerScore": 49, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "4HiDr5Trn416e7UuAAAB", "name": "Traveller", "position": 16, "hand": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "energyCubes": ["gnana", "gnana", "gnana", "bhakti", "artha"], "omTemp": [1, 1], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}], "outerScore": 51, "innerScore": 5, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}}], "started": true, "turnIndex": 0, "roundCount": 12, "travelDeck": [{"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}], "eventDeck": [{"id": "E2", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}, {"id": "E3", "type": "extraHop"}, {"id": "E8", "type": "wildCube"}, {"id": "E4", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "journeyDeckOuter": [{"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "travelDiscard": [{"id": "T15", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T5", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "boat"}], "faceUpEvent": [{"id": "E9", "type": "wildCube"}, {"id": "E1", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "faceUpJourneyOuter": [{"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}], "locationCubes": {"2": "karma", "3": "artha", "4": "karma", "8": "bhakti", "10": "artha", "11": "gnana", "14": "gnana", "17": "gnana", "19": "bhakti", "20": "karma", "21": "artha", "22": "bhakti", "23": "artha", "24": "bhakti", "25": "gnana", "26": "gnana", "27": "gnana", "28": "bhakti", "29": "gnana", "30": "bhakti", "31": "artha", "32": "gnana", "34": "bhakti", "35": "bhakti", "36": "karma", "37": "artha", "38": "artha", "39": "artha", "40": "bhakti", "41": "karma", "42": "bhakti", "43": "artha", "44": "karma", "45": "karma", "46": "gnana", "47": "artha", "48": "gnana", "undefined": "karma"}, "locationOm": {"50": true, "51": true, "59": true, "60": true}, "finalRound": true, "finalRoundStarter": 0, "finalRoundEnd": 1, "gameEvents": [{"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1743557778358, "data": {"eventCard": {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743557935686, "data": {"cardType": "travel", "card": {"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743557935686, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 0, "turnIndex": 0, "timestamp": 1743557935686, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 0, "turnIndex": 0, "timestamp": 1743557935686, "data": {"cardType": "travel", "card": {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743558152734, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T15", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743558157096, "data": {"path": [62, 11, 10, 49], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743558158783}, {"type": "move", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 0, "turnIndex": 1, "timestamp": 1743558182885, "data": {"path": [64, 15, 5, 52], "travelCards": ["T9", "T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 0, "turnIndex": 1, "timestamp": 1743558196149, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 0, "turnIndex": 1, "timestamp": 1743558239412, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 0, "turnIndex": 1, "timestamp": 1743558241972}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743558241972, "data": {"eventCard": {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}}}, {"type": "move", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743558584163, "data": {"path": [49, 10, 11, 7], "travelCards": ["T14"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743558593191, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743558596218, "data": {"cardType": "travel", "drawnCards": [{"id": "T5", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743558608424}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 1, "turnIndex": 1, "timestamp": 1743558711980, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 1, "turnIndex": 1, "timestamp": 1743558719354, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 1, "turnIndex": 1, "timestamp": 1743558724064, "data": {"path": [52, 5, 15, 64, 29, 33], "travelCards": ["T12", "T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 1, "turnIndex": 1, "timestamp": 1743558726464}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743558726464, "data": {"eventCard": {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}}}, {"type": "riots_discard_cube", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743558726466, "data": {"cubeType": "karma"}}, {"type": "move", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743558883368, "data": {"path": [7, 8, 54, 9], "travelCards": ["T10", "T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743558886151, "data": {"journeyType": "outer", "journeyCardId": "JO4", "omRequirement": 1}}, {"type": "endTurn", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743558951949}, {"type": "move", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 2, "turnIndex": 1, "timestamp": 1743558964193, "data": {"path": [33, 29, 28, 18], "travelCards": ["T13"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 2, "turnIndex": 1, "timestamp": 1743558968596, "data": {"journeyType": "outer", "journeyCardId": "JO9", "omRequirement": 1}}, {"type": "endTurn", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 2, "turnIndex": 1, "timestamp": 1743558982922}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743558982922, "data": {"eventCard": {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743559056436, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743559062721}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 3, "turnIndex": 1, "timestamp": 1743559156726, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 3, "turnIndex": 1, "timestamp": 1743559176953, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 3, "turnIndex": 1, "timestamp": 1743559188759}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743559188760, "data": {"eventCard": {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}}}, {"type": "move", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743559255187, "data": {"path": [9, 54], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743559265275, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743559267545, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743559269482}, {"type": "move", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 4, "turnIndex": 1, "timestamp": 1743559283380, "data": {"path": [18, 53], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 4, "turnIndex": 1, "timestamp": 1743559317280, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 4, "turnIndex": 1, "timestamp": 1743559323067, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "3bz692tnIkdxmfwFAAAH", "playerName": "Traveller", "roundCount": 4, "turnIndex": 1, "timestamp": 1743559325569}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743559325569, "data": {"eventCard": {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}}}, {"type": "move", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743559381083, "data": {"path": [54, 9, 1], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743559384133, "data": {"journeyType": "inner", "journeyCardId": "JI1", "omRequirement": 1}}, {"type": "endTurn", "playerId": "b5k-RK_WmzzkRl2cAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743559392596}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 1, "timestamp": 1743559548191, "data": {"timestamp": 1743559548191, "loadedStateRoundCount": 5, "playerCount": 2}}, {"type": "move", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 5, "turnIndex": 1, "timestamp": 1743559557480, "data": {"path": [53, 18, 15], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 5, "turnIndex": 1, "timestamp": 1743559588230, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 5, "turnIndex": 1, "timestamp": 1743559591454, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 5, "turnIndex": 1, "timestamp": 1743559593144}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743559593144, "data": {"eventCard": {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}}}, {"type": "move", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743559666025, "data": {"path": [1, 2, 3, 56], "travelCards": ["T15"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743559672566, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743559676101, "data": {"cardType": "travel", "drawnCards": [{"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743559733388}, {"type": "move", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 6, "turnIndex": 1, "timestamp": 1743559744433, "data": {"path": [15, 5], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 6, "turnIndex": 1, "timestamp": 1743559778539, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 6, "turnIndex": 1, "timestamp": 1743559799362}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743559799362, "data": {"eventCard": {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for +7 outer pts; skip for bonus cubes.", "effect": "jyotirlinga_7_outer_or_bonus_cube"}}}, {"type": "move", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743559929908, "data": {"path": [56, 3, 47, 57], "travelCards": ["T14"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743559945796, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743559950942}, {"type": "move", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 7, "turnIndex": 1, "timestamp": 1743559990665, "data": {"path": [5, 15, 64, 29, 33], "travelCards": ["T6", "T10"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 7, "turnIndex": 1, "timestamp": 1743559996829, "data": {"journeyType": "outer", "journeyCardId": "JO17", "omRequirement": 1}}, {"type": "endTurn", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 7, "turnIndex": 1, "timestamp": 1743560034577}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743560034577, "data": {"eventCard": {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}}}, {"type": "move", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743560147433, "data": {"path": [57, 41, 48, 42, 66, 61, 6], "travelCards": ["T3", "T13", "T7"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743560159850, "data": {"cardType": "travel", "drawnCards": [{"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743560160830, "data": {"cardType": "travel", "drawnCards": [{"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "triathlon_bonus", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743560162510, "data": {"bonus": 7, "usedValues": [1, 2, 3]}}, {"type": "endTurn", "playerId": "fKPw6pAlWkvhDE4DAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743560162510}, {"type": "PICK_FACE_UP_CARDS", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 8, "turnIndex": 1, "timestamp": 1743560278798, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "SBDV3U6-dL0fd0UVAAAD", "playerName": "Traveller", "roundCount": 8, "turnIndex": 1, "timestamp": 1743560299587, "data": {"cardType": "travel", "drawnCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickFromTop": true}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 1, "timestamp": 1743560747218, "data": {"timestamp": 1743560747218, "loadedStateRoundCount": 8, "playerCount": 2}}, {"type": "endTurn", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 8, "turnIndex": 1, "timestamp": 1743560756823}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743560756823, "data": {"eventCard": {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}}}, {"type": "move", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743560816043, "data": {"path": [6, 61, 63, 20, 21], "travelCards": ["T9", "T10"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743560819208, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 1}}, {"type": "endTurn", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743560855338}, {"type": "move", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 9, "turnIndex": 1, "timestamp": 1743560915527, "data": {"path": [55, 30, 31, 32, 58], "travelCards": ["T11", "T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 9, "turnIndex": 1, "timestamp": 1743560931914, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 9, "turnIndex": 1, "timestamp": 1743560943673, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 9, "turnIndex": 1, "timestamp": 1743560947539}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743560947539, "data": {"eventCard": {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743561145589, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743561151107, "data": {"cardType": "travel", "drawnCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "move", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743561162123, "data": {"path": [21, 20, 63, 62, 13], "travelCards": ["T15", "T1"], "extraHopCards": []}}, {"type": "trade", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743561175467, "data": {"cubesTraded": ["artha", "gnana"], "cubeReceived": "karma", "count": 1}}, {"type": "endTurn", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743561178920}, {"type": "PICK_FACE_UP_CARDS", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 10, "turnIndex": 1, "timestamp": 1743561259110, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 10, "turnIndex": 1, "timestamp": 1743561295638, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 10, "turnIndex": 1, "timestamp": 1743561303729, "data": {"path": [58, 32, 31, 65, 62, 11, 12], "travelCards": ["T13", "T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 10, "turnIndex": 1, "timestamp": 1743561307734}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743561307734, "data": {"eventCard": {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}}}, {"type": "collectJourney", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743561322165, "data": {"journeyType": "outer", "journeyCardId": "JO7", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743561322165, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 115, "playerOmTotal": 4, "finalRoundStarter": 0, "finalRoundEnd": 1}}, {"type": "endTurn", "playerId": "y7kL8MLazxpxKxSJAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743561325051}, {"type": "trade", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 11, "turnIndex": 1, "timestamp": 1743561388233, "data": {"cubesTraded": ["gnana", "karma"], "cubeReceived": "bhakti", "count": 1}}, {"type": "PICK_DECK_CARDS", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 11, "turnIndex": 1, "timestamp": 1743561411600, "data": {"cardType": "travel", "drawnCards": [{"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 11, "turnIndex": 1, "timestamp": 1743561422922, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 11, "turnIndex": 1, "timestamp": 1743561432393, "data": {"path": [12, 13, 14, 16], "travelCards": ["T6", "T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "4HiDr5Trn416e7UuAAAB", "playerName": "Traveller", "roundCount": 11, "turnIndex": 1, "timestamp": 1743561454566}, {"type": "global_event", "playerId": null, "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743561454566, "data": {"eventCard": {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743561454568, "data": {"winner": {"id": "y7kL8MLazxpxKxSJAAAD", "name": "<PERSON><PERSON><PERSON>", "outerScore": 66, "innerScore": 49, "totalScore": 115, "omTotal": 4}, "totalRounds": 12, "players": [{"id": "y7kL8MLazxpxKxSJAAAD", "name": "<PERSON><PERSON><PERSON>", "outerScore": 66, "innerScore": 49, "totalScore": 115, "omTotal": 4}, {"id": "4HiDr5Trn416e7UuAAAB", "name": "Traveller", "outerScore": 51, "innerScore": 5, "totalScore": 56, "omTotal": 4}]}}], "characterDeck": [{"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}], "energyCubePile": {"artha": 4, "karma": 4, "gnana": 1, "bhakti": 3}, "currentGlobalEvent": {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, "globalEventDeck": [{"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for +7 outer pts; skip for bonus cubes.", "effect": "jyotirlinga_7_outer_or_bonus_cube"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}], "globalEventDiscard": [{"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}]}