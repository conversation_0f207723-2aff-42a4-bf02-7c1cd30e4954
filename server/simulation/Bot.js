/**
 * Bot.js
 * Base class for all bot implementations.
 * Provides core functionality for automated players in the simulation.
 */

const logger = require('../utils/logger');

class Bot {
  /**
   * Initialize a new bot
   * @param {object} params - Configuration parameters
   * @param {string} params.id - The bot's socket ID (simulated)
   * @param {string} params.name - The bot's name
   * @param {object} params.strategy - The strategy object that defines bot behavior
   * @param {object} params.gameState - Reference to the game state
   * @param {object} params.logger - Logger for recording bot actions
   * @param {object} [params.options] - Additional bot options
   * @param {number} [params.options.thinkingSpeed=1] - Speed multiplier for thinking time
   * @param {number} [params.options.maxDecisionsLogSize=100] - Maximum number of decisions to keep in log
   */
  constructor({ id, name, strategy, gameState, logger, options = {} }) {
    this.id = id;
    this.name = name;
    this.strategy = strategy;
    this.gameState = gameState;
    this.logger = logger || console;

    // Bot options
    this.options = {
      thinkingSpeed: 1,
      maxDecisionsLogSize: 100, // Limit the size of the decisions log
      ...options
    };

    // Default thinking time in milliseconds
    this.thinkingTime = {
      min: 50,   // Minimum thinking time (milliseconds)
      max: 200   // Maximum thinking time (milliseconds)
    };

    // Bot statistics
    this.actionsTaken = 0;
    this.turnsPlayed = 0;
    this.decisionsLog = [];
    this.hasChosenStartingPosition = false; // Track if starting position has been chosen
    
    // Om track position tracking
    this.omSpace = 0; // Default to position 0 on Om track
    this.omStackPosition = 0; // Position in the stack at the current Om space
  }

  /**
   * Update the gameState reference
   * @param {Object} gameState - The new game state reference
   */
  setGameState(gameState) {
    if (gameState) {
      this.gameState = gameState;
      this.logger.info(`Bot ${this.name} gameState reference updated`);
    } else {
      this.logger.error(`Bot ${this.name} attempted to set null gameState`);
    }
  }

  /**
   * Update the player state for this bot
   * @param {Object} state - The new player state
   */
  updatePlayerState(state) {
    this.setPlayerState(state);
  }

  /**
   * Set the player state for this bot
   * @param {Object} state - The current game state
   */
  setPlayerState(state) {
    this._playerState = state;

    // Log debug info about the state
    if (state && state.locations && state.edges) {
      this.logger.info(`Bot ${this.name} game state overview: Locations=${state.locations.length}, Edges=${state.edges.length}, FaceUpTravel=${(state.faceUpTravel || []).length}, JourneyCards=${(state.faceUpJourneyInner || []).length + (state.faceUpJourneyOuter || []).length} (Inner=${(state.faceUpJourneyInner || []).length}, Outer=${(state.faceUpJourneyOuter || []).length})`);
    }

    // Log player position and resources
    if (state && state.player) {
      const player = state.player;
      const position = player.position;
      const collectedJourneys = player.collectedJourneys || [];

      // Update Om track position information if available in the state
      if (state.omTrack) {
        this._updateOmTrackPosition(state);
      }

      this.logger.info(`Bot ${this.name} at ${position ? `Location ${position}` : 'Unknown location'}\nHand [${(player.hand || []).length}]: ${(player.hand || []).map(card => `${card.cardType || card.type}:${card.value}`).join(', ')}\nCollected Journeys: ${collectedJourneys.length}\nOM Tokens: Inner=${player.omTokens?.inner || player.omSlotsInner?.filter(x => x > 0).length || 0}, Outer=${player.omTokens?.outer || player.omSlotsOuter?.filter(x => x > 0).length || 0}\nEnergy: Bhakti=${player.energyCubes?.bhakti || 0}, Gnana=${player.energyCubes?.gnana || 0}, Karma=${player.energyCubes?.karma || 0}, Artha=${player.energyCubes?.artha || 0}\nOm Track: Space=${this.omSpace}, Stack Position=${this.omStackPosition}`);
    }
  }

  /**
   * Update the bot's Om track position based on the current game state
   * @param {Object} state - The current game state
   * @private
   */
  _updateOmTrackPosition(state) {
    if (!state.omTrack) return;
    
    // Find this bot's position on the Om track
    for (let space = state.omTrack.length - 1; space >= 0; space--) {
      const stack = state.omTrack[space] || [];
      const stackPosition = stack.findIndex(playerId => playerId === this.id);
      
      if (stackPosition !== -1) {
        this.omSpace = space;
        this.omStackPosition = stackPosition;
        return;
      }
    }
    
    // If not found on the Om track, default to position 0
    this.omSpace = 0;
    this.omStackPosition = 0;
  }

  /**
   * Get the player state for this bot
   * @returns {Object} The current player state
   */
  getPlayerState() {
    return this._playerState;
  }

  /**
   * Get the bot's turn order ranking based on Om track position
   * This is used to determine the turn order - higher ranking goes first
   * @returns {Object} Object with omSpace and omStackPosition for sorting
   */
  getTurnOrderRanking() {
    return {
      omSpace: this.omSpace,
      omStackPosition: this.omStackPosition
    };
  }

  /**
   * Plan the next turn for this bot
   * @returns {Object} The turn action to take
   */
  async planTurn() {
    try {
      this.turnsPlayed++;

      // Log memory usage periodically
      if (this.turnsPlayed % 10 === 0) {
        const memoryUsage = process.memoryUsage();
        this.logger.warn(`[MEMORY-WATCH] Bot ${this.name} starting turn ${this.turnsPlayed} - Heap: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB, RSS: ${Math.round(memoryUsage.rss / 1024 / 1024)}MB, External: ${Math.round(memoryUsage.external / 1024 / 1024)}MB, ArrayBuffers: ${Math.round(memoryUsage.arrayBuffers / 1024 / 1024)}MB`);
      } else {
        this.logger.info(`Bot ${this.name} is taking turn ${this.turnsPlayed} - Memory: Heap=${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB, RSS=${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`);
      }

      // Check if we have a valid player state before proceeding
      let playerState = this.getPlayerState();
      if (!playerState || !playerState.player) {
        this.logger.error(`Invalid player state for bot ${this.name}. Cannot plan turn.`);
        
        // Force refresh player state from gameState
        if (this.gameState) {
          this.logger.warn(`Attempting to recover player state for bot ${this.name}...`);
          const refreshedState = this.gameState.getPlayerState(this.id);
          if (refreshedState && refreshedState.player) {
            this.logger.info(`Successfully recovered player state for bot ${this.name}`);
            this.setPlayerState(refreshedState);
            playerState = refreshedState;
          } else {
            // Initialize a default minimal state if we still can't get one
            this.logger.warn(`Creating default minimal player state for bot ${this.name}`);
            const defaultState = {
              isMyTurn: true,
              player: {
                id: this.id,
                name: this.name,
                position: null,
                hand: [],
                collectedJourneys: [],
                energyCubes: { bhakti: 0, gnana: 0, karma: 0, artha: 0 }
              },
              locations: [],
              edges: []
            };
            this.setPlayerState(defaultState);
            playerState = defaultState;
          }
        }
        
        // If we still don't have a valid state, end the turn
        if (!playerState || !playerState.player) {
          return { action: 'endTurn', params: {} };
        }
      }


      // Ensure player has collectedJourneys property
      if (!playerState.player.collectedJourneys) {
        playerState.player.collectedJourneys = [];
        this.logger.warn(`Bot ${this.name} missing collectedJourneys property in player state. Adding empty array.`);
      }

      // Delegate to the strategy to plan the turn
      return this.strategy.planTurn(this);
    } catch (error) {
      this.logger.error(`Error in ${this.name}.planTurn: ${error.message}`);
      return { action: 'endTurn', params: {} }; // End turn on error instead of skipping
    }
  }

  /**
   * Take a turn using the bot's strategy
   * @returns {Promise} Resolves when the turn is complete
   */
  async takeTurn() {
    this.turnsPlayed++;

    try {
      // Check if we have a valid player state before proceeding
      const playerState = this.getPlayerState();
      if (!playerState || !playerState.player) {
        this.logger.error(`Invalid player state for bot ${this.name}`);
        return { action: 'endTurn', params: {} };
      }

      // Ensure player has collectedJourneys property
      if (!playerState.player.collectedJourneys) {
        playerState.player.collectedJourneys = [];
        this.logger.warn(`Bot ${this.name} missing collectedJourneys property in takeTurn. Adding empty array.`);
      }

      // Log the bot's hand and game state at the beginning of each turn
      if (playerState.player.hand && playerState.player.hand.length > 0) {
        const travelCards = playerState.player.hand.filter(card => card.type === 'travel' || card.cardType === 'travel');
        const travelCardDetails = travelCards.map(card => `${card.id}(${card.value || '?'})`).join(', ');
        this.logger.info(`Bot ${this.name} hand: ${playerState.player.hand.length} cards total, ${travelCards.length} travel cards: ${travelCardDetails}`);
      } else {
        this.logger.info(`Bot ${this.name} hand: empty`);
      }

      // Log OM temp, inner and outer scores
      const player = playerState.player;
      const omTemp = player.omTemp || [];
      const innerScore = player.innerScore || 0;
      const outerScore = player.outerScore || 0;
      const omSlotsOuter = player.omSlotsOuter || [];
      const omSlotsInner = player.omSlotsInner || [];

      this.logger.info(`Bot ${this.name} OM temp: ${omTemp.length > 0 ? omTemp.join(', ') : 'empty'}`);
      this.logger.info(`Bot ${this.name} scores: Inner=${innerScore}, Outer=${outerScore}, Total=${innerScore + outerScore}`);
      this.logger.info(`Bot ${this.name} OM slots: Inner=[${omSlotsInner.join(',')}], Outer=[${omSlotsOuter.join(',')}]`);

      // Validate player state has required components
      if (!playerState.locations || !playerState.edges) {
        this.logger.error(`Player state missing critical game data for bot ${this.name}`);
        return { action: 'endTurn', params: {} };
      }

      // Track number of action attempts (to prevent infinite loops)
      let actionAttempts = 0;
      const MAX_ACTION_ATTEMPTS = 3; // Limit number of retries
      let actionSucceeded = false;
      let failedMoveAction = null;
      let lastFailureReason = null;

      // MODIFIED: We'll now maintain a list of all available actions for the turn
      let allTurnActions = [];
      let turnEndAction = null;
      let executedEndTurn = false;

      while (actionAttempts < MAX_ACTION_ATTEMPTS && !actionSucceeded) {
        // Increment attempt counter
        actionAttempts++;

        // If this is not the first attempt, refresh the player state
        if (actionAttempts > 1) {
          this._prepareUpdatedPlayerState();
          this.logger.warn(`Bot ${this.name} retry attempt ${actionAttempts} after action failure. Previous failure: ${lastFailureReason || 'Unknown reason'}`);

          // Add a small delay between attempts
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        // Delegate to strategy to decide what actions to take
        let actionPlans;
        try {
          actionPlans = await this.strategy.planTurn(this);
        } catch (error) {
          this.logger.error(`Bot ${this.name} strategy threw error during planTurn: ${error.message}`);
          this.logger.error(error.stack);
          lastFailureReason = `Strategy error: ${error.message}`;
          continue;
        }

        // Handle null or empty result from strategy
        if (!actionPlans) {
          this.logger.warn(`Bot ${this.name} strategy returned null for planTurn. Current state: ${JSON.stringify({
            position: this.getPlayerState()?.player?.position,
            handSize: this.getPlayerState()?.player?.hand?.length,
            isMyTurn: this.getPlayerState()?.isMyTurn
          })}`);
          break; // Exit the loop, will end turn below
        }

        // NEW LOGGING: Log the actions returned by the strategy
        this.logger.info(`Bot ${this.name} strategy returned ${Array.isArray(actionPlans) ? actionPlans.length : 1} actions`);
        if (Array.isArray(actionPlans)) {
          actionPlans.forEach((action, index) => {
            this.logger.info(`  Action ${index+1}: ${action.action} - params: ${JSON.stringify(action.params)}`);
          });
        } else {
          this.logger.info(`  Single action: ${actionPlans.action} - params: ${JSON.stringify(actionPlans.params)}`);
        }

        // Ensure actionPlans is an array - convert single action to array if needed
        const actionsArray = Array.isArray(actionPlans) ? actionPlans : [actionPlans];

        // Filter out any null or undefined actions
        const validActions = actionsArray.filter(action => action && action.action);

        // Separate endTurn action from the rest of the actions
        // We'll execute all actual actions first, then end turn at the end
        allTurnActions = validActions.filter(action => action.action !== 'endTurn');
        turnEndAction = validActions.find(action => action.action === 'endTurn');

        this.logger.info(`Bot ${this.name} has ${allTurnActions.length} game actions and ${turnEndAction ? 1 : 0} end turn actions`);

        if (allTurnActions.length === 0 && !turnEndAction) {
          this.logger.warn(`Bot ${this.name} strategy returned no valid actions`);
          break; // Exit the loop, will end turn below
        }

        // Track whether any actions were successfully executed
        let actionsExecuted = 0;

        // Try to execute each action in sequence
        for (const action of allTurnActions) {
          try {
            const success = await this._executeAction(action);
            if (success) {
              actionsExecuted++;
              this.logger.info(`Bot ${this.name} successfully executed action: ${action.action}`);
            } else {
              lastFailureReason = `Action ${action.action} failed to execute`;
              this.logger.warn(`Bot ${this.name} failed to execute action: ${action.action}. State: ${JSON.stringify({
                position: this.getPlayerState()?.player?.position,
                handSize: this.getPlayerState()?.player?.hand?.length,
                action: action
              })}`);
              break;
            }
          } catch (error) {
            lastFailureReason = `Action error: ${error.message}`;
            this.logger.error(`Bot ${this.name} error executing action ${action.action}: ${error.message}`);
            this.logger.error(error.stack);
            break;
          }
        }

        // After executing all normal actions, execute the endTurn action if present
        if (turnEndAction) {
          try {
            this.logger.info(`Bot ${this.name} executing end turn action`);
            await this._executeAction(turnEndAction);
            executedEndTurn = true;
          } catch (error) {
            this.logger.error(`Error executing endTurn action: ${error.message}`);
          }
        }

        // If any actions were executed successfully, we're done
        if (actionsExecuted > 0) {
          actionSucceeded = true;
          this.logger.info(`Bot ${this.name} successfully executed ${actionsExecuted}/${allTurnActions.length} actions for turn ${this.turnsPlayed}`);
          break;
        }

        // If we've gone through all the actions and none succeeded, try again (up to MAX_ACTION_ATTEMPTS)
        this.logger.warn(`Bot ${this.name} failed all actions on attempt ${actionAttempts} of ${MAX_ACTION_ATTEMPTS}`);
      }

      // If all action attempts failed, log a warning
      if (!actionSucceeded) {
        this.logger.warn(`Bot ${this.name} failed to execute any actions after ${MAX_ACTION_ATTEMPTS} attempts`);

        // Add to the decisions log with memory management
        this._addToDecisionsLog({
          turn: this.turnsPlayed,
          action: 'none',
          timestamp: Date.now(),
          outcome: 'all_actions_failed'
        });
      }

      // Only explicitly end the turn if we didn't already execute an endTurn action
      if (!executedEndTurn) {
        await this._simulateThinking();
        this.logger.info(`Bot ${this.name} explicitly ending turn ${this.turnsPlayed} (no endTurn action was executed)`);
        await this._endTurn();
      } else {
        this.logger.info(`Bot ${this.name} completed turn ${this.turnsPlayed} (endTurn was part of action plan)`);
      }

      return actionSucceeded;
    } catch (error) {
      // Improved error logging with limited details to save memory
      this.logger.error(`Bot ${this.name} encountered an error during turn ${this.turnsPlayed}: ${error.message} ${error.stack}`);

      try {
        // Try to end the turn gracefully
        await this._endTurn();
      } catch (endTurnError) {
        this.logger.error(`Failed to end turn after error: ${endTurnError.message}`);
      }

      return false;
    }
  }

  /**
   * Add an entry to the decisions log with size control
   * @param {object} entry - The log entry to add
   * @private
   */
  _addToDecisionsLog(entry) {
    // Add the new entry
    this.decisionsLog.push(entry);

    // Maintain maximum size for memory efficiency
    if (this.decisionsLog.length > this.options.maxDecisionsLogSize) {
      this.decisionsLog = this.decisionsLog.slice(-this.options.maxDecisionsLogSize);
    }
  }

  /**
   * Execute a planned action
   * @param {object} actionPlan - The action plan from the strategy
   * @returns {Promise} Resolves when the action is complete
   * @private
   */
  async _executeAction(actionPlan) {
    await this._simulateThinking();

    const { action, params } = actionPlan;
    this.logger.info(`Executing action: ${JSON.stringify(actionPlan)} / ${action} with params: ${JSON.stringify(params)}`);

    // Add detailed logging for card picking actions
    if (action === 'pickCards') {
      const handBefore = this.gameState.players.find(p => p.id === this.id)?.hand || [];
      this.logger.info(`CARD PICK ATTEMPT - Hand before: ${handBefore.length} cards, picking type: ${params.type}, from face-up: ${params.pickFromFaceUp?.length || 0}, from deck: ${params.pickFromTop ? 'yes' : 'no'}`);
    }

    try {
      switch (action) {
        case 'movePlayer':
          // Use our enhanced movePlayer method instead of direct gameState call
          // This provides better validation and logging
          const moveResult = await this.movePlayer(params);
          if (!moveResult) {
            throw new Error(`Move failed - could not complete the requested move`);
          }
          break;

        case 'pickCards':
          const { type, ids, pickFromFaceUp } = params;
          if (!type || (type !== 'travel' && type !== 'event')) {
            throw new Error(`Invalid card type: ${type}`);
          }

          // Support both "ids" and "pickFromFaceUp" parameter names
          const pickedCardIds = pickFromFaceUp || ids || [];
          if (!Array.isArray(pickedCardIds)) {
            throw new Error(`Invalid pickFromFaceUp: ${JSON.stringify(pickedCardIds)}`);
          }

          // Make sure we pass the pickFromTop parameter if it exists
          const pickResult = this.gameState.pickCards(this.id, {
            type,
            pickFromFaceUp: pickedCardIds,
            pickFromTop: params.pickFromTop
          });

          if (pickResult === false) {
            throw new Error(`Card picking failed - could not pick the requested cards`);
          }

          // Add logging after picking cards
          const handAfter = this.gameState.players.find(p => p.id === this.id)?.hand || [];
          this.logger.info(`CARD PICK RESULT - Hand after: ${handAfter.length} cards, picked type: ${type}`);
          if (handAfter.length > 0) {
            this.logger.info(`CARD PICK RESULT - Current hand: ${handAfter.map(c => `${c.id}(${c.value || '?'})`).join(', ')}`);
          }
          break;

        case 'chooseStartingPosition':
          // Handle starting position selection for bots
          const { position, nodeId } = params;
          // Support both 'position' and 'nodeId' parameter names
          const startPosition = position || nodeId;
          if (!startPosition || typeof startPosition !== 'number') {
            throw new Error(`Invalid starting position: ${JSON.stringify(params)}`);
          }

          // Find the player
          const player = this.gameState.players.find(p => p.id === this.id);
          if (player) {
            // Set the position
            player.position = startPosition;
            this.logger.info(`Bot ${this.name} chose starting position ${startPosition}`);
          } else {
            throw new Error(`Bot ${this.name} not found in game state`);
          }
          break;

        case 'collectJourney':
          const { journeyCardId, journeyType } = params;
          if (!journeyCardId) {
            throw new Error(`Invalid journeyCardId: ${journeyCardId}`);
          }
          if (!journeyType || (journeyType !== 'inner' && journeyType !== 'outer')) {
            throw new Error(`Invalid journeyType: ${journeyType}`);
          }
          const collectResult = this.gameState.collectJourney(this.id, journeyCardId, journeyType);
          if (collectResult === false) {
            throw new Error(`Journey card collection failed`);
          }
          break;

        case 'endTurn':
          this.logger.info(`Bot ${this.name} preparing to end turn (flagging for turn end)`);
          // We need to actually end the turn here
          this.gameState.endTurn(this.id);
          break;

        case 'requestStartPosition':
          // This is a placeholder action that just ensures we request a starting position
          // We'll convert it to a chooseStartingPosition action with a random valid position
          const validStartPositions = [61, 62, 63, 64, 65]; // Airport nodes are valid starting positions
          const randomPosition = validStartPositions[Math.floor(Math.random() * validStartPositions.length)];

          // Find the player
          const startPlayer = this.gameState.players.find(p => p.id === this.id);
          if (startPlayer) {
            // Set the position
            startPlayer.position = randomPosition;
            this.logger.info(`Bot ${this.name} was assigned starting position ${randomPosition}`);
          } else {
            throw new Error(`Bot ${this.name} not found in game state`);
          }
          break;

        case 'move':
          // Handle basic movement for bots - convert it to proper movePlayer format
          const { nodeId: targetNodeId, cardId } = params;
          if (!targetNodeId || typeof targetNodeId !== 'number') {
            throw new Error(`Invalid target node: ${targetNodeId}`);
          }
          if (!cardId) {
            throw new Error('Card ID is required for movement');
          }

          // Find the player
          const movePlayer = this.gameState.players.find(p => p.id === this.id);
          if (!movePlayer) {
            throw new Error(`Bot ${this.name} not found in game state`);
          }

          // Get current position
          const currentPosition = movePlayer.position;
          if (!currentPosition) {
            throw new Error(`Bot ${this.name} has no current position`);
          }

          // Construct a path (just from current to target)
          const movePath = [currentPosition, targetNodeId];

          // Call the movePlayer function with the card
          const basicMoveResult = this.gameState.movePlayer(
            this.id,
            movePath,
            [cardId],
            0,  // extraHopCount = 0 for basic move
            false  // isTriathlon = false for basic move
          );
          if (basicMoveResult === false) {
            throw new Error(`Move failed - could not complete the requested move`);
          }
          this.logger.info(`Bot ${this.name} moved from ${currentPosition} to ${targetNodeId} using card ${cardId}`);

          // Log a warning since we should be using movePlayer action instead
          this.logger.warn(`Bot ${this.name} used deprecated 'move' action - update to use 'movePlayer' action instead`);
          break;

        default:
          throw new Error(`Unknown action type: ${action}`);
      }

      return true;
    } catch (error) {
      // Log the error but don't rethrow - instead return false to indicate the action failed
      this.logger.error(`Error executing action ${action}: ${error.message}`);

      // Add to the decisions log
      this._addToDecisionsLog({
        turn: this.turnsPlayed,
        action: action,
        timestamp: Date.now(),
        outcome: 'failed',
        error: error.message
      });

      // Refresh state after a failed action so the bot can try again
      this._prepareUpdatedPlayerState();

      // Return false to indicate the action failed
      return false;
    }
  }

  /**
   * Check if a path includes airport-to-airport travel
   * @param {Array} path - Array of node IDs in the path
   * @returns {boolean} - True if the path includes airport-to-airport travel
   * @private
   */
  _pathHasAirportTravel(path) {
    if (!path || path.length < 2) return false;

    // Airport nodes are in the range 61-66
    for (let i = 0; i < path.length - 1; i++) {
      const current = path[i];
      const next = path[i+1];

      const isCurrentAirport = current >= 61 && current <= 66;
      const isNextAirport = next >= 61 && next <= 66;

      if (isCurrentAirport && isNextAirport) {
        return true;
      }
    }

    return false;
  }

  /**
   * End the bot's turn
   * @returns {Promise} Resolves when the turn is ended
   * @private
   */
  async _endTurn() {
    await this._simulateThinking();
    try {
      this.gameState.endTurn(this.id);
    } catch (error) {
      this.logger.error(`Error ending turn for Bot ${this.name}: ${error.message}`);
      throw error;
    }
    return true;
  }

  /**
   * Simulate human thinking time to make the simulation more realistic
   * @returns {Promise} Resolves after a random delay
   * @private
   */
  async _simulateThinking() {
    // Calculate thinking time based on speed setting
    const actualMinTime = Math.max(10, this.thinkingTime.min / this.options.thinkingSpeed);
    const actualMaxTime = Math.max(20, this.thinkingTime.max / this.options.thinkingSpeed);

    const thinkingTime = Math.floor(
      Math.random() * (actualMaxTime - actualMinTime + 1) + actualMinTime
    );

    return new Promise(resolve => setTimeout(resolve, thinkingTime));
  }

  /**
   * Set the thinking speed for this bot
   * @param {number} speed - Speed multiplier (higher = faster)
   */
  setThinkingSpeed(speed) {
    if (typeof speed === 'number' && speed > 0) {
      this.options.thinkingSpeed = speed;
    }
  }

  /**
   * Get bot statistics
   * @returns {object} Statistics about this bot's performance
   */
  getStats() {
    try {
      // Return stats without including the full player state
      return {
        id: this.id,
        name: this.name,
        strategyName: this.strategy.name,
        turnsPlayed: this.turnsPlayed,
        actionsTaken: this.actionsTaken,
        // Include only essential player state data
        playerScore: {
          outerScore: this.getPlayerState().player.outerScore || 0,
          innerScore: this.getPlayerState().player.innerScore || 0
        },
        // Return only the most recent decisions to save memory
        recentDecisions: this.decisionsLog.slice(-10)
      };
    } catch (error) {
      this.logger.error(`Error getting stats for Bot ${this.name}: ${error.message}`);
      return {
        id: this.id,
        name: this.name,
        strategyName: this.strategy?.name || 'unknown',
        turnsPlayed: this.turnsPlayed,
        actionsTaken: this.actionsTaken,
        error: error.message
      };
    }
  }

  /**
   * Prepare and update the player state data from the gameState
   * @private
   */
  _prepareUpdatedPlayerState() {
    // Get the current game state
    const gameState = this.gameState;
    if (!gameState) {
      this.logger.error(`Bot ${this.name} has no gameState reference`);
      return;
    }

    // Find this bot's player in the game state
    const player = gameState.players.find(p => p.id === this.id);
    if (!player) {
      this.logger.error(`Bot ${this.name} not found in gameState.players array`);
      return;
    }

    // Prepare the player state
    const playerState = gameState.getPlayerState(this.id);
    if (!playerState) {
      this.logger.error(`Failed to get player state for bot ${this.name}`);
      return;
    }

    // Reset moveAttemptFailed flag if this is a new turn
    if (player && player.moveAttemptFailed) {
      // If we're at the start of a new turn, reset the flag
      if (playerState.isMyTurn && !this._lastTurnPlayerState?.isMyTurn) {
        player.moveAttemptFailed = false;
        this.logger.info(`Bot ${this.name} starting a new turn - resetting moveAttemptFailed flag`);
      }
    }

    // Store this state for comparison in the next update
    this._lastTurnPlayerState = { isMyTurn: playerState.isMyTurn };

    // Ensure player has collectedJourneys property
    if (playerState.player && !playerState.player.collectedJourneys) {
      playerState.player.collectedJourneys = [];
      this.logger.warn(`Bot ${this.name} missing collectedJourneys property in _prepareUpdatedPlayerState. Adding empty array.`);
    }

    // Set the player state
    this.setPlayerState(playerState);
  }

  /**
   * Execute a movePlayer action
   * @param {Object} params - The action parameters
   * @returns {Promise<boolean>} - Whether the action was successful
   */
  async movePlayer(params) {
    this.logger.info(`Executing action: ${JSON.stringify({ action: 'movePlayer', params })} / movePlayer with params: ${JSON.stringify(params)}`);

    try {
      const state = this.getPlayerState();
      if (!state || !state.player) {
        this.logger.error('Invalid player state in movePlayer');
        return false;
      }

      // Log position before movement
      const positionBefore = state.player.position || state.player.currentNodeId;
      this.logger.info(`Bot ${this.name} move attempt - current position: ${positionBefore}, cards: ${params.cardIds.join(', ')}, path: ${params.path.join(' -> ')}`);

      // Log player's hand before movement
      const handBefore = state.player.hand || [];
      this.logger.info(`Bot ${this.name} hand before move: ${handBefore.map(c => `${c.id}(${c.value || 'n/a'})`).join(', ')}`);

      // More thorough verification of cards in hand
      const handCardIds = new Set(handBefore.map(card => card.id));

      // Check each card ID individually and log clear error messages
      const missingCards = params.cardIds.filter(cardId => !handCardIds.has(cardId));

      if (missingCards.length > 0) {
        this.logger.error(`Bot ${this.name} is missing some cards needed for move: ${missingCards.join(', ')}`);
        this.logger.error(`Available cards: ${Array.from(handCardIds).join(', ')}`);
        // Set the move failed flag to signal that the move could not be completed
        state.player.moveAttemptFailed = true;
        return false;
      }

      // Verify starting position matches first node in path
      if (positionBefore !== params.path[0]) {
        this.logger.error(`Bot ${this.name} is at position ${positionBefore} but path starts at ${params.path[0]}`);
        // Set the move failed flag to signal that the move could not be completed
        state.player.moveAttemptFailed = true;
        return false;
      }

      // Validate that we're not trying to move to the same location with no cards
      if (params.path.length === 1 || (params.path.length === 2 && params.path[0] === params.path[1])) {
        if (params.cardIds.length === 0) {
          this.logger.error(`Bot ${this.name} attempted to move to current location ${params.path[0]} with no travel cards`);
          state.player.moveAttemptFailed = true;
          return false;
        }
      }

      // Call the handler in the game state
      const result = this.gameState.movePlayer(
        this.id,
        params.path,
        params.cardIds,
        params.extraHopCount || 0,  // Pass extraHopCount as 4th param (default to 0)
        params.isTriathlon || false  // Pass isTriathlon as 5th param (default to false)
      );

      if (!result) {
        // Enhanced error logging for move failures
        this.logger.error(`Bot ${this.name} move failed - setting moveAttemptFailed flag`);
        this.logger.error(`Move details: path=${params.path.join('->')}, cardIds=${params.cardIds.join(',')}, extraHops=${params.extraHopCount || 0}`);

        // Check for common failure reasons
        if (params.cardIds.length === 0) {
          this.logger.error(`Move failure reason: No travel cards provided for movement`);
        } else if (params.path.length <= 1) {
          this.logger.error(`Move failure reason: Invalid path length (${params.path.length})`);
        } else {
          // Calculate the total distance of the path
          const pathDistance = params.path.length - 1;
          // Calculate the total value of the cards
          const cardValues = handBefore
            .filter(card => params.cardIds.includes(card.id))
            .map(card => card.value || 0);
          const totalCardValue = cardValues.reduce((sum, val) => sum + val, 0);

          this.logger.error(`Move failure reason: Path distance (${pathDistance}) doesn't match card values (${totalCardValue}) + extra hops (${params.extraHopCount || 0})`);
        }

        // Update state to reflect failed move
        state.player.moveAttemptFailed = true;
        return false;
      }

      // Get updated position after successful move
      // Force refresh player state to ensure we're using the latest data
      this._prepareUpdatedPlayerState(); // Get fresh state after move
      const updatedState = this.getPlayerState();
      const positionAfter = updatedState.player.position || updatedState.player.currentNodeId;
      this.logger.info(`Bot ${this.name} move successful - new position: ${positionAfter}`);

      // Log player's hand after movement
      const handAfter = updatedState.player.hand || [];
      this.logger.info(`Bot ${this.name} hand after move: ${handAfter.map(c => `${c.id}(${c.value || 'n/a'})`).join(', ')}`);

      // Verify if the position actually changed and notify the strategy
      if (this.strategy && typeof this.strategy._verifyMoveSuccess === 'function') {
        this.logger.info(`Verifying move success: ${positionBefore} -> ${positionAfter}`);
        this.strategy._verifyMoveSuccess(positionAfter);
      } else if (positionBefore === positionAfter) {
        // If strategy doesn't have verification but position didn't change, log a warning
        this.logger.warn(`Bot ${this.name} position didn't change after move: still at ${positionAfter}`);
        state.player.moveAttemptFailed = true;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error executing action movePlayer: ${error.message}`);
      return false;
    }
  }
}

module.exports = Bot;