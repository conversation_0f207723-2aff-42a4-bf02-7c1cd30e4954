// Om: The Journey Rulebook Styles - Print Edition
// Based on standard letter size (8.5" x 11") with uniform margins

// Color Palette
$navy-blue: #205F80;
$burnt-orange: #E87E1E;
$sand: #F4EBDD;
$body-grey: #555;
$white: #FFFFFF;

// Energy Cube Colors
$artha-color: #FFD700; // yellow/gold
$karma-color: #32CD32; // green
$gnana-color: #4169E1; // blue
$bhakti-color: #9370DB; // purple

// Typography
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue:wght@400&family=Lato:wght@400;700&display=swap');

// Page Setup for print
@page {
  size: letter;
  /* uniform ¾‑inch margins all around */
  margin: 0.75in;

  /* page numbers only (bottom‑centre) */
  @bottom-center {
    content: counter(page);
    font-family: 'Lato', sans-serif;
    font-size: 10pt;
    color: $body-grey;
  }
}

// Page breaks
.page-break {
  break-after: page;
}

// Base Grid System (12-column)
html {
  font-size: 12pt;
  line-height: 24pt;
}

body {
  font-family: 'Lato', sans-serif;
  font-size: 10pt;
  line-height: 14pt;
  color: $body-grey;
  background: $white;
  margin: 0;
  padding: 0;
}

// Grid container
.container {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 12pt;
  max-width: 100%;
}

// Typography Hierarchy
h1 {
  font-family: 'Bebas Neue', cursive;
  font-size: 36pt;
  line-height: 36pt;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 0 0 24pt 0;
  font-weight: 400;
}

h2 {
  font-family: 'Bebas Neue', cursive;
  font-size: 26pt;
  line-height: 26pt;
  color: $navy-blue;
  text-transform: uppercase;
  margin: 24pt 0 12pt 0;
  font-weight: 400;
}

h3 {
  font-family: 'Lato', sans-serif;
  font-size: 14pt;
  line-height: 18pt;
  color: $navy-blue;
  font-weight: 700;
  margin: 18pt 0 6pt 0;
}

h4 {
  font-family: 'Lato', sans-serif;
  font-size: 12pt;
  line-height: 16pt;
  color: $burnt-orange;
  font-weight: 700;
  margin: 12pt 0 6pt 0;
}

p {
  margin: 0 0 12pt 0;
  font-family: 'Lato', sans-serif;
  font-size: 10pt;
  line-height: 14pt;
}

// Fix for "The game balances physical exploration..." section
.main-content.with-sidebar-right p {
  font-family: 'Lato', sans-serif;
  font-size: 10pt;
  line-height: 14pt;
}

/* ----- List Defaults ----- */
ul {
  list-style-type: disc;
  margin-left: 18pt;
}
ol {
  list-style-type: decimal;
  margin-left: 18pt;
}

// Lists
ul, ol {
  margin: 0 0 12pt 0;
  padding-left: 18pt;
  
  li {
    margin-bottom: 6pt;
  }
}

// Boxes & Call-outs
.callout-box {
  background: $sand;
  border: 1pt solid $navy-blue;
  border-radius: 4px;
  padding: 5pt;
  margin: 12pt 0;
  
  &.important {
    background: lighten($burnt-orange, 40%);
    border-color: $burnt-orange;
  }
  
  &.setup {
    background: lighten($navy-blue, 45%);
    border-color: $navy-blue;
  }
  
  p:last-child {
    margin-bottom: 0;
  }
}

// Sidebar layouts
.sidebar-left {
  grid-column: 1 / 4;
}

.sidebar-right {
  grid-column: 10 / 13;
}

.main-content {
  grid-column: 1 / 13;
  
  &.with-sidebar-left {
    grid-column: 5 / 13;
  }
  
  &.with-sidebar-right {
    grid-column: 1 / 9;
  }
}

// Tables
table {
  width: 100%;
  border-collapse: collapse;
  margin: 12pt 0;
  font-size: 9pt;
  line-height: 12pt;
  
  th {
    background: $navy-blue;
    color: $white;
    padding: 6pt;
    font-weight: 700;
    text-align: left;
  }
  
  td {
    padding: 6pt;
    border-bottom: 0.5pt solid lighten($navy-blue, 20%);
  }
  
  tr:nth-child(even) {
    background: darken($sand, 5%);
  }
}

// Component table
.component-table {
  table {
    th:first-child {
      width: 15%;
    }
    th:nth-child(2) {
      width: 20%;
    }
    th:nth-child(3) {
      width: 65%;
    }
  }
}

// Icons (32x32px)
.icon {
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 6pt;
  
  &.energy-cube {
    border-radius: 3px;
    
    &.artha { background: $artha-color; }
    &.karma { background: $karma-color; }
    &.gnana { background: $gnana-color; }
    &.bhakti { background: $bhakti-color; }
  }
  
  &.om-token {
    background: $burnt-orange;
    border-radius: 50%;
    color: $white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 10pt;
  }
}

// Image placeholders for print
.game-image {
  max-width: 100%;
  height: auto;
  border: 1pt solid $navy-blue;
  background: $sand;
  margin: 12pt 0;
  display: block;
  
  &.board-section {
    width: 100%;
    height: 300px;
    margin: 12pt 0;
  }
}

// Cover page
.cover-page {
  grid-column: 1 / 13;
  background: linear-gradient(135deg, $navy-blue 0%, $burnt-orange 100%);
  color: $white;
  padding: 72pt 36pt;
  text-align: center;
  height: 9.5in; /* Letter size minus margins */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  h1 {
    color: $white;
    font-size: 48pt;
    line-height: 48pt;
    margin-bottom: 24pt;
  }
  
  .subtitle {
    font-size: 18pt;
    line-height: 24pt;
    margin-bottom: 36pt;
  }
  
  .cover-image {
    width: 80%;
    margin: 24pt 0;
    
    img {
      width: 100%;
      height: auto;
      border: 4pt solid $white;
    }
  }
  
  .game-info {
    font-size: 14pt;
    line-height: 18pt;
    margin: 36pt 0;
    
    .players, .time {
      display: inline-block;
      margin: 0 12pt;
    }
  }
}

// Component spread
.component-spread {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 12pt;

  .component-category {
    grid-column: span 6;
    margin-bottom: 24pt;

    h3 {
      color: $burnt-orange;
      border-bottom: 1pt solid $burnt-orange;
      padding-bottom: 3pt;
    }

    .component-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12pt;
      margin-top: 12pt;
    }

    .component-item {
      text-align: center;

      .component-image {
        width: 80px;
        height: 80px;
        background: $sand;
        border: 1pt solid $navy-blue;
        border-radius: 4px;
        margin: 0 auto 6pt auto;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8pt;
        color: $body-grey;
      }

      .component-name {
        font-weight: 700;
        font-size: 9pt;
        margin-bottom: 3pt;
      }

      .component-desc {
        font-size: 8pt;
        line-height: 10pt;
      }
    }
  }
}

// Icon legend footer
.icon-legend {
  background: $sand;
  border-top: 1pt solid $navy-blue;
  padding: 6pt 12pt;
  margin-top: 24pt;
  font-size: 8pt;
  position: fixed;
  bottom: 0.5in;
  left: 0.75in;
  right: 0.75in;
  
  .legend-row {
    display: flex;
    flex-wrap: wrap;
    gap: 12pt;
    
    .legend-item {
      display: flex;
      align-items: center;
      
      .icon {
        width: 16px;
        height: 16px;
        margin-right: 3pt;
      }
    }
  }
}

// Board diagram
.board-diagram {
  background: $sand;
  border: 2pt solid $navy-blue;
  border-radius: 8px;
  padding: 12pt;
  margin: 18pt 0;
  
  p {
    margin: 6pt 0 0 0;
  }
}

// Example gameplay
.example-turn {
  background: lighten($burnt-orange, 45%);
  border-left: 4pt solid $burnt-orange;
  padding: 12pt;
  margin: 18pt 0;
  
  .turn-header {
    font-weight: 700;
    color: $burnt-orange;
    margin-bottom: 6pt;
  }
  
  .turn-step {
    margin: 6pt 0;
    padding-left: 12pt;
    
    &:before {
      content: "→";
      color: $burnt-orange;
      font-weight: 700;
      margin-right: 6pt;
      margin-left: -12pt;
    }
  }
}

// Reference cards
.reference-card {
  background: $white;
  border: 1pt solid $navy-blue;
  border-radius: 6px;
  padding: 8pt;
  margin: 6pt;
  display: inline-block;
  vertical-align: top;
  width: 200px;
  
  .card-title {
    background: $navy-blue;
    color: $white;
    padding: 4pt 6pt;
    margin: -8pt -8pt 6pt -8pt;
    border-radius: 5px 5px 0 0;
    font-weight: 700;
    font-size: 9pt;
  }
  
  .card-cost {
    font-size: 8pt;
    margin-bottom: 4pt;
    
    .icon {
      width: 12px;
      height: 12px;
      margin-right: 2pt;
    }
  }
  
  .card-points {
    color: $burnt-orange;
    font-weight: 700;
    text-align: right;
    font-size: 14pt;
  }
}

// Utility classes
.text-center { text-align: center; }
.text-right { text-align: right; }
.font-bold { font-weight: 700; }
.color-navy { color: $navy-blue; }
.color-orange { color: $burnt-orange; }

.margin-top { margin-top: 12pt; }
.margin-bottom { margin-bottom: 12pt; }
.padding { padding: 6pt; } 