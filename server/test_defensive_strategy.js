const { SimulationRunner } = require('./simulation/SimulationRunner');

/**
 * Test script for defensive bot strategy
 * Runs a simulation with configurable number of bots (2 or 3)
 *
 * Usage: node server/test_defensive_strategy.js [numBots]
 * Where numBots is either 2 or 3 (defaults to 2 if not specified)
 */

// Parse command line arguments
const args = process.argv.slice(2);
let numBots = 2; // Default to 2 bots

if (args.length > 0) {
  const requestedBots = parseInt(args[0], 10);
  if (requestedBots === 2 || requestedBots === 3) {
    numBots = requestedBots;
  } else {
    console.warn(`Invalid number of bots: ${args[0]}. Must be either 2 or 3. Using default of 2 bots.`);
  }
}

// Create bot configurations based on number of bots
const botConfigs = [];
for (let i = 0; i < numBots; i++) {
  // Use letters A, B, C for bot names
  const botName = `Bot-${String.fromCharCode(65 + i)}`;
  // Alternate between defensive and optimized defensive strategies
  const strategy = i % 2 === 0 ? 'defensive' : 'opt-defensive';
  botConfigs.push({ name: botName, strategy });
}

// Configuration for the simulation
const options = {
  numGames: 1,
  speed: 0.5,
  bots: botConfigs,
  saveOutput: true,
  memoryStats: true,
  verbose: true
};

console.log(`Starting simulation with ${numBots} bots...`);
console.log('Bot configurations:', botConfigs.map(bot => `${bot.name} (${bot.strategy})`).join(', '));

// Run the simulation
const runner = new SimulationRunner(options);
runner.start()
  .then((results) => {
    console.log('Simulation completed with results:', results);
  })
  .catch((error) => {
    console.error('Simulation failed:', error);
  });