/**
 * Simplified BotFactory for the bot client
 *
 * Creates bot strategies without the full Bot implementation
 */

// Import bot strategies
const RandomStrategy = require('../server/simulation/strategies/RandomStrategy');
const DefensiveStrategy = require('../server/simulation/strategies/DefensiveStrategy');
const OptimizedDefensiveStrategy = require('../server/simulation/strategies/OptimizedDefensiveStrategy');
const BaseStrategy = require('../server/simulation/strategies/BaseStrategy');

class BotFactory {
  /**
   * Create a new BotFactory
   * @param {object} params - Configuration
   * @param {object} params.logger - Logger for recording bot actions
   */
  constructor({ logger = console }) {
    this.logger = logger;

    // Set up strategy registry - only register strategies that actually exist
    this.strategyRegistry = new Map([
      ['random', RandomStrategy],
      ['defensive', DefensiveStrategy],
      ['opt-defensive', OptimizedDefensiveStrategy],
      ['base', BaseStrategy]
    ]);
  }

  /**
   * Get a list of available strategy names
   * @returns {string[]} Array of strategy names
   */
  getAvailableStrategies() {
    return Array.from(this.strategyRegistry.keys());
  }

  /**
   * Create a strategy instance
   * @param {string} strategyName - Name of the strategy to use
   * @param {object} [config={}] - Configuration for the strategy
   * @returns {object} Strategy instance
   */
  createStrategy(strategyName, config = {}) {
    // Get the strategy class, with fallback to defensive
    let StrategyClass = this.strategyRegistry.get(strategyName);

    // If strategy not found, log warning and fall back to defensive strategy
    if (!StrategyClass) {
      this.logger.warn(`Unknown strategy: ${strategyName}, falling back to 'defensive' strategy`);
      StrategyClass = this.strategyRegistry.get('defensive');

      // If defensive also not found (shouldn't happen), try random as second fallback
      if (!StrategyClass) {
        this.logger.warn(`Defensive strategy not found either, trying 'random' strategy`);
        StrategyClass = this.strategyRegistry.get('random');

        // If random also not found, use base strategy as final fallback
        if (!StrategyClass) {
          this.logger.warn(`Random strategy not found either, falling back to 'base' strategy`);
          StrategyClass = this.strategyRegistry.get('base');

          // If no strategy is available, throw error
          if (!StrategyClass) {
            throw new Error(`Failed to find any valid strategies. No fallback available.`);
          }
        }
      }
    }

    // Create and return the strategy instance
    this.logger.info(`Creating strategy '${strategyName}' (using ${StrategyClass.name})`);
    return new StrategyClass(config);
  }
}

module.exports = BotFactory;