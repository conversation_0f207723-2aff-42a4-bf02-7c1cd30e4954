const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const sharp = require('sharp');

// Configuration
const htmlDir = path.join(__dirname, 'journey-card-html');
const outputDir = path.join(__dirname, 'journey-card-images');

// Create output directory if it doesn't exist
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Get all HTML files
const htmlFiles = fs.readdirSync(htmlDir)
  .filter(file => file.endsWith('.html'));

console.log(`Found ${htmlFiles.length} HTML files to convert`);

// Function to convert HTML to PNG using wkhtmltoimage
async function convertHtmlToPng(htmlFile) {
  const inputPath = path.join(htmlDir, htmlFile);
  const tempOutputPath = path.join(outputDir, `temp_${htmlFile.replace('.html', '.png')}`);
  const finalOutputPath = path.join(outputDir, htmlFile.replace('.html', '.png'));

  console.log(`Converting ${htmlFile} to PNG...`);

  try {
    // Use Chrome headless to capture screenshot
    const command = `"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" --headless --disable-gpu --screenshot="${tempOutputPath}" --window-size=825,1125 --default-background-color=0 "file://${inputPath}"`;

    await execPromise(command);

    // Resize the image to the exact dimensions using sharp
    await sharp(tempOutputPath)
      .resize({
        width: 825,
        height: 1125,
        fit: 'fill'
      })
      .toFile(finalOutputPath);

    // Remove the temporary file
    fs.unlinkSync(tempOutputPath);

    console.log(`Successfully converted ${htmlFile} to ${finalOutputPath}`);
    return true;
  } catch (error) {
    console.error(`Error converting ${htmlFile}:`, error.message);
    return false;
  }
}

// Process files sequentially to avoid overwhelming the system
async function processFiles() {
  let successCount = 0;
  let failCount = 0;

  for (const htmlFile of htmlFiles) {
    const success = await convertHtmlToPng(htmlFile);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  console.log(`\nConversion complete!`);
  console.log(`Successfully converted: ${successCount} files`);
  console.log(`Failed conversions: ${failCount} files`);
  console.log(`Output directory: ${outputDir}`);
}

// Start processing
processFiles();
