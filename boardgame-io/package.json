{"name": "om-journey", "version": "1.0.0", "description": "Om: The Journey boardgame implementation with boardgame.io", "main": "index.js", "scripts": {"start": "webpack serve --mode development --open", "build": "webpack --mode production", "server": "node src/game/Server.js", "dev": "concurrently \"npm run server\" \"npm run start\""}, "dependencies": {"boardgame.io": "^0.50.2", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "babel-loader": "^9.2.1", "concurrently": "^8.2.1", "copy-webpack-plugin": "^13.0.0", "css-loader": "^6.11.0", "html-webpack-plugin": "^5.6.3", "react-scripts": "5.0.1", "style-loader": "^3.3.4", "webpack": "^5.99.5", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}