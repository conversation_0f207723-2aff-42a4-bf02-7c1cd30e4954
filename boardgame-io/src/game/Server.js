const { Server } = require('boardgame.io/server');
// import { Server } from 'boardgame.io/server';
const path = require('path');
const OmJourney = require('./Game');

// More detailed server configuration with better CORS
const server = Server({
  games: [O<PERSON><PERSON>our<PERSON>],
  origins: [/localhost:\d+/]
});

// Start the server
const PORT = process.env.PORT || 8000;
server.run(PORT, () => {
  console.log(`\n===========================================`);
  console.log(`Boardgame.io server running on http://localhost:${PORT}`);
  console.log(`Game available: ${OmJourney.name || 'om-journey'}`);
  console.log(`===========================================\n`);
});