const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const http = require('http');
const handler = require('serve-handler');

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, 'journey-card-images');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Start a simple HTTP server to serve the HTML file and assets
const server = http.createServer((request, response) => {
  return handler(request, response, {
    public: __dirname,
  });
});

async function captureCards() {
  const port = 3456;
  server.listen(port, async () => {
    console.log(`Server running at http://localhost:${port}`);
    
    try {
      // Launch browser
      const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      const page = await browser.newPage();
      
      // Set viewport to match card dimensions
      await page.setViewport({
        width: 825,
        height: 1125,
        deviceScaleFactor: 1,
      });
      
      // Navigate to the HTML file
      await page.goto(`http://localhost:${port}/journey-card-renderer.html`, {
        waitUntil: 'networkidle0',
      });
      
      // Get all journey cards
      const allCards = await page.evaluate(() => {
        return window.journeyCards.all;
      });
      
      console.log(`Found ${allCards.length} journey cards to capture`);
      
      // Process each card
      for (const card of allCards) {
        console.log(`Processing card ${card.id} for location ${card.locationId}`);
        
        // Render the card
        await page.evaluate((cardId) => {
          window.cardRendered = false;
          window.renderCard(cardId);
        }, card.id);
        
        // Wait for card to be rendered
        await page.waitForFunction('window.cardRendered === true', { timeout: 5000 });
        
        // Wait a bit for any animations or image loading
        await page.waitForTimeout(500);
        
        // Take screenshot
        const screenshotPath = path.join(outputDir, `${card.id}.png`);
        await page.screenshot({
          path: screenshotPath,
          clip: {
            x: 0,
            y: 0,
            width: 825,
            height: 1125,
          },
        });
        
        console.log(`Saved screenshot to ${screenshotPath}`);
      }
      
      // Close browser
      await browser.close();
      console.log('All cards captured successfully!');
    } catch (error) {
      console.error('Error capturing cards:', error);
    } finally {
      // Close server
      server.close(() => {
        console.log('Server closed');
      });
    }
  });
}

captureCards();
