Simulation analysis:
1. Rounds - min, max
2. End game Score difference - avg, min, max
3. Journey card count by type - avg, min, max
4. Total Bonus scores (i.e event based) collected - avg, min, max
5. Dry turns - avg, min, max

Journey card selection improvement:
- End game strategy: if total score is >= 70, go for a single card that provides convergence with best cube difference (aka quickest route)
example, if player is at 82 points and sees following JC cards: 
1. a card with 30 points and cube difference of 3 cubes
2. a card with 20 points and cube difference of 1 cubes
then player should go for card with 20 points since it will converge them (82 + 20 > 100) quicker

Trade:
- when selecting a journey card

Handling event cards:
The defensive bot strategy only ever special-cases four global events (<PERSON><PERSON><PERSON> Distraction, Drizzle of Delay, Turbulent Skies and Election Campaigns) and it also looks for vehicle-reward events (Eco Trail, Rajput Caravans, Urban Ride, Road Warriors, Rails and Sails, Heavy Haul) when picking cards, but absolutely none of the following global events are handled in the strategy at all:
• Maha Kumbh - strategy: always collect an om token
• Drought of Spirits - strategy: collect an om token if the plan was to collect an inner journey card
• Bountiful Bhandara  - strategy: at the start of turn, check if current targeted journey card requirements are met. if not, check if any other journey card requirements are met (since player got 2 additional cubes this turn) AND can travel to the journey card location with current hand cards. if yes, then collect this card.
• Triathlon - if moving to collect an om token or energy cube this round, check if player can use a combination of 1,2 and 3 travel cards (using in hand and faceup travel cards) to reach this location and collect any required faceup cards if needed for it.
• Riots - no need to handle explicitly
• Om Meditation - no need to handle explicitly
• Merchant’s Midas
• Professor’s Insight
• Pilgrim’s Grace
• Engineer’s Precision
• Frozen North - this event limits player movements if starting in north. follow the movement restrictions
• Solar South - if the targeted location is in south AND player has > 0 energy cubes AND is targeting om tokens or energy cube collection then choose a location for the same om token / energy collection but in a different region.
• Himalayan NE
• Central Heart
• Excess Baggage
• Heritage Site Renovations
• Spirit of Seva
• Pushkar Holy Dip
• Parikrama in the Clouds
• Cultural Exchange