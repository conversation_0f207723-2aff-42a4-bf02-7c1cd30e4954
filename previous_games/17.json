{"description": "<PERSON><PERSON> got bonus cube maxxing her to 5 cubes, unable to convert and unable to converge", "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 36, "hand": [{"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}], "energyCubes": ["artha"], "omTemp": [1], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "outerScore": 53, "innerScore": 49, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 2, "hand": [{"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}], "energyCubes": ["gnana", "artha", "gnana"], "omTemp": [], "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "outerScore": 85, "innerScore": 5, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 33, "hand": [{"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}], "energyCubes": ["bhakti", "artha", "artha"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}], "outerScore": 54, "innerScore": 53, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}}], "started": true, "turnIndex": 2, "roundCount": 16, "travelDeck": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "eventDeck": [{"id": "E6", "type": "wildCube"}, {"id": "E1", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "journeyDeckOuter": [{"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "travelDiscard": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "faceUpEvent": [{"id": "E9", "type": "wildCube"}, {"id": "E2", "type": "extraHop"}, {"id": "E8", "type": "wildCube"}, {"id": "E3", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}], "faceUpJourneyOuter": [{"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "locationCubes": {"1": "gnana", "5": "karma", "6": "gnana", "7": "artha", "9": "gnana", "11": "bhakti", "12": "gnana", "13": "gnana", "16": "bhakti", "19": "bhakti", "23": "bhakti", "24": "gnana", "43": "bhakti", "44": "karma", "46": "bhakti", "47": "artha", "undefined": "bhakti"}, "locationOm": {}, "finalRound": true, "finalRoundStarter": 2, "finalRoundEnd": 1, "omTokenVictory": false, "omTokenVictor": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358470116, "data": {"eventId": "global-event-29", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358567616, "data": {"cardType": "travel", "card": {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358567616, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358567616, "data": {"cardType": "travel", "card": {"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358567616, "data": {"cardType": "travel", "card": {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358567616, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358567616, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "move", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358569872, "data": {"path": [62, 11, 10, 49], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1745358572872, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745358575876, "data": {"nextPlayerId": "YotmgFF6VeLaWvoYAAAF", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745358616391, "data": {"cardType": "travel", "pickedCards": [{"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745358622763, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1745358635582, "data": {"path": [64, 15, 18, 53], "travelCards": ["T21"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745358648440, "data": {"nextPlayerId": "BnHPKmWjGEDI0NZDAAAB", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745358697447, "data": {"path": [65, 31, 30, 55], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745358788274, "data": {"cardType": "travel", "pickedCards": [{"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1745358792271, "data": {"cardType": "travel", "pickedCards": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1745358825004, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "diwali_distraction_points", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745358825004, "data": {"previousInnerScore": 0, "newInnerScore": 5, "pointsGained": 5}}, {"type": "diwali_distraction_points", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745358825004, "data": {"previousInnerScore": 0, "newInnerScore": 5, "pointsGained": 5}}, {"type": "diwali_distraction_points", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745358825004, "data": {"previousInnerScore": 0, "newInnerScore": 5, "pointsGained": 5}}, {"type": "endTurn", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745358825004, "data": {"nextPlayerId": "HuSZ-Ne57dB5XKh6AAAD", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745358826750, "data": {"path": [49, 10, 11, 7], "travelCards": ["T4", "T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1745358829756, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745358832758, "data": {"nextPlayerId": "YotmgFF6VeLaWvoYAAAF", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745358897772, "data": {"path": [53, 18, 15, 5, 52], "travelCards": ["T23", "T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745358915448, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1745358930962, "data": {"cardType": "travel", "pickedCards": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745358933092, "data": {"nextPlayerId": "BnHPKmWjGEDI0NZDAAAB", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745359002868, "data": {"path": [55, 30, 31, 32, 58], "travelCards": ["T24", "T16"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745359027184, "data": {"cardType": "travel", "pickedCards": [{"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1745359036765, "data": {"cardType": "travel", "pickedCards": [{"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1745359041413, "data": {"eventId": "global-event-8", "eventName": "Triathlon", "eventEffect": "triathlon_bonus"}}, {"type": "endTurn", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745359041413, "data": {"nextPlayerId": "HuSZ-Ne57dB5XKh6AAAD", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745359043417, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1745359046422, "data": {"path": [7, 54], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745359049424, "data": {"nextPlayerId": "YotmgFF6VeLaWvoYAAAF", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745359128523, "data": {"path": [52, 5, 15, 64, 29], "travelCards": ["T17", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1745359135573, "data": {"journeyType": "outer", "journeyCardId": "JO14", "omRequirement": 1}}, {"type": "endTurn", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1745359141527, "data": {"nextPlayerId": "BnHPKmWjGEDI0NZDAAAB", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1745359155641, "data": {"path": [58, 32, 36], "travelCards": ["T20"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1745359180124, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1745359183019, "data": {"eventId": "global-event-22", "eventName": "Up and Over", "eventEffect": "up_and_over_reward"}}, {"type": "endTurn", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745359183019, "data": {"nextPlayerId": "HuSZ-Ne57dB5XKh6AAAD", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745359185513, "data": {"vehicle": "helicopter", "effect": "up_and_over_reward", "outerPoints": 5}}, {"type": "move", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745359185513, "data": {"path": [54, 7, 11, 12, 50], "travelCards": ["T9", "T13"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1745359188517, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745359191518, "data": {"nextPlayerId": "YotmgFF6VeLaWvoYAAAF", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745359289329, "data": {"cardType": "travel", "drawnCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745359300829, "data": {"cardType": "travel", "drawnCards": [{"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "travel_card_reward", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745359323157, "data": {"vehicle": "helicopter", "effect": "up_and_over_reward", "outerPoints": 5}}, {"type": "move", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1745359323157, "data": {"path": [29, 64, 63, 21], "travelCards": ["T21"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "YotmgFF6VeLaWvoYAAAF", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745359329251, "data": {"nextPlayerId": "BnHPKmWjGEDI0NZDAAAB", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745359363236, "data": {"path": [36, 32, 31, 65, 66, 42], "travelCards": ["T11", "T19"], "extraHopCards": []}}, {"type": "trade", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745359370117, "data": {"cubesTraded": ["karma"], "cubeReceived": "gnana", "count": 1}}, {"type": "collectJourney", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1745359374034, "data": {"journeyType": "inner", "journeyCardId": "JI21", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1745359386141, "data": {"eventId": "global-event-4", "eventName": "Drought of Spirits", "eventEffect": "no_inner_journey_cards"}}, {"type": "endTurn", "playerId": "BnHPKmWjGEDI0NZDAAAB", "playerName": "Nat<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745359386141, "data": {"nextPlayerId": "HuSZ-Ne57dB5XKh6AAAD", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745359387787, "data": {"cardType": "travel", "pickedCards": [{"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1745359390791, "data": {"path": [50, 12, 11, 62, 63, 21, 20, 51], "travelCards": ["T18", "T7", "T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "HuSZ-Ne57dB5XKh6AAAD", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745359393792, "data": {"nextPlayerId": "YotmgFF6VeLaWvoYAAAF", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 1, "timestamp": 1745359839482, "data": {"timestamp": 1745359839482, "loadedStateRoundCount": 4, "playerCount": 3}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745359876823, "data": {"cardType": "travel", "pickedCards": [{"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745359932641, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1745359977354, "data": {"path": [21, 20], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1745359982346, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1745360013442, "data": {"path": [42, 43, 60], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1745360049115, "data": {"cardType": "travel", "drawnCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1745360050974, "data": {"cardType": "travel", "drawnCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickFromTop": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1745360053669, "data": {"eventId": "global-event-32", "eventName": "Central Heart", "eventEffect": "central_heart"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745360053670, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745360055267, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1745360058271, "data": {"path": [51, 20, 21, 22], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745360061273, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745360100692, "data": {"path": [20, 21, 28, 37], "travelCards": ["T22"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1745360106264, "data": {"journeyType": "outer", "journeyCardId": "JO20", "omRequirement": 1}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1745360111030, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1745360247653, "data": {"path": [60, 43, 42, 48], "travelCards": ["T14", "T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1745360262860, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1745360266806, "data": {"cardType": "travel", "pickedCards": [{"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1745360268956, "data": {"eventId": "global-event-7", "eventName": "Election Campaigns", "eventEffect": "double_trade_no_travel"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1745360268956, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1745360270960, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745360273961, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745360429534, "data": {"cardType": "travel", "pickedCards": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1745360436112, "data": {"cardType": "travel", "pickedCards": [{"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1745360449082, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1745360490901, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1745360492803, "data": {"eventId": "global-event-15", "eventName": "Biker Gang", "eventEffect": "biker_gang_reward"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1745360492803, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1745360494433, "data": {"path": [22, 21, 28], "travelCards": ["T15", "T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745360497435, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745360557152, "data": {"vehicle": "motorbike", "effect": "biker_gang_reward", "outerPoints": 5}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745360557152, "data": {"path": [37, 65, 66, 42, 48, 57], "travelCards": ["T20", "T22"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745360576277, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1745360583058, "data": {"cardType": "travel", "pickedCards": [{"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1745360585209, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1745360621924, "data": {"path": [48, 42, 66, 62, 11, 10], "travelCards": ["T10", "T17"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1745360630886, "data": {"journeyType": "inner", "journeyCardId": "JI6", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1745360638336, "data": {"eventId": "global-event-17", "eventName": "Top Gear", "eventEffect": "top_gear_reward"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745360638336, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745360640723, "data": {"path": [28, 18], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1745360643725, "data": {"journeyType": "outer", "journeyCardId": "JO9", "omRequirement": 1}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745360646727, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745360695086, "data": {"path": [57, 47, 3, 56], "travelCards": ["T21"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1745360741055, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1745360749244, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1745360832265, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1745360842308, "data": {"vehicle": "car", "effect": "top_gear_reward", "outerPoints": 5}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1745360842308, "data": {"path": [10, 11, 62, 63, 21, 20, 51], "travelCards": ["T23", "T6", "T4"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1745360848716, "data": {"eventId": "global-event-3", "eventName": "<PERSON><PERSON>", "eventEffect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745360848716, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745360850959, "data": {"cardType": "travel", "pickedCards": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1745360853963, "data": {"path": [18, 28, 21, 22, 23, 59], "travelCards": ["T24", "T18"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745360856964, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745361039750, "data": {"path": [56, 3, 4], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1745361080589, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1745361084502, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1745361091850, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1745361096543, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1745361129937, "data": {"path": [51, 20, 21, 63, 25], "travelCards": ["T11", "T3"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1745361144886, "data": {"eventId": "global-event-11", "eventName": "Pedal Power", "eventEffect": "pedal_power_reward"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745361144886, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745361147350, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1745361150353, "data": {"path": [59, 23, 38], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745361153354, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1745361181770, "data": {"path": [4, 61, 62, 13, 14], "travelCards": ["T12", "T13"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1745361238719, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1745361247405, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1745361249440, "data": {"eventId": "global-event-18", "eventName": "Hop on Hop off", "eventEffect": "hop_on_hop_off_reward"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745361249440, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745361251720, "data": {"path": [38, 39], "travelCards": ["T16"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1745361254723, "data": {"cardType": "travel", "pickedCards": [{"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1745361257724, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1745361335694, "data": {"cardType": "travel", "drawnCards": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1745361341678, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1745361368724, "data": {"path": [14, 13, 34, 33, 29, 28], "travelCards": ["T22", "T8"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1745361381569, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1745361438499, "data": {"path": [25, 27], "travelCards": ["T14"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1745361517234, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1745361518535, "data": {"eventId": "global-event-13", "eventName": "<PERSON><PERSON> of Valor", "eventEffect": "steed_of_valor_reward"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1745361518535, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1745361520175, "data": {"path": [39, 65, 31], "travelCards": ["T17"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1745361523178, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1745361526180, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "trade", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1745361543058, "data": {"cubesTraded": ["karma"], "cubeReceived": "artha", "count": 1}}, {"type": "collectJourney", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1745361546679, "data": {"journeyType": "outer", "journeyCardId": "JO13", "omRequirement": 2}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745361598326, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745361659851, "data": {"vehicle": "horse", "effect": "steed_of_valor_reward", "outerPoints": 5}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745361659851, "data": {"path": [27, 25, 24, 63, 64, 29, 33], "travelCards": ["T23", "T2", "T7"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745361696898, "data": {"cardType": "travel", "drawnCards": [{"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1745361699672, "data": {"cardType": "travel", "drawnCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickFromTop": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1745361703371, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1745361703371, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1745361705611, "data": {"path": [31, 30], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1745361708613, "data": {"journeyType": "outer", "journeyCardId": "JO15", "omRequirement": 1}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1745361711616, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1745361738411, "data": {"cardType": "travel", "pickedCards": [{"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1745361743047, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745361745709, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745361776520, "data": {"path": [33, 34], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1745361789770, "data": {"journeyType": "outer", "journeyCardId": "JO18", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1745361798379, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1745361798379, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 14, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1745361800812, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1745361803815, "data": {"path": [30, 31, 32], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1745361806817, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 14, "turnCount": null}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1745361924744, "data": {"path": [28, 21, 63, 64], "travelCards": ["T21"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 2, "timestamp": 1745361948850, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 14, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 14, "turnIndex": 2, "timestamp": 1745361990279, "data": {"cardType": "travel", "pickedCards": [{"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 14, "turnIndex": 2, "timestamp": 1745361996905, "data": {"path": [34, 13, 62, 64, 15], "travelCards": ["T18", "T20"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 15, "turnIndex": 0, "timestamp": 1745362003356, "data": {"eventId": "global-event-30", "eventName": "Breezy East", "eventEffect": "breezy_east"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1745362003356, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 15, "turnCount": null}}, {"type": "region_based_reward", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1745362005765, "data": {"region": "East", "vehicle": "trek", "effect": "breezy_east", "innerPoints": 7}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1745362005765, "data": {"path": [32, 31, 45], "travelCards": ["T15", "T14"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1745362008768, "data": {"cardType": "travel", "pickedCards": [{"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 1, "timestamp": 1745362011770, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 15, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 1, "timestamp": 1745362051429, "data": {"cardType": "travel", "pickedCards": [{"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 1, "timestamp": 1745362067210, "data": {"path": [64, 61, 4, 3], "travelCards": ["T23"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 2, "timestamp": 1745362071065, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 15, "turnCount": null}}, {"type": "move", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 15, "turnIndex": 2, "timestamp": 1745362180380, "data": {"path": [15, 64, 29, 33], "travelCards": ["T24"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 15, "turnIndex": 2, "timestamp": 1745362188796, "data": {"journeyType": "outer", "journeyCardId": "JO17", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 15, "turnIndex": 2, "timestamp": 1745362188796, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 107, "finalRoundStarter": 2, "finalRoundEnd": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 16, "turnIndex": 0, "timestamp": 1745362196332, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "endTurn", "playerId": "dcBFZJt7Kdl1Gp9VAAAD", "playerName": "Nat<PERSON><PERSON>", "roundCount": 16, "turnIndex": 0, "timestamp": 1745362196332, "data": {"nextPlayerId": "dua9PaiTMwxURorLAAAF", "newRound": true, "roundCount": 16, "turnCount": null}}, {"type": "move", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 16, "turnIndex": 0, "timestamp": 1745362198532, "data": {"path": [45, 31, 32, 36], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 16, "turnIndex": 0, "timestamp": 1745362201532, "data": {"journeyType": "inner", "journeyCardId": "JI17", "omRequirement": 1}}, {"type": "endTurn", "playerId": "dua9PaiTMwxURorLAAAF", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 16, "turnIndex": 1, "timestamp": 1745362204534, "data": {"nextPlayerId": "PhRxDHy350_33pcqAAAB", "newRound": false, "roundCount": 16, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 16, "turnIndex": 1, "timestamp": 1745362219723, "data": {"vehicle": "camel", "effect": "desert_caravan_reward", "outerPoints": 5}}, {"type": "move", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 16, "turnIndex": 1, "timestamp": 1745362219723, "data": {"path": [3, 2], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 16, "turnIndex": 1, "timestamp": 1745362239484, "data": {"cardType": "travel", "pickedCards": [{"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "PhRxDHy350_33pcqAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 16, "turnIndex": 2, "timestamp": 1745362241909, "data": {"nextPlayerId": "dcBFZJt7Kdl1Gp9VAAAD", "newRound": false, "roundCount": 16, "turnCount": null}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 16, "turnIndex": 2, "timestamp": 1745362241912, "data": {"winner": {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "outerScore": 54, "innerScore": 53, "totalScore": 107, "omTotal": 4, "winByOm": false, "winByScore": true}, "winCondition": "SCORE_THRESHOLD", "totalRounds": 16, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 53, "innerScore": 49, "totalScore": 102, "omTotal": 4}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "outerScore": 85, "innerScore": 5, "totalScore": 90, "omTotal": 4}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "outerScore": 54, "innerScore": 53, "totalScore": 107, "omTotal": 4}]}}], "roundSummaries": [{"roundNumber": 1, "timestamp": 1745358825003, "players": [{"id": "HuSZ-Ne57dB5XKh6AAAD", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 49, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "YotmgFF6VeLaWvoYAAAF", "name": "<PERSON><PERSON><PERSON>", "position": 53, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}, {"id": "BnHPKmWjGEDI0NZDAAAB", "name": "Nat<PERSON><PERSON>", "position": 55, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 0, "total": 0}, "collectedJourneys": 0, "gained": null}], "faceUpCards": {"travel": [{"id": "T8", "value": 2, "vehicle": "motorbike"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T19", "value": 2, "vehicle": "rickshaw"}, {"id": "T20", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO14", "locationId": 29, "requiredCubes": {}, "points": 20}, {"id": "JO20", "locationId": 37, "requiredCubes": {}, "points": 20}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI21", "locationId": 42, "requiredCubes": {}, "points": 24}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}]}, "globalEvent": {"id": "global-event-29", "name": "Solar South", "effect": "solar_south"}}, {"roundNumber": 2, "timestamp": 1745359041412, "players": [{"id": "HuSZ-Ne57dB5XKh6AAAD", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 7, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": null}, {"id": "YotmgFF6VeLaWvoYAAAF", "name": "<PERSON><PERSON><PERSON>", "position": 52, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "BnHPKmWjGEDI0NZDAAAB", "name": "Nat<PERSON><PERSON>", "position": 58, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T14", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO14", "locationId": 29, "requiredCubes": {}, "points": 20}, {"id": "JO20", "locationId": 37, "requiredCubes": {}, "points": 20}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI21", "locationId": 42, "requiredCubes": {}, "points": 24}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}]}, "globalEvent": {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "effect": "gain_5_inner_no_cube_pickup"}}, {"roundNumber": 3, "timestamp": 1745359183018, "players": [{"id": "HuSZ-Ne57dB5XKh6AAAD", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 54, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "YotmgFF6VeLaWvoYAAAF", "name": "<PERSON><PERSON><PERSON>", "position": 29, "hand": {"total": 1, "travel": 1}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 20, "inner": 5, "total": 25}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "BnHPKmWjGEDI0NZDAAAB", "name": "Nat<PERSON><PERSON>", "position": 36, "hand": {"total": 4, "travel": 4}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 5, "total": 5}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T7", "value": 2, "vehicle": "rickshaw"}, {"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T2", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO20", "locationId": 37, "requiredCubes": {}, "points": 20}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI21", "locationId": 42, "requiredCubes": {}, "points": 24}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}]}, "globalEvent": {"id": "global-event-8", "name": "Triathlon", "effect": "triathlon_bonus"}}, {"roundNumber": 4, "timestamp": 1745359386140, "players": [{"id": "HuSZ-Ne57dB5XKh6AAAD", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 50, "hand": {"total": 2, "travel": 2}, "omTemp": 3, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 5, "total": 10}, "collectedJourneys": 0, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "YotmgFF6VeLaWvoYAAAF", "name": "<PERSON><PERSON><PERSON>", "position": 21, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "BnHPKmWjGEDI0NZDAAAB", "name": "Nat<PERSON><PERSON>", "position": 42, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 29, "total": 29}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T12", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO20", "locationId": 37, "requiredCubes": {}, "points": 20}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-22", "name": "Up and Over", "effect": "up_and_over_reward"}}, {"roundNumber": 5, "timestamp": 1745360053669, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 51, "hand": {"total": 1, "travel": 1}, "omTemp": 3, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 5, "total": 10}, "collectedJourneys": 0, "gained": null}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 20, "hand": {"total": 3, "travel": 3}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 2, "artha": 0}, "scores": {"outer": 25, "inner": 5, "total": 30}, "collectedJourneys": 1, "gained": null}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 60, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 29, "total": 29}, "collectedJourneys": 1, "gained": null}], "faceUpCards": {"travel": [{"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T1", "value": 1, "vehicle": "camel"}, {"id": "T24", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO20", "locationId": 37, "requiredCubes": {}, "points": 20}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-4", "name": "Drought of Spirits", "effect": "no_inner_journey_cards"}}, {"roundNumber": 6, "timestamp": 1745360268955, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 22, "hand": {"total": 2, "travel": 2}, "omTemp": 3, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 5, "inner": 5, "total": 10}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 37, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 45, "inner": 5, "total": 50}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": 1}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 48, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 29, "total": 29}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T3", "value": 1, "vehicle": "trek"}, {"id": "T1", "value": 1, "vehicle": "camel"}, {"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T22", "value": 3, "vehicle": "boat"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO13", "locationId": 28, "requiredCubes": {}, "points": 30}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-32", "name": "Central Heart", "effect": "central_heart"}}, {"roundNumber": 7, "timestamp": 1745360492802, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 22, "hand": {"total": 4, "travel": 4}, "omTemp": 3, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 0}, "scores": {"outer": 5, "inner": 5, "total": 10}, "collectedJourneys": 0, "gained": null}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 37, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 45, "inner": 5, "total": 50}, "collectedJourneys": 2, "gained": null}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 48, "hand": {"total": 4, "travel": 4}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 1}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 29, "total": 29}, "collectedJourneys": 1, "gained": null}], "faceUpCards": {"travel": [{"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T19", "value": 2, "vehicle": "rickshaw"}, {"id": "T12", "value": 3, "vehicle": "truck"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO13", "locationId": 28, "requiredCubes": {}, "points": 30}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI6", "locationId": 10, "requiredCubes": {}, "points": 24}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-7", "name": "Election Campaigns", "effect": "double_trade_no_travel"}}, {"roundNumber": 8, "timestamp": 1745360638336, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 28, "hand": {"total": 2, "travel": 2}, "omTemp": 3, "omSlots": {"outer": 0, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 5, "inner": 5, "total": 10}, "collectedJourneys": 0, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 57, "hand": {"total": 4, "travel": 4}, "omTemp": 1, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 50, "inner": 5, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 10, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 0, "inner": 2}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 0, "inner": 53, "total": 53}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T13", "value": 1, "vehicle": "camel"}, {"id": "T11", "value": 3, "vehicle": "train"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO9", "locationId": 18, "requiredCubes": {}, "points": 24}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO13", "locationId": 28, "requiredCubes": {}, "points": 30}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-15", "name": "Biker Gang", "effect": "biker_gang_reward"}}, {"roundNumber": 9, "timestamp": 1745360848716, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 18, "hand": {"total": 1, "travel": 1}, "omTemp": 2, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 29, "inner": 5, "total": 34}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": 1}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 56, "hand": {"total": 4, "travel": 4}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 0, "karma": 1, "artha": 1}, "scores": {"outer": 50, "inner": 5, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 51, "hand": {"total": 1, "travel": 1}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 2}, "energyCubes": {"total": 0, "bhakti": 0, "gnana": 0, "karma": 0, "artha": 0}, "scores": {"outer": 5, "inner": 53, "total": 58}, "collectedJourneys": 2, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T18", "value": 2, "vehicle": "car"}, {"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T8", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO13", "locationId": 28, "requiredCubes": {}, "points": 30}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-17", "name": "Top Gear", "effect": "top_gear_reward"}}, {"roundNumber": 10, "timestamp": 1745361144886, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 59, "hand": {"total": 1, "travel": 1}, "omTemp": 3, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 29, "inner": 12, "total": 41}, "collectedJourneys": 1, "gained": {"omTemp": 1, "energyCubes": null, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 4, "hand": {"total": 4, "travel": 4}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 0, "gnana": 1, "karma": 2, "artha": 1}, "scores": {"outer": 50, "inner": 5, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 2, "bhakti": null, "gnana": 1, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 25, "hand": {"total": 1, "travel": 1}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 2}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 5, "inner": 53, "total": 58}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 2, "bhakti": 1, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T9", "value": 3, "vehicle": "helicopter"}, {"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T14", "value": 1, "vehicle": "horse"}, {"id": "T5", "value": 2, "vehicle": "bus"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO13", "locationId": 28, "requiredCubes": {}, "points": 30}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-3", "name": "<PERSON><PERSON>", "effect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"roundNumber": 11, "timestamp": 1745361249440, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 38, "hand": {"total": 2, "travel": 2}, "omTemp": 3, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 1, "karma": 1, "artha": 0}, "scores": {"outer": 29, "inner": 12, "total": 41}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 14, "hand": {"total": 2, "travel": 2}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 0, "gnana": 1, "karma": 3, "artha": 1}, "scores": {"outer": 50, "inner": 5, "total": 55}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 25, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 2}, "energyCubes": {"total": 2, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 1}, "scores": {"outer": 5, "inner": 53, "total": 58}, "collectedJourneys": 2, "gained": null}], "faceUpCards": {"travel": [{"id": "T2", "value": 1, "vehicle": "horse"}, {"id": "T17", "value": 2, "vehicle": "bus"}, {"id": "T23", "value": 3, "vehicle": "train"}, {"id": "T4", "value": 1, "vehicle": "cycle"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO13", "locationId": 28, "requiredCubes": {}, "points": 30}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-11", "name": "Pedal Power", "effect": "pedal_power_reward"}}, {"roundNumber": 12, "timestamp": 1745361518534, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 39, "hand": {"total": 2, "travel": 2}, "omTemp": 3, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 0, "gnana": 1, "karma": 1, "artha": 1}, "scores": {"outer": 29, "inner": 12, "total": 41}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 28, "hand": {"total": 2, "travel": 2}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 0, "gnana": 1, "karma": 3, "artha": 1}, "scores": {"outer": 50, "inner": 5, "total": 55}, "collectedJourneys": 2, "gained": null}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 27, "hand": {"total": 4, "travel": 4}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 2}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 2}, "scores": {"outer": 5, "inner": 53, "total": 58}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T4", "value": 1, "vehicle": "cycle"}, {"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T15", "value": 1, "vehicle": "trek"}, {"id": "T10", "value": 3, "vehicle": "boat"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO13", "locationId": 28, "requiredCubes": {}, "points": 30}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-18", "name": "Hop on Hop off", "effect": "hop_on_hop_off_reward"}}, {"roundNumber": 13, "timestamp": 1745361703370, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 31, "hand": {"total": 3, "travel": 3}, "omTemp": 3, "omSlots": {"outer": 1, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 0, "gnana": 1, "karma": 1, "artha": 2}, "scores": {"outer": 29, "inner": 12, "total": 41}, "collectedJourneys": 1, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 28, "hand": {"total": 2, "travel": 2}, "omTemp": 0, "omSlots": {"outer": 3, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 80, "inner": 5, "total": 85}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 33, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 0, "inner": 2}, "energyCubes": {"total": 4, "bhakti": 1, "gnana": 0, "karma": 1, "artha": 2}, "scores": {"outer": 10, "inner": 53, "total": 63}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T10", "value": 3, "vehicle": "boat"}, {"id": "T21", "value": 3, "vehicle": "helicopter"}, {"id": "T20", "value": 2, "vehicle": "motorbike"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO15", "locationId": 30, "requiredCubes": {}, "points": 24}, {"id": "JO18", "locationId": 34, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "effect": "steed_of_valor_reward"}}, {"roundNumber": 14, "timestamp": 1745361798378, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 30, "hand": {"total": 2, "travel": 2}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 1, "karma": 0, "artha": 1}, "scores": {"outer": 53, "inner": 12, "total": 65}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": null, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": 1}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 28, "hand": {"total": 4, "travel": 4}, "omTemp": 0, "omSlots": {"outer": 3, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 80, "inner": 5, "total": 85}, "collectedJourneys": 3, "gained": null}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 34, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 2}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 2}, "scores": {"outer": 34, "inner": 53, "total": 87}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T6", "value": 2, "vehicle": "car"}, {"id": "T20", "value": 2, "vehicle": "motorbike"}, {"id": "T14", "value": 1, "vehicle": "horse"}, {"id": "T22", "value": 3, "vehicle": "boat"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}, {"id": "JO22", "locationId": 43, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-6", "name": "Turbulent Skies", "effect": "no_airport_travel"}}, {"roundNumber": 15, "timestamp": 1745362003355, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 32, "hand": {"total": 3, "travel": 3}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 4, "bhakti": 1, "gnana": 2, "karma": 0, "artha": 1}, "scores": {"outer": 53, "inner": 12, "total": 65}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": 1, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 64, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 3, "inner": 0}, "energyCubes": {"total": 1, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 0}, "scores": {"outer": 80, "inner": 5, "total": 85}, "collectedJourneys": 3, "gained": null}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 15, "hand": {"total": 2, "travel": 2}, "omTemp": 1, "omSlots": {"outer": 1, "inner": 2}, "energyCubes": {"total": 4, "bhakti": 1, "gnana": 0, "karma": 1, "artha": 2}, "scores": {"outer": 34, "inner": 53, "total": 87}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": 1, "artha": null}, "journeyCards": null}}], "faceUpCards": {"travel": [{"id": "T22", "value": 3, "vehicle": "boat"}, {"id": "T23", "value": 3, "vehicle": "train"}, {"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T17", "value": 2, "vehicle": "bus"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO17", "locationId": 33, "requiredCubes": {}, "points": 20}, {"id": "JO22", "locationId": 43, "requiredCubes": {}, "points": 27}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-28", "name": "<PERSON>", "effect": "sandy_west"}}, {"roundNumber": 16, "timestamp": 1745362196332, "players": [{"id": "dua9PaiTMwxURorLAAAF", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 45, "hand": {"total": 2, "travel": 2}, "omTemp": 2, "omSlots": {"outer": 2, "inner": 0}, "energyCubes": {"total": 5, "bhakti": 2, "gnana": 2, "karma": 0, "artha": 1}, "scores": {"outer": 53, "inner": 19, "total": 72}, "collectedJourneys": 2, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": 1, "gnana": null, "karma": null, "artha": null}, "journeyCards": null}}, {"id": "PhRxDHy350_33pcqAAAB", "name": "<PERSON><PERSON><PERSON>", "position": 3, "hand": {"total": 3, "travel": 3}, "omTemp": 0, "omSlots": {"outer": 3, "inner": 0}, "energyCubes": {"total": 2, "bhakti": 0, "gnana": 1, "karma": 0, "artha": 1}, "scores": {"outer": 80, "inner": 5, "total": 85}, "collectedJourneys": 3, "gained": {"omTemp": null, "energyCubes": {"total": 1, "bhakti": null, "gnana": null, "karma": null, "artha": 1}, "journeyCards": null}}, {"id": "dcBFZJt7Kdl1Gp9VAAAD", "name": "Nat<PERSON><PERSON>", "position": 33, "hand": {"total": 1, "travel": 1}, "omTemp": 0, "omSlots": {"outer": 2, "inner": 2}, "energyCubes": {"total": 3, "bhakti": 1, "gnana": 0, "karma": 0, "artha": 2}, "scores": {"outer": 54, "inner": 53, "total": 107}, "collectedJourneys": 4, "gained": {"omTemp": null, "energyCubes": null, "journeyCards": 1}}], "faceUpCards": {"travel": [{"id": "T22", "value": 3, "vehicle": "boat"}, {"id": "T16", "value": 1, "vehicle": "cycle"}, {"id": "T5", "value": 2, "vehicle": "bus"}, {"id": "T2", "value": 1, "vehicle": "horse"}], "journeyOuter": [{"id": "JO12", "locationId": 27, "requiredCubes": {}, "points": 27}, {"id": "JO5", "locationId": 11, "requiredCubes": {}, "points": 27}, {"id": "JO22", "locationId": 43, "requiredCubes": {}, "points": 27}, {"id": "JO2", "locationId": 5, "requiredCubes": {}, "points": 24}], "journeyInner": [{"id": "JI17", "locationId": 36, "requiredCubes": {}, "points": 30}, {"id": "JI7", "locationId": 14, "requiredCubes": {}, "points": 24}, {"id": "JI2", "locationId": 3, "requiredCubes": {}, "points": 30}, {"id": "JI22", "locationId": 48, "requiredCubes": {}, "points": 30}]}, "globalEvent": {"id": "global-event-30", "name": "Breezy East", "effect": "breezy_east"}}], "characterDeck": [{"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}], "energyCubePile": {"artha": 7, "karma": 10, "gnana": 5, "bhakti": 5}, "currentGlobalEvent": {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, "globalEventDeck": [{"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}], "globalEventDiscard": [{"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}], "nameMode": false}