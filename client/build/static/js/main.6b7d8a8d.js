/*! For license information please see main.6b7d8a8d.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},153:(e,t,n)=>{var r=n(43),o=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,i={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)a.call(t,r)&&!s.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:o,type:e,key:c,ref:u,props:i,_owner:l.current}}t.Fragment=i,t.jsx=c,t.jsxs=c},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var b=x.prototype=new v;b.constructor=x,m(b,y.prototype),b.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},C={key:!0,ref:!0,__self:!0,__source:!0};function j(e,t,r){var o,i={},a=null,l=null;if(null!=t)for(o in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(a=""+t.key),t)k.call(t,o)&&!C.hasOwnProperty(o)&&(i[o]=t[o]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];i.children=c}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===i[o]&&(i[o]=s[o]);return{$$typeof:n,type:e,key:a,ref:l,props:i,_owner:S.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function N(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function T(e,t,o,i,a){var l=typeof e;"undefined"!==l&&"boolean"!==l||(e=null);var s=!1;if(null===e)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return a=a(s=e),e=""===i?"."+N(s,0):i,w(a)?(o="",null!=e&&(o=e.replace(_,"$&/")+"/"),T(a,t,o,"",(function(e){return e}))):null!=a&&(E(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,o+(!a.key||s&&s.key===a.key?"":(""+a.key).replace(_,"$&/")+"/")+e)),t.push(a)),1;if(s=0,i=""===i?".":i+":",w(e))for(var c=0;c<e.length;c++){var u=i+N(l=e[c],c);s+=T(l,t,o,u,a)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(l=e.next()).done;)s+=T(l=l.value,t,o,u=i+N(l,c++),a);else if("object"===l)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function R(e,t,n){if(null==e)return e;var r=[],o=0;return T(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function $(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var P={current:null},O={transition:null},L={ReactCurrentDispatcher:P,ReactCurrentBatchConfig:O,ReactCurrentOwner:S};function I(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=o,t.Profiler=a,t.PureComponent=x,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.act=I,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=m({},e.props),i=e.key,a=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,l=S.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)k.call(t,c)&&!C.hasOwnProperty(c)&&(o[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)o.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];o.children=s}return{$$typeof:n,type:e.type,key:i,ref:a,props:o,_owner:l}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=j,t.createFactory=function(e){var t=j.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:$}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=O.transition;O.transition={};try{e()}finally{O.transition=t}},t.unstable_act=I,t.useCallback=function(e,t){return P.current.useCallback(e,t)},t.useContext=function(e){return P.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return P.current.useDeferredValue(e)},t.useEffect=function(e,t){return P.current.useEffect(e,t)},t.useId=function(){return P.current.useId()},t.useImperativeHandle=function(e,t,n){return P.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return P.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return P.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return P.current.useMemo(e,t)},t.useReducer=function(e,t,n){return P.current.useReducer(e,t,n)},t.useRef=function(e){return P.current.useRef(e)},t.useState=function(e){return P.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return P.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return P.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<i(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else{if(!(c<o&&0>i(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var l=Date,s=l.now();t.unstable_now=function(){return l.now()-s}}var c=[],u=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function b(e){for(var t=r(u);null!==t;){if(null===t.callback)o(u);else{if(!(t.startTime<=e))break;o(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(g=!1,b(e),!m)if(null!==r(c))m=!0,O(k);else{var t=r(u);null!==t&&L(w,t.startTime-e)}}function k(e,n){m=!1,g&&(g=!1,v(E),E=-1),h=!0;var i=p;try{for(b(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!T());){var a=f.callback;if("function"===typeof a){f.callback=null,p=f.priorityLevel;var l=a(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof l?f.callback=l:f===r(c)&&o(c),b(n)}else o(c);f=r(c)}if(null!==f)var s=!0;else{var d=r(u);null!==d&&L(w,d.startTime-n),s=!1}return s}finally{f=null,p=i,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,C=!1,j=null,E=-1,_=5,N=-1;function T(){return!(t.unstable_now()-N<_)}function R(){if(null!==j){var e=t.unstable_now();N=e;var n=!0;try{n=j(!0,e)}finally{n?S():(C=!1,j=null)}}else C=!1}if("function"===typeof x)S=function(){x(R)};else if("undefined"!==typeof MessageChannel){var $=new MessageChannel,P=$.port2;$.port1.onmessage=R,S=function(){P.postMessage(null)}}else S=function(){y(R,0)};function O(e){j=e,C||(C=!0,S())}function L(e,n){E=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,O(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?a+i:a:i=a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=1073741823;break;case 4:l=1e4;break;default:l=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:i,expirationTime:l=i+l,sortIndex:-1},i>a?(e.sortIndex=i,n(u,e),null===r(c)&&e===r(u)&&(g?(v(E),E=-1):g=!0,L(w,i-a))):(e.sortIndex=l,n(c,e),m||h||(m=!0,O(k))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},358:(e,t)=>{const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,o=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,i=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,l=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function c(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),o=n(853);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,l={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(l[e]=t,e=0;e<t.length;e++)a.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,o,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function x(e,t,n,r){var o=g.hasOwnProperty(t)?g[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var b=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),_=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),R=Symbol.for("react.suspense_list"),$=Symbol.for("react.memo"),P=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var O=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var L=Symbol.iterator;function I(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=L&&e[L]||e["@@iterator"])?e:null}var z,A=Object.assign;function F(e){if(void 0===z)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);z=t&&t[1]||""}return"\n"+z+e}var M=!1;function B(e,t){if(!e||M)return"";M=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&"string"===typeof c.stack){for(var o=c.stack.split("\n"),i=r.stack.split("\n"),a=o.length-1,l=i.length-1;1<=a&&0<=l&&o[a]!==i[l];)l--;for(;1<=a&&0<=l;a--,l--)if(o[a]!==i[l]){if(1!==a||1!==l)do{if(a--,0>--l||o[a]!==i[l]){var s="\n"+o[a].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=a&&0<=l);break}}}finally{M=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function D(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case j:return"Profiler";case C:return"StrictMode";case T:return"Suspense";case R:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case _:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case N:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case $:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case P:t=e._payload,e=e._init;try{return W(e(t))}catch(n){}}return null}function U(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===C?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function V(e){e._valueTracker||(e._valueTracker=function(e){var t=q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function J(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return A({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function K(e,t){null!=(t=t.checked)&&x(e,"checked",t,!1)}function X(e,t){K(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return A({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(i(92));if(te(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function ie(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ae(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,de=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ue(e,t)}))}:ue);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ye=A({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(i(62))}}function xe(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var be=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ce=null;function je(e){if(e=bo(e)){if("function"!==typeof ke)throw Error(i(280));var t=e.stateNode;t&&(t=ko(t),ke(e.stateNode,e.type,t))}}function Ee(e){Se?Ce?Ce.push(e):Ce=[e]:Se=e}function _e(){if(Se){var e=Se,t=Ce;if(Ce=Se=null,je(e),t)for(e=0;e<t.length;e++)je(t[e])}}function Ne(e,t){return e(t)}function Te(){}var Re=!1;function $e(e,t,n){if(Re)return e(t,n);Re=!0;try{return Ne(e,t,n)}finally{Re=!1,(null!==Se||null!==Ce)&&(Te(),_e())}}function Pe(e,t){var n=e.stateNode;if(null===n)return null;var r=ko(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Oe=!1;if(u)try{var Le={};Object.defineProperty(Le,"passive",{get:function(){Oe=!0}}),window.addEventListener("test",Le,Le),window.removeEventListener("test",Le,Le)}catch(ue){Oe=!1}function Ie(e,t,n,r,o,i,a,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var ze=!1,Ae=null,Fe=!1,Me=null,Be={onError:function(e){ze=!0,Ae=e}};function De(e,t,n,r,o,i,a,l,s){ze=!1,Ae=null,Ie.apply(Be,arguments)}function We(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ue(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if(We(e)!==e)throw Error(i(188))}function qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=We(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(r=o.return)){n=r;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===n)return He(o),e;if(a===r)return He(o),t;a=a.sibling}throw Error(i(188))}if(n.return!==r.return)n=o,r=a;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=a;break}if(s===r){l=!0,r=o,n=a;break}s=s.sibling}if(!l){for(s=a.child;s;){if(s===n){l=!0,n=a,r=o;break}if(s===r){l=!0,r=a,n=o;break}s=s.sibling}if(!l)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(e))?Ve(e):null}function Ve(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ve(e);if(null!==t)return t;e=e.sibling}return null}var Je=o.unstable_scheduleCallback,Qe=o.unstable_cancelCallback,Ye=o.unstable_shouldYield,Ge=o.unstable_requestPaint,Ke=o.unstable_now,Xe=o.unstable_getCurrentPriorityLevel,Ze=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,it=null;var at=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/st|0)|0},lt=Math.log,st=Math.LN2;var ct=64,ut=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,a=268435455&n;if(0!==a){var l=a&~o;0!==l?r=dt(l):0!==(i&=a)&&(r=dt(i))}else 0!==(a=n&~o)?r=dt(a):0!==i&&(r=dt(i));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(i=t&-t)||16===o&&0!==(4194240&i)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-at(t)),r|=e[n],t&=~o;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-at(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-at(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var xt=0;function bt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,kt,St,Ct,jt,Et=!1,_t=[],Nt=null,Tt=null,Rt=null,$t=new Map,Pt=new Map,Ot=[],Lt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function It(e,t){switch(e){case"focusin":case"focusout":Nt=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Rt=null;break;case"pointerover":case"pointerout":$t.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pt.delete(t.pointerId)}}function zt(e,t,n,r,o,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},null!==t&&(null!==(t=bo(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function At(e){var t=xo(e.target);if(null!==t){var n=We(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ue(n)))return e.blockedOn=t,void jt(e.priority,(function(){St(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Ft(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=bo(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);be=r,n.target.dispatchEvent(r),be=null,t.shift()}return!0}function Mt(e,t,n){Ft(e)&&n.delete(t)}function Bt(){Et=!1,null!==Nt&&Ft(Nt)&&(Nt=null),null!==Tt&&Ft(Tt)&&(Tt=null),null!==Rt&&Ft(Rt)&&(Rt=null),$t.forEach(Mt),Pt.forEach(Mt)}function Dt(e,t){e.blockedOn===t&&(e.blockedOn=null,Et||(Et=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Bt)))}function Wt(e){function t(t){return Dt(t,e)}if(0<_t.length){Dt(_t[0],e);for(var n=1;n<_t.length;n++){var r=_t[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Nt&&Dt(Nt,e),null!==Tt&&Dt(Tt,e),null!==Rt&&Dt(Rt,e),$t.forEach(t),Pt.forEach(t),n=0;n<Ot.length;n++)(r=Ot[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Ot.length&&null===(n=Ot[0]).blockedOn;)At(n),null===n.blockedOn&&Ot.shift()}var Ut=b.ReactCurrentBatchConfig,Ht=!0;function qt(e,t,n,r){var o=xt,i=Ut.transition;Ut.transition=null;try{xt=1,Jt(e,t,n,r)}finally{xt=o,Ut.transition=i}}function Vt(e,t,n,r){var o=xt,i=Ut.transition;Ut.transition=null;try{xt=4,Jt(e,t,n,r)}finally{xt=o,Ut.transition=i}}function Jt(e,t,n,r){if(Ht){var o=Yt(e,t,n,r);if(null===o)Hr(e,t,r,Qt,n),It(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Nt=zt(Nt,e,t,n,r,o),!0;case"dragenter":return Tt=zt(Tt,e,t,n,r,o),!0;case"mouseover":return Rt=zt(Rt,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return $t.set(i,zt($t.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Pt.set(i,zt(Pt.get(i)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(It(e,r),4&t&&-1<Lt.indexOf(e)){for(;null!==o;){var i=bo(o);if(null!==i&&wt(i),null===(i=Yt(e,t,n,r))&&Hr(e,t,r,Qt,n),i===o)break;o=i}null!==o&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Qt=null;function Yt(e,t,n,r){if(Qt=null,null!==(e=xo(e=we(r))))if(null===(t=We(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ue(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Xe()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Kt=null,Xt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Xt,r=n.length,o="value"in Kt?Kt.value:Kt.textContent,i=o.length;for(e=0;e<r&&n[e]===o[e];e++);var a=r-e;for(t=1;t<=a&&n[r-t]===o[i-t];t++);return Zt=o.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,o,i){for(var a in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(o):o[a]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return A(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,ln,sn,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},un=on(cn),dn=A({},cn,{view:0,detail:0}),fn=on(dn),pn=A({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(an=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=an=0,sn=e),an)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),hn=on(pn),mn=on(A({},pn,{dataTransfer:0})),gn=on(A({},dn,{relatedTarget:0})),yn=on(A({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=A({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xn=on(vn),bn=on(A({},cn,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function jn(){return Cn}var En=A({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),_n=on(En),Nn=on(A({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=on(A({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jn})),Rn=on(A({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),$n=A({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Pn=on($n),On=[9,13,27,32],Ln=u&&"CompositionEvent"in window,In=null;u&&"documentMode"in document&&(In=document.documentMode);var zn=u&&"TextEvent"in window&&!In,An=u&&(!Ln||In&&8<In&&11>=In),Fn=String.fromCharCode(32),Mn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==On.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Dn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Wn=!1;var Un={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Un[e.type]:"textarea"===t}function qn(e,t,n,r){Ee(r),0<(t=Vr(t,"onChange")).length&&(n=new un("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Vn=null,Jn=null;function Qn(e){Fr(e,0)}function Yn(e){if(J(wo(e)))return e}function Gn(e,t){if("change"===e)return t}var Kn=!1;if(u){var Xn;if(u){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Xn=Zn}else Xn=!1;Kn=Xn&&(!document.documentMode||9<document.documentMode)}function tr(){Vn&&(Vn.detachEvent("onpropertychange",nr),Jn=Vn=null)}function nr(e){if("value"===e.propertyName&&Yn(Jn)){var t=[];qn(t,Jn,e,we(e)),$e(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Jn=n,(Vn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn(Jn)}function ir(e,t){if("click"===e)return Yn(t)}function ar(e,t){if("input"===e||"change"===e)return Yn(t)}var lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!lr(e[o],t[o]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ur(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=void 0===r.end?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=ur(n,i);var a=ur(n,r);o&&a&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=u&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,xr=!1;function br(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;xr||null==gr||gr!==Q(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&sr(vr,r)||(vr=r,0<(r=Vr(yr,"onSelect")).length&&(t=new un("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Sr={},Cr={};function jr(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Cr)return Sr[e]=n[t];return e}u&&(Cr=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Er=jr("animationend"),_r=jr("animationiteration"),Nr=jr("animationstart"),Tr=jr("transitionend"),Rr=new Map,$r="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Pr(e,t){Rr.set(e,t),s(t,[e])}for(var Or=0;Or<$r.length;Or++){var Lr=$r[Or];Pr(Lr.toLowerCase(),"on"+(Lr[0].toUpperCase()+Lr.slice(1)))}Pr(Er,"onAnimationEnd"),Pr(_r,"onAnimationIteration"),Pr(Nr,"onAnimationStart"),Pr("dblclick","onDoubleClick"),Pr("focusin","onFocus"),Pr("focusout","onBlur"),Pr(Tr,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ir="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ir));function Ar(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,a,l,s,c){if(De.apply(this,arguments),ze){if(!ze)throw Error(i(198));var u=Ae;ze=!1,Ae=null,Fe||(Fe=!0,Me=u)}}(r,t,void 0,e),e.currentTarget=null}function Fr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var a=r.length-1;0<=a;a--){var l=r[a],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==i&&o.isPropagationStopped())break e;Ar(o,l,c),i=s}else for(a=0;a<r.length;a++){if(s=(l=r[a]).instance,c=l.currentTarget,l=l.listener,s!==i&&o.isPropagationStopped())break e;Ar(o,l,c),i=s}}}if(Fe)throw e=Me,Fe=!1,Me=null,e}function Mr(e,t){var n=t[go];void 0===n&&(n=t[go]=new Set);var r=e+"__bubble";n.has(r)||(Ur(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Ur(n,e,r,t)}var Dr="_reactListening"+Math.random().toString(36).slice(2);function Wr(e){if(!e[Dr]){e[Dr]=!0,a.forEach((function(t){"selectionchange"!==t&&(zr.has(t)||Br(t,!1,e),Br(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Dr]||(t[Dr]=!0,Br("selectionchange",!1,t))}}function Ur(e,t,n,r){switch(Gt(t)){case 1:var o=qt;break;case 4:o=Vt;break;default:o=Jt}n=o.bind(null,t,n,e),o=void 0,!Oe||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,o){var i=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var a=r.tag;if(3===a||4===a){var l=r.stateNode.containerInfo;if(l===o||8===l.nodeType&&l.parentNode===o)break;if(4===a)for(a=r.return;null!==a;){var s=a.tag;if((3===s||4===s)&&((s=a.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;a=a.return}for(;null!==l;){if(null===(a=xo(l)))return;if(5===(s=a.tag)||6===s){r=i=a;continue e}l=l.parentNode}}r=r.return}$e((function(){var r=i,o=we(n),a=[];e:{var l=Rr.get(e);if(void 0!==l){var s=un,c=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=_n;break;case"focusin":c="focus",s=gn;break;case"focusout":c="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Tn;break;case Er:case _r:case Nr:s=yn;break;case Tr:s=Rn;break;case"scroll":s=fn;break;case"wheel":s=Pn;break;case"copy":case"cut":case"paste":s=xn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Nn}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==l?l+"Capture":null:l;u=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Pe(h,f))&&u.push(qr(h,m,p)))),d)break;h=h.return}0<u.length&&(l=new s(l,c,null,n,o),a.push({event:l,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===be||!(c=n.relatedTarget||n.fromElement)||!xo(c)&&!c[mo])&&(s||l)&&(l=o.window===o?o:(l=o.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?xo(c):null)&&(c!==(d=We(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(u=Nn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?l:wo(s),p=null==c?l:wo(c),(l=new u(m,h+"leave",s,n,o)).target=d,l.relatedTarget=p,m=null,xo(o)===r&&((u=new u(f,h+"enter",c,n,o)).target=p,u.relatedTarget=d,m=u),d=m,s&&c)e:{for(f=c,h=0,p=u=s;p;p=Jr(p))h++;for(p=0,m=f;m;m=Jr(m))p++;for(;0<h-p;)u=Jr(u),h--;for(;0<p-h;)f=Jr(f),p--;for(;h--;){if(u===f||null!==f&&u===f.alternate)break e;u=Jr(u),f=Jr(f)}u=null}else u=null;null!==s&&Qr(a,l,s,u,!1),null!==c&&null!==d&&Qr(a,d,c,u,!0)}if("select"===(s=(l=r?wo(r):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=Gn;else if(Hn(l))if(Kn)g=ar;else{g=or;var y=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=ir);switch(g&&(g=g(e,r))?qn(a,g,n,o):(y&&y(e,l,r),"focusout"===e&&(y=l._wrapperState)&&y.controlled&&"number"===l.type&&ee(l,"number",l.value)),y=r?wo(r):window,e){case"focusin":(Hn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,br(a,n,o);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":br(a,n,o)}var v;if(Ln)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else Wn?Bn(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(An&&"ko"!==n.locale&&(Wn||"onCompositionStart"!==x?"onCompositionEnd"===x&&Wn&&(v=en()):(Xt="value"in(Kt=o)?Kt.value:Kt.textContent,Wn=!0)),0<(y=Vr(r,x)).length&&(x=new bn(x,e,null,n,o),a.push({event:x,listeners:y}),v?x.data=v:null!==(v=Dn(n))&&(x.data=v))),(v=zn?function(e,t){switch(e){case"compositionend":return Dn(t);case"keypress":return 32!==t.which?null:(Mn=!0,Fn);case"textInput":return(e=t.data)===Fn&&Mn?null:e;default:return null}}(e,n):function(e,t){if(Wn)return"compositionend"===e||!Ln&&Bn(e,t)?(e=en(),Zt=Xt=Kt=null,Wn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return An&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Vr(r,"onBeforeInput")).length&&(o=new bn("onBeforeInput","beforeinput",null,n,o),a.push({event:o,listeners:r}),o.data=v))}Fr(a,t)}))}function qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,i=o.stateNode;5===o.tag&&null!==i&&(o=i,null!=(i=Pe(e,n))&&r.unshift(qr(e,i,o)),null!=(i=Pe(e,t))&&r.push(qr(e,i,o))),e=e.return}return r}function Jr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,o){for(var i=t._reactName,a=[];null!==n&&n!==r;){var l=n,s=l.alternate,c=l.stateNode;if(null!==s&&s===r)break;5===l.tag&&null!==c&&(l=c,o?null!=(s=Pe(n,i))&&a.unshift(qr(n,s,l)):o||null!=(s=Pe(n,i))&&a.push(qr(n,s,l))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var Yr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Kr(e){return("string"===typeof e?e:""+e).replace(Yr,"\n").replace(Gr,"")}function Xr(e,t,n){if(t=Kr(t),Kr(e)!==t&&n)throw Error(i(425))}function Zr(){}var eo=null,to=null;function no(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ro="function"===typeof setTimeout?setTimeout:void 0,oo="function"===typeof clearTimeout?clearTimeout:void 0,io="function"===typeof Promise?Promise:void 0,ao="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof io?function(e){return io.resolve(null).then(e).catch(lo)}:ro;function lo(e){setTimeout((function(){throw e}))}function so(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Wt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Wt(t)}function co(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function uo(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fo=Math.random().toString(36).slice(2),po="__reactFiber$"+fo,ho="__reactProps$"+fo,mo="__reactContainer$"+fo,go="__reactEvents$"+fo,yo="__reactListeners$"+fo,vo="__reactHandles$"+fo;function xo(e){var t=e[po];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mo]||n[po]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=uo(e);null!==e;){if(n=e[po])return n;e=uo(e)}return t}n=(e=n).parentNode}return null}function bo(e){return!(e=e[po]||e[mo])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wo(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function ko(e){return e[ho]||null}var So=[],Co=-1;function jo(e){return{current:e}}function Eo(e){0>Co||(e.current=So[Co],So[Co]=null,Co--)}function _o(e,t){Co++,So[Co]=e.current,e.current=t}var No={},To=jo(No),Ro=jo(!1),$o=No;function Po(e,t){var n=e.type.contextTypes;if(!n)return No;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,i={};for(o in n)i[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Oo(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Lo(){Eo(Ro),Eo(To)}function Io(e,t,n){if(To.current!==No)throw Error(i(168));_o(To,t),_o(Ro,n)}function zo(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(i(108,U(e)||"Unknown",o));return A({},n,r)}function Ao(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||No,$o=To.current,_o(To,e),_o(Ro,Ro.current),!0}function Fo(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=zo(e,t,$o),r.__reactInternalMemoizedMergedChildContext=e,Eo(Ro),Eo(To),_o(To,e)):Eo(Ro),_o(Ro,n)}var Mo=null,Bo=!1,Do=!1;function Wo(e){null===Mo?Mo=[e]:Mo.push(e)}function Uo(){if(!Do&&null!==Mo){Do=!0;var e=0,t=xt;try{var n=Mo;for(xt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Mo=null,Bo=!1}catch(o){throw null!==Mo&&(Mo=Mo.slice(e+1)),Je(Ze,Uo),o}finally{xt=t,Do=!1}}return null}var Ho=[],qo=0,Vo=null,Jo=0,Qo=[],Yo=0,Go=null,Ko=1,Xo="";function Zo(e,t){Ho[qo++]=Jo,Ho[qo++]=Vo,Vo=e,Jo=t}function ei(e,t,n){Qo[Yo++]=Ko,Qo[Yo++]=Xo,Qo[Yo++]=Go,Go=e;var r=Ko;e=Xo;var o=32-at(r)-1;r&=~(1<<o),n+=1;var i=32-at(t)+o;if(30<i){var a=o-o%5;i=(r&(1<<a)-1).toString(32),r>>=a,o-=a,Ko=1<<32-at(t)+o|n<<o|r,Xo=i+e}else Ko=1<<i|n<<o|r,Xo=e}function ti(e){null!==e.return&&(Zo(e,1),ei(e,1,0))}function ni(e){for(;e===Vo;)Vo=Ho[--qo],Ho[qo]=null,Jo=Ho[--qo],Ho[qo]=null;for(;e===Go;)Go=Qo[--Yo],Qo[Yo]=null,Xo=Qo[--Yo],Qo[Yo]=null,Ko=Qo[--Yo],Qo[Yo]=null}var ri=null,oi=null,ii=!1,ai=null;function li(e,t){var n=$c(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function si(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ri=e,oi=co(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ri=e,oi=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Go?{id:Ko,overflow:Xo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=$c(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ri=e,oi=null,!0);default:return!1}}function ci(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ui(e){if(ii){var t=oi;if(t){var n=t;if(!si(e,t)){if(ci(e))throw Error(i(418));t=co(n.nextSibling);var r=ri;t&&si(e,t)?li(r,n):(e.flags=-4097&e.flags|2,ii=!1,ri=e)}}else{if(ci(e))throw Error(i(418));e.flags=-4097&e.flags|2,ii=!1,ri=e}}}function di(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ri=e}function fi(e){if(e!==ri)return!1;if(!ii)return di(e),ii=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!no(e.type,e.memoizedProps)),t&&(t=oi)){if(ci(e))throw pi(),Error(i(418));for(;t;)li(e,t),t=co(t.nextSibling)}if(di(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){oi=co(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}oi=null}}else oi=ri?co(e.stateNode.nextSibling):null;return!0}function pi(){for(var e=oi;e;)e=co(e.nextSibling)}function hi(){oi=ri=null,ii=!1}function mi(e){null===ai?ai=[e]:ai.push(e)}var gi=b.ReactCurrentBatchConfig;function yi(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var o=r,a=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===a?t.ref:(t=function(e){var t=o.refs;null===e?delete t[a]:t[a]=e},t._stringRef=a,t)}if("string"!==typeof e)throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function vi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function xi(e){return(0,e._init)(e._payload)}function bi(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=Oc(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function l(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ac(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function c(e,t,n,r){var i=n.type;return i===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===P&&xi(i)===t.type)?((r=o(t,n.props)).ref=yi(e,t,n),r.return=e,r):((r=Lc(n.type,n.key,n.props,null,e.mode,r)).ref=yi(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Fc(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=Ic(n,e.mode,r,i)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Ac(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Lc(t.type,t.key,t.props,null,e.mode,n)).ref=yi(e,null,t),n.return=e,n;case k:return(t=Fc(t,e.mode,n)).return=e,t;case P:return f(e,(0,t._init)(t._payload),n)}if(te(t)||I(t))return(t=Ic(t,e.mode,n,null)).return=e,t;vi(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===o?c(e,t,n,r):null;case k:return n.key===o?u(e,t,n,r):null;case P:return p(e,t,(o=n._init)(n._payload),r)}if(te(n)||I(n))return null!==o?null:d(e,t,n,r,null);vi(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case k:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case P:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||I(r))return d(t,e=e.get(n)||null,r,o,null);vi(t,r)}return null}function m(o,i,l,s){for(var c=null,u=null,d=i,m=i=0,g=null;null!==d&&m<l.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=p(o,d,l[m],s);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(o,d),i=a(y,i,m),null===u?c=y:u.sibling=y,u=y,d=g}if(m===l.length)return n(o,d),ii&&Zo(o,m),c;if(null===d){for(;m<l.length;m++)null!==(d=f(o,l[m],s))&&(i=a(d,i,m),null===u?c=d:u.sibling=d,u=d);return ii&&Zo(o,m),c}for(d=r(o,d);m<l.length;m++)null!==(g=h(d,o,m,l[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),i=a(g,i,m),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach((function(e){return t(o,e)})),ii&&Zo(o,m),c}function g(o,l,s,c){var u=I(s);if("function"!==typeof u)throw Error(i(150));if(null==(s=u.call(s)))throw Error(i(151));for(var d=u=null,m=l,g=l=0,y=null,v=s.next();null!==m&&!v.done;g++,v=s.next()){m.index>g?(y=m,m=null):y=m.sibling;var x=p(o,m,v.value,c);if(null===x){null===m&&(m=y);break}e&&m&&null===x.alternate&&t(o,m),l=a(x,l,g),null===d?u=x:d.sibling=x,d=x,m=y}if(v.done)return n(o,m),ii&&Zo(o,g),u;if(null===m){for(;!v.done;g++,v=s.next())null!==(v=f(o,v.value,c))&&(l=a(v,l,g),null===d?u=v:d.sibling=v,d=v);return ii&&Zo(o,g),u}for(m=r(o,m);!v.done;g++,v=s.next())null!==(v=h(m,o,g,v.value,c))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),l=a(v,l,g),null===d?u=v:d.sibling=v,d=v);return e&&m.forEach((function(e){return t(o,e)})),ii&&Zo(o,g),u}return function e(r,i,a,s){if("object"===typeof a&&null!==a&&a.type===S&&null===a.key&&(a=a.props.children),"object"===typeof a&&null!==a){switch(a.$$typeof){case w:e:{for(var c=a.key,u=i;null!==u;){if(u.key===c){if((c=a.type)===S){if(7===u.tag){n(r,u.sibling),(i=o(u,a.props.children)).return=r,r=i;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===P&&xi(c)===u.type){n(r,u.sibling),(i=o(u,a.props)).ref=yi(r,u,a),i.return=r,r=i;break e}n(r,u);break}t(r,u),u=u.sibling}a.type===S?((i=Ic(a.props.children,r.mode,s,a.key)).return=r,r=i):((s=Lc(a.type,a.key,a.props,null,r.mode,s)).ref=yi(r,i,a),s.return=r,r=s)}return l(r);case k:e:{for(u=a.key;null!==i;){if(i.key===u){if(4===i.tag&&i.stateNode.containerInfo===a.containerInfo&&i.stateNode.implementation===a.implementation){n(r,i.sibling),(i=o(i,a.children||[])).return=r,r=i;break e}n(r,i);break}t(r,i),i=i.sibling}(i=Fc(a,r.mode,s)).return=r,r=i}return l(r);case P:return e(r,i,(u=a._init)(a._payload),s)}if(te(a))return m(r,i,a,s);if(I(a))return g(r,i,a,s);vi(r,a)}return"string"===typeof a&&""!==a||"number"===typeof a?(a=""+a,null!==i&&6===i.tag?(n(r,i.sibling),(i=o(i,a)).return=r,r=i):(n(r,i),(i=Ac(a,r.mode,s)).return=r,r=i),l(r)):n(r,i)}}var wi=bi(!0),ki=bi(!1),Si=jo(null),Ci=null,ji=null,Ei=null;function _i(){Ei=ji=Ci=null}function Ni(e){var t=Si.current;Eo(Si),e._currentValue=t}function Ti(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ri(e,t){Ci=e,Ei=ji=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(xl=!0),e.firstContext=null)}function $i(e){var t=e._currentValue;if(Ei!==e)if(e={context:e,memoizedValue:t,next:null},null===ji){if(null===Ci)throw Error(i(308));ji=e,Ci.dependencies={lanes:0,firstContext:e}}else ji=ji.next=e;return t}var Pi=null;function Oi(e){null===Pi?Pi=[e]:Pi.push(e)}function Li(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,Oi(t)):(n.next=o.next,o.next=n),t.interleaved=n,Ii(e,r)}function Ii(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var zi=!1;function Ai(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Fi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Mi(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bi(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ns)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Ii(e,n)}return null===(o=r.interleaved)?(t.next=t,Oi(r)):(t.next=o.next,o.next=t),r.interleaved=t,Ii(e,n)}function Di(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Wi(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?o=i=a:i=i.next=a,n=n.next}while(null!==n);null===i?o=i=t:i=i.next=t}else o=i=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ui(e,t,n,r){var o=e.updateQueue;zi=!1;var i=o.firstBaseUpdate,a=o.lastBaseUpdate,l=o.shared.pending;if(null!==l){o.shared.pending=null;var s=l,c=s.next;s.next=null,null===a?i=c:a.next=c,a=s;var u=e.alternate;null!==u&&((l=(u=u.updateQueue).lastBaseUpdate)!==a&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s))}if(null!==i){var d=o.baseState;for(a=0,u=c=s=null,l=i;;){var f=l.lane,p=l.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var h=e,m=l;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=A({},d,f);break e;case 2:zi=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=o.effects)?o.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=p,s=d):u=u.next=p,a|=f;if(null===(l=l.next)){if(null===(l=o.shared.pending))break;l=(f=l).next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}if(null===u&&(s=d),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=u,null!==(t=o.shared.interleaved)){o=t;do{a|=o.lane,o=o.next}while(o!==t)}else null===i&&(o.shared.lanes=0);zs|=a,e.lanes=a,e.memoizedState=d}}function Hi(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(i(191,o));o.call(r)}}}var qi={},Vi=jo(qi),Ji=jo(qi),Qi=jo(qi);function Yi(e){if(e===qi)throw Error(i(174));return e}function Gi(e,t){switch(_o(Qi,t),_o(Ji,e),_o(Vi,qi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Eo(Vi),_o(Vi,t)}function Ki(){Eo(Vi),Eo(Ji),Eo(Qi)}function Xi(e){Yi(Qi.current);var t=Yi(Vi.current),n=se(t,e.type);t!==n&&(_o(Ji,e),_o(Vi,n))}function Zi(e){Ji.current===e&&(Eo(Vi),Eo(Ji))}var ea=jo(0);function ta(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var na=[];function ra(){for(var e=0;e<na.length;e++)na[e]._workInProgressVersionPrimary=null;na.length=0}var oa=b.ReactCurrentDispatcher,ia=b.ReactCurrentBatchConfig,aa=0,la=null,sa=null,ca=null,ua=!1,da=!1,fa=0,pa=0;function ha(){throw Error(i(321))}function ma(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function ga(e,t,n,r,o,a){if(aa=a,la=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,oa.current=null===e||null===e.memoizedState?Za:el,e=n(r,o),da){a=0;do{if(da=!1,fa=0,25<=a)throw Error(i(301));a+=1,ca=sa=null,t.updateQueue=null,oa.current=tl,e=n(r,o)}while(da)}if(oa.current=Xa,t=null!==sa&&null!==sa.next,aa=0,ca=sa=la=null,ua=!1,t)throw Error(i(300));return e}function ya(){var e=0!==fa;return fa=0,e}function va(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ca?la.memoizedState=ca=e:ca=ca.next=e,ca}function xa(){if(null===sa){var e=la.alternate;e=null!==e?e.memoizedState:null}else e=sa.next;var t=null===ca?la.memoizedState:ca.next;if(null!==t)ca=t,sa=e;else{if(null===e)throw Error(i(310));e={memoizedState:(sa=e).memoizedState,baseState:sa.baseState,baseQueue:sa.baseQueue,queue:sa.queue,next:null},null===ca?la.memoizedState=ca=e:ca=ca.next=e}return ca}function ba(e,t){return"function"===typeof t?t(e):t}function wa(e){var t=xa(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=sa,o=r.baseQueue,a=n.pending;if(null!==a){if(null!==o){var l=o.next;o.next=a.next,a.next=l}r.baseQueue=o=a,n.pending=null}if(null!==o){a=o.next,r=r.baseState;var s=l=null,c=null,u=a;do{var d=u.lane;if((aa&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=f,l=r):c=c.next=f,la.lanes|=d,zs|=d}u=u.next}while(null!==u&&u!==a);null===c?l=r:c.next=s,lr(r,t.memoizedState)||(xl=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{a=o.lane,la.lanes|=a,zs|=a,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ka(e){var t=xa(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,a=t.memoizedState;if(null!==o){n.pending=null;var l=o=o.next;do{a=e(a,l.action),l=l.next}while(l!==o);lr(a,t.memoizedState)||(xl=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Sa(){}function Ca(e,t){var n=la,r=xa(),o=t(),a=!lr(r.memoizedState,o);if(a&&(r.memoizedState=o,xl=!0),r=r.queue,za(_a.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||null!==ca&&1&ca.memoizedState.tag){if(n.flags|=2048,$a(9,Ea.bind(null,n,r,o,t),void 0,null),null===Ts)throw Error(i(349));0!==(30&aa)||ja(n,t,o)}return o}function ja(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=la.updateQueue)?(t={lastEffect:null,stores:null},la.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ea(e,t,n,r){t.value=n,t.getSnapshot=r,Na(t)&&Ta(e)}function _a(e,t,n){return n((function(){Na(t)&&Ta(e)}))}function Na(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(r){return!0}}function Ta(e){var t=Ii(e,1);null!==t&&nc(t,e,1,-1)}function Ra(e){var t=va();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ba,lastRenderedState:e},t.queue=e,e=e.dispatch=Qa.bind(null,la,e),[t.memoizedState,e]}function $a(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=la.updateQueue)?(t={lastEffect:null,stores:null},la.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Pa(){return xa().memoizedState}function Oa(e,t,n,r){var o=va();la.flags|=e,o.memoizedState=$a(1|t,n,void 0,void 0===r?null:r)}function La(e,t,n,r){var o=xa();r=void 0===r?null:r;var i=void 0;if(null!==sa){var a=sa.memoizedState;if(i=a.destroy,null!==r&&ma(r,a.deps))return void(o.memoizedState=$a(t,n,i,r))}la.flags|=e,o.memoizedState=$a(1|t,n,i,r)}function Ia(e,t){return Oa(8390656,8,e,t)}function za(e,t){return La(2048,8,e,t)}function Aa(e,t){return La(4,2,e,t)}function Fa(e,t){return La(4,4,e,t)}function Ma(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ba(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,La(4,4,Ma.bind(null,t,e),n)}function Da(){}function Wa(e,t){var n=xa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ma(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ua(e,t){var n=xa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ma(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ha(e,t,n){return 0===(21&aa)?(e.baseState&&(e.baseState=!1,xl=!0),e.memoizedState=n):(lr(n,t)||(n=mt(),la.lanes|=n,zs|=n,e.baseState=!0),t)}function qa(e,t){var n=xt;xt=0!==n&&4>n?n:4,e(!0);var r=ia.transition;ia.transition={};try{e(!1),t()}finally{xt=n,ia.transition=r}}function Va(){return xa().memoizedState}function Ja(e,t,n){var r=tc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ya(e))Ga(t,n);else if(null!==(n=Li(e,t,n,r))){nc(n,e,r,ec()),Ka(n,t,r)}}function Qa(e,t,n){var r=tc(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ya(e))Ga(t,o);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var a=t.lastRenderedState,l=i(a,n);if(o.hasEagerState=!0,o.eagerState=l,lr(l,a)){var s=t.interleaved;return null===s?(o.next=o,Oi(t)):(o.next=s.next,s.next=o),void(t.interleaved=o)}}catch(c){}null!==(n=Li(e,t,o,r))&&(nc(n,e,r,o=ec()),Ka(n,t,r))}}function Ya(e){var t=e.alternate;return e===la||null!==t&&t===la}function Ga(e,t){da=ua=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ka(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Xa={readContext:$i,useCallback:ha,useContext:ha,useEffect:ha,useImperativeHandle:ha,useInsertionEffect:ha,useLayoutEffect:ha,useMemo:ha,useReducer:ha,useRef:ha,useState:ha,useDebugValue:ha,useDeferredValue:ha,useTransition:ha,useMutableSource:ha,useSyncExternalStore:ha,useId:ha,unstable_isNewReconciler:!1},Za={readContext:$i,useCallback:function(e,t){return va().memoizedState=[e,void 0===t?null:t],e},useContext:$i,useEffect:Ia,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Oa(4194308,4,Ma.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Oa(4194308,4,e,t)},useInsertionEffect:function(e,t){return Oa(4,2,e,t)},useMemo:function(e,t){var n=va();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=va();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ja.bind(null,la,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},va().memoizedState=e},useState:Ra,useDebugValue:Da,useDeferredValue:function(e){return va().memoizedState=e},useTransition:function(){var e=Ra(!1),t=e[0];return e=qa.bind(null,e[1]),va().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=la,o=va();if(ii){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===Ts)throw Error(i(349));0!==(30&aa)||ja(r,t,n)}o.memoizedState=n;var a={value:n,getSnapshot:t};return o.queue=a,Ia(_a.bind(null,r,a,e),[e]),r.flags|=2048,$a(9,Ea.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=va(),t=Ts.identifierPrefix;if(ii){var n=Xo;t=":"+t+"R"+(n=(Ko&~(1<<32-at(Ko)-1)).toString(32)+n),0<(n=fa++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pa++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},el={readContext:$i,useCallback:Wa,useContext:$i,useEffect:za,useImperativeHandle:Ba,useInsertionEffect:Aa,useLayoutEffect:Fa,useMemo:Ua,useReducer:wa,useRef:Pa,useState:function(){return wa(ba)},useDebugValue:Da,useDeferredValue:function(e){return Ha(xa(),sa.memoizedState,e)},useTransition:function(){return[wa(ba)[0],xa().memoizedState]},useMutableSource:Sa,useSyncExternalStore:Ca,useId:Va,unstable_isNewReconciler:!1},tl={readContext:$i,useCallback:Wa,useContext:$i,useEffect:za,useImperativeHandle:Ba,useInsertionEffect:Aa,useLayoutEffect:Fa,useMemo:Ua,useReducer:ka,useRef:Pa,useState:function(){return ka(ba)},useDebugValue:Da,useDeferredValue:function(e){var t=xa();return null===sa?t.memoizedState=e:Ha(t,sa.memoizedState,e)},useTransition:function(){return[ka(ba)[0],xa().memoizedState]},useMutableSource:Sa,useSyncExternalStore:Ca,useId:Va,unstable_isNewReconciler:!1};function nl(e,t){if(e&&e.defaultProps){for(var n in t=A({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rl(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:A({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ol={isMounted:function(e){return!!(e=e._reactInternals)&&We(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ec(),o=tc(e),i=Mi(r,o);i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Bi(e,i,o))&&(nc(t,e,o,r),Di(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ec(),o=tc(e),i=Mi(r,o);i.tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),null!==(t=Bi(e,i,o))&&(nc(t,e,o,r),Di(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ec(),r=tc(e),o=Mi(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=Bi(e,o,r))&&(nc(t,e,r,n),Di(t,e,r))}};function il(e,t,n,r,o,i,a){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,i,a):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(o,i))}function al(e,t,n){var r=!1,o=No,i=t.contextType;return"object"===typeof i&&null!==i?i=$i(i):(o=Oo(t)?$o:To.current,i=(r=null!==(r=t.contextTypes)&&void 0!==r)?Po(e,o):No),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ol,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function ll(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ol.enqueueReplaceState(t,t.state,null)}function sl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Ai(e);var i=t.contextType;"object"===typeof i&&null!==i?o.context=$i(i):(i=Oo(t)?$o:To.current,o.context=Po(e,i)),o.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(rl(e,t,i,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&ol.enqueueReplaceState(o,o.state,null),Ui(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function cl(e,t){try{var n="",r=t;do{n+=D(r),r=r.return}while(r);var o=n}catch(i){o="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:o,digest:null}}function ul(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function dl(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fl="function"===typeof WeakMap?WeakMap:Map;function pl(e,t,n){(n=Mi(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hs||(Hs=!0,qs=r),dl(0,t)},n}function hl(e,t,n){(n=Mi(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){dl(0,t)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(n.callback=function(){dl(0,t),"function"!==typeof r&&(null===Vs?Vs=new Set([this]):Vs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ml(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fl;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=jc.bind(null,e,t,n),t.then(e,e))}function gl(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yl(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Mi(-1,1)).tag=2,Bi(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var vl=b.ReactCurrentOwner,xl=!1;function bl(e,t,n,r){t.child=null===e?ki(t,null,n,r):wi(t,e.child,n,r)}function wl(e,t,n,r,o){n=n.render;var i=t.ref;return Ri(t,o),r=ga(e,t,n,r,i,o),n=ya(),null===e||xl?(ii&&n&&ti(t),t.flags|=1,bl(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Hl(e,t,o))}function kl(e,t,n,r,o){if(null===e){var i=n.type;return"function"!==typeof i||Pc(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Lc(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Sl(e,t,i,r,o))}if(i=e.child,0===(e.lanes&o)){var a=i.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(a,r)&&e.ref===t.ref)return Hl(e,t,o)}return t.flags|=1,(e=Oc(i,r)).ref=t.ref,e.return=t,t.child=e}function Sl(e,t,n,r,o){if(null!==e){var i=e.memoizedProps;if(sr(i,r)&&e.ref===t.ref){if(xl=!1,t.pendingProps=r=i,0===(e.lanes&o))return t.lanes=e.lanes,Hl(e,t,o);0!==(131072&e.flags)&&(xl=!0)}}return El(e,t,n,r,o)}function Cl(e,t,n){var r=t.pendingProps,o=r.children,i=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_o(Os,Ps),Ps|=n;else{if(0===(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_o(Os,Ps),Ps|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==i?i.baseLanes:n,_o(Os,Ps),Ps|=r}else null!==i?(r=i.baseLanes|n,t.memoizedState=null):r=n,_o(Os,Ps),Ps|=r;return bl(e,t,o,n),t.child}function jl(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function El(e,t,n,r,o){var i=Oo(n)?$o:To.current;return i=Po(t,i),Ri(t,o),n=ga(e,t,n,r,i,o),r=ya(),null===e||xl?(ii&&r&&ti(t),t.flags|=1,bl(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Hl(e,t,o))}function _l(e,t,n,r,o){if(Oo(n)){var i=!0;Ao(t)}else i=!1;if(Ri(t,o),null===t.stateNode)Ul(e,t),al(t,n,r),sl(t,n,r,o),r=!0;else if(null===e){var a=t.stateNode,l=t.memoizedProps;a.props=l;var s=a.context,c=n.contextType;"object"===typeof c&&null!==c?c=$i(c):c=Po(t,c=Oo(n)?$o:To.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof a.getSnapshotBeforeUpdate;d||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(l!==r||s!==c)&&ll(t,a,r,c),zi=!1;var f=t.memoizedState;a.state=f,Ui(t,r,a,o),s=t.memoizedState,l!==r||f!==s||Ro.current||zi?("function"===typeof u&&(rl(t,n,u,r),s=t.memoizedState),(l=zi||il(t,n,l,r,f,s,c))?(d||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),a.props=r,a.state=s,a.context=c,r=l):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Fi(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:nl(t.type,l),a.props=c,d=t.pendingProps,f=a.context,"object"===typeof(s=n.contextType)&&null!==s?s=$i(s):s=Po(t,s=Oo(n)?$o:To.current);var p=n.getDerivedStateFromProps;(u="function"===typeof p||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(l!==d||f!==s)&&ll(t,a,r,s),zi=!1,f=t.memoizedState,a.state=f,Ui(t,r,a,o);var h=t.memoizedState;l!==d||f!==h||Ro.current||zi?("function"===typeof p&&(rl(t,n,p,r),h=t.memoizedState),(c=zi||il(t,n,c,r,f,h,s)||!1)?(u||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,h,s),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),a.props=r,a.state=h,a.context=s,r=c):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Nl(e,t,n,r,i,o)}function Nl(e,t,n,r,o,i){jl(e,t);var a=0!==(128&t.flags);if(!r&&!a)return o&&Fo(t,n,!1),Hl(e,t,i);r=t.stateNode,vl.current=t;var l=a&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&a?(t.child=wi(t,e.child,null,i),t.child=wi(t,null,l,i)):bl(e,t,l,i),t.memoizedState=r.state,o&&Fo(t,n,!0),t.child}function Tl(e){var t=e.stateNode;t.pendingContext?Io(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Io(0,t.context,!1),Gi(e,t.containerInfo)}function Rl(e,t,n,r,o){return hi(),mi(o),t.flags|=256,bl(e,t,n,r),t.child}var $l,Pl,Ol,Ll,Il={dehydrated:null,treeContext:null,retryLane:0};function zl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Al(e,t,n){var r,o=t.pendingProps,a=ea.current,l=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&a)),r?(l=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(a|=1),_o(ea,1&a),null===e)return ui(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=o.children,e=o.fallback,l?(o=t.mode,l=t.child,s={mode:"hidden",children:s},0===(1&o)&&null!==l?(l.childLanes=0,l.pendingProps=s):l=zc(s,o,0,null),e=Ic(e,o,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=zl(n),t.memoizedState=Il,e):Fl(t,s));if(null!==(a=e.memoizedState)&&null!==(r=a.dehydrated))return function(e,t,n,r,o,a,l){if(n)return 256&t.flags?(t.flags&=-257,Ml(e,t,l,r=ul(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(a=r.fallback,o=t.mode,r=zc({mode:"visible",children:r.children},o,0,null),(a=Ic(a,o,l,null)).flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,0!==(1&t.mode)&&wi(t,e.child,null,l),t.child.memoizedState=zl(l),t.memoizedState=Il,a);if(0===(1&t.mode))return Ml(e,t,l,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var s=r.dgst;return r=s,Ml(e,t,l,r=ul(a=Error(i(419)),r,void 0))}if(s=0!==(l&e.childLanes),xl||s){if(null!==(r=Ts)){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|l))?0:o)&&o!==a.retryLane&&(a.retryLane=o,Ii(e,o),nc(r,e,o,-1))}return mc(),Ml(e,t,l,r=ul(Error(i(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=_c.bind(null,e),o._reactRetry=t,null):(e=a.treeContext,oi=co(o.nextSibling),ri=t,ii=!0,ai=null,null!==e&&(Qo[Yo++]=Ko,Qo[Yo++]=Xo,Qo[Yo++]=Go,Ko=e.id,Xo=e.overflow,Go=t),t=Fl(t,r.children),t.flags|=4096,t)}(e,t,s,o,r,a,n);if(l){l=o.fallback,s=t.mode,r=(a=e.child).sibling;var c={mode:"hidden",children:o.children};return 0===(1&s)&&t.child!==a?((o=t.child).childLanes=0,o.pendingProps=c,t.deletions=null):(o=Oc(a,c)).subtreeFlags=14680064&a.subtreeFlags,null!==r?l=Oc(r,l):(l=Ic(l,s,n,null)).flags|=2,l.return=t,o.return=t,o.sibling=l,t.child=o,o=l,l=t.child,s=null===(s=e.child.memoizedState)?zl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=Il,o}return e=(l=e.child).sibling,o=Oc(l,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function Fl(e,t){return(t=zc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ml(e,t,n,r){return null!==r&&mi(r),wi(t,e.child,null,n),(e=Fl(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bl(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ti(e.return,t,n)}function Dl(e,t,n,r,o){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Wl(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(bl(e,t,r.children,n),0!==(2&(r=ea.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bl(e,n,t);else if(19===e.tag)Bl(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(_o(ea,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ta(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Dl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ta(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Dl(t,!0,n,null,i);break;case"together":Dl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ul(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hl(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),zs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Oc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Oc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function ql(e,t){if(!ii)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Vl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Jl(e,t,n){var r=t.pendingProps;switch(ni(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Vl(t),null;case 1:case 17:return Oo(t.type)&&Lo(),Vl(t),null;case 3:return r=t.stateNode,Ki(),Eo(Ro),Eo(To),ra(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fi(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ai&&(ac(ai),ai=null))),Pl(e,t),Vl(t),null;case 5:Zi(t);var o=Yi(Qi.current);if(n=t.type,null!==e&&null!=t.stateNode)Ol(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(i(166));return Vl(t),null}if(e=Yi(Vi.current),fi(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[po]=t,r[ho]=a,e=0!==(1&t.mode),n){case"dialog":Mr("cancel",r),Mr("close",r);break;case"iframe":case"object":case"embed":Mr("load",r);break;case"video":case"audio":for(o=0;o<Ir.length;o++)Mr(Ir[o],r);break;case"source":Mr("error",r);break;case"img":case"image":case"link":Mr("error",r),Mr("load",r);break;case"details":Mr("toggle",r);break;case"input":G(r,a),Mr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},Mr("invalid",r);break;case"textarea":oe(r,a),Mr("invalid",r)}for(var s in ve(n,a),o=null,a)if(a.hasOwnProperty(s)){var c=a[s];"children"===s?"string"===typeof c?r.textContent!==c&&(!0!==a.suppressHydrationWarning&&Xr(r.textContent,c,e),o=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==a.suppressHydrationWarning&&Xr(r.textContent,c,e),o=["children",""+c]):l.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Mr("scroll",r)}switch(n){case"input":V(r),Z(r,a,!0);break;case"textarea":V(r),ae(r);break;case"select":case"option":break;default:"function"===typeof a.onClick&&(r.onclick=Zr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[po]=t,e[ho]=r,$l(e,t,!1,!1),t.stateNode=e;e:{switch(s=xe(n,r),n){case"dialog":Mr("cancel",e),Mr("close",e),o=r;break;case"iframe":case"object":case"embed":Mr("load",e),o=r;break;case"video":case"audio":for(o=0;o<Ir.length;o++)Mr(Ir[o],e);o=r;break;case"source":Mr("error",e),o=r;break;case"img":case"image":case"link":Mr("error",e),Mr("load",e),o=r;break;case"details":Mr("toggle",e),o=r;break;case"input":G(e,r),o=Y(e,r),Mr("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=A({},r,{value:void 0}),Mr("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Mr("invalid",e)}for(a in ve(n,o),c=o)if(c.hasOwnProperty(a)){var u=c[a];"style"===a?ge(e,u):"dangerouslySetInnerHTML"===a?null!=(u=u?u.__html:void 0)&&de(e,u):"children"===a?"string"===typeof u?("textarea"!==n||""!==u)&&fe(e,u):"number"===typeof u&&fe(e,""+u):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(l.hasOwnProperty(a)?null!=u&&"onScroll"===a&&Mr("scroll",e):null!=u&&x(e,a,u,s))}switch(n){case"input":V(e),Z(e,r,!1);break;case"textarea":V(e),ae(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(a=r.value)?ne(e,!!r.multiple,a,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Vl(t),null;case 6:if(e&&null!=t.stateNode)Ll(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(n=Yi(Qi.current),Yi(Vi.current),fi(t)){if(r=t.stateNode,n=t.memoizedProps,r[po]=t,(a=r.nodeValue!==n)&&null!==(e=ri))switch(e.tag){case 3:Xr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Xr(r.nodeValue,n,0!==(1&e.mode))}a&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[po]=t,t.stateNode=r}return Vl(t),null;case 13:if(Eo(ea),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ii&&null!==oi&&0!==(1&t.mode)&&0===(128&t.flags))pi(),hi(),t.flags|=98560,a=!1;else if(a=fi(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[po]=t}else hi(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Vl(t),a=!1}else null!==ai&&(ac(ai),ai=null),a=!0;if(!a)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ea.current)?0===Ls&&(Ls=3):mc())),null!==t.updateQueue&&(t.flags|=4),Vl(t),null);case 4:return Ki(),Pl(e,t),null===e&&Wr(t.stateNode.containerInfo),Vl(t),null;case 10:return Ni(t.type._context),Vl(t),null;case 19:if(Eo(ea),null===(a=t.memoizedState))return Vl(t),null;if(r=0!==(128&t.flags),null===(s=a.rendering))if(r)ql(a,!1);else{if(0!==Ls||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=ta(e))){for(t.flags|=128,ql(a,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(a=n).flags&=14680066,null===(s=a.alternate)?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=s.childLanes,a.lanes=s.lanes,a.child=s.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=s.memoizedProps,a.memoizedState=s.memoizedState,a.updateQueue=s.updateQueue,a.type=s.type,e=s.dependencies,a.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _o(ea,1&ea.current|2),t.child}e=e.sibling}null!==a.tail&&Ke()>Ws&&(t.flags|=128,r=!0,ql(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ta(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),ql(a,!0),null===a.tail&&"hidden"===a.tailMode&&!s.alternate&&!ii)return Vl(t),null}else 2*Ke()-a.renderingStartTime>Ws&&1073741824!==n&&(t.flags|=128,r=!0,ql(a,!1),t.lanes=4194304);a.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=a.last)?n.sibling=s:t.child=s,a.last=s)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=Ke(),t.sibling=null,n=ea.current,_o(ea,r?1&n|2:1&n),t):(Vl(t),null);case 22:case 23:return dc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ps)&&(Vl(t),6&t.subtreeFlags&&(t.flags|=8192)):Vl(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function Ql(e,t){switch(ni(t),t.tag){case 1:return Oo(t.type)&&Lo(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Ki(),Eo(Ro),Eo(To),ra(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zi(t),null;case 13:if(Eo(ea),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));hi()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Eo(ea),null;case 4:return Ki(),null;case 10:return Ni(t.type._context),null;case 22:case 23:return dc(),null;default:return null}}$l=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Pl=function(){},Ol=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Yi(Vi.current);var i,a=null;switch(n){case"input":o=Y(e,o),r=Y(e,r),a=[];break;case"select":o=A({},o,{value:void 0}),r=A({},r,{value:void 0}),a=[];break;case"textarea":o=re(e,o),r=re(e,r),a=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(u in ve(n,r),n=null,o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&null!=o[u])if("style"===u){var s=o[u];for(i in s)s.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(l.hasOwnProperty(u)?a||(a=[]):(a=a||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=o?o[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(i in s)!s.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&s[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(a||(a=[]),a.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(a=a||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(a=a||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(l.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Mr("scroll",e),a||s===c||(a=[])):(a=a||[]).push(u,c))}n&&(a=a||[]).push("style",n);var u=a;(t.updateQueue=u)&&(t.flags|=4)}},Ll=function(e,t,n,r){n!==r&&(t.flags|=4)};var Yl=!1,Gl=!1,Kl="function"===typeof WeakSet?WeakSet:Set,Xl=null;function Zl(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Cc(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Cc(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,void 0!==i&&es(t,n,i)}o=o.next}while(o!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function os(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function is(e){var t=e.alternate;null!==t&&(e.alternate=null,is(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[po],delete t[ho],delete t[go],delete t[yo],delete t[vo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function as(e){return 5===e.tag||3===e.tag||4===e.tag}function ls(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||as(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(cs(e,t,n),e=e.sibling;null!==e;)cs(e,t,n),e=e.sibling}var us=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(it&&"function"===typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(ot,n)}catch(l){}switch(n.tag){case 5:Gl||Zl(n,t);case 6:var r=us,o=ds;us=null,fs(e,t,n),ds=o,null!==(us=r)&&(ds?(e=us,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):us.removeChild(n.stateNode));break;case 18:null!==us&&(ds?(e=us,n=n.stateNode,8===e.nodeType?so(e.parentNode,n):1===e.nodeType&&so(e,n),Wt(e)):so(us,n.stateNode));break;case 4:r=us,o=ds,us=n.stateNode.containerInfo,ds=!0,fs(e,t,n),us=r,ds=o;break;case 0:case 11:case 14:case 15:if(!Gl&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var i=o,a=i.destroy;i=i.tag,void 0!==a&&(0!==(2&i)||0!==(4&i))&&es(n,t,a),o=o.next}while(o!==r)}fs(e,t,n);break;case 1:if(!Gl&&(Zl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Cc(n,t,l)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Gl=(r=Gl)||null!==n.memoizedState,fs(e,t,n),Gl=r):fs(e,t,n);break;default:fs(e,t,n)}}function hs(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Kl),t.forEach((function(t){var r=Nc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ms(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var a=e,l=t,s=l;e:for(;null!==s;){switch(s.tag){case 5:us=s.stateNode,ds=!1;break e;case 3:case 4:us=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===us)throw Error(i(160));ps(a,l,o),us=null,ds=!1;var c=o.alternate;null!==c&&(c.return=null),o.return=null}catch(u){Cc(o,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),ys(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(g){Cc(e,e.return,g)}try{ns(5,e,e.return)}catch(g){Cc(e,e.return,g)}}break;case 1:ms(t,e),ys(e),512&r&&null!==n&&Zl(n,n.return);break;case 5:if(ms(t,e),ys(e),512&r&&null!==n&&Zl(n,n.return),32&e.flags){var o=e.stateNode;try{fe(o,"")}catch(g){Cc(e,e.return,g)}}if(4&r&&null!=(o=e.stateNode)){var a=e.memoizedProps,l=null!==n?n.memoizedProps:a,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===a.type&&null!=a.name&&K(o,a),xe(s,l);var u=xe(s,a);for(l=0;l<c.length;l+=2){var d=c[l],f=c[l+1];"style"===d?ge(o,f):"dangerouslySetInnerHTML"===d?de(o,f):"children"===d?fe(o,f):x(o,d,f,u)}switch(s){case"input":X(o,a);break;case"textarea":ie(o,a);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!a.multiple;var h=a.value;null!=h?ne(o,!!a.multiple,h,!1):p!==!!a.multiple&&(null!=a.defaultValue?ne(o,!!a.multiple,a.defaultValue,!0):ne(o,!!a.multiple,a.multiple?[]:"",!1))}o[ho]=a}catch(g){Cc(e,e.return,g)}}break;case 6:if(ms(t,e),ys(e),4&r){if(null===e.stateNode)throw Error(i(162));o=e.stateNode,a=e.memoizedProps;try{o.nodeValue=a}catch(g){Cc(e,e.return,g)}}break;case 3:if(ms(t,e),ys(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Wt(t.containerInfo)}catch(g){Cc(e,e.return,g)}break;case 4:default:ms(t,e),ys(e);break;case 13:ms(t,e),ys(e),8192&(o=e.child).flags&&(a=null!==o.memoizedState,o.stateNode.isHidden=a,!a||null!==o.alternate&&null!==o.alternate.memoizedState||(Ds=Ke())),4&r&&hs(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Gl=(u=Gl)||d,ms(t,e),Gl=u):ms(t,e),ys(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(Xl=e,d=e.child;null!==d;){for(f=Xl=d;null!==Xl;){switch(h=(p=Xl).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Zl(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Cc(r,n,g)}}break;case 5:Zl(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==h?(h.return=p,Xl=h):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,u?"function"===typeof(a=o.style).setProperty?a.setProperty("display","none","important"):a.display="none":(s=f.stateNode,l=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=me("display",l))}catch(g){Cc(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(g){Cc(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ms(t,e),ys(e),4&r&&hs(e);case 21:}}function ys(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(as(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(fe(o,""),r.flags&=-33),cs(e,ls(e),o);break;case 3:case 4:var a=r.stateNode.containerInfo;ss(e,ls(e),a);break;default:throw Error(i(161))}}catch(l){Cc(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vs(e,t,n){Xl=e,xs(e,t,n)}function xs(e,t,n){for(var r=0!==(1&e.mode);null!==Xl;){var o=Xl,i=o.child;if(22===o.tag&&r){var a=null!==o.memoizedState||Yl;if(!a){var l=o.alternate,s=null!==l&&null!==l.memoizedState||Gl;l=Yl;var c=Gl;if(Yl=a,(Gl=s)&&!c)for(Xl=o;null!==Xl;)s=(a=Xl).child,22===a.tag&&null!==a.memoizedState?ks(o):null!==s?(s.return=a,Xl=s):ks(o);for(;null!==i;)Xl=i,xs(i,t,n),i=i.sibling;Xl=o,Yl=l,Gl=c}bs(e)}else 0!==(8772&o.subtreeFlags)&&null!==i?(i.return=o,Xl=i):bs(e)}}function bs(e){for(;null!==Xl;){var t=Xl;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Gl||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Gl)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:nl(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&Hi(t,a,r);break;case 3:var l=t.updateQueue;if(null!==l){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Hi(t,l,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Wt(f)}}}break;default:throw Error(i(163))}Gl||512&t.flags&&os(t)}catch(p){Cc(t,t.return,p)}}if(t===e){Xl=null;break}if(null!==(n=t.sibling)){n.return=t.return,Xl=n;break}Xl=t.return}}function ws(e){for(;null!==Xl;){var t=Xl;if(t===e){Xl=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Xl=n;break}Xl=t.return}}function ks(e){for(;null!==Xl;){var t=Xl;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Cc(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(s){Cc(t,o,s)}}var i=t.return;try{os(t)}catch(s){Cc(t,i,s)}break;case 5:var a=t.return;try{os(t)}catch(s){Cc(t,a,s)}}}catch(s){Cc(t,t.return,s)}if(t===e){Xl=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Xl=l;break}Xl=t.return}}var Ss,Cs=Math.ceil,js=b.ReactCurrentDispatcher,Es=b.ReactCurrentOwner,_s=b.ReactCurrentBatchConfig,Ns=0,Ts=null,Rs=null,$s=0,Ps=0,Os=jo(0),Ls=0,Is=null,zs=0,As=0,Fs=0,Ms=null,Bs=null,Ds=0,Ws=1/0,Us=null,Hs=!1,qs=null,Vs=null,Js=!1,Qs=null,Ys=0,Gs=0,Ks=null,Xs=-1,Zs=0;function ec(){return 0!==(6&Ns)?Ke():-1!==Xs?Xs:Xs=Ke()}function tc(e){return 0===(1&e.mode)?1:0!==(2&Ns)&&0!==$s?$s&-$s:null!==gi.transition?(0===Zs&&(Zs=mt()),Zs):0!==(e=xt)?e:e=void 0===(e=window.event)?16:Gt(e.type)}function nc(e,t,n,r){if(50<Gs)throw Gs=0,Ks=null,Error(i(185));yt(e,n,r),0!==(2&Ns)&&e===Ts||(e===Ts&&(0===(2&Ns)&&(As|=n),4===Ls&&lc(e,$s)),rc(e,r),1===n&&0===Ns&&0===(1&t.mode)&&(Ws=Ke()+500,Bo&&Uo()))}function rc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-at(i),l=1<<a,s=o[a];-1===s?0!==(l&n)&&0===(l&r)||(o[a]=pt(l,t)):s<=t&&(e.expiredLanes|=l),i&=~l}}(e,t);var r=ft(e,e===Ts?$s:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Bo=!0,Wo(e)}(sc.bind(null,e)):Wo(sc.bind(null,e)),ao((function(){0===(6&Ns)&&Uo()})),n=null;else{switch(bt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Tc(n,oc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function oc(e,t){if(Xs=-1,Zs=0,0!==(6&Ns))throw Error(i(327));var n=e.callbackNode;if(kc()&&e.callbackNode!==n)return null;var r=ft(e,e===Ts?$s:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gc(e,r);else{t=r;var o=Ns;Ns|=2;var a=hc();for(Ts===e&&$s===t||(Us=null,Ws=Ke()+500,fc(e,t));;)try{vc();break}catch(s){pc(e,s)}_i(),js.current=a,Ns=o,null!==Rs?t=0:(Ts=null,$s=0,t=Ls)}if(0!==t){if(2===t&&(0!==(o=ht(e))&&(r=o,t=ic(e,o))),1===t)throw n=Is,fc(e,0),lc(e,r),rc(e,Ke()),n;if(6===t)lc(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!lr(i(),o))return!1}catch(l){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=gc(e,r))&&(0!==(a=ht(e))&&(r=a,t=ic(e,a))),1===t))throw n=Is,fc(e,0),lc(e,r),rc(e,Ke()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:case 5:wc(e,Bs,Us);break;case 3:if(lc(e,r),(130023424&r)===r&&10<(t=Ds+500-Ke())){if(0!==ft(e,0))break;if(((o=e.suspendedLanes)&r)!==r){ec(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ro(wc.bind(null,e,Bs,Us),t);break}wc(e,Bs,Us);break;case 4:if(lc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-at(r);a=1<<l,(l=t[l])>o&&(o=l),r&=~a}if(r=o,10<(r=(120>(r=Ke()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Cs(r/1960))-r)){e.timeoutHandle=ro(wc.bind(null,e,Bs,Us),r);break}wc(e,Bs,Us);break;default:throw Error(i(329))}}}return rc(e,Ke()),e.callbackNode===n?oc.bind(null,e):null}function ic(e,t){var n=Ms;return e.current.memoizedState.isDehydrated&&(fc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Bs,Bs=n,null!==t&&ac(t)),e}function ac(e){null===Bs?Bs=e:Bs.push.apply(Bs,e)}function lc(e,t){for(t&=~Fs,t&=~As,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-at(t),r=1<<n;e[n]=-1,t&=~r}}function sc(e){if(0!==(6&Ns))throw Error(i(327));kc();var t=ft(e,0);if(0===(1&t))return rc(e,Ke()),null;var n=gc(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=ic(e,r))}if(1===n)throw n=Is,fc(e,0),lc(e,t),rc(e,Ke()),n;if(6===n)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wc(e,Bs,Us),rc(e,Ke()),null}function cc(e,t){var n=Ns;Ns|=1;try{return e(t)}finally{0===(Ns=n)&&(Ws=Ke()+500,Bo&&Uo())}}function uc(e){null!==Qs&&0===Qs.tag&&0===(6&Ns)&&kc();var t=Ns;Ns|=1;var n=_s.transition,r=xt;try{if(_s.transition=null,xt=1,e)return e()}finally{xt=r,_s.transition=n,0===(6&(Ns=t))&&Uo()}}function dc(){Ps=Os.current,Eo(Os)}function fc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oo(n)),null!==Rs)for(n=Rs.return;null!==n;){var r=n;switch(ni(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Lo();break;case 3:Ki(),Eo(Ro),Eo(To),ra();break;case 5:Zi(r);break;case 4:Ki();break;case 13:case 19:Eo(ea);break;case 10:Ni(r.type._context);break;case 22:case 23:dc()}n=n.return}if(Ts=e,Rs=e=Oc(e.current,null),$s=Ps=t,Ls=0,Is=null,Fs=As=zs=0,Bs=Ms=null,null!==Pi){for(t=0;t<Pi.length;t++)if(null!==(r=(n=Pi[t]).interleaved)){n.interleaved=null;var o=r.next,i=n.pending;if(null!==i){var a=i.next;i.next=o,r.next=a}n.pending=r}Pi=null}return e}function pc(e,t){for(;;){var n=Rs;try{if(_i(),oa.current=Xa,ua){for(var r=la.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}ua=!1}if(aa=0,ca=sa=la=null,da=!1,fa=0,Es.current=null,null===n||null===n.return){Ls=1,Is=t,Rs=null;break}e:{var a=e,l=n.return,s=n,c=t;if(t=$s,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gl(l);if(null!==h){h.flags&=-257,yl(h,l,s,0,t),1&h.mode&&ml(a,u,t),c=u;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(c),t.updateQueue=g}else m.add(c);break e}if(0===(1&t)){ml(a,u,t),mc();break e}c=Error(i(426))}else if(ii&&1&s.mode){var y=gl(l);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),yl(y,l,s,0,t),mi(cl(c,s));break e}}a=c=cl(c,s),4!==Ls&&(Ls=2),null===Ms?Ms=[a]:Ms.push(a),a=l;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t,Wi(a,pl(0,c,t));break e;case 1:s=c;var v=a.type,x=a.stateNode;if(0===(128&a.flags)&&("function"===typeof v.getDerivedStateFromError||null!==x&&"function"===typeof x.componentDidCatch&&(null===Vs||!Vs.has(x)))){a.flags|=65536,t&=-t,a.lanes|=t,Wi(a,hl(a,s,t));break e}}a=a.return}while(null!==a)}bc(n)}catch(b){t=b,Rs===n&&null!==n&&(Rs=n=n.return);continue}break}}function hc(){var e=js.current;return js.current=Xa,null===e?Xa:e}function mc(){0!==Ls&&3!==Ls&&2!==Ls||(Ls=4),null===Ts||0===(268435455&zs)&&0===(268435455&As)||lc(Ts,$s)}function gc(e,t){var n=Ns;Ns|=2;var r=hc();for(Ts===e&&$s===t||(Us=null,fc(e,t));;)try{yc();break}catch(o){pc(e,o)}if(_i(),Ns=n,js.current=r,null!==Rs)throw Error(i(261));return Ts=null,$s=0,Ls}function yc(){for(;null!==Rs;)xc(Rs)}function vc(){for(;null!==Rs&&!Ye();)xc(Rs)}function xc(e){var t=Ss(e.alternate,e,Ps);e.memoizedProps=e.pendingProps,null===t?bc(e):Rs=t,Es.current=null}function bc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Jl(n,t,Ps)))return void(Rs=n)}else{if(null!==(n=Ql(n,t)))return n.flags&=32767,void(Rs=n);if(null===e)return Ls=6,void(Rs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Rs=t);Rs=t=e}while(null!==t);0===Ls&&(Ls=5)}function wc(e,t,n){var r=xt,o=_s.transition;try{_s.transition=null,xt=1,function(e,t,n,r){do{kc()}while(null!==Qs);if(0!==(6&Ns))throw Error(i(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-at(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}(e,a),e===Ts&&(Rs=Ts=null,$s=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Js||(Js=!0,Tc(tt,(function(){return kc(),null}))),a=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||a){a=_s.transition,_s.transition=null;var l=xt;xt=1;var s=Ns;Ns|=4,Es.current=null,function(e,t){if(eo=Ht,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(w){n=null;break e}var l=0,s=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(s=l+o),f!==a||0!==r&&3!==f.nodeType||(c=l+r),3===f.nodeType&&(l+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++u===o&&(s=l),p===a&&++d===r&&(c=l),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(to={focusedElem:e,selectionRange:n},Ht=!1,Xl=t;null!==Xl;)if(e=(t=Xl).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Xl=e;else for(;null!==Xl;){t=Xl;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,x=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:nl(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(i(163))}}catch(w){Cc(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Xl=e;break}Xl=t.return}m=ts,ts=!1}(e,n),gs(n,e),hr(to),Ht=!!eo,to=eo=null,e.current=n,vs(n,e,o),Ge(),Ns=s,xt=l,_s.transition=a}else e.current=n;if(Js&&(Js=!1,Qs=e,Ys=o),a=e.pendingLanes,0===a&&(Vs=null),function(e){if(it&&"function"===typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(ot,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rc(e,Ke()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hs)throw Hs=!1,e=qs,qs=null,e;0!==(1&Ys)&&0!==e.tag&&kc(),a=e.pendingLanes,0!==(1&a)?e===Ks?Gs++:(Gs=0,Ks=e):Gs=0,Uo()}(e,t,n,r)}finally{_s.transition=o,xt=r}return null}function kc(){if(null!==Qs){var e=bt(Ys),t=_s.transition,n=xt;try{if(_s.transition=null,xt=16>e?16:e,null===Qs)var r=!1;else{if(e=Qs,Qs=null,Ys=0,0!==(6&Ns))throw Error(i(331));var o=Ns;for(Ns|=4,Xl=e.current;null!==Xl;){var a=Xl,l=a.child;if(0!==(16&Xl.flags)){var s=a.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Xl=u;null!==Xl;){var d=Xl;switch(d.tag){case 0:case 11:case 15:ns(8,d,a)}var f=d.child;if(null!==f)f.return=d,Xl=f;else for(;null!==Xl;){var p=(d=Xl).sibling,h=d.return;if(is(d),d===u){Xl=null;break}if(null!==p){p.return=h,Xl=p;break}Xl=h}}}var m=a.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Xl=a}}if(0!==(2064&a.subtreeFlags)&&null!==l)l.return=a,Xl=l;else e:for(;null!==Xl;){if(0!==(2048&(a=Xl).flags))switch(a.tag){case 0:case 11:case 15:ns(9,a,a.return)}var v=a.sibling;if(null!==v){v.return=a.return,Xl=v;break e}Xl=a.return}}var x=e.current;for(Xl=x;null!==Xl;){var b=(l=Xl).child;if(0!==(2064&l.subtreeFlags)&&null!==b)b.return=l,Xl=b;else e:for(l=x;null!==Xl;){if(0!==(2048&(s=Xl).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(k){Cc(s,s.return,k)}if(s===l){Xl=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Xl=w;break e}Xl=s.return}}if(Ns=o,Uo(),it&&"function"===typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(ot,e)}catch(k){}r=!0}return r}finally{xt=n,_s.transition=t}}return!1}function Sc(e,t,n){e=Bi(e,t=pl(0,t=cl(n,t),1),1),t=ec(),null!==e&&(yt(e,1,t),rc(e,t))}function Cc(e,t,n){if(3===e.tag)Sc(e,e,n);else for(;null!==t;){if(3===t.tag){Sc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Vs||!Vs.has(r))){t=Bi(t,e=hl(t,e=cl(n,e),1),1),e=ec(),null!==t&&(yt(t,1,e),rc(t,e));break}}t=t.return}}function jc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&n,Ts===e&&($s&n)===n&&(4===Ls||3===Ls&&(130023424&$s)===$s&&500>Ke()-Ds?fc(e,0):Fs|=n),rc(e,t)}function Ec(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=ec();null!==(e=Ii(e,t))&&(yt(e,t,n),rc(e,n))}function _c(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ec(e,n)}function Nc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}null!==r&&r.delete(t),Ec(e,n)}function Tc(e,t){return Je(e,t)}function Rc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $c(e,t,n,r){return new Rc(e,t,n,r)}function Pc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Oc(e,t){var n=e.alternate;return null===n?((n=$c(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Lc(e,t,n,r,o,a){var l=2;if(r=e,"function"===typeof e)Pc(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case S:return Ic(n.children,o,a,t);case C:l=8,o|=8;break;case j:return(e=$c(12,n,t,2|o)).elementType=j,e.lanes=a,e;case T:return(e=$c(13,n,t,o)).elementType=T,e.lanes=a,e;case R:return(e=$c(19,n,t,o)).elementType=R,e.lanes=a,e;case O:return zc(n,o,a,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:l=10;break e;case _:l=9;break e;case N:l=11;break e;case $:l=14;break e;case P:l=16,r=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=$c(l,n,t,o)).elementType=e,t.type=r,t.lanes=a,t}function Ic(e,t,n,r){return(e=$c(7,e,r,t)).lanes=n,e}function zc(e,t,n,r){return(e=$c(22,e,r,t)).elementType=O,e.lanes=n,e.stateNode={isHidden:!1},e}function Ac(e,t,n){return(e=$c(6,e,null,t)).lanes=n,e}function Fc(e,t,n){return(t=$c(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mc(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Bc(e,t,n,r,o,i,a,l,s){return e=new Mc(e,t,n,l,s),1===t?(t=1,!0===i&&(t|=8)):t=0,i=$c(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ai(i),e}function Dc(e){if(!e)return No;e:{if(We(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Oo(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var n=e.type;if(Oo(n))return zo(e,n,t)}return t}function Wc(e,t,n,r,o,i,a,l,s){return(e=Bc(n,r,!0,e,0,i,0,l,s)).context=Dc(null),n=e.current,(i=Mi(r=ec(),o=tc(n))).callback=void 0!==t&&null!==t?t:null,Bi(n,i,o),e.current.lanes=o,yt(e,o,r),rc(e,r),e}function Uc(e,t,n,r){var o=t.current,i=ec(),a=tc(o);return n=Dc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Mi(i,a)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Bi(o,t,a))&&(nc(e,o,a,i),Di(e,o,a)),a}function Hc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Vc(e,t){qc(e,t),(e=e.alternate)&&qc(e,t)}Ss=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ro.current)xl=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return xl=!1,function(e,t,n){switch(t.tag){case 3:Tl(t),hi();break;case 5:Xi(t);break;case 1:Oo(t.type)&&Ao(t);break;case 4:Gi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;_o(Si,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(_o(ea,1&ea.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Al(e,t,n):(_o(ea,1&ea.current),null!==(e=Hl(e,t,n))?e.sibling:null);_o(ea,1&ea.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Wl(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),_o(ea,ea.current),r)break;return null;case 22:case 23:return t.lanes=0,Cl(e,t,n)}return Hl(e,t,n)}(e,t,n);xl=0!==(131072&e.flags)}else xl=!1,ii&&0!==(1048576&t.flags)&&ei(t,Jo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ul(e,t),e=t.pendingProps;var o=Po(t,To.current);Ri(t,n),o=ga(null,t,r,e,o,n);var a=ya();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Oo(r)?(a=!0,Ao(t)):a=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Ai(t),o.updater=ol,t.stateNode=o,o._reactInternals=t,sl(t,r,e,n),t=Nl(null,t,r,!0,a,n)):(t.tag=0,ii&&a&&ti(t),bl(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ul(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return Pc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===N)return 11;if(e===$)return 14}return 2}(r),e=nl(r,e),o){case 0:t=El(null,t,r,e,n);break e;case 1:t=_l(null,t,r,e,n);break e;case 11:t=wl(null,t,r,e,n);break e;case 14:t=kl(null,t,r,nl(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,El(e,t,r,o=t.elementType===r?o:nl(r,o),n);case 1:return r=t.type,o=t.pendingProps,_l(e,t,r,o=t.elementType===r?o:nl(r,o),n);case 3:e:{if(Tl(t),null===e)throw Error(i(387));r=t.pendingProps,o=(a=t.memoizedState).element,Fi(e,t),Ui(t,r,null,n);var l=t.memoizedState;if(r=l.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Rl(e,t,r,n,o=cl(Error(i(423)),t));break e}if(r!==o){t=Rl(e,t,r,n,o=cl(Error(i(424)),t));break e}for(oi=co(t.stateNode.containerInfo.firstChild),ri=t,ii=!0,ai=null,n=ki(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(hi(),r===o){t=Hl(e,t,n);break e}bl(e,t,r,n)}t=t.child}return t;case 5:return Xi(t),null===e&&ui(t),r=t.type,o=t.pendingProps,a=null!==e?e.memoizedProps:null,l=o.children,no(r,o)?l=null:null!==a&&no(r,a)&&(t.flags|=32),jl(e,t),bl(e,t,l,n),t.child;case 6:return null===e&&ui(t),null;case 13:return Al(e,t,n);case 4:return Gi(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wi(t,null,r,n):bl(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,wl(e,t,r,o=t.elementType===r?o:nl(r,o),n);case 7:return bl(e,t,t.pendingProps,n),t.child;case 8:case 12:return bl(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,a=t.memoizedProps,l=o.value,_o(Si,r._currentValue),r._currentValue=l,null!==a)if(lr(a.value,l)){if(a.children===o.children&&!Ro.current){t=Hl(e,t,n);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var s=a.dependencies;if(null!==s){l=a.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===a.tag){(c=Mi(-1,n&-n)).tag=2;var u=a.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}a.lanes|=n,null!==(c=a.alternate)&&(c.lanes|=n),Ti(a.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===a.tag)l=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(l=a.return))throw Error(i(341));l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Ti(l,n,t),l=a.sibling}else l=a.child;if(null!==l)l.return=a;else for(l=a;null!==l;){if(l===t){l=null;break}if(null!==(a=l.sibling)){a.return=l.return,l=a;break}l=l.return}a=l}bl(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Ri(t,n),r=r(o=$i(o)),t.flags|=1,bl(e,t,r,n),t.child;case 14:return o=nl(r=t.type,t.pendingProps),kl(e,t,r,o=nl(r.type,o),n);case 15:return Sl(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:nl(r,o),Ul(e,t),t.tag=1,Oo(r)?(e=!0,Ao(t)):e=!1,Ri(t,n),al(t,r,o),sl(t,r,o,n),Nl(null,t,r,!0,e,n);case 19:return Wl(e,t,n);case 22:return Cl(e,t,n)}throw Error(i(156,t.tag))};var Jc="function"===typeof reportError?reportError:function(e){console.error(e)};function Qc(e){this._internalRoot=e}function Yc(e){this._internalRoot=e}function Gc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Kc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Xc(){}function Zc(e,t,n,r,o){var i=n._reactRootContainer;if(i){var a=i;if("function"===typeof o){var l=o;o=function(){var e=Hc(a);l.call(e)}}Uc(t,a,e,o)}else a=function(e,t,n,r,o){if(o){if("function"===typeof r){var i=r;r=function(){var e=Hc(a);i.call(e)}}var a=Wc(t,r,e,0,null,!1,0,"",Xc);return e._reactRootContainer=a,e[mo]=a.current,Wr(8===e.nodeType?e.parentNode:e),uc(),a}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var l=r;r=function(){var e=Hc(s);l.call(e)}}var s=Bc(e,0,!1,null,0,!1,0,"",Xc);return e._reactRootContainer=s,e[mo]=s.current,Wr(8===e.nodeType?e.parentNode:e),uc((function(){Uc(t,s,n,r)})),s}(n,t,e,o,r);return Hc(a)}Yc.prototype.render=Qc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Uc(e,t,null,null)},Yc.prototype.unmount=Qc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc((function(){Uc(null,e,null,null)})),t[mo]=null}},Yc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ct();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ot.length&&0!==t&&t<Ot[n].priority;n++);Ot.splice(n,0,e),0===n&&At(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),rc(t,Ke()),0===(6&Ns)&&(Ws=Ke()+500,Uo()))}break;case 13:uc((function(){var t=Ii(e,1);if(null!==t){var n=ec();nc(t,e,1,n)}})),Vc(e,1)}},kt=function(e){if(13===e.tag){var t=Ii(e,134217728);if(null!==t)nc(t,e,134217728,ec());Vc(e,134217728)}},St=function(e){if(13===e.tag){var t=tc(e),n=Ii(e,t);if(null!==n)nc(n,e,t,ec());Vc(e,t)}},Ct=function(){return xt},jt=function(e,t){var n=xt;try{return xt=e,t()}finally{xt=n}},ke=function(e,t,n){switch(t){case"input":if(X(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ko(r);if(!o)throw Error(i(90));J(r),X(r,o)}}}break;case"textarea":ie(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ne=cc,Te=uc;var eu={usingClientEntryPoint:!1,Events:[bo,wo,ko,Ee,_e,cc]},tu={findFiberByHostInstance:xo,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nu={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=qe(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var ru=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ru.isDisabled&&ru.supportsFiber)try{ot=ru.inject(nu),it=ru}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gc(t))throw Error(i(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Gc(e))throw Error(i(299));var n=!1,r="",o=Jc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Bc(e,1,!1,null,0,n,0,r,o),e[mo]=t.current,Wr(8===e.nodeType?e.parentNode:e),new Qc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=qe(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,n){if(!Kc(t))throw Error(i(200));return Zc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Gc(e))throw Error(i(405));var r=null!=n&&n.hydratedSources||null,o=!1,a="",l=Jc;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onRecoverableError&&(l=n.onRecoverableError)),t=Wc(t,null,e,1,null!=n?n:null,o,0,a,l),e[mo]=t.current,Wr(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Yc(t)},t.render=function(e,t,n){if(!Kc(t))throw Error(i(200));return Zc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Kc(e))throw Error(i(40));return!!e._reactRootContainer&&(uc((function(){Zc(null,null,e,!1,(function(){e._reactRootContainer=null,e[mo]=null}))})),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Kc(n))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return Zc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/";var r={};n.r(r),n.d(r,{Decoder:()=>kn,Encoder:()=>bn,PacketType:()=>xn,protocol:()=>vn});var o=n(43),i=n(391),a=(n(358),"popstate");function l(){return h((function(e,t){let{pathname:n,search:r,hash:o}=e.location;return d("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:f(t)}),null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function s(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function c(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function u(e,t){return{usr:e.state,key:e.key,idx:t}}function d(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return{pathname:"string"===typeof e?e:e.pathname,search:"",hash:"",..."string"===typeof t?p(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function f(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function p(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function h(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:o=document.defaultView,v5Compat:i=!1}=r,l=o.history,c="POP",p=null,h=m();function m(){return(l.state||{idx:null}).idx}function g(){c="POP";let e=m(),t=null==e?null:e-h;h=e,p&&p({action:c,location:v.location,delta:t})}function y(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,n="string"===typeof e?e:f(e);return n=n.replace(/ $/,"%20"),s(t,`No window.location.(origin|href) available to create URL for href: ${n}`),new URL(n,t)}null==h&&(h=0,l.replaceState({...l.state,idx:h},""));let v={get action(){return c},get location(){return e(o,l)},listen(e){if(p)throw new Error("A history only accepts one active listener");return o.addEventListener(a,g),p=e,()=>{o.removeEventListener(a,g),p=null}},createHref:e=>t(o,e),createURL:y,encodeLocation(e){let t=y(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){c="PUSH";let r=d(v.location,e,t);n&&n(r,e),h=m()+1;let a=u(r,h),s=v.createHref(r);try{l.pushState(a,"",s)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;o.location.assign(s)}i&&p&&p({action:c,location:v.location,delta:1})},replace:function(e,t){c="REPLACE";let r=d(v.location,e,t);n&&n(r,e),h=m();let o=u(r,h),a=v.createHref(r);l.replaceState(o,"",a),i&&p&&p({action:c,location:v.location,delta:0})},go:e=>l.go(e)};return v}new WeakMap;function m(e,t){return g(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function g(e,t,n,r){let o=R(("string"===typeof t?p(t):t).pathname||"/",n);if(null==o)return null;let i=y(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let a=null;for(let l=0;null==a&&l<i.length;++l){let e=T(o);a=_(i[l],e,r)}return a}function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=(e,o,i)=>{let a={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};a.relativePath.startsWith("/")&&(s(a.relativePath.startsWith(r),`Absolute route path "${a.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),a.relativePath=a.relativePath.slice(r.length));let l=I([r,a.relativePath]),c=n.concat(a);e.children&&e.children.length>0&&(s(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),y(e.children,t,c,l)),(null!=e.path||e.index)&&t.push({path:l,score:E(l,e.index),routesMeta:c})};return e.forEach(((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of v(e.path))o(e,t,n);else o(e,t)})),t}function v(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===r.length)return o?[i,""]:[i];let a=v(r.join("/")),l=[];return l.push(...a.map((e=>""===e?i:[i,e].join("/")))),o&&l.push(...a),l.map((t=>e.startsWith("/")&&""===t?"/":t))}var x=/^:[\w-]+$/,b=3,w=2,k=1,S=10,C=-2,j=e=>"*"===e;function E(e,t){let n=e.split("/"),r=n.length;return n.some(j)&&(r+=C),t&&(r+=w),n.filter((e=>!j(e))).reduce(((e,t)=>e+(x.test(t)?b:""===t?k:S)),r)}function _(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,o={},i="/",a=[];for(let l=0;l<r.length;++l){let e=r[l],s=l===r.length-1,c="/"===i?t:t.slice(i.length)||"/",u=N({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},c),d=e.route;if(!u&&s&&n&&!r[r.length-1].route.index&&(u=N({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(o,u.params),a.push({params:o,pathname:I([i,u.pathname]),pathnameBase:z(I([i,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(i=I([i,u.pathnameBase]))}return a}function N(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];c("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let i=new RegExp(o,t?void 0:"i");return[i,r]}(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],a=i.replace(/(.)\/+$/,"$1"),l=o.slice(1),s=r.reduce(((e,t,n)=>{let{paramName:r,isOptional:o}=t;if("*"===r){let e=l[n]||"";a=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const s=l[n];return e[r]=o&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{});return{params:s,pathname:i,pathnameBase:a,pattern:e}}function T(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return c(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function R(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function $(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function P(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function O(e){let t=P(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function L(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=p(e):(r={...e},s(!r.pathname||!r.pathname.includes("?"),$("?","pathname","search",r)),s(!r.pathname||!r.pathname.includes("#"),$("#","pathname","hash",r)),s(!r.search||!r.search.includes("#"),$("#","search","hash",r)));let i,a=""===e||""===r.pathname,l=a?"/":r.pathname;if(null==l)i=n;else{let e=t.length-1;if(!o&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}i=e>=0?t[e]:"/"}let c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:o=""}="string"===typeof e?p(e):e,i=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:i,search:A(r),hash:F(o)}}(r,i),u=l&&"/"!==l&&l.endsWith("/"),d=(a||"."===l)&&n.endsWith("/");return c.pathname.endsWith("/")||!u&&!d||(c.pathname+="/"),c}var I=e=>e.join("/").replace(/\/\/+/g,"/"),z=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),A=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",F=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function M(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var B=["POST","PUT","PATCH","DELETE"],D=(new Set(B),["GET",...B]);new Set(D),Symbol("ResetLoaderData");var W=o.createContext(null);W.displayName="DataRouter";var U=o.createContext(null);U.displayName="DataRouterState";var H=o.createContext({isTransitioning:!1});H.displayName="ViewTransition";var q=o.createContext(new Map);q.displayName="Fetchers";var V=o.createContext(null);V.displayName="Await";var J=o.createContext(null);J.displayName="Navigation";var Q=o.createContext(null);Q.displayName="Location";var Y=o.createContext({outlet:null,matches:[],isDataRoute:!1});Y.displayName="Route";var G=o.createContext(null);G.displayName="RouteError";function K(){return null!=o.useContext(Q)}function X(){return s(K(),"useLocation() may be used only in the context of a <Router> component."),o.useContext(Q).location}var Z="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ee(e){o.useContext(J).static||o.useLayoutEffect(e)}function te(){let{isDataRoute:e}=o.useContext(Y);return e?function(){let{router:e}=ue("useNavigate"),t=fe("useNavigate"),n=o.useRef(!1);ee((()=>{n.current=!0}));let r=o.useCallback((async function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};c(n.current,Z),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...o}))}),[e,t]);return r}():function(){s(K(),"useNavigate() may be used only in the context of a <Router> component.");let e=o.useContext(W),{basename:t,navigator:n}=o.useContext(J),{matches:r}=o.useContext(Y),{pathname:i}=X(),a=JSON.stringify(O(r)),l=o.useRef(!1);ee((()=>{l.current=!0}));let u=o.useCallback((function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(c(l.current,Z),!l.current)return;if("number"===typeof r)return void n.go(r);let s=L(r,JSON.parse(a),i,"path"===o.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:I([t,s.pathname])),(o.replace?n.replace:n.push)(s,o.state,o)}),[t,n,a,i,e]);return u}()}o.createContext(null);function ne(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=o.useContext(Y),{pathname:r}=X(),i=JSON.stringify(O(n));return o.useMemo((()=>L(e,JSON.parse(i),r,"path"===t)),[e,i,r,t])}function re(e,t,n,r){s(K(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:i,static:a}=o.useContext(J),{matches:l}=o.useContext(Y),u=l[l.length-1],d=u?u.params:{},f=u?u.pathname:"/",h=u?u.pathnameBase:"/",g=u&&u.route;{let e=g&&g.path||"";me(f,!g||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${f}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let y,v=X();if(t){let e="string"===typeof t?p(t):t;s("/"===h||e.pathname?.startsWith(h),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${h}" but pathname "${e.pathname}" was given in the \`location\` prop.`),y=e}else y=v;let x=y.pathname||"/",b=x;if("/"!==h){let e=h.replace(/^\//,"").split("/");b="/"+x.replace(/^\//,"").split("/").slice(e.length).join("/")}let w=!a&&n&&n.matches&&n.matches.length>0?n.matches:m(e,{pathname:b});c(g||null!=w,`No routes matched location "${y.pathname}${y.search}${y.hash}" `),c(null==w||void 0!==w[w.length-1].route.element||void 0!==w[w.length-1].route.Component||void 0!==w[w.length-1].route.lazy,`Matched leaf route at location "${y.pathname}${y.search}${y.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let k=se(w&&w.map((e=>Object.assign({},e,{params:Object.assign({},d,e.params),pathname:I([h,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:I([h,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);return t&&k?o.createElement(Q.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...y},navigationType:"POP"}},k):k}function oe(){let e=pe(),t=M(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",i={padding:"0.5rem",backgroundColor:r},a={padding:"2px 4px",backgroundColor:r},l=null;return console.error("Error handled by React Router default ErrorBoundary:",e),l=o.createElement(o.Fragment,null,o.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),o.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",o.createElement("code",{style:a},"ErrorBoundary")," or"," ",o.createElement("code",{style:a},"errorElement")," prop on your route.")),o.createElement(o.Fragment,null,o.createElement("h2",null,"Unexpected Application Error!"),o.createElement("h3",{style:{fontStyle:"italic"}},t),n?o.createElement("pre",{style:i},n):null,l)}var ie=o.createElement(oe,null),ae=class extends o.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?o.createElement(Y.Provider,{value:this.props.routeContext},o.createElement(G.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function le(e){let{routeContext:t,match:n,children:r}=e,i=o.useContext(W);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),o.createElement(Y.Provider,{value:t},r)}function se(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,i=n?.errors;if(null!=i){let e=r.findIndex((e=>e.route.id&&void 0!==i?.[e.route.id]));s(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let a=!1,l=-1;if(n)for(let o=0;o<r.length;o++){let e=r[o];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=o),e.route.id){let{loaderData:t,errors:o}=n,i=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!o||void 0===o[e.route.id]);if(e.route.lazy||i){a=!0,r=l>=0?r.slice(0,l+1):[r[0]];break}}}return r.reduceRight(((e,s,c)=>{let u,d=!1,f=null,p=null;n&&(u=i&&s.route.id?i[s.route.id]:void 0,f=s.route.errorElement||ie,a&&(l<0&&0===c?(me("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,p=null):l===c&&(d=!0,p=s.route.hydrateFallbackElement||null)));let h=t.concat(r.slice(0,c+1)),m=()=>{let t;return t=u?f:d?p:s.route.Component?o.createElement(s.route.Component,null):s.route.element?s.route.element:e,o.createElement(le,{match:s,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(s.route.ErrorBoundary||s.route.errorElement||0===c)?o.createElement(ae,{location:n.location,revalidation:n.revalidation,component:f,error:u,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}function ce(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ue(e){let t=o.useContext(W);return s(t,ce(e)),t}function de(e){let t=o.useContext(U);return s(t,ce(e)),t}function fe(e){let t=function(e){let t=o.useContext(Y);return s(t,ce(e)),t}(e),n=t.matches[t.matches.length-1];return s(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function pe(){let e=o.useContext(G),t=de("useRouteError"),n=fe("useRouteError");return void 0!==e?e:t.errors?.[n]}var he={};function me(e,t,n){t||he[e]||(he[e]=!0,c(!1,n))}o.memo((function(e){let{routes:t,future:n,state:r}=e;return re(t,void 0,r,n)}));function ge(e){let{to:t,replace:n,state:r,relative:i}=e;s(K(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=o.useContext(J);c(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:l}=o.useContext(Y),{pathname:u}=X(),d=te(),f=L(t,O(l),u,"path"===i),p=JSON.stringify(f);return o.useEffect((()=>{d(JSON.parse(p),{replace:n,state:r,relative:i})}),[d,p,i,n,r]),null}function ye(e){s(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ve(e){let{basename:t="/",children:n=null,location:r,navigationType:i="POP",navigator:a,static:l=!1}=e;s(!K(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let u=t.replace(/^\/*/,"/"),d=o.useMemo((()=>({basename:u,navigator:a,static:l,future:{}})),[u,a,l]);"string"===typeof r&&(r=p(r));let{pathname:f="/",search:h="",hash:m="",state:g=null,key:y="default"}=r,v=o.useMemo((()=>{let e=R(f,u);return null==e?null:{location:{pathname:e,search:h,hash:m,state:g,key:y},navigationType:i}}),[u,f,h,m,g,y,i]);return c(null!=v,`<Router basename="${u}"> is not able to match the URL "${f}${h}${m}" because it does not start with the basename, so the <Router> won't render anything.`),null==v?null:o.createElement(J.Provider,{value:d},o.createElement(Q.Provider,{children:n,value:v}))}function xe(e){let{children:t,location:n}=e;return re(be(t),n)}o.Component;function be(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return o.Children.forEach(e,((e,r)=>{if(!o.isValidElement(e))return;let i=[...t,r];if(e.type===o.Fragment)return void n.push.apply(n,be(e.props.children,i));s(e.type===ye,`[${"string"===typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),s(!e.props.index||!e.props.children,"An index route cannot have child routes.");let a={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(a.children=be(e.props.children,i)),n.push(a)})),n}var we="get",ke="application/x-www-form-urlencoded";function Se(e){return null!=e&&"string"===typeof e.tagName}var Ce=null;var je=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ee(e){return null==e||je.has(e)?e:(c(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ke}"`),null)}function _e(e,t){let n,r,o,i,a;if(Se(l=e)&&"form"===l.tagName.toLowerCase()){let a=e.getAttribute("action");r=a?R(a,t):null,n=e.getAttribute("method")||we,o=Ee(e.getAttribute("enctype"))||ke,i=new FormData(e)}else if(function(e){return Se(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Se(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let a=e.form;if(null==a)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||a.getAttribute("action");if(r=l?R(l,t):null,n=e.getAttribute("formmethod")||a.getAttribute("method")||we,o=Ee(e.getAttribute("formenctype"))||Ee(a.getAttribute("enctype"))||ke,i=new FormData(a,e),!function(){if(null===Ce)try{new FormData(document.createElement("form"),0),Ce=!1}catch(e){Ce=!0}return Ce}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";i.append(`${e}x`,"0"),i.append(`${e}y`,"0")}else t&&i.append(t,r)}}else{if(Se(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=we,r=null,o=ke,a=e}var l;return i&&"text/plain"===o&&(a=i,i=void 0),{action:r,method:n.toLowerCase(),encType:o,formData:i,body:a}}function Ne(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function Te(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}function Re(e){return null!=e&&"string"===typeof e.page}function $e(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Pe(e,t,n,r,o,i){let a=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===i?t.filter(((e,t)=>a(e,t)||l(e,t))):"data"===i?t.filter(((t,i)=>{let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(a(t,i)||l(t,i))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0})):[]}function Oe(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let o=[r.module];return r.clientActionModule&&(o=o.concat(r.clientActionModule)),r.clientLoaderModule&&(o=o.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(o=o.concat(r.hydrateFallbackModule)),r.imports&&(o=o.concat(r.imports)),o})).flat(1),[...new Set(r)];var r}function Le(e,t){let n=new Set,r=new Set(t);return e.reduce(((e,o)=>{if(t&&!Re(o)&&"script"===o.as&&o.href&&r.has(o.href))return e;let i=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(o));return n.has(i)||(n.add(i),e.push({key:i,link:o})),e}),[])}function Ie(e){return{__html:e}}Symbol("SingleFetchRedirect");function ze(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===R(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}o.Component;function Ae(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let r,i=o.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."\n        );\n      '}});if(M(t))return o.createElement(Fe,{title:"Unhandled Thrown Response!"},o.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),i);if(t instanceof Error)r=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);r=new Error(e)}return o.createElement(Fe,{title:"Application Error!",isOutsideRemixApp:n},o.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),o.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),i)}function Fe(e){let{title:t,renderScripts:n,isOutsideRemixApp:r,children:i}=e,{routeModules:a}=Ue();return a.root?.Layout&&!r?i:o.createElement("html",{lang:"en"},o.createElement("head",null,o.createElement("meta",{charSet:"utf-8"}),o.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),o.createElement("title",null,t)),o.createElement("body",null,o.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},i,n?o.createElement(Ge,null):null)))}function Me(e){return!0===e}function Be(){let e=o.useContext(W);return Ne(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function De(){let e=o.useContext(U);return Ne(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var We=o.createContext(void 0);function Ue(){let e=o.useContext(We);return Ne(e,"You must render this element inside a <HydratedRouter> element"),e}function He(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function qe(e,t,n){if(n&&!Ye)return[e[0]];if(t){let n=e.findIndex((e=>void 0!==t[e.route.id]));return e.slice(0,n+1)}return e}function Ve(e){let{page:t,...n}=e,{router:r}=Be(),i=o.useMemo((()=>m(r.routes,t,r.basename)),[r.routes,t,r.basename]);return i?o.createElement(Qe,{page:t,matches:i,...n}):null}function Je(e){let{manifest:t,routeModules:n}=Ue(),[r,i]=o.useState([]);return o.useEffect((()=>{let r=!1;return async function(e,t,n){return Le((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await Te(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter($e).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}(e,t,n).then((e=>{r||i(e)})),()=>{r=!0}}),[e,t,n]),r}function Qe(e){let{page:t,matches:n,...r}=e,i=X(),{manifest:a,routeModules:l}=Ue(),{basename:s}=Be(),{loaderData:c,matches:u}=De(),d=o.useMemo((()=>Pe(t,n,u,a,i,"data")),[t,n,u,a,i]),f=o.useMemo((()=>Pe(t,n,u,a,i,"assets")),[t,n,u,a,i]),p=o.useMemo((()=>{if(t===i.pathname+i.search+i.hash)return[];let e=new Set,r=!1;if(n.forEach((t=>{let n=a.routes[t.route.id];n&&n.hasLoader&&(!d.some((e=>e.route.id===t.route.id))&&t.route.id in c&&l[t.route.id]?.shouldRevalidate||n.hasClientLoader?r=!0:e.add(t.route.id))})),0===e.size)return[];let o=ze(t,s);return r&&e.size>0&&o.searchParams.set("_routes",n.filter((t=>e.has(t.route.id))).map((e=>e.route.id)).join(",")),[o.pathname+o.search]}),[s,c,i,a,d,n,t,l]),h=o.useMemo((()=>Oe(f,a)),[f,a]),m=Je(f);return o.createElement(o.Fragment,null,p.map((e=>o.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r}))),h.map((e=>o.createElement("link",{key:e,rel:"modulepreload",href:e,...r}))),m.map((e=>{let{key:t,link:n}=e;return o.createElement("link",{key:t,...n})})))}We.displayName="FrameworkContext";var Ye=!1;function Ge(e){let{manifest:t,serverHandoffString:n,isSpaMode:r,ssr:i,renderMeta:a}=Ue(),{router:l,static:s,staticContext:c}=Be(),{matches:u}=De(),d=Me(i);a&&(a.didRenderScripts=!0);let f=qe(u,null,r);o.useEffect((()=>{Ye=!0}),[]);let p=o.useMemo((()=>{let r=c?`window.__reactRouterContext = ${n};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",i=s?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${d?"":`import ${JSON.stringify(t.url)}`};\n${f.map(((e,n)=>{let r=`route${n}`,o=t.routes[e.route.id];Ne(o,`Route ${e.route.id} not found in manifest`);let{clientActionModule:i,clientLoaderModule:a,hydrateFallbackModule:l,module:s}=o,c=[...i?[{module:i,varName:`${r}_clientAction`}]:[],...a?[{module:a,varName:`${r}_clientLoader`}]:[],...l?[{module:l,varName:`${r}_HydrateFallback`}]:[],{module:s,varName:`${r}_main`}];return 1===c.length?`import * as ${r} from ${JSON.stringify(s)};`:[c.map((e=>`import * as ${e.varName} from "${e.module}";`)).join("\n"),`const ${r} = {${c.map((e=>`...${e.varName}`)).join(",")}};`].join("\n")})).join("\n")}\n  ${d?`window.__reactRouterManifest = ${JSON.stringify(function(e,t){let n=new Set(t.state.matches.map((e=>e.route.id))),r=t.state.location.pathname.split("/").filter(Boolean),o=["/"];for(r.pop();r.length>0;)o.push(`/${r.join("/")}`),r.pop();o.forEach((e=>{let r=m(t.routes,e,t.basename);r&&r.forEach((e=>n.add(e.route.id)))}));let i=[...n].reduce(((t,n)=>Object.assign(t,{[n]:e.routes[n]})),{});return{...e,routes:i}}(t,l),null,2)};`:""}\n  window.__reactRouterRouteModules = {${f.map(((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`)).join(",")}};\n\nimport(${JSON.stringify(t.entry.module)});`:" ";return o.createElement(o.Fragment,null,o.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ie(r),type:void 0}),o.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ie(i),type:"module",async:!0}))}),[]),h=Ye?[]:t.entry.imports.concat(Oe(f,t,{includeHydrateFallback:!0}));return Ye?null:o.createElement(o.Fragment,null,d?null:o.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin}),o.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin}),(g=h,[...new Set(g)]).map((t=>o.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin}))),p);var g}function Ke(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach((t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)}))}}var Xe="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{Xe&&(window.__reactRouterVersion="7.3.0")}catch(ar){}function Ze(e){let{basename:t,children:n,window:r}=e,i=o.useRef();null==i.current&&(i.current=l({window:r,v5Compat:!0}));let a=i.current,[s,c]=o.useState({action:a.action,location:a.location}),u=o.useCallback((e=>{o.startTransition((()=>c(e)))}),[c]);return o.useLayoutEffect((()=>a.listen(u)),[a,u]),o.createElement(ve,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:a})}var et=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,tt=o.forwardRef((function(e,t){let n,{onClick:r,discover:i="render",prefetch:a="none",relative:l,reloadDocument:u,replace:d,state:p,target:h,to:m,preventScrollReset:g,viewTransition:y,...v}=e,{basename:x}=o.useContext(J),b="string"===typeof m&&et.test(m),w=!1;if("string"===typeof m&&b&&(n=m,Xe))try{let e=new URL(window.location.href),t=m.startsWith("//")?new URL(e.protocol+m):new URL(m),n=R(t.pathname,x);t.origin===e.origin&&null!=n?m=n+t.search+t.hash:w=!0}catch(ar){c(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let k=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s(K(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=o.useContext(J),{hash:i,pathname:a,search:l}=ne(e,{relative:t}),c=a;return"/"!==n&&(c="/"===a?n:I([n,a])),r.createHref({pathname:c,search:l,hash:i})}(m,{relative:l}),[S,C,j]=function(e,t){let n=o.useContext(We),[r,i]=o.useState(!1),[a,l]=o.useState(!1),{onFocus:s,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:f}=t,p=o.useRef(null);o.useEffect((()=>{if("render"===e&&l(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{l(e.isIntersecting)}))}),{threshold:.5});return p.current&&e.observe(p.current),()=>{e.disconnect()}}}),[e]),o.useEffect((()=>{if(r){let e=setTimeout((()=>{l(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let h=()=>{i(!0)},m=()=>{i(!1),l(!1)};return n?"intent"!==e?[a,p,{}]:[a,p,{onFocus:He(s,h),onBlur:He(c,m),onMouseEnter:He(u,h),onMouseLeave:He(d,m),onTouchStart:He(f,h)}]:[!1,p,{}]}(a,v),E=function(e){let{target:t,replace:n,state:r,preventScrollReset:i,relative:a,viewTransition:l}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=te(),c=X(),u=ne(e,{relative:a});return o.useCallback((o=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(o,t)){o.preventDefault();let t=void 0!==n?n:f(c)===f(u);s(e,{replace:t,state:r,preventScrollReset:i,relative:a,viewTransition:l})}}),[c,s,u,n,r,t,e,i,a,l])}(m,{replace:d,state:p,target:h,preventScrollReset:g,relative:l,viewTransition:y});let _=o.createElement("a",{...v,...j,href:n||k,onClick:w||u?r:function(e){r&&r(e),e.defaultPrevented||E(e)},ref:Ke(t,C),target:h,"data-discover":b||"render"!==i?void 0:"true"});return S&&!b?o.createElement(o.Fragment,null,_,o.createElement(Ve,{page:k})):_}));tt.displayName="Link",o.forwardRef((function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:i="",end:a=!1,style:l,to:c,viewTransition:u,children:d,...f}=e,p=ne(c,{relative:f.relative}),h=X(),m=o.useContext(U),{navigator:g,basename:y}=o.useContext(J),v=null!=m&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=o.useContext(H);s(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=ot("useViewTransitionState"),i=ne(e,{relative:t.relative});if(!n.isTransitioning)return!1;let a=R(n.currentLocation.pathname,r)||n.currentLocation.pathname,l=R(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=N(i.pathname,l)||null!=N(i.pathname,a)}(p)&&!0===u,x=g.encodeLocation?g.encodeLocation(p).pathname:p.pathname,b=h.pathname,w=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;r||(b=b.toLowerCase(),w=w?w.toLowerCase():null,x=x.toLowerCase()),w&&y&&(w=R(w,y)||w);const k="/"!==x&&x.endsWith("/")?x.length-1:x.length;let S,C=b===x||!a&&b.startsWith(x)&&"/"===b.charAt(k),j=null!=w&&(w===x||!a&&w.startsWith(x)&&"/"===w.charAt(x.length)),E={isActive:C,isPending:j,isTransitioning:v},_=C?n:void 0;S="function"===typeof i?i(E):[i,C?"active":null,j?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let T="function"===typeof l?l(E):l;return o.createElement(tt,{...f,"aria-current":_,className:S,ref:t,style:T,to:c,viewTransition:u},"function"===typeof d?d(E):d)})).displayName="NavLink";var nt=o.forwardRef(((e,t)=>{let{discover:n="render",fetcherKey:r,navigate:i,reloadDocument:a,replace:l,state:c,method:u=we,action:d,onSubmit:p,relative:h,preventScrollReset:m,viewTransition:g,...y}=e,v=lt(),x=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=o.useContext(J),r=o.useContext(Y);s(r,"useFormAction must be used inside a RouteContext");let[i]=r.matches.slice(-1),a={...ne(e||".",{relative:t})},l=X();if(null==e){a.search=l.search;let e=new URLSearchParams(a.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();a.search=n?`?${n}`:""}}e&&"."!==e||!i.route.index||(a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(a.pathname="/"===a.pathname?n:I([n,a.pathname]));return f(a)}(d,{relative:h}),b="get"===u.toLowerCase()?"get":"post",w="string"===typeof d&&et.test(d);return o.createElement("form",{ref:t,method:b,action:x,onSubmit:a?p:e=>{if(p&&p(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=t?.getAttribute("formmethod")||u;v(t||e.currentTarget,{fetcherKey:r,method:n,navigate:i,replace:l,state:c,relative:h,preventScrollReset:m,viewTransition:g})},...y,"data-discover":w||"render"!==n?void 0:"true"})}));function rt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ot(e){let t=o.useContext(W);return s(t,rt(e)),t}nt.displayName="Form";var it=0,at=()=>`__${String(++it)}__`;function lt(){let{router:e}=ot("useSubmit"),{basename:t}=o.useContext(J),n=fe("useRouteId");return o.useCallback((async function(r){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:i,method:a,encType:l,formData:s,body:c}=_e(r,t);if(!1===o.navigate){let t=o.fetcherKey||at();await e.fetch(t,n,o.action||i,{preventScrollReset:o.preventScrollReset,formData:s,body:c,formMethod:o.method||a,formEncType:o.encType||l,flushSync:o.flushSync})}else await e.navigate(o.action||i,{preventScrollReset:o.preventScrollReset,formData:s,body:c,formMethod:o.method||a,formEncType:o.encType||l,replace:o.replace,state:o.state,fromRouteId:n,flushSync:o.flushSync,viewTransition:o.viewTransition})}),[e,t,n])}new TextEncoder;const st=Object.create(null);st.open="0",st.close="1",st.ping="2",st.pong="3",st.message="4",st.upgrade="5",st.noop="6";const ct=Object.create(null);Object.keys(st).forEach((e=>{ct[st[e]]=e}));const ut={type:"error",data:"parser error"},dt="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),ft="function"===typeof ArrayBuffer,pt=e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,ht=(e,t,n)=>{let{type:r,data:o}=e;return dt&&o instanceof Blob?t?n(o):mt(o,n):ft&&(o instanceof ArrayBuffer||pt(o))?t?n(o):mt(new Blob([o]),n):n(st[r]+(o||""))},mt=(e,t)=>{const n=new FileReader;return n.onload=function(){const e=n.result.split(",")[1];t("b"+(e||""))},n.readAsDataURL(e)};function gt(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let yt;const vt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",xt="undefined"===typeof Uint8Array?[]:new Uint8Array(256);for(let sr=0;sr<64;sr++)xt[vt.charCodeAt(sr)]=sr;const bt="function"===typeof ArrayBuffer,wt=(e,t)=>{if("string"!==typeof e)return{type:"message",data:St(e,t)};const n=e.charAt(0);if("b"===n)return{type:"message",data:kt(e.substring(1),t)};return ct[n]?e.length>1?{type:ct[n],data:e.substring(1)}:{type:ct[n]}:ut},kt=(e,t)=>{if(bt){const n=(e=>{let t,n,r,o,i,a=.75*e.length,l=e.length,s=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);const c=new ArrayBuffer(a),u=new Uint8Array(c);for(t=0;t<l;t+=4)n=xt[e.charCodeAt(t)],r=xt[e.charCodeAt(t+1)],o=xt[e.charCodeAt(t+2)],i=xt[e.charCodeAt(t+3)],u[s++]=n<<2|r>>4,u[s++]=(15&r)<<4|o>>2,u[s++]=(3&o)<<6|63&i;return c})(e);return St(n,t)}return{base64:!0,data:e}},St=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,Ct=String.fromCharCode(30);function jt(){return new TransformStream({transform(e,t){!function(e,t){dt&&e.data instanceof Blob?e.data.arrayBuffer().then(gt).then(t):ft&&(e.data instanceof ArrayBuffer||pt(e.data))?t(gt(e.data)):ht(e,!1,(e=>{yt||(yt=new TextEncoder),t(yt.encode(e))}))}(e,(n=>{const r=n.length;let o;if(r<126)o=new Uint8Array(1),new DataView(o.buffer).setUint8(0,r);else if(r<65536){o=new Uint8Array(3);const e=new DataView(o.buffer);e.setUint8(0,126),e.setUint16(1,r)}else{o=new Uint8Array(9);const e=new DataView(o.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(r))}e.data&&"string"!==typeof e.data&&(o[0]|=128),t.enqueue(o),t.enqueue(n)}))}})}let Et;function _t(e){return e.reduce(((e,t)=>e+t.length),0)}function Nt(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let o=0;o<t;o++)n[o]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function Tt(e){if(e)return function(e){for(var t in Tt.prototype)e[t]=Tt.prototype[t];return e}(e)}Tt.prototype.on=Tt.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},Tt.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},Tt.prototype.off=Tt.prototype.removeListener=Tt.prototype.removeAllListeners=Tt.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var o=0;o<r.length;o++)if((n=r[o])===t||n.fn===t){r.splice(o,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},Tt.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var o=(n=n.slice(0)).length;r<o;++r)n[r].apply(this,t)}return this},Tt.prototype.emitReserved=Tt.prototype.emit,Tt.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},Tt.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Rt="function"===typeof Promise&&"function"===typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),$t="undefined"!==typeof self?self:"undefined"!==typeof window?window:Function("return this")();function Pt(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce(((t,n)=>(e.hasOwnProperty(n)&&(t[n]=e[n]),t)),{})}const Ot=$t.setTimeout,Lt=$t.clearTimeout;function It(e,t){t.useNativeTimers?(e.setTimeoutFn=Ot.bind($t),e.clearTimeoutFn=Lt.bind($t)):(e.setTimeoutFn=$t.setTimeout.bind($t),e.clearTimeoutFn=$t.clearTimeout.bind($t))}function zt(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class At extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class Ft extends Tt{constructor(e){super(),this.writable=!1,It(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new At(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=wt(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return-1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){const t=function(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}(e);return t.length?"?"+t:""}}class Mt extends Ft{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",(function(){--e||t()}))),this.writable||(e++,this.once("drain",(function(){--e||t()})))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){((e,t)=>{const n=e.split(Ct),r=[];for(let o=0;o<n.length;o++){const e=wt(n[o],t);if(r.push(e),"error"===e.type)break}return r})(e,this.socket.binaryType).forEach((e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)})),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,((e,t)=>{const n=e.length,r=new Array(n);let o=0;e.forEach(((e,i)=>{ht(e,!1,(e=>{r[i]=e,++o===n&&t(r.join(Ct))}))}))})(e,(e=>{this.doWrite(e,(()=>{this.writable=!0,this.emitReserved("drain")}))}))}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=zt()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let Bt=!1;try{Bt="undefined"!==typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(lr){}const Dt=Bt;function Wt(){}class Ut extends Mt{constructor(e){if(super(e),"undefined"!==typeof location){const t="https:"===location.protocol;let n=location.port;n||(n=t?"443":"80"),this.xd="undefined"!==typeof location&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",((e,t)=>{this.onError("xhr post error",e,t)}))}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",((e,t)=>{this.onError("xhr poll error",e,t)})),this.pollXhr=e}}class Ht extends Tt{constructor(e,t,n){super(),this.createRequest=e,It(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=void 0!==n.data?n.data:null,this._create()}_create(){var e;const t=Pt(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(t);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let e in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&n.setRequestHeader(e,this._opts.extraHeaders[e])}}catch(ar){}if("POST"===this._method)try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(ar){}try{n.setRequestHeader("Accept","*/*")}catch(ar){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var e;3===n.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(n.getResponseHeader("set-cookie"))),4===n.readyState&&(200===n.status||1223===n.status?this._onLoad():this.setTimeoutFn((()=>{this._onError("number"===typeof n.status?n.status:0)}),0))},n.send(this._data)}catch(ar){return void this.setTimeoutFn((()=>{this._onError(ar)}),0)}"undefined"!==typeof document&&(this._index=Ht.requestsCount++,Ht.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if("undefined"!==typeof this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=Wt,e)try{this._xhr.abort()}catch(ar){}"undefined"!==typeof document&&delete Ht.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(Ht.requestsCount=0,Ht.requests={},"undefined"!==typeof document)if("function"===typeof attachEvent)attachEvent("onunload",qt);else if("function"===typeof addEventListener){addEventListener("onpagehide"in $t?"pagehide":"unload",qt,!1)}function qt(){for(let e in Ht.requests)Ht.requests.hasOwnProperty(e)&&Ht.requests[e].abort()}const Vt=function(){const e=Jt({xdomain:!1});return e&&null!==e.responseType}();function Jt(e){const t=e.xdomain;try{if("undefined"!==typeof XMLHttpRequest&&(!t||Dt))return new XMLHttpRequest}catch(ar){}if(!t)try{return new($t[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(ar){}}const Qt="undefined"!==typeof navigator&&"string"===typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class Yt extends Ft{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=Qt?{}:Pt(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(lr){return this.emitReserved("error",lr)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;ht(n,this.supportsBinary,(e=>{try{this.doWrite(n,e)}catch(ar){}r&&Rt((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){"undefined"!==typeof this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=zt()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const Gt=$t.WebSocket||$t.MozWebSocket;const Kt={websocket:class extends Yt{createSocket(e,t,n){return Qt?new Gt(e,t,n):t?new Gt(e,t):new Gt(e)}doWrite(e,t){this.ws.send(t)}},webtransport:class extends Ft{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(lr){return this.emitReserved("error",lr)}this._transport.closed.then((()=>{this.onClose()})).catch((e=>{this.onError("webtransport error",e)})),this._transport.ready.then((()=>{this._transport.createBidirectionalStream().then((e=>{const t=function(e,t){Et||(Et=new TextDecoder);const n=[];let r=0,o=-1,i=!1;return new TransformStream({transform(a,l){for(n.push(a);;){if(0===r){if(_t(n)<1)break;const e=Nt(n,1);i=128===(128&e[0]),o=127&e[0],r=o<126?3:126===o?1:2}else if(1===r){if(_t(n)<2)break;const e=Nt(n,2);o=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(_t(n)<8)break;const e=Nt(n,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>Math.pow(2,21)-1){l.enqueue(ut);break}o=i*Math.pow(2,32)+t.getUint32(4),r=3}else{if(_t(n)<o)break;const e=Nt(n,o);l.enqueue(wt(i?e:Et.decode(e),t)),r=0}if(0===o||o>e){l.enqueue(ut);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),r=jt();r.readable.pipeTo(e.writable),this._writer=r.writable.getWriter();const o=()=>{n.read().then((e=>{let{done:t,value:n}=e;t||(this.onPacket(n),o())})).catch((e=>{}))};o();const i={type:"open"};this.query.sid&&(i.data=`{"sid":"${this.query.sid}"}`),this._writer.write(i).then((()=>this.onOpen()))}))}))}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;this._writer.write(n).then((()=>{r&&Rt((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}},polling:class extends Ut{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=Vt&&!t}request(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign(e,{xd:this.xd},this.opts),new Ht(Jt,this.uri(),e)}}},Xt=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Zt=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function en(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");-1!=n&&-1!=r&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let o=Xt.exec(e||""),i={},a=14;for(;a--;)i[Zt[a]]=o[a]||"";return-1!=n&&-1!=r&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=function(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||r.splice(0,1);"/"==t.slice(-1)&&r.splice(r.length-1,1);return r}(0,i.path),i.queryKey=function(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(e,t,r){t&&(n[t]=r)})),n}(0,i.query),i}const tn="function"===typeof addEventListener&&"function"===typeof removeEventListener,nn=[];tn&&addEventListener("offline",(()=>{nn.forEach((e=>e()))}),!1);class rn extends Tt{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"===typeof e&&(t=e,e=null),e){const n=en(e);t.hostname=n.host,t.secure="https"===n.protocol||"wss"===n.protocol,t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=en(t.host).host);It(this,t),this.secure=null!=t.secure?t.secure:"undefined"!==typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!==typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!==typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach((e=>{const t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e})),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"===typeof this.opts.query&&(this.opts.query=function(e){let t={},n=e.split("&");for(let r=0,o=n.length;r<o;r++){let e=n[r].split("=");t[decodeURIComponent(e[0])]=decodeURIComponent(e[1])}return t}(this.opts.query)),tn&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},nn.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(0===this.transports.length)return void this.setTimeoutFn((()=>{this.emitReserved("error","No transports available")}),0);const e=this.opts.rememberUpgrade&&rn.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",(e=>this._onClose("transport close",e)))}onOpen(){this.readyState="open",rn.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn((()=>{this._onClose("ping timeout")}),e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let n=0;n<this.writeBuffer.length;n++){const r=this.writeBuffer[n].data;if(r&&(e+="string"===typeof(t=r)?function(e){let t=0,n=0;for(let r=0,o=e.length;r<o;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}(t):Math.ceil(1.33*(t.byteLength||t.size))),n>0&&e>this._maxPayload)return this.writeBuffer.slice(0,n);e+=2}var t;return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,Rt((()=>{this._onClose("ping timeout")}),this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,r){if("function"===typeof t&&(r=t,t=void 0),"function"===typeof n&&(r=n,n=null),"closing"===this.readyState||"closed"===this.readyState)return;(n=n||{}).compress=!1!==n.compress;const o={type:e,data:t,options:n};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),r&&this.once("flush",r),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(()=>{this.upgrading?n():e()})):this.upgrading?n():e()),this}_onError(e){if(rn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),tn&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const e=nn.indexOf(this._offlineEventListener);-1!==e&&nn.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}rn.protocol=4;class on extends rn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;rn.priorWebsocketSuccess=!1;const r=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",(e=>{if(!n)if("pong"===e.type&&"probe"===e.data){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;rn.priorWebsocketSuccess="websocket"===t.name,this.transport.pause((()=>{n||"closed"!==this.readyState&&(c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}))}else{const e=new Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}})))};function o(){n||(n=!0,c(),t.close(),t=null)}const i=e=>{const n=new Error("probe error: "+e);n.transport=t.name,o(),this.emitReserved("upgradeError",n)};function a(){i("transport closed")}function l(){i("socket closed")}function s(e){t&&e.name!==t.name&&o()}const c=()=>{t.removeListener("open",r),t.removeListener("error",i),t.removeListener("close",a),this.off("close",l),this.off("upgrading",s)};t.once("open",r),t.once("error",i),t.once("close",a),this.once("close",l),this.once("upgrading",s),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn((()=>{n||t.open()}),200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}class an extends on{constructor(e){const t="object"===typeof e?e:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(!t.transports||t.transports&&"string"===typeof t.transports[0])&&(t.transports=(t.transports||["polling","websocket","webtransport"]).map((e=>Kt[e])).filter((e=>!!e))),super(e,t)}}const ln="function"===typeof ArrayBuffer,sn=Object.prototype.toString,cn="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===sn.call(Blob),un="function"===typeof File||"undefined"!==typeof File&&"[object FileConstructor]"===sn.call(File);function dn(e){return ln&&(e instanceof ArrayBuffer||(e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer)(e))||cn&&e instanceof Blob||un&&e instanceof File}function fn(e,t){if(!e||"object"!==typeof e)return!1;if(Array.isArray(e)){for(let t=0,n=e.length;t<n;t++)if(fn(e[t]))return!0;return!1}if(dn(e))return!0;if(e.toJSON&&"function"===typeof e.toJSON&&1===arguments.length)return fn(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&fn(e[n]))return!0;return!1}function pn(e){const t=[],n=e.data,r=e;return r.data=hn(n,t),r.attachments=t.length,{packet:r,buffers:t}}function hn(e,t){if(!e)return e;if(dn(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=hn(e[r],t);return n}if("object"===typeof e&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=hn(e[r],t));return n}return e}function mn(e,t){return e.data=gn(e.data,t),delete e.attachments,e}function gn(e,t){if(!e)return e;if(e&&!0===e._placeholder){if("number"===typeof e.num&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=gn(e[n],t);else if("object"===typeof e)for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=gn(e[n],t));return e}const yn=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],vn=5;var xn;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(xn||(xn={}));class bn{constructor(e){this.replacer=e}encode(e){return e.type!==xn.EVENT&&e.type!==xn.ACK||!fn(e)?[this.encodeAsString(e)]:this.encodeAsBinary({type:e.type===xn.EVENT?xn.BINARY_EVENT:xn.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id})}encodeAsString(e){let t=""+e.type;return e.type!==xn.BINARY_EVENT&&e.type!==xn.BINARY_ACK||(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=pn(e),n=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(n),r}}function wn(e){return"[object Object]"===Object.prototype.toString.call(e)}class kn extends Tt{constructor(e){super(),this.reviver=e}add(e){let t;if("string"===typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===xn.BINARY_EVENT;n||t.type===xn.BINARY_ACK?(t.type=n?xn.EVENT:xn.ACK,this.reconstructor=new Sn(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else{if(!dn(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t))}}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(void 0===xn[n.type])throw new Error("unknown packet type "+n.type);if(n.type===xn.BINARY_EVENT||n.type===xn.BINARY_ACK){const r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);const o=e.substring(r,t);if(o!=Number(o)||"-"!==e.charAt(t))throw new Error("Illegal attachments");n.attachments=Number(o)}if("/"===e.charAt(t+1)){const r=t+1;for(;++t;){if(","===e.charAt(t))break;if(t===e.length)break}n.nsp=e.substring(r,t)}else n.nsp="/";const r=e.charAt(t+1);if(""!==r&&Number(r)==r){const r=t+1;for(;++t;){const n=e.charAt(t);if(null==n||Number(n)!=n){--t;break}if(t===e.length)break}n.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){const r=this.tryParse(e.substr(t));if(!kn.isPayloadValid(n.type,r))throw new Error("invalid payload");n.data=r}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(ar){return!1}}static isPayloadValid(e,t){switch(e){case xn.CONNECT:return wn(t);case xn.DISCONNECT:return void 0===t;case xn.CONNECT_ERROR:return"string"===typeof t||wn(t);case xn.EVENT:case xn.BINARY_EVENT:return Array.isArray(t)&&("number"===typeof t[0]||"string"===typeof t[0]&&-1===yn.indexOf(t[0]));case xn.ACK:case xn.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Sn{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const e=mn(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function Cn(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const jn=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class En extends Tt{constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[Cn(e,"open",this.onopen.bind(this)),Cn(e,"packet",this.onpacket.bind(this)),Cn(e,"error",this.onerror.bind(this)),Cn(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.unshift("message"),this.emit.apply(this,t),this}emit(e){var t,n,r;if(jn.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];if(i.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(i),this;const l={type:xn.EVENT,data:i,options:{}};if(l.options.compress=!1!==this.flags.compress,"function"===typeof i[i.length-1]){const e=this.ids++,t=i.pop();this._registerAckCallback(e,t),l.id=e}const s=null===(n=null===(t=this.io.engine)||void 0===t?void 0:t.transport)||void 0===n?void 0:n.writable,c=this.connected&&!(null===(r=this.io.engine)||void 0===r?void 0:r._hasPingExpired());return this.flags.volatile&&!s||(c?(this.notifyOutgoingListeners(l),this.packet(l)):this.sendBuffer.push(l)),this.flags={},this}_registerAckCallback(e,t){var n,r=this;const o=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===o)return void(this.acks[e]=t);const i=this.io.setTimeoutFn((()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,new Error("operation has timed out"))}),o),a=function(){r.io.clearTimeoutFn(i);for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];t.apply(r,n)};a.withError=!0,this.acks[e]=a}emitWithAck(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return new Promise(((t,r)=>{const o=(e,n)=>e?r(e):t(n);o.withError=!0,n.push(o),this.emit(e,...n)}))}_addToQueue(e){var t=this;let n;"function"===typeof e[e.length-1]&&(n=e.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((function(e){if(r!==t._queue[0])return;if(null!==e)r.tryCount>t._opts.retries&&(t._queue.shift(),n&&n(e));else if(t._queue.shift(),n){for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];n(null,...i)}return r.pending=!1,t._drainQueue()})),this._queue.push(r),this._drainQueue()}_drainQueue(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.connected||0===this._queue.length)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth((e=>{this._sendConnectPacket(e)})):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:xn.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach((e=>{if(!this.sendBuffer.some((t=>String(t.id)===e))){const t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,new Error("socket has been disconnected"))}}))}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case xn.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case xn.EVENT:case xn.BINARY_EVENT:this.onevent(e);break;case xn.ACK:case xn.BINARY_ACK:this.onack(e);break;case xn.DISCONNECT:this.ondisconnect();break;case xn.CONNECT_ERROR:this.destroy();const t=new Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){const t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&"string"===typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(){if(!n){n=!0;for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];t.packet({type:xn.ACK,id:e,data:o})}}}onack(e){const t=this.acks[e.id];"function"===typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach((e=>this.emitEvent(e))),this.receiveBuffer=[],this.sendBuffer.forEach((e=>{this.notifyOutgoingListeners(e),this.packet(e)})),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach((e=>e())),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:xn.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function _n(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}_n.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-n:e+n}return 0|Math.min(e,this.max)},_n.prototype.reset=function(){this.attempts=0},_n.prototype.setMin=function(e){this.ms=e},_n.prototype.setMax=function(e){this.max=e},_n.prototype.setJitter=function(e){this.jitter=e};class Nn extends Tt{constructor(e,t){var n;super(),this.nsps={},this.subs=[],e&&"object"===typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,It(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(n=t.randomizationFactor)&&void 0!==n?n:.5),this.backoff=new _n({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;const o=t.parser||r;this.encoder=new o.Encoder,this.decoder=new o.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new an(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const r=Cn(t,"open",(function(){n.onopen(),e&&e()})),o=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},i=Cn(t,"error",o);if(!1!==this._timeout){const e=this._timeout,n=this.setTimeoutFn((()=>{r(),o(new Error("timeout")),t.close()}),e);this.opts.autoUnref&&n.unref(),this.subs.push((()=>{this.clearTimeoutFn(n)}))}return this.subs.push(r),this.subs.push(i),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(Cn(e,"ping",this.onping.bind(this)),Cn(e,"data",this.ondata.bind(this)),Cn(e,"error",this.onerror.bind(this)),Cn(e,"close",this.onclose.bind(this)),Cn(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(ar){this.onclose("parse error",ar)}}ondecoded(e){Rt((()=>{this.emitReserved("packet",e)}),this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new En(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const n of t){if(this.nsps[n].active)return}this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach((e=>e())),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn((()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open((t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()})))}),t);this.opts.autoUnref&&n.unref(),this.subs.push((()=>{this.clearTimeoutFn(n)}))}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const Tn={};function Rn(e,t){"object"===typeof e&&(t=e,e=void 0);const n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=e;n=n||"undefined"!==typeof location&&location,null==e&&(e=n.protocol+"//"+n.host),"string"===typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?n.protocol+e:n.host+e),/^(https?|wss?):\/\//.test(e)||(e="undefined"!==typeof n?n.protocol+"//"+e:"https://"+e),r=en(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const o=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+o+":"+r.port+t,r.href=r.protocol+"://"+o+(n&&n.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),r=n.source,o=n.id,i=n.path,a=Tn[o]&&i in Tn[o].nsps;let l;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?l=new Nn(r,t):(Tn[o]||(Tn[o]=new Nn(r,t)),l=Tn[o]),n.query&&!t.query&&(t.query=n.queryKey),l.socket(n.path,t)}Object.assign(Rn,{Manager:Nn,Socket:En,io:Rn,connect:Rn});var $n=n(579);const Pn=function(e){let{socket:t,gameState:n}=e;const[r,i]=(0,o.useState)(""),[a,l]=(0,o.useState)(!1),[s,c]=(0,o.useState)(!1),u=()=>{""!==r.trim()&&(t.emit("joinGame",r.trim()),i(""))},d=()=>{console.log("Attempting to start game..."),t.emit("startGame");t.once("gameState",(e=>{console.log("Game state after startGame event:",{started:e.started,players:e.players.length})}))};return(0,$n.jsx)("div",{className:"game-container",style:{backgroundImage:"url(/assets/images/om_the_journey_poster.jpg)",backgroundSize:"contain",backgroundPosition:"center center",backgroundRepeat:"no-repeat",backgroundColor:"#150D07",minHeight:"100vh",width:"100%",display:"flex",alignItems:"flex-end",justifyContent:"center",padding:"0 20px 40px"},children:(0,$n.jsxs)("div",{className:"card",style:{maxWidth:"400px",margin:"0 auto",textAlign:"center",background:"rgba(255, 255, 255, 0.85)",backdropFilter:"blur(5px)",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.3)",borderRadius:"12px",border:"1px solid rgba(255, 255, 255, 0.2)"},children:[(0,$n.jsx)("h2",{style:{color:"#5D3D1E",fontSize:"1.5rem",marginBottom:"0.5rem",marginTop:"0.5rem"},children:"Join the Game"}),(0,$n.jsxs)("div",{style:{padding:"0.5rem 1rem 1.5rem"},children:[(0,$n.jsxs)("div",{style:{display:"flex",gap:"0.5rem",marginTop:"0.5rem"},children:[(0,$n.jsx)("input",{type:"text",placeholder:"Enter your name",value:r,onChange:e=>i(e.target.value),onKeyPress:e=>{"Enter"===e.key&&u()},style:{flex:1,padding:"0.6rem",borderRadius:"6px",border:"1px solid #ddd"}}),(0,$n.jsx)("button",{onClick:u,style:{background:"#5D3D1E",color:"white",border:"none",borderRadius:"6px",padding:"0.6rem 1rem",fontWeight:"bold",cursor:"pointer"},children:"Join"})]}),(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",marginTop:"1rem",justifyContent:"center",background:"rgba(255, 255, 255, 0.3)",padding:"0.5rem",borderRadius:"6px"},children:[(0,$n.jsx)("input",{type:"checkbox",id:"botMode",checked:a,onChange:e=>l(e.target.checked),style:{marginRight:"0.5rem"}}),(0,$n.jsx)("label",{htmlFor:"botMode",style:{color:"#5D3D1E",fontWeight:"bold",cursor:"pointer"},children:"Enable Bot Mode (Play against external bots)"})]}),a&&(0,$n.jsx)("div",{style:{marginTop:"0.5rem",padding:"0.5rem",background:"rgba(212, 160, 72, 0.2)",borderRadius:"6px",fontSize:"0.9rem",color:"#5D3D1E"},children:a?"Bot Mode enabled. Start your bot clients using 'npm run start-bot' in a separate terminal.":null})]}),(0,$n.jsxs)("div",{style:{borderTop:"1px solid rgba(0,0,0,0.1)",padding:"1rem",backgroundColor:"rgba(255, 255, 255, 0.5)",borderBottomLeftRadius:"12px",borderBottomRightRadius:"12px"},children:[(0,$n.jsxs)("h3",{style:{margin:"0 0 0.8rem",color:"#5D3D1E"},children:["Players (",n.players.length,"/3)"]}),0===n.players.length?(0,$n.jsx)("p",{style:{color:"#5D3D1E",fontStyle:"italic",margin:"0.5rem 0"},children:"No players have joined yet"}):(0,$n.jsx)("div",{className:"flex gap-md",style:{justifyContent:"center",flexWrap:"wrap",margin:"0.8rem 0"},children:n.players.map((e=>(0,$n.jsxs)("div",{className:"card",style:{minWidth:"120px",padding:"0.6rem",background:e.id===t.id?"#5D3D1E":"rgba(255, 255, 255, 0.8)",color:e.id===t.id?"white":"#5D3D1E",borderRadius:"6px",margin:"0.25rem",fontWeight:e.id===t.id?"bold":"normal"},children:[e.name," ",e.id===t.id?"(you)":"",e.id&&e.id.startsWith("bot-")?" (Bot)":""]},e.id)))}),a&&1===n.players.length&&n.players[0].id===t.id?(0,$n.jsx)("button",{onClick:d,style:{backgroundColor:"#D4A048",color:"#5D3D1E",border:"none",borderRadius:"6px",padding:"0.6rem 1.5rem",fontWeight:"bold",cursor:"pointer",margin:"0.5rem 0 0"},children:"Start Game with Bot"}):n.players.length>=2&&n.players.length<=3?(0,$n.jsx)("button",{onClick:d,style:{backgroundColor:"#D4A048",color:"#5D3D1E",border:"none",borderRadius:"6px",padding:"0.6rem 1.5rem",fontWeight:"bold",cursor:"pointer",margin:"0.5rem 0 0"},children:"Start Game"}):(0,$n.jsx)("p",{style:{color:"#5D3D1E",margin:"0.5rem 0 0"},children:n.players.length<1?"Join the game to play":a?"Bot Mode enabled but you need to join first":"At least 2 players required to start"})]})]})})};const On=function(e){let{player:t,onClose:n,onTrade:r,socket:i}=e;const[a,l]=(0,o.useState)([]),[s,c]=(0,o.useState)(null),u=(t.energyCubes.filter((e=>"artha"===e.toLowerCase())),t.energyCubes.filter((e=>"bhakti"===e.toLowerCase())),t.energyCubes.filter((e=>"gnana"===e.toLowerCase())),t.energyCubes.filter((e=>"karma"===e.toLowerCase())),t.character?t.character.ability.takes:[]),d=t.character?t.character.ability.gives:"";(0,o.useEffect)((()=>{c(null)}),[a]),(0,o.useEffect)((()=>{if(i){const e=e=>{c(e)};return i.on("tradeError",e),()=>{i.off("tradeError",e)}}}),[i]);const f=(e,n)=>{const r=`${e}-${n}`,o=a.some((e=>e.id===r)),i=u.includes(e.toLowerCase());return(0,$n.jsx)("div",{className:`energy-cube ${e.toLowerCase()} ${i?"selectable":""} ${o?"selected":""}`,style:{width:"30px",height:"30px",margin:"5px",opacity:i?1:.5,cursor:i?"pointer":"not-allowed"},onClick:()=>((e,n)=>{const r=`${e}-${n}`;a.some((e=>e.id===r))?l(a.filter((e=>e.id!==r))):u.includes(e.toLowerCase())&&a.length<1?l([...a,{id:r,type:e}]):u.includes(e.toLowerCase())?a.length>=1&&c("You can only select 1 energy cube"):c(`You can't trade ${e} cubes with your ${t.character.type}`)})(e,n),title:i?`Select this ${e} cube`:`Can't trade ${e} cubes`},r)};return(0,$n.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.7)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3},children:(0,$n.jsxs)("div",{style:{backgroundColor:"#fff",borderRadius:"8px",padding:"20px",width:"90%",maxWidth:"400px",boxShadow:"0 5px 15px rgba(0, 0, 0, 0.3)"},children:[(0,$n.jsx)("h3",{style:{marginTop:0,borderBottom:"1px solid #eee",paddingBottom:"10px"},children:"Trade Energy Cubes"}),(0,$n.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,$n.jsxs)("div",{style:{backgroundColor:"#f8f8f8",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[(0,$n.jsxs)("p",{style:{marginTop:0,fontWeight:"bold"},children:[t.character.type,"'s Ability:"]}),(0,$n.jsx)("p",{style:{margin:0},children:t.character.description})]}),(0,$n.jsx)("p",{style:{fontWeight:"bold",marginBottom:"5px"},children:"Select 1 energy cube to trade:"}),(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap",justifyContent:"center"},children:t.energyCubes.map(((e,t)=>f(e,t)))}),a.length>0&&(0,$n.jsxs)("div",{style:{marginTop:"15px",backgroundColor:"#f0f9ff",padding:"10px",borderRadius:"6px",textAlign:"center"},children:[(0,$n.jsx)("p",{children:"You will receive:"}),(0,$n.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",marginTop:"5px"},children:(0,$n.jsx)("div",{className:`energy-cube ${d.toLowerCase()}`,style:{width:"40px",height:"40px"}})}),(0,$n.jsxs)("p",{style:{marginTop:"5px",fontWeight:"bold",textTransform:"capitalize"},children:["1 ",d," cube"]})]}),s&&(0,$n.jsx)("div",{style:{color:"red",marginTop:"10px",padding:"8px",backgroundColor:"#fff0f0",borderRadius:"4px",textAlign:"center"},children:s})]}),(0,$n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginTop:"15px"},children:[(0,$n.jsx)("button",{onClick:n,style:{padding:"8px 15px",backgroundColor:"#f0f0f0",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Cancel"}),(0,$n.jsx)("button",{onClick:()=>{if(1!==a.length)return void c("Select exactly 1 energy cube");const e=a.map((e=>e.type.toLowerCase()));i.emit("tradeEnergyCubes",e),n()},disabled:1!==a.length,style:{padding:"8px 15px",backgroundColor:1===a.length?"#4caf50":"#cccccc",color:"white",border:"none",borderRadius:"4px",cursor:1===a.length?"pointer":"not-allowed"},children:"Trade"})]})]})})};const Ln=function(e){var t,n,r,i,a,l;let{player:s,isActive:c,winner:u,socket:d,gameState:f}=e;if(!s)return console.error("PlayerMat received null or undefined player"),(0,$n.jsx)("div",{className:"player-mat error",children:"Error: Player data missing"});const p=s.omSlotsOuter?s.omSlotsOuter.reduce(((e,t)=>e+t),0):0,h=s.omSlotsInner?s.omSlotsInner.reduce(((e,t)=>e+t),0):0,m=null===f||void 0===f?void 0:f.currentGlobalEvent,[g,y]=(0,o.useState)(!1),v=u&&s.id===u.id,x=v&&u.winByOm,b=v&&u.winByScore,w=()=>{if(!("triathlon_bonus"===(null===m||void 0===m?void 0:m.effect)))return!1;const e=s.hand&&Array.isArray(s.hand)?s.hand.filter((e=>e&&void 0!==e.value)).map((e=>e.value)):[];return e.includes(1)&&e.includes(2)&&e.includes(3)};return console.log("PlayerMat rendering for:",{playerName:s.name,playerId:s.id,isActive:c,position:s.position,hasCharacter:!!s.character,characterType:null===(t=s.character)||void 0===t||null===(n=t.type)||void 0===n?void 0:n.toLowerCase(),handLength:(null===(r=s.hand)||void 0===r?void 0:r.length)||0,energyCubesLength:(null===(i=s.energyCubes)||void 0===i?void 0:i.length)||0,omSlotsOuterLength:(null===(a=s.omSlotsOuter)||void 0===a?void 0:a.length)||0,omSlotsInnerLength:(null===(l=s.omSlotsInner)||void 0===l?void 0:l.length)||0}),(0,$n.jsxs)("div",{className:`player-mat ${c?"active":""} ${v?"winner":""}`,style:{width:"250px",border:v?"3px solid gold":void 0,boxShadow:v?"0 0 10px gold":void 0,position:"relative"},children:[v&&(0,$n.jsx)("div",{style:{position:"absolute",top:"-15px",right:"-15px",backgroundColor:"gold",color:"#333",fontWeight:"bold",padding:"5px 10px",borderRadius:"20px",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",zIndex:10,transform:"rotate(15deg)"},children:x?"Winner by OM!":b?"Winner by Score!":"Winner!"}),(0,$n.jsx)("h3",{style:{borderBottom:v?"2px solid gold":"2px solid var(--accent-color)",paddingBottom:"0.5rem",marginBottom:"0.75rem",color:v?"goldenrod":void 0,fontWeight:v?"bold":void 0},children:s.name}),w()&&(0,$n.jsxs)("div",{onClick:()=>{w()&&d.emit("triathlonMovement")},style:{padding:"8px 12px",background:"linear-gradient(45deg, #FF9800, #FF5722)",borderRadius:"8px",color:"white",fontWeight:"bold",textAlign:"center",marginBottom:"15px",cursor:"pointer",boxShadow:"0 0 10px rgba(255, 152, 0, 0.7)",animation:"pulse 1.5s infinite"},children:[(0,$n.jsx)("style",{children:"\n              @keyframes pulse {\n                0% { box-shadow: 0 0 10px rgba(255, 152, 0, 0.7); }\n                50% { box-shadow: 0 0 20px rgba(255, 87, 34, 0.9); }\n                100% { box-shadow: 0 0 10px rgba(255, 152, 0, 0.7); }\n              }\n            "}),"Triathlon (6 Hops)"]}),(()=>{if(!s.character)return null;const e=s.character,t=e.type.toLowerCase();return(0,$n.jsxs)("div",{className:"character-card",style:{margin:"1rem 0"},children:[(0,$n.jsx)("div",{className:"character-card-image",style:{borderRadius:"8px",width:"100%",height:"180px",overflow:"hidden",boxShadow:"0 2px 4px rgba(0,0,0,0.2)"},children:(0,$n.jsx)("img",{src:`/assets/images/characters/${t}.jpg`,alt:e.type,className:"character-debug-img",style:{width:"100%",height:"100%",objectFit:"contain",borderRadius:"inherit"},onLoad:e=>{console.log(`Character image loaded successfully: ${t}.jpg`)},onError:e=>{console.error(`Failed to load character image: ${t}.jpg, src: ${e.target.src}`),e.target.style.display="none",e.target.parentNode.innerText=t.charAt(0).toUpperCase()}})}),(0,$n.jsx)("div",{className:"character-card-title",style:{textAlign:"center",fontWeight:"bold",marginTop:"0.5rem",fontSize:"1.1rem"},children:e.type}),(0,$n.jsx)("div",{style:{textAlign:"center",fontSize:"0.8rem",marginTop:"0.25rem",color:"#666"},children:e.description})]})})(),(0,$n.jsxs)("div",{className:"flex",style:{justifyContent:"space-between"},children:[(0,$n.jsxs)("div",{children:[(0,$n.jsxs)("p",{children:[(0,$n.jsx)("strong",{children:"Position:"})," ",s.position]}),(0,$n.jsxs)("p",{children:[(0,$n.jsx)("strong",{children:"OM (temp):"})," ",s.omTemp&&Array.isArray(s.omTemp)?s.omTemp.length:0]})]}),(0,$n.jsxs)("div",{children:[(0,$n.jsx)("p",{children:(0,$n.jsx)("strong",{children:"Score:"})}),(0,$n.jsxs)("p",{children:["Outer: ",s.outerScore]}),(0,$n.jsxs)("p",{children:["Inner: ",s.innerScore]}),v&&(0,$n.jsxs)("p",{style:{color:"goldenrod",fontWeight:"bold"},children:["Total: ",s.outerScore+s.innerScore]})]})]}),(0,$n.jsxs)("div",{style:{margin:"0.75rem 0"},children:[(0,$n.jsx)("p",{children:(0,$n.jsx)("strong",{children:"OM Slots:"})}),(0,$n.jsxs)("div",{className:"flex",style:{justifyContent:"space-between"},children:[(0,$n.jsxs)("div",{children:[(0,$n.jsxs)("p",{children:["Outer: ",p,"/7"]}),(0,$n.jsx)("div",{className:"flex gap-sm",children:s.omSlotsOuter&&Array.isArray(s.omSlotsOuter)?s.omSlotsOuter.map(((e,t)=>(0,$n.jsx)("div",{style:{width:"20px",height:"20px",border:v&&e?"1px solid gold":"1px solid #ccc",background:e?v?"gold":"var(--accent-color)":"transparent",borderRadius:"4px",textAlign:"center",lineHeight:"20px",fontSize:"0.7rem"},children:e||""},t))):null})]}),(0,$n.jsxs)("div",{children:[(0,$n.jsxs)("p",{children:["Inner: ",h,"/7"]}),(0,$n.jsx)("div",{className:"flex gap-sm",children:s.omSlotsInner&&Array.isArray(s.omSlotsInner)?s.omSlotsInner.map(((e,t)=>(0,$n.jsx)("div",{style:{width:"20px",height:"20px",border:v&&e?"1px solid gold":"1px solid #ccc",background:e?v?"gold":"var(--primary-color)":"transparent",borderRadius:"4px",textAlign:"center",lineHeight:"20px",fontSize:"0.7rem"},children:e||""},t))):null})]})]})]}),(0,$n.jsxs)("div",{style:{margin:"0.75rem 0"},children:[(0,$n.jsxs)("div",{className:"flex",style:{justifyContent:"space-between",alignItems:"center"},children:[(0,$n.jsx)("p",{children:(0,$n.jsx)("strong",{children:"Energy Cubes:"})}),c&&s.character&&s.energyCubes&&Array.isArray(s.energyCubes)&&s.energyCubes.length>=1&&!s.didTradeThisTurn&&(0,$n.jsx)("button",{className:"trade-btn accent",onClick:()=>y(!0),children:"Trade"})]}),(0,$n.jsx)("div",{className:"flex gap-sm",style:{flexWrap:"wrap"},children:s.energyCubes&&Array.isArray(s.energyCubes)?s.energyCubes.map(((e,t)=>(0,$n.jsx)("div",{className:`energy-cube ${e?e.toLowerCase():"unknown"}`,title:e||"Unknown"},t))):null})]}),(0,$n.jsxs)("div",{style:{margin:"0.75rem 0"},children:[(0,$n.jsxs)("div",{className:"flex",style:{justifyContent:"space-between",alignItems:"center"},children:[(0,$n.jsx)("p",{children:(0,$n.jsxs)("strong",{children:["Hand (",s.hand&&Array.isArray(s.hand)?s.hand.length:0,"/4):"]})}),c&&s.hand&&Array.isArray(s.hand)&&s.hand.some((e=>e&&void 0!==e.value))&&!s.didMoveThisTurn&&(0,$n.jsx)("button",{className:"travel-btn accent",onClick:()=>d.emit("initiateTravelCardSelection"),style:{backgroundColor:"var(--primary-color)",color:"white",padding:"5px 10px",borderRadius:"4px",border:"none",cursor:"pointer",fontWeight:"bold",boxShadow:"0 2px 4px rgba(0,0,0,0.2)"},children:"Travel"})]}),(0,$n.jsx)("div",{children:s.hand&&Array.isArray(s.hand)?s.hand.map(((e,t)=>{if(!e)return null;let n="",r="",o="#f3e5f5",i="#000",a=null;if(void 0!==e.value)return n=`Move ${e.value} spaces`,r="Travel Card",o="#FFF8E1",i="#1976d2",a=`/assets/images/vehicles/${e.value}/${e.vehicle||"camel"}.png`,(0,$n.jsx)("div",{className:"card-item",style:{padding:"0.75rem",margin:"0.5rem 0",background:o,borderRadius:"8px",border:`1px solid ${i}`,boxShadow:"0 2px 4px rgba(0,0,0,0.1)",display:"flex",justifyContent:"center",alignItems:"center",height:"150px"},children:(0,$n.jsx)("img",{src:a,alt:`${e.vehicle} (${e.value})`,style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain"},onError:e=>{e.target.style.display="none"}})},t);if("extraHop"===e.type)n="Extra Hop",r="Extra Hop",o="#e8f5e9",i="#2e7d32";else if("event"===e.type)n=e.name||e.type,r="Event Card",o="#f3e5f5",i="#7b1fa2";else if("journey"===e.type){let t="";e.required&&"object"===typeof e.required&&(t=Object.entries(e.required).map((e=>{let[t,n]=e;return`${t}:${n}`})).join(", "));let a="";e.reward&&(void 0!==e.reward.outer?a=`Outer: ${e.reward.outer}`:void 0!==e.reward.inner&&(a=`Inner: ${e.reward.inner}`)),n=`${t} \u2192 ${a}`,r="Journey Card",o="#fff3e0",i="#f57c00"}else"wildCube"===e.type&&(n="Wild Energy Cube",r="Event Card",o="#f3e5f5",i="#7b1fa2");return(0,$n.jsxs)("div",{className:"card-item",style:{fontSize:"0.9rem",padding:"0.75rem",margin:"0.5rem 0",background:o,borderRadius:"8px",border:`1px solid ${i}`,boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[(0,$n.jsx)("div",{style:{fontWeight:"bold",marginBottom:"0.5rem",color:i,fontSize:"1rem"},children:r}),(0,$n.jsx)("div",{style:{color:"#333",lineHeight:"1.4"},children:n})]},t)})):null})]}),g&&(0,$n.jsx)(On,{player:s,onClose:()=>y(!1),socket:d})]})};const In=function(e){var t;let{card:n,onClose:r,onCollect:o,gameState:i}=e;if(!n)return null;const a=n.reward&&void 0!==n.reward.inner,l=a?"Inner":"Outer",s=a?n.reward.inner:n.reward.outer,c=a?"var(--primary-color)":"var(--accent-color)",u=null===i||void 0===i||null===(t=i.locations)||void 0===t?void 0:t.find((e=>e.id===n.locationId)),d=u?u.name:"Unknown Location",f=u?u.region:"unknown",p=u?u.journeyType:"",h=[];n.required&&Object.entries(n.required).forEach((e=>{let[t,n]=e;for(let r=0;r<n;r++)h.push(t.toLowerCase())}));const m=(()=>{const e=i.players.find((e=>e.id===localStorage.getItem("playerId")));if(!e||!n.required)return 0;const t=e.energyCubes||[],r={artha:t.filter((e=>"artha"===e)).length,karma:t.filter((e=>"karma"===e)).length,gnana:t.filter((e=>"gnana"===e)).length,bhakti:t.filter((e=>"bhakti"===e)).length};let o=0;for(const[i,a]of Object.entries(n.required)){o+=Math.max(0,a-r[i])}return o})(),g=i.players.find((e=>e.id===localStorage.getItem("playerId"))),y=null!==g&&void 0!==g&&g.hand?g.hand.filter((e=>"wildCube"===e.type)).length:0;return(0,$n.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.7)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1500,backdropFilter:"blur(3px)"},children:(0,$n.jsxs)("div",{style:{width:"90%",maxWidth:"550px",background:"white",borderRadius:"12px",overflow:"hidden",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.3)",display:"flex",flexDirection:"column",maxHeight:"90vh"},children:[(0,$n.jsxs)("div",{style:{background:c,color:"white",padding:"16px 20px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,$n.jsxs)("div",{children:[(0,$n.jsx)("h2",{style:{margin:0,fontSize:"1.5rem",fontWeight:"bold"},children:d}),(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px",fontSize:"0.9rem",marginTop:"4px"},children:[(0,$n.jsxs)("span",{children:["#",n.locationId]}),(0,$n.jsx)("div",{style:{width:"12px",height:"12px",borderRadius:"50%",background:`var(--${f.toLowerCase()}-color)`,border:"1px solid white"}}),(0,$n.jsx)("span",{children:f})]})]}),(0,$n.jsx)("button",{onClick:r,style:{background:"rgba(255, 255, 255, 0.3)",border:"none",borderRadius:"50%",width:"36px",height:"36px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"1.2rem",cursor:"pointer",color:"white",boxShadow:"none"},children:"\u2715"})]}),(0,$n.jsxs)("div",{style:{display:"flex",flexDirection:"column",flex:1,overflow:"auto"},children:[(0,$n.jsx)("div",{style:{height:"240px",backgroundImage:`url(/assets/images/cards/${n.locationId}.png)`,backgroundSize:"cover",backgroundPosition:"center",position:"relative"},children:(0,$n.jsx)("div",{style:{position:"absolute",bottom:"16px",right:"16px",background:c,color:"white",borderRadius:"50%",width:"48px",height:"48px",display:"flex",justifyContent:"center",alignItems:"center",fontSize:"1.4rem",fontWeight:"bold",border:"3px solid white",boxShadow:"0 4px 8px rgba(0,0,0,0.3)"},children:s})}),(0,$n.jsxs)("div",{style:{padding:"24px"},children:[(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:"20px"},children:[(0,$n.jsxs)("div",{style:{fontSize:"1rem",fontWeight:"bold",color:c,marginRight:"10px",textTransform:"uppercase"},children:[l," Journey"]}),(0,$n.jsx)("div",{style:{fontSize:"0.9rem",color:"#666",fontStyle:"italic"},children:p})]}),(0,$n.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,$n.jsx)("h3",{style:{fontSize:"1rem",marginBottom:"10px",color:"#333",borderBottom:"1px solid #eee",paddingBottom:"8px"},children:"Required Resources"}),(0,$n.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,$n.jsxs)("div",{style:{display:"flex",gap:"8px"},children:[h.map(((e,t)=>(0,$n.jsx)("div",{style:{width:"36px",height:"36px",borderRadius:"6px",background:`var(--${e}-color)`,border:"1px solid #666",boxShadow:"0 2px 4px rgba(0,0,0,0.2)"}},t))),0===h.length&&(0,$n.jsx)("div",{style:{color:"#666",fontStyle:"italic"},children:"No resources required"})]}),m>0&&g&&(0,$n.jsxs)("div",{style:{padding:"8px 12px",background:m<=y?"rgba(0, 180, 0, 0.1)":"rgba(220, 0, 0, 0.1)",borderRadius:"6px",display:"flex",alignItems:"center",gap:"8px"},children:[(0,$n.jsxs)("span",{style:{fontWeight:"bold",color:m<=y?"green":"red"},children:["Will use ",m," wild cube",m>1?"s":""]}),(0,$n.jsxs)("div",{style:{fontSize:"0.9rem",color:"#666"},children:["(",y," available)"]})]})]})]}),(0,$n.jsxs)("div",{children:[(0,$n.jsx)("h3",{style:{fontSize:"1rem",marginBottom:"10px",color:"#333",borderBottom:"1px solid #eee",paddingBottom:"8px"},children:"Reward"}),(0,$n.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:(0,$n.jsxs)("div",{style:{fontSize:"1.1rem",fontWeight:"bold",color:c,display:"flex",alignItems:"center",gap:"8px"},children:[(0,$n.jsxs)("span",{style:{background:c,color:"white",width:"36px",height:"36px",borderRadius:"50%",display:"flex",justifyContent:"center",alignItems:"center"},children:["+",s]}),(0,$n.jsxs)("span",{children:[l," OM Points"]})]})})]})]})]}),(0,$n.jsxs)("div",{style:{padding:"16px",borderTop:"1px solid #eee",display:"flex",justifyContent:"flex-end",gap:"12px"},children:[(0,$n.jsx)("button",{className:"secondary",onClick:r,children:"Cancel"}),(0,$n.jsx)("button",{onClick:()=>o(n),style:{background:c},children:"Collect Card"})]})]})})};const zn=function(e){let{socket:t,gameState:n}=e;const{faceUpTravel:r,faceUpEvent:i,faceUpJourneyOuter:a,faceUpJourneyInner:l,players:s,turnIndex:c,currentGlobalEvent:u,energyCubePile:d}=n,f=s[c],[p,h]=(0,o.useState)([]),[m,g]=(0,o.useState)(null),[y,v]=(0,o.useState)(null);(0,o.useEffect)((()=>{h([]),g(null),console.log("FaceUpCards received updated gameState, current faceUpTravel:",r?r.map((e=>e.id)):"none")}),[n]);const x=e=>e.id===f.id,b=(e,t)=>void 0!==t.reward.outer?e.collectedJourneys.filter((e=>e.reward&&void 0!==e.reward.outer)).length:e.collectedJourneys.filter((e=>e.reward&&void 0!==e.reward.inner)).length,w=(e,t)=>{const n=t.position===e.locationId;return("no_inner_journey_cards"!==(null===u||void 0===u?void 0:u.effect)||void 0===e.reward.inner)&&(n&&((e,t)=>{const n=t.hand?t.hand.filter((e=>"wildCube"===e.type)).length:0,r=t.energyCubes||[],o={artha:r.filter((e=>"artha"===e)).length,karma:r.filter((e=>"karma"===e)).length,gnana:r.filter((e=>"gnana"===e)).length,bhakti:r.filter((e=>"bhakti"===e)).length};let i=0;for(const[a,l]of Object.entries(e))i+=Math.max(0,l-o[a]);return i<=n})(e.required,t)&&((e,t)=>{const n=[1,1,2,3],r=b(t,e);if(r>=n.length)return!1;const o=n[r];return t.omTemp.length>=o})(e,t))},k=(e,r,o)=>{var i;const a=f&&f.id===t.id,l=a&&x(f)&&w(e,f),s=[1,1,2,3],c=b(f,e),d=c<s.length?s[c]:"\u2715",p=null===(i=n.locations)||void 0===i?void 0:i.find((t=>t.id===e.locationId)),h=p?p.name:"Unknown Location",m=p?p.region:"unknown",g=p?p.journeyType:"",y=void 0!==e.reward.inner?e.reward.inner:e.reward.outer,k=[];e.required&&Object.entries(e.required).forEach((e=>{let[t,n]=e;for(let r=0;r<n;r++)k.push(t.toLowerCase())}));const S="no_inner_journey_cards"===(null===u||void 0===u?void 0:u.effect)&&void 0!==e.reward.inner;return(0,$n.jsxs)("div",{className:"card-item journey-card",style:{cursor:l?"pointer":"default",opacity:l?1:.7,width:"200px",height:"280px",borderRadius:"8px",overflow:"hidden",position:"relative",boxShadow:"0 4px 8px rgba(0,0,0,0.2)",background:"#fff",border:"2px solid "+("inner"===o?"var(--primary-color)":"var(--accent-color)"),transition:"transform 0.2s ease, box-shadow 0.2s ease"},onClick:()=>{a&&v(e)},children:[(0,$n.jsxs)("div",{style:{background:"inner"===o?"var(--primary-color)":"var(--accent-color)",color:"white",padding:"8px 12px",borderTopLeftRadius:"6px",borderTopRightRadius:"6px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,$n.jsx)("div",{style:{fontWeight:"bold",fontSize:"1rem"},children:h}),(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px",fontSize:"0.8rem"},children:[(0,$n.jsxs)("span",{children:["#",e.locationId]}),(0,$n.jsx)("div",{style:{width:"12px",height:"12px",borderRadius:"50%",background:`var(--${m.toLowerCase()}-color)`,border:"1px solid #fff"}})]})]}),(0,$n.jsxs)("div",{style:{height:"160px",background:"#eee",backgroundImage:`url(/assets/images/cards/${e.locationId}.png)`,backgroundSize:"cover",backgroundPosition:"center",position:"relative"},children:[(0,$n.jsx)("div",{style:{position:"absolute",bottom:"8px",right:"8px",background:"inner"===o?"var(--primary-color)":"var(--accent-color)",color:"white",borderRadius:"50%",width:"32px",height:"32px",display:"flex",justifyContent:"center",alignItems:"center",fontSize:"1.2rem",fontWeight:"bold",border:"2px solid white"},children:y}),S&&(0,$n.jsxs)("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backgroundColor:"rgba(0, 0, 0, 0.7)",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",color:"white",padding:"10px",textAlign:"center"},children:[(0,$n.jsx)("div",{style:{fontSize:"1.2rem",fontWeight:"bold",marginBottom:"8px"},children:"BLOCKED"}),(0,$n.jsx)("div",{style:{fontSize:"0.8rem"},children:"Drought of Spirits event prevents inner journey collection"})]})]}),(0,$n.jsxs)("div",{style:{padding:"12px",borderTop:"1px solid #eee"},children:[(0,$n.jsxs)("div",{style:{fontSize:"0.85rem",color:"#666",marginBottom:"8px",fontStyle:"italic"},children:["inner"===o?"Inner Journey":"Outer Journey",": ",g]}),(0,$n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,$n.jsx)("div",{style:{display:"flex",gap:"4px"},children:k.map(((e,t)=>(0,$n.jsx)("div",{style:{width:"24px",height:"24px",borderRadius:"4px",background:`var(--${e}-color)`,border:"1px solid #666",boxShadow:"0 1px 2px rgba(0,0,0,0.2)"}},t)))}),(0,$n.jsx)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:(0,$n.jsx)("div",{style:{background:"#ffd700",color:"#333",width:"24px",height:"24px",borderRadius:"50%",display:"flex",justifyContent:"center",alignItems:"center",fontSize:"0.9rem",fontWeight:"bold",border:"1px solid #333"},children:d})})]}),l&&(0,$n.jsx)("div",{style:{position:"absolute",top:"40px",right:"-28px",background:"var(--accent-color)",color:"white",padding:"2px 30px",transform:"rotate(45deg)",boxShadow:"0 2px 4px rgba(0,0,0,0.3)",fontSize:"0.8rem",fontWeight:"bold"},children:"Available"})]})]},r)};return(0,$n.jsxs)("div",{className:"face-up-cards",children:[u?(0,$n.jsxs)("div",{className:"card-container global-event-card",children:[(0,$n.jsx)("div",{className:"card-title",style:{color:"#d84315",borderBottom:"2px solid #ffb74d"},children:"Global Event"}),(0,$n.jsxs)("div",{className:"card-item",style:{background:"linear-gradient(135deg, #fff8e1 0%, #ffe0b2 100%)",border:"2px solid #ffb74d",boxShadow:"0 4px 8px rgba(255, 167, 38, 0.2)",padding:"16px",borderRadius:"8px"},children:[(0,$n.jsx)("h3",{style:{color:"#d84315",marginTop:"0",marginBottom:"12px",fontSize:"1.2rem",textAlign:"center"},children:u.name}),(0,$n.jsx)("p",{style:{fontSize:"1rem",marginBottom:"0",textAlign:"center",fontStyle:"italic",color:"#5d4037"},children:u.text})]})]}):null,(0,$n.jsxs)("div",{className:"card-container travel-cards",children:[(0,$n.jsxs)("div",{className:"card-title",children:["Travel Cards",(0,$n.jsxs)("div",{className:"card-actions",children:[(0,$n.jsxs)("button",{className:"action-button "+(p.length?"":"disabled"),onClick:()=>{p.length&&(console.log("Picking travel cards with IDs:",p),console.log("Current faceUpTravel before request:",r.map((e=>e.id))),t.emit("pickCards",{type:"travel",pickFromFaceUp:p}),h([]))},disabled:!p.length,children:["Pick Selected (",p.length,")"]}),(0,$n.jsx)("button",{className:"action-button",onClick:()=>{console.log("Picking travel card from top of deck"),t.emit("pickCards",{type:"travel",pickFromTop:!0})},children:"Pick from Top"})]})]}),(0,$n.jsx)("div",{className:"card-grid",children:null===r||void 0===r?void 0:r.map(((e,n)=>((e,n)=>{const r=p.includes(e.id),o=f&&f.id===t.id&&x(f),i=`/assets/images/vehicles/${e.value}/${e.vehicle||"camel"}.png`,a="max_moves_2_and_cost_artha_north_east"===(null===u||void 0===u?void 0:u.effect)&&e.value>2;return(0,$n.jsxs)("div",{className:`card-item travel-card ${r?"selected":""} ${a?"disabled":""}`,style:{cursor:o&&!a?"pointer":"default",opacity:o&&!a?1:.7,border:r?"2px solid var(--primary-color)":"1px solid #ddd",background:r?"#e3f2fd":a?"#eeeeee":"#FFF8E1",borderRadius:"8px",width:"120px",height:"180px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",padding:"10px",position:"relative",margin:"10px"},onClick:()=>{o&&!a&&(p.includes(e.id)?h(p.filter((t=>t!==e.id))):p.length<2?h([...p,e.id]):alert("You can only select up to 2 travel cards."))},children:[(0,$n.jsx)("div",{style:{position:"absolute",top:"8px",left:"8px",fontWeight:"bold",fontSize:"1.5rem"},children:e.value}),(0,$n.jsx)("div",{style:{width:"100px",height:"100px",display:"flex",alignItems:"center",justifyContent:"center"},children:(0,$n.jsx)("img",{src:i,alt:`${e.vehicle} (${e.value})`,style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain",marginBottom:"10px"},onError:e=>{e.target.style.display="none",e.target.nextSibling.style.fontSize="4rem"}})}),(0,$n.jsxs)("div",{style:{fontSize:"0.9rem",marginTop:"auto",textAlign:"center",textTransform:"capitalize"},children:[e.vehicle||"Vehicle"," (",e.value,")"]}),a&&(0,$n.jsx)("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"rgba(0,0,0,0.3)",color:"white",fontWeight:"bold",borderRadius:"8px",fontSize:"1rem"},children:"Max 2 Moves"})]},n)})(e,`travel-${n}`)))})]}),(0,$n.jsxs)("div",{className:"journey-cards-section",style:{display:"flex",flexDirection:"row",justifyContent:"space-between",gap:"16px"},children:[(0,$n.jsxs)("div",{className:"card-container journey-cards outer",style:{flex:1},children:[(0,$n.jsx)("div",{className:"card-title",children:"Outer Journey Cards"}),(0,$n.jsx)("div",{className:"card-grid",children:null===a||void 0===a?void 0:a.map(((e,t)=>k(e,t,"outer")))})]}),(0,$n.jsxs)("div",{className:"card-container journey-cards inner",style:{flex:1},children:[(0,$n.jsx)("div",{className:"card-title",children:"Inner Journey Cards"}),(0,$n.jsx)("div",{className:"card-grid",children:null===l||void 0===l?void 0:l.map(((e,t)=>k(e,t,"inner")))})]})]}),y&&(0,$n.jsx)(In,{card:y,onClose:()=>v(null),onCollect:e=>{if(!w(e,f))return void alert("You do not meet the requirements to collect this journey card.");const n=[1,1,2,3][b(f,e)],r=void 0!==e.reward.outer?"outer":"inner";v(null),t.emit("collectJourney",{journeyCardId:e.id,journeyType:r,requiredOm:n})},canCollect:w(y,f),player:f})]})},An={1:{x:350,y:220},2:{x:510,y:200},3:{x:710,y:310},4:{x:610,y:410},6:{x:470,y:530},7:{x:250,y:380},9:{x:430,y:100},54:{x:250,y:120},56:{x:650,y:140},61:{x:420,y:400},10:{x:120,y:730},11:{x:260,y:530},12:{x:390,y:735},13:{x:380,y:860},14:{x:240,y:900},16:{x:180,y:1050},19:{x:100,y:830},49:{x:90,y:600},50:{x:390,y:635},62:{x:210,y:780},20:{x:580,y:1200},21:{x:715,y:1135},22:{x:875,y:1180},23:{x:1150,y:1210},24:{x:900,y:1355},25:{x:720,y:1355},27:{x:450,y:1270},51:{x:470,y:1150},59:{x:1100,y:1355},63:{x:740,y:1255},5:{x:550,y:680},15:{x:780,y:550},18:{x:950,y:700},28:{x:1e3,y:850},29:{x:770,y:850},33:{x:600,y:810},34:{x:510,y:900},52:{x:630,y:580},53:{x:950,y:550},64:{x:780,y:700},30:{x:1160,y:750},31:{x:1360,y:600},32:{x:1430,y:700},36:{x:1400,y:900},37:{x:1250,y:950},38:{x:1150,y:1050},39:{x:1300,y:1100},55:{x:1210,y:600},58:{x:1530,y:810},65:{x:1300,y:850},42:{x:1280,y:200},43:{x:1480,y:300},44:{x:1350,y:400},45:{x:1210,y:500},46:{x:1e3,y:400},47:{x:900,y:300},48:{x:1320,y:80},57:{x:1e3,y:160},60:{x:1420,y:150},66:{x:1200,y:300}},Fn=[{from:1,to:2},{from:2,to:3},{from:3,to:4},{from:4,to:6},{from:6,to:7},{from:1,to:9},{from:9,to:54},{from:3,to:56},{from:7,to:54},{from:4,to:61},{from:6,to:61},{from:7,to:11},{from:3,to:47},{from:10,to:11},{from:11,to:12},{from:12,to:13},{from:13,to:14},{from:14,to:16},{from:14,to:19},{from:13,to:34},{from:10,to:49},{from:12,to:50},{from:11,to:62},{from:13,to:62},{from:19,to:62},{from:16,to:27},{from:20,to:21},{from:21,to:22},{from:22,to:23},{from:23,to:24},{from:24,to:25},{from:25,to:27},{from:20,to:51},{from:23,to:59},{from:24,to:63},{from:25,to:63},{from:21,to:63},{from:21,to:28},{from:5,to:15},{from:15,to:18},{from:18,to:28},{from:28,to:29},{from:29,to:33},{from:33,to:34},{from:28,to:37},{from:5,to:52},{from:18,to:53},{from:15,to:64},{from:29,to:64},{from:21,to:28},{from:30,to:31},{from:31,to:32},{from:32,to:36},{from:65,to:37},{from:37,to:38},{from:38,to:39},{from:30,to:55},{from:32,to:58},{from:31,to:65},{from:39,to:65},{from:23,to:38},{from:31,to:45},{from:48,to:42},{from:42,to:43},{from:43,to:44},{from:44,to:45},{from:45,to:46},{from:46,to:47},{from:47,to:57},{from:57,to:48},{from:43,to:60},{from:42,to:66},{from:44,to:66},{from:31,to:45}];const Mn=function(e){let{socket:t,currentPosition:n,onCancel:r}=e;const[i,a]=(0,o.useState)(n?`${n}`:""),[l,s]=(0,o.useState)("");return(0,$n.jsxs)("div",{className:"card",style:{marginBottom:"1rem",padding:"1rem"},children:[(0,$n.jsx)("h3",{children:"Custom Movement"}),(0,$n.jsxs)("p",{style:{fontSize:"0.9rem",marginBottom:"0.75rem",color:"#666"},children:["Enter a path of location IDs (comma separated).",(0,$n.jsx)("br",{}),'For example: "',n,', 4, 7" would move from your current position to node 4, then to node 7.']}),(0,$n.jsxs)("div",{className:"flex flex-col gap-md",children:[(0,$n.jsx)("input",{type:"text",placeholder:`e.g. ${n}, 2, 3`,value:i,onChange:e=>a(e.target.value)}),l&&(0,$n.jsx)("div",{style:{color:"red",fontSize:"0.9rem"},children:l}),(0,$n.jsxs)("div",{className:"flex gap-md",children:[(0,$n.jsx)("button",{className:"secondary",onClick:r,style:{flex:1},children:"Cancel"}),(0,$n.jsx)("button",{style:{flex:1},onClick:()=>{const e=i.split(",").map((e=>parseInt(e.trim(),10))).filter((e=>!isNaN(e)));e.length<2?s("Path must include at least a start and end position"):e[0]===n?(s(""),t.emit("customMove",{path:e,withAnimation:!0}),a(""),r&&r()):s(`Path must start from your current position (${n})`)},children:"Move"})]})]})]})};const Bn=function(e){var t,n;let{player:r,isActive:i,winner:a,socket:l,gameState:s}=e;const c=r.omSlotsOuter.reduce(((e,t)=>e+t),0),u=r.omSlotsInner.reduce(((e,t)=>e+t),0),d=null===s||void 0===s?void 0:s.currentGlobalEvent,[f,p]=(0,o.useState)(!1),[h,m]=(0,o.useState)(!1),g=Boolean(a&&a.id&&r.id===a.id),y=g&&a.winByOm,v=g&&a.winByScore,x=()=>{if(!("triathlon_bonus"===(null===d||void 0===d?void 0:d.effect)))return!1;const e=r.hand.filter((e=>void 0!==e.value)).map((e=>e.value));return e.includes(1)&&e.includes(2)&&e.includes(3)};(0,o.useEffect)((()=>{a&&console.log("CompactPlayerMat - Winner data available:",{winnerName:a.name,winnerId:a.id,playerName:r.name,playerId:r.id,isMatch:r.id===a.id,isWinner:g})}),[a,r.id,g,r.name]),console.log("CompactPlayerMat rendering for:",r.name,"Character:",r.character,"Character Type:",null===(t=r.character)||void 0===t||null===(n=t.type)||void 0===n?void 0:n.toLowerCase());const b=r.id===l.id;return(0,$n.jsxs)("div",{className:`compact-player-mat ${i?"active":""} ${g?"winner":""} ${b?"current-user":""}`,"data-player-id":r.id,style:{width:"230px",padding:"8px",backgroundColor:g?"rgba(255, 250, 205, 0.95)":i?"rgba(255, 255, 240, 0.95)":"rgba(245, 245, 245, 0.85)",borderRadius:"8px",boxShadow:g?"0 0 8px gold":"0 2px 4px rgba(0,0,0,0.1)",margin:"4px 0",fontSize:"0.8rem",border:g?"2px solid gold":i?"2px solid var(--accent-color)":"1px solid #ddd",position:"relative"},children:[g&&(0,$n.jsx)("div",{style:{position:"absolute",top:"-10px",right:"-10px",backgroundColor:"gold",color:"#333",fontWeight:"bold",padding:"3px 6px",borderRadius:"12px",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",fontSize:"0.7rem",zIndex:10,transform:"rotate(10deg)"},children:y?"Winner by OM!":v?"Winner by Score!":"Winner!"}),(0,$n.jsxs)("h4",{style:{margin:"0 0 5px 0",padding:"0 0 5px 0",borderBottom:g?"1px solid gold":"1px solid #ddd",display:"flex",justifyContent:"space-between",alignItems:"center",fontSize:"0.9rem",color:g?"goldenrod":void 0,fontWeight:g?"bold":void 0},children:[(0,$n.jsx)("span",{children:r.name}),(0,$n.jsxs)("span",{style:{fontSize:"0.75rem",backgroundColor:g?"gold":void 0,padding:g?"0 5px":void 0,borderRadius:g?"4px":void 0,color:g?"#333":void 0},children:[c,"/",u]})]}),(0,$n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"5px",fontSize:"0.8rem"},children:[(0,$n.jsxs)("div",{children:["Pos: ",r.position]}),(0,$n.jsxs)("div",{style:{fontWeight:g?"bold":void 0,color:g?"goldenrod":void 0},children:[(0,$n.jsxs)("strong",{children:["Score: O: ",r.outerScore," I: ",r.innerScore]}),g&&(0,$n.jsxs)("span",{style:{marginLeft:"5px"},children:["\u2192 ",r.outerScore+r.innerScore]})]}),(0,$n.jsxs)("div",{children:["OM: ",r.omTemp.length]})]}),(0,$n.jsxs)("div",{style:{display:"flex",flexWrap:"wrap",alignItems:"center",justifyContent:"space-between",marginBottom:"5px"},children:[(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(()=>{if(!r.character)return null;const e=r.character.type.toLowerCase();return(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",background:"rgba(240, 240, 240, 0.5)",padding:"3px 6px",borderRadius:"4px",marginRight:"6px",cursor:"pointer"},onClick:()=>m(!0),title:"Click to view character details",children:[(0,$n.jsx)("div",{style:{width:"24px",height:"24px",borderRadius:"4px",background:"#ddd",display:"flex",justifyContent:"center",alignItems:"center",fontSize:"8px",marginRight:"4px",fontWeight:"bold",overflow:"hidden"},children:(0,$n.jsx)("img",{src:`/assets/images/characters/${e}.jpg`,alt:r.character.type,className:"character-debug-img-compact",style:{width:"100%",height:"100%",objectFit:"cover"},onLoad:t=>{console.log(`CompactPlayerMat: Character image loaded successfully: ${e}.jpg`)},onError:t=>{console.error(`Failed to load character image in CompactPlayerMat: ${e}.jpg, src: ${t.target.src}`),t.target.style.display="none",t.target.parentNode.innerText=e.charAt(0).toUpperCase()}})}),(0,$n.jsx)("span",{style:{fontSize:"0.7rem"},children:r.character.type})]})})(),(()=>{const e={artha:0,karma:0,gnana:0,bhakti:0};return r.energyCubes.forEach((t=>{e[t.toLowerCase()]++})),Object.entries(e).map((e=>{let[t,n]=e;return n>0&&(0,$n.jsxs)("div",{className:"flex",style:{alignItems:"center",marginRight:"8px"},children:[(0,$n.jsx)("div",{className:`energy-cube ${t}`,style:{width:"12px",height:"12px",margin:"0 3px 0 0"}}),(0,$n.jsx)("span",{style:{fontSize:"0.8rem"},children:n})]},t)}))})()]}),(0,$n.jsx)("div",{style:{display:"flex",flexDirection:"column",marginTop:"8px"},children:(0,$n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:"6px",marginBottom:"6px"},children:[i&&r.character&&r.energyCubes.length>=1&&!r.didTradeThisTurn&&(0,$n.jsx)("button",{style:{padding:"2px 6px",fontSize:"0.7rem",backgroundColor:"var(--accent-color)",color:"white",border:"none",borderRadius:"3px",cursor:"pointer",boxShadow:"0 1px 3px rgba(0,0,0,0.2)"},onClick:()=>p(!0),children:"Trade"}),i&&r.hand.some((e=>void 0!==e.value))&&!r.didMoveThisTurn&&(0,$n.jsx)("button",{style:{padding:"2px 6px",fontSize:"0.7rem",backgroundColor:"var(--primary-color)",color:"white",border:"none",borderRadius:"3px",cursor:"pointer",boxShadow:"0 1px 3px rgba(0,0,0,0.2)"},onClick:()=>l.emit("initiateTravelCardSelection"),children:"Travel"}),x()&&(0,$n.jsx)("button",{style:{padding:"2px 6px",fontSize:"0.7rem",background:"linear-gradient(45deg, #FF9800, #FF5722)",color:"white",border:"none",borderRadius:"3px",cursor:"pointer",boxShadow:"0 0 5px rgba(255, 152, 0, 0.7)",animation:"pulse 1.5s infinite"},onClick:()=>{x()&&l.emit("triathlonMovement")},children:"Triathlon"})]})})]}),(0,$n.jsxs)("div",{style:{marginTop:"6px",borderTop:g?"1px solid gold":"1px solid #eee",paddingTop:"6px"},children:[(0,$n.jsxs)("div",{style:{fontSize:"0.75rem",fontWeight:"bold",marginBottom:"2px"},children:["Hand Cards (",r.hand.length,"/5):"]}),(()=>{if(!r.hand||0===r.hand.length)return(0,$n.jsx)("div",{style:{color:"#999",fontStyle:"italic",fontSize:"0.7rem",textAlign:"center",padding:"4px 0"},children:"No cards"});const e=r.hand.filter((e=>void 0!==e.value)),t=r.hand.filter((e=>void 0===e.value));return(0,$n.jsxs)("div",{children:[e.length>0&&(0,$n.jsx)("div",{children:(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap",marginBottom:"4px"},children:e.map((e=>(e=>{const t=`/assets/images/vehicles/${e.value}/${e.vehicle||"camel"}.png`;return(0,$n.jsx)("div",{style:{width:"52px",height:"75px",backgroundColor:"#F5F5F5",borderRadius:"4px",border:"1px solid #1976d2",display:"flex",justifyContent:"center",alignItems:"center",margin:"0 4px 0 0",padding:"2px",position:"relative",overflow:"hidden"},children:(0,$n.jsx)("img",{src:t,alt:`${e.vehicle} (${e.value})`,style:{width:"100%",height:"100%",objectFit:"contain"},onError:e=>{e.target.style.display="none"}})},e.id||`travel-${e.value}`)})(e)))})}),t.length>0&&(0,$n.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"4px",marginTop:e.length>0?"6px":"0"},children:t.map(((e,t)=>{let n="",r="#e3f2fd",o="#1976d2";return"extraHop"===e.type?(n="Extra Hop",r="#e8f5e9",o="#2e7d32"):"event"===e.type?(n=e.name||"Event",r="#f3e5f5",o="#7b1fa2"):"journey"===e.type?(n="Journey",r="#fff3e0",o="#f57c00"):"wildCube"===e.type&&(n="Wild Cube",r="#f3e5f5",o="#7b1fa2"),(0,$n.jsxs)("div",{style:{fontSize:"0.75rem",padding:"3px 6px",backgroundColor:r,borderRadius:"3px",color:o,boxShadow:"0 1px 2px rgba(0,0,0,0.1)",display:"flex",justifyContent:"space-between"},children:[(0,$n.jsx)("span",{children:n}),e.id&&(0,$n.jsxs)("span",{style:{fontSize:"0.65rem",opacity:.7},children:["#",e.id.slice(-3)]})]},t)}))})]})})()]}),(0,$n.jsx)("div",{style:{display:"flex",alignItems:"center",marginTop:"8px",justifyContent:"space-between",fontSize:"0.8rem"},children:(0,$n.jsxs)("div",{children:[(0,$n.jsx)("span",{style:{fontWeight:"bold"},children:"Pos: "}),r.position]})}),f&&(0,$n.jsx)(On,{player:r,onClose:()=>p(!1),socket:l}),(()=>{if(!h||!r.character)return null;const e=r.character,t=e.type.toLowerCase();return(0,$n.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.7)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3},children:(0,$n.jsxs)("div",{style:{backgroundColor:"#fff",borderRadius:"8px",padding:"20px",width:"90%",maxWidth:"320px",boxShadow:"0 5px 15px rgba(0, 0, 0, 0.3)",position:"relative"},children:[(0,$n.jsx)("button",{onClick:()=>m(!1),style:{position:"absolute",top:"10px",right:"10px",backgroundColor:"transparent",border:"none",fontSize:"20px",cursor:"pointer",color:"#666"},children:"\xd7"}),(0,$n.jsx)("h3",{style:{marginTop:0,textAlign:"center",borderBottom:"1px solid #eee",paddingBottom:"10px"},children:e.type}),(0,$n.jsx)("div",{style:{margin:"15px 0",textAlign:"center"},children:(0,$n.jsx)("div",{style:{width:"200px",height:"200px",margin:"0 auto",borderRadius:"8px",overflow:"hidden",boxShadow:"0 2px 4px rgba(0,0,0,0.2)"},children:(0,$n.jsx)("img",{src:`/assets/images/characters/${t}.jpg`,alt:e.type,style:{width:"100%",height:"100%",objectFit:"contain"}})})}),(0,$n.jsxs)("div",{style:{marginTop:"15px"},children:[(0,$n.jsx)("p",{style:{fontWeight:"bold",marginBottom:"5px",fontSize:"0.9rem"},children:"Trading Ability:"}),(0,$n.jsx)("p",{style:{margin:0,fontSize:"0.9rem"},children:e.description}),(0,$n.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",marginTop:"15px",backgroundColor:"#f8f8f8",padding:"10px",borderRadius:"6px"},children:[(0,$n.jsxs)("div",{style:{textAlign:"center"},children:[(0,$n.jsx)("p",{style:{fontWeight:"bold",margin:0,fontSize:"0.9rem"},children:"Takes"}),(0,$n.jsx)("div",{style:{display:"flex",justifyContent:"center",marginTop:"5px",gap:"5px"},children:e.ability.takes.map(((e,t)=>(0,$n.jsx)("div",{className:`energy-cube ${e.toLowerCase()}`,style:{width:"20px",height:"20px"}},t)))})]}),(0,$n.jsxs)("div",{style:{textAlign:"center"},children:[(0,$n.jsx)("p",{style:{fontWeight:"bold",margin:0,fontSize:"0.9rem"},children:"Gives"}),(0,$n.jsx)("div",{style:{display:"flex",justifyContent:"center",marginTop:"5px"},children:(0,$n.jsx)("div",{className:`energy-cube ${e.ability.gives.toLowerCase()}`,style:{width:"20px",height:"20px"}})})]})]})]})]})})})()]})};const Dn=function(e){let{faceUpTravel:t,faceUpEvent:n,socket:r,currentGlobalEvent:i,energyCubePile:a,onCardsPicked:l}=e;const[s,c]=(0,o.useState)([]),[u,d]=(0,o.useState)(null),f=(0,o.useRef)({}),p=(e,t)=>{const n=s.includes(e.id),r=`/assets/images/vehicles/${e.value}/${e.vehicle||"camel"}.png`;return(0,$n.jsxs)("div",{ref:t=>f.current[e.id]=t,className:"compact-card travel-card "+(n?"selected":""),style:{width:"75px",height:"70px",backgroundColor:n?"#bbdefb":"#FFF8E1",borderRadius:"6px",border:n?"2px solid #1565c0":"1px solid #1976d2",display:"flex",justifyContent:"center",alignItems:"center",margin:"0 6px 6px 0",boxShadow:n?"0 0 8px rgba(25, 118, 210, 0.5)":"0 2px 4px rgba(0,0,0,0.1)",padding:"4px",position:"relative",cursor:"pointer",transition:"all 0.2s ease",overflow:"hidden"},onClick:()=>{return t=e.id,void(s.includes(t)?c(s.filter((e=>e!==t))):c([...s,t]));var t},children:[(0,$n.jsx)("img",{src:r,alt:`${e.vehicle} (${e.value})`,style:{width:"100%",height:"100%",objectFit:"contain"},onError:e=>{e.target.style.display="none"}}),n&&(0,$n.jsx)("div",{style:{position:"absolute",top:"-8px",right:"-8px",backgroundColor:"#1976d2",color:"white",width:"20px",height:"20px",borderRadius:"50%",display:"flex",justifyContent:"center",alignItems:"center",fontSize:"0.7rem",fontWeight:"bold",border:"1px solid white",zIndex:2},children:"\u2713"})]},`travel-${t}`)};return(0,$n.jsx)("div",{className:"compact-cards-container",style:{padding:"8px",backgroundColor:"#f5f5f5",borderRadius:"8px",marginBottom:"16px"},children:(0,$n.jsxs)("div",{style:{marginBottom:"12px"},children:[(0,$n.jsxs)("div",{style:{fontWeight:"bold",fontSize:"0.9rem",marginBottom:"8px",display:"flex",justifyContent:"space-between",alignItems:"center",color:"#1976d2"},children:[(0,$n.jsx)("span",{children:"Travel Cards"}),(0,$n.jsxs)("div",{children:[(0,$n.jsxs)("button",{onClick:()=>{if(!s.length)return;const e=t.filter((e=>s.includes(e.id)));if(l){const t=e.map((e=>{if(f.current[e.id]){const t=f.current[e.id].getBoundingClientRect();return{cardId:e.id,position:{x:t.left+t.width/2,y:t.top+t.height/2}}}return null})).filter((e=>null!==e));t.length>0&&l(e,t)}r.emit("pickCards",{type:"travel",pickFromFaceUp:s}),c([])},disabled:0===s.length,style:{padding:"4px 8px",marginRight:"4px",backgroundColor:s.length>0?"#1976d2":"#e0e0e0",color:s.length>0?"white":"#9e9e9e",border:"none",borderRadius:"4px",cursor:s.length>0?"pointer":"default",fontSize:"0.7rem"},children:["Pick Selected (",s.length,")"]}),(0,$n.jsx)("button",{onClick:()=>{r.emit("pickCards",{type:"travel",pickFromTop:!0})},style:{padding:"4px 8px",backgroundColor:"#1976d2",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"0.7rem"},children:"From Deck"})]})]}),(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap"},children:t&&t.map(((e,t)=>p(e,t)))})]})})};const Wn=function(e){let{faceUpJourneyOuter:t,faceUpJourneyInner:n,gameState:r,currentPlayer:o,socket:i,setJourneyModal:a}=e;const l=(e,t)=>{if(!e||!t)return!0;const n={};(t.energyCubes||[]).forEach((e=>{const t=e.toLowerCase();n[t]=(n[t]||0)+1}));let r=t.wildCubes||0;for(const[o,i]of Object.entries(e)){const e=n[o.toLowerCase()]||0;if(e>=i)continue;const t=i-e;if(!(t<=r))return!1;r-=t}return!0},s=(e,t)=>{if(!t)return!1;const n=[1,1,2,3],r=c(t,e),o=r<n.length?n[r]:1/0;return t.omTemp.length>=o},c=(e,t)=>{if(!e)return 0;const n=void 0!==t.reward.inner?"inner":"outer",r=t.locationId;return("inner"===n?e.journeyInner||[]:e.journeyOuter||[]).filter((e=>e.locationId===r)).length},u=(e,t,n)=>{var c,u,d,f;const p="outer"===n,h=p?"#fff3e0":"#e8eaf6",m=p?"#f57c00":"#3f51b5",g=p?"#e65100":"#283593",y=e.locationName||`Loc ${e.locationId}`,v=null===(c=r.locations)||void 0===c?void 0:c.find((t=>t.id===e.locationId)),x=(null===v||void 0===v?void 0:v.name)||y,b=(null===v||void 0===v?void 0:v.region)||"Unknown",w=b?`var(--${b.toLowerCase()}-color)`:"#9e9e9e",k=(y.length>12&&y.substring(0,10),p?null===(u=e.reward)||void 0===u?void 0:u.outer:null===(d=e.reward)||void 0===d?void 0:d.inner),S=`/assets/images/cards/${e.locationId}.png`,C=o&&i&&o.id===i.id,j=C&&((e,t)=>{var n;if(!t)return!1;const o=t.position===e.locationId;if("no_inner_journey_cards"===(null===r||void 0===r||null===(n=r.currentGlobalEvent)||void 0===n?void 0:n.effect)&&void 0!==e.reward.inner)return!1;const i=l(e.required,t),a=s(e,t);return o&&i&&a})(e,o),E="no_inner_journey_cards"===(null===r||void 0===r||null===(f=r.currentGlobalEvent)||void 0===f?void 0:f.effect)&&!p;return(0,$n.jsxs)("div",{className:`compact-card journey-card ${p?"outer":"inner"} ${j?"collectable":""}`,style:{width:"110px",height:"160px",backgroundColor:h,borderRadius:"8px",border:j?"2px solid #4caf50":`1px solid ${m}`,display:"flex",flexDirection:"column",justifyContent:"space-between",alignItems:"center",margin:"0 6px 6px 0",boxShadow:j?"0 0 8px rgba(76, 175, 80, 0.5)":"0 2px 4px rgba(0,0,0,0.2)",padding:"4px",fontSize:"0.7rem",overflow:"hidden",cursor:"pointer",transition:"all 0.2s ease",position:"relative"},onClick:t=>{if(t.stopPropagation(),C){null!==document.querySelector(".board-fullscreen")&&localStorage.setItem("pendingJourneyModal",JSON.stringify(e)),a(e)}},children:[(0,$n.jsx)("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",width:"100%",marginBottom:"2px"},children:(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px",width:"100%"},children:[(0,$n.jsx)("div",{style:{width:"10px",height:"10px",borderRadius:"50%",backgroundColor:w,border:"1px solid #fff",boxShadow:"0 1px 2px rgba(0,0,0,0.2)"}}),(0,$n.jsxs)("div",{style:{fontSize:"0.7rem",fontWeight:"bold",color:g,textAlign:"left",flex:1,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"flex",alignItems:"center",gap:"2px"},children:[(0,$n.jsx)("span",{style:{fontSize:"0.6rem",backgroundColor:p?"rgba(245, 124, 0, 0.2)":"rgba(63, 81, 181, 0.2)",color:g,borderRadius:"4px",padding:"1px 2px",fontWeight:"bold",minWidth:"14px",textAlign:"center",border:`1px solid ${m}`},children:e.locationId}),(0,$n.jsx)("span",{children:x})]})]})}),(0,$n.jsxs)("div",{style:{width:"95%",height:"60px",borderRadius:"4px",position:"relative",marginBottom:"2px",overflow:"hidden"},children:[(0,$n.jsx)("img",{src:S,alt:x,style:{width:"100%",height:"100%",objectFit:"cover",objectPosition:"center"},onError:e=>{e.target.style.backgroundColor="#f5f5f5"}},`card-image-${e.locationId}-${t}`),(0,$n.jsx)("div",{style:{position:"absolute",bottom:"2px",right:"2px",backgroundColor:p?"#ff9800":"#3f51b5",color:"white",width:"22px",height:"22px",borderRadius:"50%",display:"flex",justifyContent:"center",alignItems:"center",fontSize:"0.8rem",fontWeight:"bold",border:"1px solid white",boxShadow:"0 1px 3px rgba(0,0,0,0.3)"},children:k}),E&&(0,$n.jsxs)("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",backgroundColor:"rgba(0, 0, 0, 0.7)",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",color:"white",padding:"5px",textAlign:"center",borderRadius:"4px"},children:[(0,$n.jsx)("div",{style:{fontSize:"0.9rem",fontWeight:"bold",marginBottom:"2px"},children:"BLOCKED"}),(0,$n.jsx)("div",{style:{fontSize:"0.6rem"},children:"Drought of Spirits"})]})]}),(0,$n.jsxs)("div",{style:{marginBottom:"2px"},children:[" ",(_=e.required,_?(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap",justifyContent:"center",marginBottom:"2px"},children:Object.entries(_).map((e=>{let[t,n]=e;return n>0&&(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",margin:"0 2px"},children:[(0,$n.jsx)("div",{className:`energy-cube ${t.toLowerCase()}`,style:{width:"12px",height:"12px",margin:"0 2px 0 0"}}),(0,$n.jsx)("span",{style:{fontSize:"0.7rem"},children:n})]},t)}))}):null)]}),j&&(0,$n.jsx)("div",{style:{position:"absolute",top:"25px",right:"-25px",backgroundColor:"#4caf50",color:"white",padding:"2px 25px",transform:"rotate(45deg)",boxShadow:"0 2px 4px rgba(0,0,0,0.3)",fontSize:"0.65rem",fontWeight:"bold",zIndex:1},children:"Available"})]},`journey-${n}-${t}`);var _};return(0,$n.jsxs)("div",{className:"compact-journey-cards-container",style:{backgroundColor:"rgba(245, 245, 245, 0.85)",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",padding:"8px",width:"100%",maxWidth:"450px",margin:"0",display:"flex",flexDirection:"column",height:"100%",maxHeight:"95vh",overflowY:"auto"},children:[(()=>{const e=null===r||void 0===r?void 0:r.currentGlobalEvent;if(!e)return null;const t=`/assets/images/global_event_cards/${{max_moves_2_and_cost_artha_north_east:0,gain_5_inner_no_cube_pickup:1,jyotirlinga_7_inner_or_bonus_cube:2,no_inner_journey_cards:3,draw_2_cubes_bonus_5_outer:4,no_airport_travel:5,double_trade_no_travel:6,triathlon_bonus:7,riots_discard:8,om_meditation:9,pedal_power_reward:10,footpath_reverie_reward:11,steed_of_valor_reward:12,desert_caravan_reward:13,biker_gang_reward:14,rickshaw_rhapsody_reward:15,top_gear_reward:16,hop_on_hop_off_reward:17,bullet_train_reward:18,scenic_cruise_reward:19,heavy_haul_reward:20,up_and_over_reward:21,merchants_midas_reward:22,professors_insight_reward:23,pilgrims_grace_reward:24,engineers_precision_reward:25,frozen_north:26,sandy_west:27,solar_south:28,breezy_east:29,himalayan_ne:30,central_heart:31}[e.effect]}.png`;return(0,$n.jsxs)("div",{className:"global-event-card-container",style:{width:"100%",marginBottom:"12px"},children:[(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"6px"},children:[(0,$n.jsx)("h3",{style:{margin:0,fontSize:"0.9rem",color:"#e65100",fontWeight:"bold"},children:"Global Event"}),(0,$n.jsx)("div",{style:{fontSize:"0.7rem",color:"#757575",fontStyle:"italic"}})]}),(0,$n.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",backgroundColor:"#fff8e1",border:"2px solid #ffb74d",borderRadius:"8px",padding:"8px",boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)",position:"relative",overflow:"hidden",minHeight:"240px"},children:[(0,$n.jsx)("div",{style:{width:"100%",textAlign:"center",marginBottom:"6px",color:"#d84315",fontWeight:"bold",fontSize:"0.9rem"},children:e.name}),(0,$n.jsx)("div",{style:{width:"100%",height:"160px",borderRadius:"4px",overflow:"hidden",marginBottom:"6px",border:"1px solid #ffe0b2"},children:(0,$n.jsx)("img",{src:t,alt:e.name,style:{width:"100%",height:"100%",objectFit:"contain",objectPosition:"center"},onError:e=>{console.error(`Failed to load global event image: ${t}`),e.target.style.display="none",e.target.parentNode.innerHTML='<div style="display: flex; align-items: center; justify-content: center; height: 100%; text-align: center; padding: 10px; color: #d84315; font-style: italic;">Event Image<br/>Not Available</div>'}})}),(0,$n.jsx)("div",{style:{width:"100%",textAlign:"center",fontSize:"0.8rem",color:"#5d4037",fontStyle:"italic",padding:"0 4px"},children:e.text})]})]})})(),(0,$n.jsxs)("div",{children:[(0,$n.jsx)("h3",{style:{margin:"0 0 8px 0",fontSize:"0.9rem"},children:"Outer Journey Cards"}),(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap"},children:t&&t.map(((e,t)=>u(e,t,"outer")))})]}),(0,$n.jsxs)("div",{style:{marginTop:"15px"},children:[(0,$n.jsx)("h3",{style:{margin:"0 0 8px 0",fontSize:"0.9rem"},children:"Inner Journey Cards"}),(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap"},children:n&&n.map(((e,t)=>u(e,t,"inner")))})]})]})};const Un=function(e){let{player:t,onCancel:n,onSelectCards:r,numberOfCardsToSelect:i=1,title:a,description:l}=e;const[s,c]=(0,o.useState)([]),u=t.hand.filter((e=>"travel"===e.type));return(0,$n.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.7)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:9999},children:(0,$n.jsxs)("div",{style:{backgroundColor:"#fff",borderRadius:"8px",padding:"20px",width:"90%",maxWidth:"600px",boxShadow:"0 5px 15px rgba(0, 0, 0, 0.3)"},children:[(0,$n.jsx)("h2",{style:{marginTop:0,textAlign:"center"},children:a||"Select Travel Cards"}),(0,$n.jsx)("p",{style:{textAlign:"center",marginBottom:"20px"},children:l||(i>10?"Select travel cards for your move. The total value will determine how far you can travel.":`Select ${i} travel card(s) to discard.`)}),(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"10px",justifyContent:"center",marginBottom:"20px"},children:u.map((e=>{const t=s.some((t=>t.id===e.id)),n=`/assets/images/vehicles/${e.value}/${e.vehicle||"camel"}.png`;return(0,$n.jsxs)("div",{onClick:()=>(e=>{s.some((t=>t.id===e.id))?c(s.filter((t=>t.id!==e.id))):s.length<i&&c([...s,e])})(e),style:{width:"100px",height:"130px",padding:"8px",backgroundColor:t?"#E3F2FD":"#FFF8E1",borderRadius:"8px",cursor:"pointer",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"space-between",border:t?"3px solid #2196F3":"1px solid #ddd",boxShadow:t?"0 0 8px rgba(33, 150, 243, 0.7)":"0 2px 4px rgba(0,0,0,0.1)"},children:[(0,$n.jsx)("div",{style:{position:"absolute",top:"8px",left:"8px",backgroundColor:t?"#2196F3":"#FFA000",color:"white",borderRadius:"50%",width:"30px",height:"30px",display:"flex",justifyContent:"center",alignItems:"center",fontWeight:"bold",fontSize:"16px",boxShadow:"0 1px 3px rgba(0,0,0,0.3)"},children:e.value}),(0,$n.jsx)("div",{style:{flexGrow:1,display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"80px"},children:(0,$n.jsx)("img",{src:n,alt:`${e.vehicle} (${e.value})`,style:{maxWidth:"80%",maxHeight:"80%",objectFit:"contain"},onError:e=>{e.target.style.display="none"}})}),(0,$n.jsx)("div",{style:{marginTop:"auto",fontSize:"12px",textAlign:"center",textTransform:"capitalize"},children:e.vehicle||"Travel"})]},e.id)}))}),0===u.length&&(0,$n.jsx)("p",{style:{textAlign:"center",color:"#666"},children:"You don't have any travel cards to select."}),(0,$n.jsxs)("div",{style:{display:"flex",justifyContent:"center",gap:"10px",marginTop:"20px"},children:[(0,$n.jsx)("button",{onClick:n,style:{padding:"8px 16px",backgroundColor:"#f44336",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Cancel"}),(0,$n.jsx)("button",{onClick:()=>{i<=10?s.length===i&&(console.log("Confirming card selection for discard:",s.map((e=>e.id))),r(s.map((e=>e.id)))):s.length>0&&(console.log("Confirming travel card selection:",s),r(s))},disabled:i<=10&&s.length!==i||i>10&&0===s.length||0===u.length,style:{padding:"8px 16px",backgroundColor:(i<=10&&s.length===i||i>10&&s.length>0)&&u.length>0?"#4CAF50":"#ccc",color:"white",border:"none",borderRadius:"4px",cursor:(i<=10&&s.length===i||i>10&&s.length>0)&&u.length>0?"pointer":"not-allowed"},children:"Confirm"})]})]})})};const Hn=function(e){let{player:t,onCancel:n,onSelectCubes:r,numberOfCubesToSelect:i=2,title:a,description:l}=e;const[s,c]=(0,o.useState)([]),[u,d]=(0,o.useState)(null),f=Math.min(i,t.energyCubes.length);(0,o.useEffect)((()=>{c([]),d(null)}),[]);const p={};return t.energyCubes.forEach(((e,t)=>{p[e]||(p[e]=[]),p[e].push({type:e,index:t})})),(0,$n.jsx)("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.7)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:9999},children:(0,$n.jsxs)("div",{style:{backgroundColor:"#fff",borderRadius:"8px",padding:"20px",width:"90%",maxWidth:"450px",boxShadow:"0 5px 15px rgba(0, 0, 0, 0.3)"},children:[(0,$n.jsx)("h2",{style:{marginTop:0,textAlign:"center"},children:a||"Select Energy Cubes"}),(0,$n.jsx)("p",{style:{textAlign:"center",marginBottom:"20px"},children:l||`Select ${f} energy cubes to discard.`}),u&&(0,$n.jsx)("p",{style:{color:"red",textAlign:"center",marginBottom:"15px"},children:u}),(0,$n.jsx)("div",{style:{marginBottom:"20px"},children:Object.entries(p).map((e=>{let[t,n]=e;return(0,$n.jsxs)("div",{style:{marginBottom:"15px"},children:[(0,$n.jsxs)("h3",{style:{textTransform:"capitalize",margin:"0 0 8px 0",fontSize:"1rem"},children:[t," (",n.length,")"]}),(0,$n.jsx)("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:n.map((e=>{const t=s.some((t=>t.id===`${e.type}-${e.index}`)),n=function(e){return{artha:"#FF9800",karma:"#4CAF50",gnana:"#2196F3",bhakti:"#9C27B0"}[e.toLowerCase()]||"#ccc"}(e.type);return(0,$n.jsx)("div",{onClick:()=>((e,t)=>{const n=`${e}-${t}`;s.some((e=>e.id===n))?(c(s.filter((e=>e.id!==n))),d(null)):s.length<f?(c([...s,{type:e,id:n,index:t}]),d(null)):d(`You can only select ${f} cubes`)})(e.type,e.index),style:{width:"40px",height:"40px",backgroundColor:n,borderRadius:"4px",cursor:"pointer",display:"flex",justifyContent:"center",alignItems:"center",border:t?"3px solid #2196F3":"1px solid #0000001a",boxShadow:t?"0 0 8px rgba(33, 150, 243, 0.7)":"none"},children:t&&(0,$n.jsx)("div",{style:{width:"20px",height:"20px",borderRadius:"50%",backgroundColor:"white",display:"flex",justifyContent:"center",alignItems:"center",fontWeight:"bold",color:"#2196F3"},children:"\u2713"})},`${e.type}-${e.index}`)}))})]},t)}))}),0===Object.keys(p).length&&(0,$n.jsx)("p",{style:{textAlign:"center",color:"#666",marginBottom:"20px"},children:"You don't have any energy cubes."}),(0,$n.jsxs)("div",{style:{display:"flex",justifyContent:"center",gap:"10px",marginTop:"20px"},children:[(0,$n.jsx)("button",{onClick:()=>{console.log("Cancelling cube selection"),n&&n()},style:{padding:"8px 16px",backgroundColor:"#f44336",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Cancel"}),(0,$n.jsx)("button",{onClick:()=>{s.length===f?(console.log("Confirming cube selection:",s),r(s)):d(`Please select exactly ${f} cubes`)},disabled:s.length!==f||0===Object.keys(p).length,style:{padding:"8px 16px",backgroundColor:s.length===f&&Object.keys(p).length>0?"#4CAF50":"#ccc",color:"white",border:"none",borderRadius:"4px",cursor:s.length===f&&Object.keys(p).length>0?"pointer":"not-allowed"},children:"Confirm"})]})]})})};const qn=function(e){let{cards:t,cardPositions:n,targetPosition:r,onAnimationComplete:i}=e;const[a,l]=(0,o.useState)(!1);(0,o.useEffect)((()=>{const e=setTimeout((()=>{l(!0)}),300);return()=>clearTimeout(e)}),[]);const s=()=>{i&&i()};return t&&0!==t.length?(0,$n.jsx)("div",{className:"card-animation-container",children:t.map(((e,o)=>{if(!e||"object"!==typeof e||!e.id)return console.error("Invalid card data:",e),null;const i=`/assets/images/vehicles/${e.value||1}/${e.vehicle||"camel"}.png`,l=360*o,c=n&&n[e.id]?n[e.id]:{x:0,y:0};return(0,$n.jsx)("div",{className:"animated-card",style:{position:"fixed",left:c.x,top:c.y,width:"100px",height:"90px",backgroundColor:"#FFF8E1",borderRadius:"6px",border:"1px solid #1976d2",display:"flex",justifyContent:"center",alignItems:"center",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",zIndex:1e3,transition:a?"all 1.8s cubic-bezier(0.2, 0.8, 0.2, 1.0)":"none",transitionDelay:`${l}ms`,transform:a?`translate(${r.x-c.x}px, ${r.y-c.y}px) scale(0.7)`:"translate(0, 0) scale(1.2)",opacity:a?0:1,pointerEvents:"none"},onTransitionEnd:o===t.length-1?s:null,children:(0,$n.jsx)("img",{src:i,alt:`${e.vehicle} (${e.value})`,style:{width:"100%",height:"100%",objectFit:"contain"},onError:e=>{e.target.style.display="none"}})},`anim-${e.id}`)}))}):null},Vn=n.p+"static/media/end_turn.637a30ecfceeb0dc07a2.mp3",Jn=n.p+"static/media/bus_bell.29cdc06035afb5083045.mp3",Qn=n.p+"static/media/om.040fa3a836568cf0cfc8.mp3",Yn={artha:"var(--artha-color)",karma:"var(--karma-color)",gnana:"var(--gnana-color)",bhakti:"var(--bhakti-color)"};const Gn=(e,t)=>{if(!e)return 0;const n=void 0!==t.reward.inner?"inner":"outer",r=t.locationId;return("inner"===n?e.journeyInner||[]:e.journeyOuter||[]).filter((e=>e.locationId===r)).length},Kn=function(e){var t,n;let{socket:r,gameState:i,winner:a}=e;console.log("InteractiveBoard rendering with:",{socketId:null===r||void 0===r?void 0:r.id,turnIndex:null===i||void 0===i?void 0:i.turnIndex,playersLength:null===i||void 0===i||null===(t=i.players)||void 0===t?void 0:t.length,currentPlayerExists:(null===i||void 0===i?void 0:i.players)&&void 0!==(null===i||void 0===i?void 0:i.turnIndex)&&i.turnIndex>=0&&i.turnIndex<i.players.length});const l=(0,o.useRef)(null),s=(0,o.useRef)(null),{players:c,turnIndex:u,locationCubes:d,locationOm:f}=i,p=c&&void 0!==u&&u>=0&&u<c.length?c[u]:null,[h,m]=(0,o.useState)(!1),[g,y]=(0,o.useState)([]),[v,x]=(0,o.useState)((null===p||void 0===p?void 0:p.position)||0),[b,w]=(0,o.useState)((null===p||void 0===p?void 0:p.hand)||[]),[k,S]=(0,o.useState)(!1),[C,j]=(0,o.useState)([]),[E,_]=(0,o.useState)(0),[N,T]=(0,o.useState)(null),[R,$]=(0,o.useState)(null),[P,O]=(0,o.useState)(!1),[L,I]=(0,o.useState)(0),[z,A]=(0,o.useState)(!1),[F,M]=(0,o.useState)(!1),[B,D]=(0,o.useState)(!1),[W,U]=(0,o.useState)(i.nameMode||!1),[H,q]=(0,o.useState)(1),[V,J]=(0,o.useState)({x:0,y:0}),[Q,Y]=(0,o.useState)(!1),[G,K]=(0,o.useState)({x:0,y:0}),[X,Z]=(0,o.useState)(!1),[ee,te]=(0,o.useState)(new Map),[ne,re]=(0,o.useState)(null),[oe,ie]=(0,o.useState)((()=>"false"!==localStorage.getItem("soundEnabled"))),[ae,le]=(0,o.useState)({}),[se,ce]=(0,o.useState)({active:!1,cards:[],sourcePosition:{x:0,y:0},targetPosition:{x:0,y:0}}),[ue,de]=(0,o.useState)(!1),[fe,pe]=(0,o.useState)(!1),[he,me]=(0,o.useState)([]),[ge,ye]=(0,o.useState)(!1),[ve,xe]=(0,o.useState)(null),[be,we]=(0,o.useState)(null),[ke,Se]=(0,o.useState)(!1),[Ce,je]=(0,o.useState)(!1),[Ee,_e]=(0,o.useState)(null);(0,o.useEffect)((()=>(r.on("cardPickAnimation",(e=>{try{if(!e||!e.playerId||e.playerId===r.id)return;if(!e.cards||!Array.isArray(e.cards)||0===e.cards.length)return;const t=e.cards.filter((e=>e&&"object"===typeof e&&e.id));if(0===t.length)return;const n=document.querySelector(".compact-cards-container");if(!n)return;const o=document.querySelector(`.compact-player-mat[data-player-id="${e.playerId}"]`);if(!o)return;const i=o.getBoundingClientRect(),a={x:i.left+i.width/2,y:i.top+i.height/2},l={};e.cardPositions&&"object"===typeof e.cardPositions&&Object.keys(e.cardPositions).forEach((e=>{const t=n.getBoundingClientRect();l[e]={x:t.left+t.width/3,y:t.top+t.height/2}})),t.forEach((e=>{if(!l[e.id]){const t=n.getBoundingClientRect();l[e.id]={x:t.left+t.width/2,y:t.top+t.height/2}}})),ce({active:!0,cards:t,cardPositions:l,targetPosition:a})}catch(t){console.error("Error processing card animation data:",t)}})),r.on("initiateTravelCardSelection",(()=>{pe(!0)})),r.on("needsHeavyHaulCubeSelection",(e=>{ye(!0),e&&e.travelCardIds&&xe(e.travelCardIds)})),r.on("needsEnergyCubeSelection",(e=>{Se(!0),_e(e)})),r.on("needsTravelCardSelection",(e=>{console.log("Received needsTravelCardSelection event:",e),je(!0),_e(e)})),r.on("playerMoved",(e=>{if(e&&e.path){if(oe&&e.playerId!==r.id){new Audio(Jn).play()}Ne(e.path,null,e.playerId)}})),()=>{r.off("initiateTravelCardSelection"),r.off("needsHeavyHaulCubeSelection"),r.off("needsEnergyCubeSelection"),r.off("needsTravelCardSelection"),r.off("playerMoved")})),[r,null===p||void 0===p?void 0:p.id,oe]);const Ne=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e||e.length<=1)return void(t&&t());S(!0),j(e),_(0),T(e[0]),$(n);const r=setInterval((()=>{_((n=>{const r=n+1;return r<e.length?(T(e[r]),r):(S(!1),j([]),$(null),t&&t(),0)}))}),300);setTimeout((()=>{clearInterval(r),S(!1),$(null),t&&t()}),300*e.length)};(0,o.useEffect)((()=>{void 0!==(null===p||void 0===p?void 0:p.position)&&x(p.position)}),[null===p||void 0===p?void 0:p.position]),(0,o.useEffect)((()=>{null!==p&&void 0!==p&&p.hand&&w(p.hand)}),[null===p||void 0===p?void 0:p.hand]),(0,o.useEffect)((()=>{const e=e=>{"Escape"===e.key&&z&&(ne?re(null):localStorage.removeItem("pendingJourneyModal"),A(!1))};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}}),[z,ne]),(0,o.useEffect)((()=>{if(!z)if(ne)localStorage.removeItem("pendingJourneyModal");else{const t=localStorage.getItem("pendingJourneyModal");if(t)try{const e=JSON.parse(t);setTimeout((()=>{re(e),localStorage.removeItem("pendingJourneyModal")}),50)}catch(e){console.error("Error parsing pendingJourneyModal:",e),localStorage.removeItem("pendingJourneyModal")}}}),[z,ne]),(0,o.useEffect)((()=>{z&&(q(1),J({x:-250,y:0}))}),[z]);const Te=()=>{r.emit("toggleNameMode")},Re=e=>{var t;const n=new Set;Fn.forEach((t=>{t.from===e&&n.add(t.to),t.to===e&&n.add(t.from)}));const r=null===(t=i.locations)||void 0===t?void 0:t.find((t=>t.id===e));var o;r&&"Airport"===r.journeyType&&(null===(o=i.locations)||void 0===o||o.forEach((t=>{"Airport"===t.journeyType&&t.id!==e&&n.add(t.id)})));return Array.from(n)},$e=(e,t)=>{var n,r;const o=null===(n=i.locations)||void 0===n?void 0:n.find((t=>t.id===e)),a=null===(r=i.locations)||void 0===r?void 0:r.find((e=>e.id===t));if("Airport"===(null===o||void 0===o?void 0:o.journeyType)&&"Airport"===(null===a||void 0===a?void 0:a.journeyType))return[e,t];const l=[[e]],s=new Set([e]);for(;l.length>0;){const e=l.shift(),n=e[e.length-1];if(n===t)return e;const r=Re(n);for(const t of r)s.has(t)||(s.add(t),l.push([...e,t]))}return null};(0,o.useEffect)((()=>(r.on("triathlonMovement",(()=>{Z(!0);const e=Pe(v,6);y(e),m(!0);const t=new Map;for(const n of e){const e=Oe(v,n,6);e&&t.set(n,e)}te(t)})),()=>{r.off("triathlonMovement")})),[r,v]);const Pe=(e,t)=>{if(t<=0)return[];const n=new Set,r=function(t,o){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Set;if(i.add(t),0===o)return void(t!==e&&n.add(t));const a=Re(t);for(const e of a)i.has(e)||r(e,o-1,new Set(i))};return r(e,t),Array.from(n)},Oe=(e,t,n)=>{if(0===n&&e===t)return[e];let r=null;const o=function(e,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(r)return;const a=[...i,e];if(0===n)return void(e===t&&(r=a));const l=Re(e);for(const t of l)i.includes(t)||o(t,n-1,a)};return o(e,n,[]),r},Le=()=>{if(h)m(!1),y([]),O(!1),me([]);else{if(he.length>0){var e;const t=he.reduce(((e,t)=>e+t.value),0);let n=t;const r=ze(v);"himalayan_ne"===(null===(e=i.currentGlobalEvent)||void 0===e?void 0:e.effect)&&"northeast"===r&&(n=Math.floor(t/2),console.log(`Applied Himalayan NE effect: Showing locations at ${n} hops away (half the card value ${t})`));const o=Pe(v,n);return y(o),void m(!0)}if(0===p.hand.filter((e=>e.value&&!isNaN(e.value))).length)return;pe(!0)}},Ie=e=>{var t,n;if(!g.includes(e))return;if(X){const t=ee.get(e);if(t){const n=p.hand.filter((e=>e.value&&(1===e.value||2===e.value||3===e.value))),o=n.find((e=>1===e.value)),i=n.find((e=>2===e.value)),a=n.find((e=>3===e.value));if(o&&i&&a){const n=[o.id,i.id,a.id];return x(e),w(b.filter((e=>!n.includes(e.id)))),r.emit("movePlayer",{path:t,travelCardIds:n,extraHopCount:0,isTriathlon:!0}),m(!1),y([]),Z(!1),void te(new Map)}}return console.log("Error with triathlon movement - path or cards not found"),m(!1),y([]),Z(!1),void te(new Map)}if(he.length>0){var o;const t=he.reduce(((e,t)=>e+t.value),0);let n=t;const a=ze(v);"himalayan_ne"===(null===(o=i.currentGlobalEvent)||void 0===o?void 0:o.effect)&&"northeast"===a&&(n=Math.floor(t/2),console.log(`Applied Himalayan NE effect: Finding path with ${n} hops (half the card value ${t})`));const l=Oe(v,e,n);if(l){const t=he.map((e=>e.id));return x(e),w(b.filter((e=>!t.includes(e.id)))),r.emit("movePlayer",{path:l,travelCardIds:t,extraHopCount:0}),m(!1),y([]),void me([])}return void console.log("Error: No valid path found with exact hop count")}const a=p.hand.filter((e=>e.value&&!isNaN(e.value))),l=null===(t=i.locations)||void 0===t?void 0:t.find((e=>e.id===v)),s=null===(n=i.locations)||void 0===n?void 0:n.find((t=>t.id===e)),c="Airport"===(null===l||void 0===l?void 0:l.journeyType)&&"Airport"===(null===s||void 0===s?void 0:s.journeyType);if(c){const t=a.find((e=>1===e.value));if(t)return x(e),w(b.filter((e=>e.id!==t.id))),r.emit("movePlayer",{path:[v,e],travelCardIds:[t.id],extraHopCount:0}),m(!1),void y([])}const u=$e(v,e);if(u){const t=u.length-1;let n=!1;for(let e=0;e<u.length-1;e++){var d,f;const t=u[e],r=u[e+1],o=null===(d=i.locations)||void 0===d?void 0:d.find((e=>e.id===t)),a=null===(f=i.locations)||void 0===f?void 0:f.find((e=>e.id===r));if("Airport"===(null===o||void 0===o?void 0:o.journeyType)&&"Airport"===(null===a||void 0===a?void 0:a.journeyType)){n=!0;break}}let o=t;if(n){let e=0,n=!1;for(let t=0;t<u.length;t++){var h;const r=null===(h=i.locations)||void 0===h?void 0:h.find((e=>e.id===u[t]));"Airport"===(null===r||void 0===r?void 0:r.journeyType)?n||(n=!0):n&&(e++,n=!1)}n&&e++,o=t-e+1}const l=((e,t)=>{for(let n=1;n<=Math.min(4,e.length);n++){const n=[],r=(t,o,i)=>{if(0!==o){if(!(i.length>=4||o<=0||t>=e.length))for(let n=t;n<e.length;n++)e[n].value<=o&&(i.push(e[n]),r(n+1,o-e[n].value,i),i.pop())}else n.push([...i])};if(r(0,t,[]),n.length>0)return n[0].map((e=>e.id))}return null})(a,o);if(l)return x(e),w(b.filter((e=>!l.includes(e.id)))),r.emit("movePlayer",{path:u,travelCardIds:l,extraHopCount:0}),m(!1),void y([])}console.log("No valid travel card/path found for the selected destination")},ze=e=>{var t;e=parseInt(e,10);const n=null===(t=i.locations)||void 0===t?void 0:t.find((t=>t.id===e));if(n&&n.region)return n.region.toLowerCase();const r={49:"jyotirlinga",50:"jyotirlinga",51:"jyotirlinga",52:"jyotirlinga",53:"jyotirlinga",54:"jyotirlinga",55:"jyotirlinga",56:"jyotirlinga",57:"jyotirlinga",58:"jyotirlinga",59:"jyotirlinga",60:"jyotirlinga",61:"airport",62:"airport",63:"airport",64:"airport",65:"airport",66:"airport"};return r[e]?r[e]:e>=1&&e<=9?"north":e>=10&&e<=19?"west":e>=20&&e<=27?"south":e>=28&&e<=35?"central":e>=36&&e<=40?"east":e>=41&&e<=48?"northeast":"unknown"},Ae=e=>e!==v&&(!!h&&g.includes(e)),Fe=(e,t)=>{const n=e.position,r=An[n];if(!r)return null;const o=c.filter((t=>t.position===e.position)).length,i=c.filter((t=>t.position===e.position)).indexOf(e),a=o>1?20*(i-(o-1)/2):0,l=e.id===p.id;return l&&k?null:(0,$n.jsxs)("g",{transform:`translate(${r.x+a}, ${r.y-25})`,children:[(0,$n.jsx)("circle",{cx:"0",cy:"0",r:"15",fill:l?"#ff5722":"#999",stroke:"#fff",strokeWidth:"2",style:{filter:l?"drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))":"",cursor:l&&!h?"pointer":"default"},onClick:()=>l&&Le()}),(0,$n.jsx)("text",{x:"0",y:"0",textAnchor:"middle",dominantBaseline:"middle",fontSize:"10",fontWeight:"bold",fill:"#fff",children:e.name.charAt(0).toUpperCase()})]},`player-${e.id}`)},Me=()=>{if(!k||!N)return null;const e=An[N];if(!e)return null;C.length;const t=R||(null===p||void 0===p?void 0:p.id),n=c.find((e=>e.id===t))||p||{name:"Unknown"},r=["#ff5722","#2196f3","#4caf50"][c.findIndex((e=>e.id===t))]||"#ff5722";return(0,$n.jsxs)("g",{children:[C.length>1&&(0,$n.jsx)("path",{d:C.map(((e,t)=>{const n=An[e];return 0===t?`M ${n.x} ${n.y}`:`L ${n.x} ${n.y}`})).join(" "),stroke:r,strokeWidth:"3",strokeDasharray:"5,5",fill:"none",opacity:"0.7"}),(0,$n.jsxs)("g",{transform:`translate(${e.x}, ${e.y-25})`,children:[(0,$n.jsx)("circle",{cx:"0",cy:"0",r:"15",fill:r,stroke:"#fff",strokeWidth:"2",style:{filter:`drop-shadow(0 0 7px ${r}99)`,animation:"moveAnimation 0.8s infinite"}}),(0,$n.jsx)("text",{x:"0",y:"0",textAnchor:"middle",dominantBaseline:"middle",fontSize:"10",fontWeight:"bold",fill:"#fff",children:n.name.charAt(0).toUpperCase()})]})]},"animating-player")},Be=()=>Object.entries(d||{}).map((e=>{let[t,n]=e;t=parseInt(t,10);const r=An[t];if(!r||!n)return null;const o=Array.isArray(n)?n:[n];return 0===o.length||W?null:(0,$n.jsx)("g",{children:o.map(((e,n)=>{const i=Yn[e]||"#ccc",a=n*(2*Math.PI)/o.length,l=r.x+31.25*Math.cos(a),s=r.y+31.25*Math.sin(a);return(0,$n.jsx)("rect",{x:l-6.25,y:s-6.25,width:"12.5",height:"12.5",rx:"2.5",fill:i,stroke:"#fff",strokeWidth:"1"},`cube-${t}-${n}`)}))},`cubes-${t}`)})),De=()=>Object.entries(f||{}).map((e=>{let[t,n]=e;t=parseInt(t,10);const r=An[t];if(!r)return null;let o;return o="boolean"===typeof n?!0===n?1:0:parseInt(n,10),isNaN(o)||o<=0?null:(0,$n.jsxs)("g",{transform:`translate(${r.x}, ${r.y+25})`,children:[(0,$n.jsx)("circle",{cx:"0",cy:"0",r:"15",fill:"#ffd700",stroke:"#fff",strokeWidth:"1"}),(0,$n.jsx)("text",{x:"0",y:"0",textAnchor:"middle",dominantBaseline:"middle",fontSize:"12.5",fontWeight:"bold",fill:"#000",children:o})]},`om-${t}`)}));return(0,o.useEffect)((()=>{const e=e=>{"soundEnabled"===e.key&&ie("false"!==e.newValue)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)}),[]),(0,o.useEffect)((()=>{void 0!==i.nameMode&&U(i.nameMode)}),[i.nameMode]),(0,o.useEffect)((()=>{if(0===Object.keys(ae).length)return void le(f||{});let e=null;if(Object.keys(ae).some((t=>(ae[t]||0)>(f[t]||0)&&(e=parseInt(t,10),!0)))&&oe&&p&&r.id!==p.id){const t=p.position>=49&&p.position<=60,n=e===p.position;if(t&&n){new Audio(Qn).play()}}le(f||{})}),[f,oe,null===p||void 0===p?void 0:p.id,null===r||void 0===r?void 0:r.id,null===p||void 0===p?void 0:p.position]),(0,o.useEffect)((()=>{const e=()=>{!document.fullscreenElement&&F&&(M(!1),A(!1))};return document.addEventListener("fullscreenchange",e),()=>{document.removeEventListener("fullscreenchange",e)}}),[F]),(0,$n.jsxs)("div",{className:"interactive-board",children:[(0,$n.jsxs)("div",{className:"board-header",style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"10px",background:"#f5f5f5",borderBottom:"1px solid #ddd"},children:[(0,$n.jsxs)("div",{children:[(0,$n.jsx)("h3",{children:"Game Board"}),(0,$n.jsxs)("p",{style:{fontSize:"0.9rem",color:"#666"},children:["Your current position: ",(0,$n.jsx)("strong",{children:p?p.position:"Unknown"})]})]}),(0,$n.jsxs)("div",{className:"flex gap-sm",children:[(0,$n.jsx)("button",{onClick:()=>D(!B),className:"secondary",children:B?"Show Labels":"Hide Labels"}),(0,$n.jsx)("button",{onClick:Te,className:"secondary",children:W?"Show Numbers":"Show Cubes"}),(0,$n.jsxs)("button",{onClick:()=>{const e=document.documentElement;document.fullscreenElement?document.exitFullscreen&&document.exitFullscreen().then((()=>{M(!1),A(!1)})).catch((e=>{console.error(`Error attempting to exit fullscreen: ${e.message}`)})):e.requestFullscreen&&e.requestFullscreen().then((()=>{M(!0),A(!0)})).catch((e=>{console.error(`Error attempting to enable fullscreen: ${e.message}`)}))},className:"secondary",children:[(0,$n.jsx)("span",{role:"img","aria-label":"Full Screen",children:"\ud83d\udd0d"})," ",z?"Exit Full Screen":"Full Screen"]}),p&&r&&p.id===r.id&&(0,$n.jsxs)($n.Fragment,{children:[(0,$n.jsx)("button",{onClick:Le,disabled:0===b.filter((e=>"travel"===e.type)).length,children:"Move with Cards"}),(0,$n.jsx)("button",{className:"accent",onClick:()=>O(!0),children:"Custom Movement"})]})]})]}),(0,$n.jsxs)("div",{className:"board-area",style:{position:"relative",height:"calc(100vh - 60px)",overflow:"hidden"},children:[h&&(0,$n.jsxs)("div",{style:{padding:"10px",backgroundColor:"rgba(255, 255, 255, 0.9)",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",marginBottom:"10px"},children:[(0,$n.jsx)("h3",{children:"Choose a destination:"}),(0,$n.jsx)("p",{style:{fontSize:"0.9rem",color:"#666"},children:"Click on a highlighted location on the board to move there."}),(0,$n.jsx)("button",{className:"secondary",onClick:()=>{m(!1),y([]),O(!1)},style:{marginTop:"0.5rem"},children:"Cancel"})]}),P&&(0,$n.jsx)(Mn,{socket:r,currentPosition:v,onCancel:()=>O(!1)}),!z&&(0,$n.jsxs)("svg",{ref:l,viewBox:"0 0 1600 1400",className:"board-svg "+(B?"hide-labels":""),children:[(0,$n.jsx)("image",{href:"/assets/images/map_take_2.jpg",width:"1600",height:"1400",x:"0",y:"0",preserveAspectRatio:"xMidYMid slice"}),Fn.map((e=>{const t=An[e.from],n=An[e.to];if(!t||!n)return null;const r=(t.x+n.x)/2,o=(t.y+n.y)/2,i=n.x-t.x,a=n.y-t.y,l=Math.sqrt(i*i+a*a),s=Math.min(.2*l,40),c=r+-a/l*s,u=o+i/l*s,d=`M ${t.x} ${t.y} Q ${c} ${u} ${n.x} ${n.y}`;return(0,$n.jsx)("path",{d:d,fill:"none",stroke:"#ffffff",strokeWidth:"3",strokeLinecap:"round",opacity:"0.9"},`${e.from}-${e.to}`)})),Object.entries(An).map((e=>{var t;let[n,r]=e;n=parseInt(n,10);const o=Ae(n),a="#ffffff",l=ze(n),s=null===(t=i.locations)||void 0===t?void 0:t.find((e=>e.id===n)),c=s?s.name:"",u=n>=49&&n<=60,f="Airport"===(null===s||void 0===s?void 0:s.journeyType)||n>=61&&n<=66;let p=0,h=40;switch(l){case"north":case"northeast":h=-45;break;case"south":h=45;break;case"west":p=-70,h=0;break;case"east":p=70,h=0;break;case"central":p=n%2===0?70:-70,h=0;break;case"airport":h=60}11===n&&(p=-80);const m=r.x+p,g=r.y+h,y=c.length,v=Math.max(120,Math.min(7.5*y,180));return(0,$n.jsxs)("g",{className:"node-group",children:[u&&(0,$n.jsx)("polygon",{points:`${r.x},${r.y-32.5} ${r.x+7.5},${r.y-15} ${r.x+27.5},${r.y-15} ${r.x+12.5},${r.y-5} ${r.x+17.5},${r.y+12.5} ${r.x},${r.y} ${r.x-17.5},${r.y+12.5} ${r.x-12.5},${r.y-5} ${r.x-27.5},${r.y-15} ${r.x-7.5},${r.y-15}`,fill:o?"rgba(255, 87, 34, 0.2)":a,stroke:o?"#ff5722":"#333",strokeWidth:o?3:1.5,className:`node-region-${l} node ${o?"node-highlighted":""}`,style:{cursor:o?"pointer":"default",transition:"all 0.3s ease",pointerEvents:o?"auto":"none",filter:o?"drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))":"none"},onClick:()=>o&&Ie(n)}),f&&(0,$n.jsx)("polygon",{points:`${r.x},${r.y-31.25} ${r.x+31.25},${r.y} ${r.x},${r.y+31.25} ${r.x-31.25},${r.y}`,fill:o?"rgba(255, 87, 34, 0.2)":a,stroke:o?"#ff5722":"#333",strokeWidth:o?3:1.5,className:`node-region-${l} node ${o?"node-highlighted":""}`,style:{cursor:o?"pointer":"default",transition:"all 0.3s ease",pointerEvents:o?"auto":"none",filter:o?"drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))":"none"},onClick:()=>o&&Ie(n)}),!u&&!f&&(0,$n.jsx)("circle",{cx:r.x,cy:r.y,r:"22.5",fill:o?"rgba(255, 87, 34, 0.2)":a,stroke:o?"#ff5722":"#333",strokeWidth:o?3:1.5,className:`node-region-${l} node ${o?"node-highlighted":""}`,style:{cursor:o?"pointer":"default",transition:"all 0.3s ease",pointerEvents:o?"auto":"none",filter:o?"drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))":"none"},onClick:()=>o&&Ie(n)}),W?d[n]&&(0,$n.jsx)("g",{children:(Array.isArray(d[n])?d[n]:[d[n]]).map(((e,t)=>{const o=Yn[e]||"#ccc";return(0,$n.jsx)("rect",{x:r.x-6.25,y:r.y-6.25,width:"12.5",height:"12.5",rx:"2.5",fill:o,stroke:"#fff",strokeWidth:"1"},`inner-cube-${n}-${t}`)}))}):(0,$n.jsx)("text",{x:r.x,y:r.y,textAnchor:"middle",dominantBaseline:"middle",fontSize:"16",fontWeight:"bold",fill:"#000",style:{filter:"drop-shadow(0px 0px 1px rgba(255, 255, 255, 0.8))"},children:n}),(0,$n.jsxs)("g",{className:"node-label-container",transform:`translate(${m}, ${g})`,children:[(0,$n.jsx)("rect",{x:-v/2,y:"-12",width:v,height:"24",rx:"6",ry:"6",className:"location-label-background",fill:"rgba(255, 255, 255, 0.95)",stroke:"#ccc",strokeWidth:"1"}),(0,$n.jsx)("text",{x:"0",y:"0",textAnchor:"middle",dominantBaseline:"middle",fontSize:"11",fontWeight:"bold",fill:"#333",className:"enhanced-location-label",children:c})]}),(0,$n.jsx)("title",{children:`Node ${n} (${(null===s||void 0===s?void 0:s.name)||"unknown"})`})]},`node-${n}`)})),Be(),De(),c.map(((e,t)=>Fe(e))),Me()]}),z&&(0,$n.jsxs)("div",{className:"board-fullscreen",style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"#f0f8ff",zIndex:1e3,overflow:"hidden"},children:[(0,$n.jsxs)("div",{style:{position:"absolute",top:"15px",left:"15px",zIndex:10,display:"flex",gap:"8px",alignItems:"center"},children:[(0,$n.jsx)("button",{onClick:()=>{ne?re(null):localStorage.removeItem("pendingJourneyModal"),document.fullscreenElement&&document.exitFullscreen().catch((e=>{console.error(`Error exiting fullscreen: ${e.message}`)})),M(!1),A(!1)},style:{background:"rgba(255, 255, 255, 0.8)",borderRadius:"50%",width:"36px",height:"36px",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",border:"none",cursor:"pointer",fontSize:"18px",marginRight:"6px",color:"#333"},children:"\u2715"}),(0,$n.jsxs)("div",{style:{backgroundColor:"rgba(255, 255, 255, 0.8)",padding:"6px 12px",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",display:"flex",alignItems:"center",gap:"8px"},children:[(0,$n.jsx)("div",{style:{fontWeight:"bold",fontSize:"0.9rem",color:"#333"},children:"Current Turn:"}),(0,$n.jsxs)("div",{style:{color:"var(--primary-color)",fontWeight:"bold",fontSize:"0.9rem",display:"flex",alignItems:"center",gap:"4px"},children:[p?p.name:"Loading...",p&&r&&p.id===r.id&&(0,$n.jsx)("span",{style:{fontSize:"0.8rem",color:"#666",fontStyle:"italic",fontWeight:"normal"},children:"(you)"})]})]}),(0,$n.jsxs)("div",{style:{display:"flex",gap:"8px"},children:[p&&r&&p.id===r.id&&(0,$n.jsx)("button",{onClick:()=>{if(console.log("End turn clicked. Current state:",{currentPlayer:p?{id:p.id,name:p.name,position:p.position}:null,turnIndex:i.turnIndex,playersLength:i.players.length}),oe){new Audio(Vn).play()}r.emit("endTurn")},className:"accent",style:{padding:"6px 12px",fontSize:"0.85rem",backgroundColor:"var(--accent-color)",color:"white",borderRadius:"4px",border:"none",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",cursor:"pointer"},children:"End Turn"}),(0,$n.jsx)("button",{onClick:()=>r.emit("saveGame"),className:"secondary",style:{padding:"6px 12px",fontSize:"0.85rem",backgroundColor:"var(--secondary-color)",color:"white",borderRadius:"4px",border:"none",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",cursor:"pointer"},children:"Save Game"}),(0,$n.jsx)("button",{onClick:()=>de(!ue),className:"secondary",style:{padding:"6px 12px",fontSize:"0.85rem",backgroundColor:"var(--secondary-color)",color:"white",borderRadius:"4px",border:"none",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",cursor:"pointer"},children:ue?"Show":"Hide"}),(0,$n.jsx)("button",{onClick:Te,className:"secondary",style:{padding:"6px 12px",fontSize:"0.85rem",backgroundColor:"var(--secondary-color)",color:"white",borderRadius:"4px",border:"none",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",cursor:"pointer"},children:W?"Show Numbers":"Show Cubes"})]})]}),(0,$n.jsxs)("div",{style:{position:"absolute",bottom:"15px",left:"280px",zIndex:10,background:"rgba(255, 255, 255, 0.7)",borderRadius:"8px",padding:"5px",display:"flex",boxShadow:"0 2px 4px rgba(0,0,0,0.2)"},children:[(0,$n.jsx)("button",{onClick:()=>{q((e=>Math.min(e+.2,3)))},className:"secondary",style:{padding:"5px 10px",margin:"0 2px",background:"transparent",boxShadow:"none",fontSize:"18px",fontWeight:"bold",border:"none",cursor:"pointer"},children:"+"}),(0,$n.jsx)("button",{onClick:()=>{q((e=>Math.max(e-.2,.5)))},className:"secondary",style:{padding:"5px 10px",margin:"0 2px",background:"transparent",boxShadow:"none",fontSize:"18px",fontWeight:"bold",border:"none",cursor:"pointer"},children:"\u2212"}),(0,$n.jsx)("button",{onClick:()=>{q(1),J({x:-250,y:0})},className:"secondary",style:{padding:"5px 10px",margin:"0 2px",background:"transparent",boxShadow:"none",fontSize:"12px",border:"none",cursor:"pointer"},children:"Reset"})]}),(0,$n.jsx)("div",{style:{position:"absolute",top:"70px",left:"15px",bottom:"15px",width:"250px",overflowY:"auto",zIndex:5,padding:"5px",display:ue?"none":"flex",flexDirection:"column",gap:"8px",transition:"all 0.3s ease"},children:(0,$n.jsxs)("div",{style:{marginBottom:"10px"},children:[(0,$n.jsxs)("div",{style:{fontSize:"0.9rem",fontWeight:"bold",backgroundColor:"rgba(245, 245, 245, 0.85)",padding:"6px 8px",borderRadius:"8px",marginBottom:"4px",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,$n.jsx)("span",{children:"Energy Cubes: "}),(0,$n.jsx)("div",{style:{display:"flex",gap:"8px"},children:Object.entries(i.energyCubePile||{}).map((e=>{let[t,n]=e;return(0,$n.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,$n.jsx)("div",{className:`energy-cube ${t}`,style:{width:"12px",height:"12px",marginRight:"3px"}}),(0,$n.jsx)("span",{style:{fontSize:"0.8rem"},children:n})]},t)}))})]}),c.map(((e,t)=>(0,$n.jsx)(Bn,{player:e,isActive:t===u,winner:a,socket:r,gameState:i},e.id)))]})}),(0,$n.jsx)("div",{style:{position:"absolute",bottom:"15px",left:"15px",width:"250px",zIndex:5,padding:"5px",display:ue?"none":"block",transition:"all 0.3s ease"},children:(0,$n.jsx)(Dn,{faceUpTravel:i.faceUpTravel,faceUpEvent:i.faceUpEvent,socket:r,currentGlobalEvent:i.currentGlobalEvent,energyCubePile:i.energyCubePile,onCardsPicked:(e,t)=>{if(!e||0===e.length)return;if(!t||!Array.isArray(t)||0===t.length)return void console.warn("No valid card positions provided for animation");const n=document.querySelector(".compact-player-mat.current-user");if(!n)return void console.warn("Could not find current player mat for animation target");const r=n.getBoundingClientRect(),o={x:r.left+r.width/2,y:r.top+r.height/2},i={};try{t.forEach((e=>{e&&e.cardId&&e.position&&(i[e.cardId]=e.position)}))}catch(a){console.error("Error processing card positions:",a)}0!==Object.keys(i).length?ce({active:!0,cards:e,cardPositions:i,targetPosition:o}):console.warn("No valid card positions could be processed")}})}),(0,$n.jsxs)("div",{style:{position:"absolute",top:"15px",right:"15px",bottom:"15px",width:"420px",overflowY:"auto",zIndex:10,padding:"5px",display:ue?"none":"flex",flexDirection:"column",gap:"8px",background:"rgba(255, 255, 255, 0.85)",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.2)",transition:"all 0.3s ease"},children:[(0,$n.jsx)("div",{style:{fontSize:"1rem",fontWeight:"bold",marginBottom:"5px",padding:"5px",borderBottom:"1px solid #ddd",textAlign:"center"},children:"Journey Cards"}),(0,$n.jsx)(Wn,{faceUpJourneyOuter:i.faceUpJourneyOuter,faceUpJourneyInner:i.faceUpJourneyInner,gameState:i,currentPlayer:p,socket:r,setJourneyModal:re})]}),(0,$n.jsx)("div",{className:"board-fullscreen-content",onMouseDown:e=>{0===e.button&&(Y(!0),K({x:e.clientX-V.x,y:e.clientY-V.y}))},onMouseMove:e=>{Q&&J({x:e.clientX-G.x,y:e.clientY-G.y})},onMouseUp:()=>{Y(!1)},onMouseLeave:()=>{Y(!1)},style:{cursor:Q?"grabbing":"grab",position:"absolute",top:0,left:0,right:0,bottom:0,marginRight:ue?"0":"420px",display:"flex",justifyContent:"center",alignItems:"center",transition:"all 0.3s ease"},children:(0,$n.jsxs)("svg",{ref:s,viewBox:"-100 0 1800 1400",className:"board-fullscreen-svg "+(B?"hide-labels":""),style:{transform:`scale(${H}) translate(${V.x/H}px, ${V.y/H}px)`,transformOrigin:"center center",width:"100%",height:"100%",margin:"0 auto"},children:[(0,$n.jsx)("image",{href:"/assets/images/map_take_2.jpg",width:"1800",height:"1400",x:"-100",y:"0",preserveAspectRatio:"xMidYMid slice"}),Fn.map((e=>{const t=An[e.from],n=An[e.to];if(!t||!n)return null;const r=(t.x+n.x)/2,o=(t.y+n.y)/2,i=n.x-t.x,a=n.y-t.y,l=Math.sqrt(i*i+a*a),s=Math.min(.2*l,40),c=r+-a/l*s,u=o+i/l*s,d=`M ${t.x} ${t.y} Q ${c} ${u} ${n.x} ${n.y}`;return(0,$n.jsx)("path",{d:d,fill:"none",stroke:"#ffffff",strokeWidth:"4.5",strokeLinecap:"round",opacity:"0.9"},`${e.from}-${e.to}`)})),Object.entries(An).map((e=>{var t;let[n,r]=e;n=parseInt(n,10);const o=Ae(n),a="#ffffff",l=ze(n),s=null===(t=i.locations)||void 0===t?void 0:t.find((e=>e.id===n)),c=s?s.name:"",u=n>=49&&n<=60,f="Airport"===(null===s||void 0===s?void 0:s.journeyType)||n>=61&&n<=66;let p=0,h=40;switch(l){case"north":case"northeast":h=-45;break;case"south":h=45;break;case"west":p=-90,h=0;break;case"east":p=70,h=0;break;case"central":p=n%2===0?70:-70,h=0;break;case"jyotirlinga":p=n%2===0?60:-60,h=0;break;case"airport":h=60}11===n&&(p=-90);const m=r.x+p,g=r.y+h,y=c.length,v=Math.max(140,Math.min(10*y,240));return(0,$n.jsxs)("g",{className:"node-group",children:[u&&(0,$n.jsx)("polygon",{points:`${r.x},${r.y-32.5} ${r.x+7.5},${r.y-15} ${r.x+27.5},${r.y-15} ${r.x+12.5},${r.y-5} ${r.x+17.5},${r.y+12.5} ${r.x},${r.y} ${r.x-17.5},${r.y+12.5} ${r.x-12.5},${r.y-5} ${r.x-27.5},${r.y-15} ${r.x-7.5},${r.y-15}`,fill:o?"rgba(255, 87, 34, 0.2)":a,stroke:o?"#ff5722":"#333",strokeWidth:o?3:1.5,className:`node-region-${l} node ${o?"node-highlighted":""}`,style:{cursor:o?"pointer":"default",transition:"all 0.3s ease",pointerEvents:o?"auto":"none",filter:o?"drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))":"none"},onClick:()=>o&&Ie(n)}),f&&(0,$n.jsx)("polygon",{points:`${r.x},${r.y-31.25} ${r.x+31.25},${r.y} ${r.x},${r.y+31.25} ${r.x-31.25},${r.y}`,fill:o?"rgba(255, 87, 34, 0.2)":a,stroke:o?"#ff5722":"#333",strokeWidth:o?3:1.5,className:`node-region-${l} node ${o?"node-highlighted":""}`,style:{cursor:o?"pointer":"default",transition:"all 0.3s ease",pointerEvents:o?"auto":"none",filter:o?"drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))":"none"},onClick:()=>o&&Ie(n)}),!u&&!f&&(0,$n.jsx)("circle",{cx:r.x,cy:r.y,r:"22.5",fill:o?"rgba(255, 87, 34, 0.2)":a,stroke:o?"#ff5722":"#333",strokeWidth:o?3:1.5,className:`node-region-${l} node ${o?"node-highlighted":""}`,style:{cursor:o?"pointer":"default",transition:"all 0.3s ease",pointerEvents:o?"auto":"none",filter:o?"drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))":"none"},onClick:()=>o&&Ie(n)}),W?d[n]&&(0,$n.jsx)("g",{children:(Array.isArray(d[n])?d[n]:[d[n]]).map(((e,t)=>{const o=Yn[e]||"#ccc";return(0,$n.jsx)("rect",{x:r.x-7.5,y:r.y-7.5,width:"15",height:"15",rx:"3",fill:o,stroke:"#fff",strokeWidth:"1"},`inner-cube-${n}-${t}`)}))}):(0,$n.jsx)("text",{x:r.x,y:r.y,textAnchor:"middle",dominantBaseline:"middle",fontSize:"18",fontWeight:"bold",fill:"#000",style:{filter:"drop-shadow(0px 0px 1px rgba(255, 255, 255, 0.8))"},children:n}),(0,$n.jsxs)("g",{className:"node-label-container",transform:`translate(${m}, ${g})`,children:[(0,$n.jsx)("rect",{x:-v/2,y:"-14",width:v,height:"28",rx:"6",ry:"6",className:"location-label-background",fill:"rgba(255, 255, 255, 0.95)",stroke:"#333",strokeWidth:"1"}),(0,$n.jsx)("text",{x:"0",y:"0",textAnchor:"middle",dominantBaseline:"middle",fontSize:"13",fontWeight:"bold",fill:"#333",className:"enhanced-location-label",children:c})]}),(0,$n.jsx)("title",{children:`Node ${n} (${(null===s||void 0===s?void 0:s.name)||"unknown"})`})]},`node-${n}`)})),c.map(((e,t)=>Fe(e))),Me(),Be(),De()]})})]})]}),ne&&(0,$n.jsx)(In,{card:ne,onClose:()=>re(null),onCollect:e=>{const t=[1,1,2,3],n=Gn(p,e),o=n<t.length?t[n]:1/0,i=void 0!==e.reward.outer?"outer":"inner";re(null),r.emit("collectJourney",{journeyCardId:e.id,journeyType:i,requiredOm:o})},isAvailable:e=>p&&r&&p.id===r.id&&p.position===e.locationId,currentPlayer:p,gameState:i}),fe&&(0,$n.jsx)(Un,{player:p,onCancel:()=>pe(!1),onSelectCards:e=>{var t,n;pe(!1),me(e);const r=e.reduce(((e,t)=>e+t.value),0);let o=r;const a=ze(v);console.log("currentPlayerRegion",a);"himalayan_ne"===(null===(t=i.currentGlobalEvent)||void 0===t?void 0:t.effect)&&"northeast"===a&&(o=Math.floor(r/2),console.log(`Applied Himalayan NE effect: Showing locations at ${o} hops away (half the card value ${r})`));const l=Pe(v,o);y(l),m(!0);const s=e.some((e=>"truck"===e.vehicle));"heavy_haul_reward"===(null===(n=i.currentGlobalEvent)||void 0===n?void 0:n.effect)&&s&&p.energyCubes.length>=3&&(xe(e.map((e=>e.id))),setTimeout((()=>{ye(!0)}),500))},numberOfCardsToSelect:100,title:"Select Travel Cards",description:"himalayan_ne"===(null===(n=i.currentGlobalEvent)||void 0===n?void 0:n.effect)&&"northeast"===ze(v)?"Himalayan Northeast event active! Your travel cards will only let you move half as far.":"Select travel cards for your move. The total value will determine how far you can travel."}),ge&&(0,$n.jsx)(Hn,{player:p,onCancel:()=>ye(!1),onSelectCubes:e=>{ye(!1),r.emit("selectEnergyCubesForHeavyHaul",{selectedCubes:e,travelCardIds:ve}),be&&be.length>0&&Ne(be,(()=>{r.emit("movePlayer",{path:be,travelCardIds:ve,isTriathlon:X}),me([]),y([]),m(!1),we(null),X&&Z(!1)})),xe(null)},numberOfCubesToSelect:2,title:"Heavy Haul Event",description:"Select 2 energy cubes to discard for using the Truck travel card."}),ke&&(0,$n.jsx)(Hn,{player:p,onCancel:()=>Se(!1),onSelectCubes:e=>{Se(!1),r.emit("selectEnergyCubesToDiscard",{selectedCubes:e}),_e(null)},numberOfCubesToSelect:(null===Ee||void 0===Ee?void 0:Ee.count)||2,title:`${null===Ee||void 0===Ee?void 0:Ee.region} Region Effect`,description:`Select ${(null===Ee||void 0===Ee?void 0:Ee.count)||2} energy cube(s) to discard for the ${null===Ee||void 0===Ee?void 0:Ee.effect} effect.`}),Ce&&(0,$n.jsx)(Un,{player:p,onCancel:()=>je(!1),onSelectCards:e=>{je(!1),r.emit("selectTravelCardsToDiscard",{selectedCardIds:e}),_e(null)},numberOfCardsToSelect:(null===Ee||void 0===Ee?void 0:Ee.count)||1,title:`${null===Ee||void 0===Ee?void 0:Ee.region} Region Effect`,description:`Select ${(null===Ee||void 0===Ee?void 0:Ee.count)||1} travel card(s) to discard for the ${null===Ee||void 0===Ee?void 0:Ee.effect} effect.`}),se.active&&(0,$n.jsx)(qn,{cards:se.cards,sourcePosition:se.sourcePosition,targetPosition:se.targetPosition,onAnimationComplete:()=>{setTimeout((()=>{ce((e=>({...e,active:!1})))}),500)}})]})};class Xn extends o.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",t.componentStack),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?(0,$n.jsxs)("div",{style:{padding:"20px",margin:"20px",backgroundColor:"#ffebee",border:"1px solid #f44336",borderRadius:"4px"},children:[(0,$n.jsxs)("h2",{children:["Something went wrong in ",this.props.componentName||"a component","."]}),(0,$n.jsxs)("details",{style:{whiteSpace:"pre-wrap",marginTop:"10px"},children:[(0,$n.jsx)("summary",{children:"Show Error Details"}),(0,$n.jsx)("p",{children:this.state.error&&this.state.error.toString()}),(0,$n.jsx)("p",{children:"Component Stack:"}),(0,$n.jsx)("pre",{children:this.state.errorInfo&&this.state.errorInfo.componentStack})]}),this.props.fallback]}):this.props.children}}const Zn=Xn;const er=function(e){let{socket:t,gameState:n,winner:r}=e;const{players:i,turnIndex:a,finalRound:l}=n,s=i&&void 0!==a&&a>=0&&a<i.length?i[a]:null,[c,u]=(0,o.useState)((()=>"false"!==localStorage.getItem("soundEnabled")));return(0,$n.jsxs)("div",{className:"flex-column",children:[(0,$n.jsx)("div",{className:"flex-grow",children:(0,$n.jsx)(Zn,{componentName:"InteractiveBoard",fallback:(0,$n.jsx)("div",{children:"Error in game board. Please refresh the page."}),children:(0,$n.jsx)(Kn,{socket:t,gameState:n,winner:r})})}),(0,$n.jsx)("div",{className:"player-section flex",style:{flexWrap:"wrap",justifyContent:"center",gap:"1rem",marginTop:"1rem"},children:i.map(((e,o)=>(0,$n.jsx)(Zn,{componentName:`PlayerMat for ${e.name}`,fallback:(0,$n.jsxs)("div",{children:["Error in player mat for ",e.name]}),children:(0,$n.jsx)(Ln,{player:e,isActive:o===a,winner:r,socket:t,gameState:n},e.id)},e.id)))}),(0,$n.jsx)("div",{className:"face-up-cards-section",style:{marginTop:"1rem"},children:(0,$n.jsx)(zn,{socket:t,gameState:n})}),(0,$n.jsxs)("div",{className:"flex gap-md mt-md",style:{justifyContent:"center"},children:[s&&t&&s.id===t.id&&(0,$n.jsx)("button",{className:"accent",onClick:()=>{if(c){new Audio(Vn).play()}t.emit("endTurn")},disabled:!!r,children:"End Turn"}),(0,$n.jsx)("button",{className:"secondary",onClick:()=>{t.emit("saveGame")},children:"Save Game"}),(0,$n.jsx)("button",{className:"secondary",onClick:()=>{const e=!c;u(e),localStorage.setItem("soundEnabled",e.toString())},title:c?"Disable sound":"Enable sound",children:c?"\ud83d\udd0a Sound On":"\ud83d\udd07 Sound Off"})]})]})},tr=()=>{const e=new URLSearchParams(window.location.search).get("server");return e?`http://${e}:4000`:`http://${window.location.hostname}:4000`},nr=Rn(tr());const rr=function(){const[e,t]=(0,o.useState)(null),[n,r]=(0,o.useState)(null),[i,a]=(0,o.useState)(!0),[l,s]=(0,o.useState)(tr());(0,o.useEffect)((()=>(nr.on("connect",(()=>{console.log("Connected to server with socket ID:",nr.id),console.log("Connected to server URL:",l),a(!1)})),nr.on("gameState",(e=>{var n,r,o;console.log("Received updated game state:",{started:e.started,faceUpTravel:null===(n=e.faceUpTravel)||void 0===n?void 0:n.map((e=>e.id)),turnIndex:e.turnIndex,currentPlayerName:null===(r=e.players)||void 0===r||null===(o=r[e.turnIndex])||void 0===o?void 0:o.name}),t(e)})),nr.on("joinError",(e=>alert(e))),nr.on("startError",(e=>alert(e))),nr.on("gameOver",(e=>{let{winner:t}=e;console.log("Game Over! Winner:",t),console.log("Win condition:",t.winByOm?"OM Tokens":t.winByScore?"Score":"Unknown"),console.log("OM Total:",t.omTotal,"Score:",t.totalScore),r(t)})),()=>{nr.off("connect"),nr.off("gameState"),nr.off("joinError"),nr.off("startError"),nr.off("gameOver")})),[]);const c=()=>(0,$n.jsxs)("div",{className:"app-navigation",children:[(0,$n.jsx)(tt,{to:"/",className:"active",children:"Game"}),(0,$n.jsx)(tt,{to:"/simulation",children:"Simulation Dashboard"}),(0,$n.jsxs)("div",{className:"connection-info",children:["Connected to: ",l]})]});return i?(0,$n.jsxs)($n.Fragment,{children:[(0,$n.jsx)(c,{}),(0,$n.jsx)("div",{className:"loading",children:"Connecting to server"})]}):e?(console.log("Game started state:",e.started),e.started?(0,$n.jsxs)($n.Fragment,{children:[(0,$n.jsx)(c,{}),(0,$n.jsx)(er,{socket:nr,gameState:e,winner:n})]}):(0,$n.jsxs)($n.Fragment,{children:[(0,$n.jsx)(c,{}),(0,$n.jsx)(Pn,{socket:nr,gameState:e})]})):(0,$n.jsxs)($n.Fragment,{children:[(0,$n.jsx)(c,{}),(0,$n.jsx)("div",{className:"loading",children:"Loading game state"})]})},or=()=>{var e;const[t,n]=(0,o.useState)([]),[r,i]=(0,o.useState)(null),[a,l]=(0,o.useState)(null),[s,c]=(0,o.useState)(!0),[u,d]=(0,o.useState)(null),[f,p]=(0,o.useState)(null),[h,m]=(0,o.useState)([]),[g,y]=(0,o.useState)("overview"),[v,x]=(0,o.useState)(10),[b,w]=(0,o.useState)([]),[k,S]=(0,o.useState)(["random","random"]),[C,j]=(0,o.useState)(!1),[E,_]=(0,o.useState)(1);(0,o.useEffect)((()=>{N();const e=setInterval(R,2e3);return()=>clearInterval(e)}),[]);const N=async()=>{c(!0);try{await Promise.all([T(),R(),$()]),c(!1)}catch(lr){d("Failed to load simulation data"),c(!1)}},T=async()=>{try{const e=await fetch("/api/metrics"),t=await e.json();return i(t),t}catch(lr){console.error("Failed to fetch metrics:",lr),d("Failed to load metrics")}},R=async()=>{try{const e=await fetch("/api/simulation/status"),t=await e.json();return l(t),j("running"===t.status),"running"!==t.status&&C&&T(),t}catch(lr){console.error("Failed to fetch status:",lr)}},$=async()=>{try{const e=await fetch("/api/simulation/strategies"),t=await e.json();return w(t.strategies),t}catch(lr){console.error("Failed to fetch strategies:",lr),d("Failed to load strategies")}},P=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;try{const e=await fetch(`/api/events?limit=${t}`),n=await e.json();return m(n.events),n}catch(lr){console.error("Failed to fetch events:",lr),d("Failed to load events")}};return s?(0,$n.jsx)("div",{className:"simulation-dashboard loading",children:"Loading simulation data..."}):u?(0,$n.jsxs)("div",{className:"simulation-dashboard error",children:[(0,$n.jsx)("h2",{children:"Error"}),(0,$n.jsx)("p",{children:u}),(0,$n.jsx)("button",{onClick:N,children:"Retry"})]}):(0,$n.jsxs)("div",{className:"simulation-dashboard",children:[(0,$n.jsx)("h1",{children:"Simulation Dashboard"}),(0,$n.jsxs)("div",{className:"dashboard-nav",children:[(0,$n.jsx)("button",{className:"overview"===g?"active":"",onClick:()=>y("overview"),children:"Overview"}),(0,$n.jsx)("button",{className:"control"===g?"active":"",onClick:()=>y("control"),children:"Control Panel"}),f&&(0,$n.jsx)("button",{className:"details"===g?"active":"",onClick:()=>y("details"),children:"Simulation Details"})]}),"overview"===g&&(0,$n.jsxs)("div",{className:"dashboard-overview",children:[(0,$n.jsxs)("div",{className:"metrics-panel",children:[(0,$n.jsx)("h2",{children:"Game Metrics"}),r&&(0,$n.jsxs)("div",{className:"metrics-grid",children:[(0,$n.jsxs)("div",{className:"metric-card",children:[(0,$n.jsx)("h3",{children:"Total Games"}),(0,$n.jsx)("div",{className:"metric-value",children:r.gameCount||0})]}),(0,$n.jsxs)("div",{className:"metric-card",children:[(0,$n.jsx)("h3",{children:"Average Rounds"}),(0,$n.jsx)("div",{className:"metric-value",children:r.roundCount?r.roundCount.toFixed(1):"N/A"})]}),(0,$n.jsxs)("div",{className:"metric-card",children:[(0,$n.jsx)("h3",{children:"Total Events"}),(0,$n.jsx)("div",{className:"metric-value",children:(null===(e=r.eventCounts)||void 0===e?void 0:e.total)||0})]}),(0,$n.jsxs)("div",{className:"metric-card",children:[(0,$n.jsx)("h3",{children:"Current Status"}),(0,$n.jsxs)("div",{className:"metric-value status-indicator",children:[(null===a||void 0===a?void 0:a.status)||"Unknown","running"===(null===a||void 0===a?void 0:a.status)&&(0,$n.jsx)("span",{className:"pulse-dot"})]})]})]})]}),r&&r.playerStats&&(0,$n.jsxs)("div",{className:"player-stats-panel",children:[(0,$n.jsx)("h2",{children:"Player Statistics"}),(0,$n.jsxs)("table",{className:"player-stats-table",children:[(0,$n.jsx)("thead",{children:(0,$n.jsxs)("tr",{children:[(0,$n.jsx)("th",{children:"Player"}),(0,$n.jsx)("th",{children:"Wins"}),(0,$n.jsx)("th",{children:"Average Score"}),(0,$n.jsx)("th",{children:"Outer Score"}),(0,$n.jsx)("th",{children:"Inner Score"}),(0,$n.jsx)("th",{children:"OM Tokens"}),(0,$n.jsx)("th",{children:"Energy Cubes"}),(0,$n.jsx)("th",{children:"Journey Cards"}),(0,$n.jsx)("th",{children:"Total Hops"})]})}),(0,$n.jsx)("tbody",{children:r.playerStats.map(((e,t)=>{var n,r,o,i,a,l,s;return(0,$n.jsxs)("tr",{children:[(0,$n.jsx)("td",{children:e.name}),(0,$n.jsx)("td",{children:e.wins||0}),(0,$n.jsx)("td",{children:(null===(n=e.avgScore)||void 0===n?void 0:n.toFixed(1))||0}),(0,$n.jsx)("td",{children:(null===(r=e.avgOuterScore)||void 0===r?void 0:r.toFixed(1))||0}),(0,$n.jsx)("td",{children:(null===(o=e.avgInnerScore)||void 0===o?void 0:o.toFixed(1))||0}),(0,$n.jsx)("td",{children:(null===(i=e.avgOmTokens)||void 0===i?void 0:i.toFixed(1))||0}),(0,$n.jsx)("td",{children:(null===(a=e.avgEnergyCubes)||void 0===a?void 0:a.toFixed(1))||0}),(0,$n.jsx)("td",{children:(null===(l=e.avgJourneyCards)||void 0===l?void 0:l.toFixed(1))||0}),(0,$n.jsx)("td",{children:(null===(s=e.avgHops)||void 0===s?void 0:s.toFixed(1))||0})]},t)}))})]})]}),r&&r.eventCounts&&(0,$n.jsxs)("div",{className:"event-stats-panel",children:[(0,$n.jsx)("h2",{children:"Event Distribution"}),(0,$n.jsx)("div",{className:"event-bars",children:Object.entries(r.eventCounts).filter((e=>{let[t]=e;return"total"!==t})).sort(((e,t)=>t[1]-e[1])).map((e=>{let[t,n]=e;return(0,$n.jsxs)("div",{className:"event-bar-container",children:[(0,$n.jsx)("div",{className:"event-label",children:t}),(0,$n.jsxs)("div",{className:"event-bar-wrapper",children:[(0,$n.jsx)("div",{className:"event-bar",style:{width:n/r.eventCounts.total*100+"%"}}),(0,$n.jsx)("span",{className:"event-count",children:n})]})]},t)}))})]})]}),"control"===g&&(0,$n.jsxs)("div",{className:"dashboard-control",children:[(0,$n.jsx)("h2",{children:"Simulation Control Panel"}),(0,$n.jsxs)("div",{className:"simulation-config",children:[(0,$n.jsxs)("div",{className:"form-group",children:[(0,$n.jsx)("label",{children:"Simulation Speed:"}),(0,$n.jsx)("input",{type:"range",min:"1",max:"50",value:v,onChange:e=>x(Number(e.target.value)),disabled:C}),(0,$n.jsxs)("span",{children:[v,"x"]})]}),(0,$n.jsxs)("div",{className:"form-group",children:[(0,$n.jsx)("label",{children:"Number of Games:"}),(0,$n.jsx)("input",{type:"number",min:"1",max:"100",value:E,onChange:e=>_(Number(e.target.value)),disabled:C})]}),(0,$n.jsxs)("div",{className:"form-group",children:[(0,$n.jsx)("label",{children:"Bot Configuration:"}),(0,$n.jsxs)("div",{className:"bot-config",children:[k.map(((e,t)=>(0,$n.jsxs)("div",{className:"bot-strategy-selector",children:[(0,$n.jsx)("select",{value:e,onChange:e=>((e,t)=>{const n=[...k];n[e]=t,S(n)})(t,e.target.value),disabled:C,children:b.map((e=>(0,$n.jsx)("option",{value:e,children:e},e)))}),k.length>2&&(0,$n.jsx)("button",{className:"remove-bot",onClick:()=>(e=>{if(k.length>2){const t=[...k];t.splice(e,1),S(t)}})(t),disabled:C,children:"\u2715"})]},t))),k.length<6&&(0,$n.jsx)("button",{className:"add-bot",onClick:()=>{k.length<6&&S([...k,"random"])},disabled:C,children:"+ Add Bot"})]})]})]}),(0,$n.jsx)("div",{className:"simulation-controls",children:C?(0,$n.jsx)("button",{className:"stop-simulation",onClick:async()=>{try{await fetch("/api/simulation/stop",{method:"POST"}),j(!1),T()}catch(lr){console.error("Failed to stop simulation:",lr),d("Failed to stop simulation")}},children:"Stop Simulation"}):(0,$n.jsx)("button",{className:"start-simulation",onClick:async()=>{try{for(const e of k)await fetch("/api/simulation/bot",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:`Bot-${Math.floor(1e3*Math.random())}`,strategyName:e})});await fetch("/api/simulation/configure",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({autoRestart:E>1,maxGames:E,simulationSpeed:v,saveResults:!0})}),await fetch("/api/simulation/start",{method:"POST",headers:{"Content-Type":"application/json"}}),j(!0)}catch(lr){console.error("Failed to start simulation:",lr),d("Failed to start simulation")}},children:"Start Simulation"})}),a&&"running"===a.status&&a.stats&&a.stats.currentGame&&(0,$n.jsxs)("div",{className:"current-simulation",children:[(0,$n.jsx)("h3",{children:"Current Simulation Progress"}),(0,$n.jsxs)("div",{className:"progress-info",children:[(0,$n.jsxs)("div",{children:["Game: ",a.stats.currentGame.gameNumber]}),(0,$n.jsxs)("div",{children:["Round: ",a.stats.currentGame.roundCount||0]}),(0,$n.jsxs)("div",{children:["Turn: ",a.stats.currentGame.turnCount||0]}),(0,$n.jsxs)("div",{children:["Speed: ",a.stats.simulationSpeed,"x"]}),(0,$n.jsxs)("div",{children:["Elapsed: ",Math.floor((Date.now()-a.stats.currentGame.startTime)/1e3),"s"]})]})]})]}),"details"===g&&f&&(0,$n.jsxs)("div",{className:"dashboard-details",children:[(0,$n.jsx)("h2",{children:"Simulation Details"}),(0,$n.jsxs)("div",{className:"simulation-header",children:[(0,$n.jsxs)("h3",{children:["Game #",f.gameNumber]}),(0,$n.jsxs)("div",{className:"simulation-meta",children:[(0,$n.jsxs)("div",{children:["Duration: ",Math.floor(f.duration/1e3),"s"]}),(0,$n.jsxs)("div",{children:["Rounds: ",f.rounds]}),(0,$n.jsxs)("div",{children:["Turns: ",f.turns]})]})]}),(0,$n.jsxs)("div",{className:"winner-panel",children:[(0,$n.jsx)("h3",{children:"Game Result"}),f.winner?(0,$n.jsxs)("div",{className:"winner-card",children:[(0,$n.jsx)("div",{className:"winner-name",children:f.winner.name}),(0,$n.jsxs)("div",{className:"winner-score",children:["Score: ",f.winner.totalScore]}),(0,$n.jsxs)("div",{className:"winner-om",children:["OM Tokens: ",f.winner.omTotal]}),(0,$n.jsx)("div",{className:"winner-badge",children:"Winner"})]}):(0,$n.jsx)("div",{className:"no-winner",children:"No winner determined"})]}),(0,$n.jsxs)("div",{className:"players-panel",children:[(0,$n.jsx)("h3",{children:"All Players"}),(0,$n.jsx)("div",{className:"players-grid",children:f.players.map(((e,t)=>(0,$n.jsxs)("div",{className:"player-card "+(f.winner&&e.id===f.winner.id?"winner":""),children:[(0,$n.jsx)("div",{className:"player-name",children:e.name}),(0,$n.jsxs)("div",{className:"player-score-breakdown",children:[(0,$n.jsxs)("div",{children:["Outer: ",e.outerScore]}),(0,$n.jsxs)("div",{children:["Inner: ",e.innerScore]}),(0,$n.jsxs)("div",{children:["Total: ",e.totalScore]})]}),(0,$n.jsxs)("div",{className:"player-om",children:["OM: ",e.omTotal]})]},t)))})]}),(0,$n.jsxs)("div",{className:"events-panel",children:[(0,$n.jsx)("h3",{children:"Event Log"}),(0,$n.jsxs)("div",{className:"event-filters",children:[(0,$n.jsx)("button",{onClick:()=>P(f.gameNumber,50),children:"Last 50"}),(0,$n.jsx)("button",{onClick:()=>P(f.gameNumber,100),children:"Last 100"}),(0,$n.jsx)("button",{onClick:()=>P(f.gameNumber,1e3),children:"All Events"})]}),(0,$n.jsx)("div",{className:"events-log",children:h.length>0?(0,$n.jsxs)("table",{className:"events-table",children:[(0,$n.jsx)("thead",{children:(0,$n.jsxs)("tr",{children:[(0,$n.jsx)("th",{children:"Time"}),(0,$n.jsx)("th",{children:"Type"}),(0,$n.jsx)("th",{children:"Player"}),(0,$n.jsx)("th",{children:"Details"})]})}),(0,$n.jsx)("tbody",{children:h.map(((e,t)=>(0,$n.jsxs)("tr",{className:`event-type-${e.type}`,children:[(0,$n.jsx)("td",{children:new Date(e.timestamp).toLocaleTimeString()}),(0,$n.jsx)("td",{children:e.type}),(0,$n.jsx)("td",{children:e.player?e.player.name:"System"}),(0,$n.jsx)("td",{children:e.details&&Object.entries(e.details).filter((e=>{let[t]=e;return"id"!==t&&"timestamp"!==t})).map((e=>{let[t,n]=e;return(0,$n.jsxs)("div",{className:"event-detail",children:[(0,$n.jsxs)("span",{className:"detail-key",children:[t,":"]}),(0,$n.jsx)("span",{className:"detail-value",children:"object"===typeof n?JSON.stringify(n):n.toString()})]},t)}))})]},t)))})]}):(0,$n.jsx)("div",{className:"no-events",children:"No events available"})})]})]})]})};const ir=function(){return(0,$n.jsxs)("div",{className:"simulation-page",children:[(0,$n.jsxs)("header",{className:"simulation-header",children:[(0,$n.jsx)("h1",{children:"OM Journey Simulation Analysis"}),(0,$n.jsx)("div",{className:"header-controls",children:(0,$n.jsx)("button",{onClick:()=>window.location.href="/",children:"Return to Game"})})]}),(0,$n.jsx)("div",{className:"simulation-container",children:(0,$n.jsx)(or,{})}),(0,$n.jsx)("footer",{className:"simulation-footer",children:(0,$n.jsx)("p",{children:"OM Journey Bot Simulation Framework"})})]})};console.log("App starting up"),console.log("PUBLIC_URL:",""),console.log("Character image path example:","/assets/images/characters/merchant.jpg");i.createRoot(document.getElementById("root")).render((0,$n.jsx)(Ze,{children:(0,$n.jsxs)(xe,{children:[(0,$n.jsx)(ye,{path:"/",element:(0,$n.jsx)(rr,{})}),(0,$n.jsx)(ye,{path:"/simulation",element:(0,$n.jsx)(ir,{})}),(0,$n.jsx)(ye,{path:"*",element:(0,$n.jsx)(ge,{to:"/",replace:!0})})]})}))})();
//# sourceMappingURL=main.6b7d8a8d.js.map