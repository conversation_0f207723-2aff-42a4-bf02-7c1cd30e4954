import React, { useEffect, useState } from 'react';

function CardPickAnimation({ cards, cardPositions, targetPosition, onAnimationComplete }) {
  const [animationStarted, setAnimationStarted] = useState(false);

  useEffect(() => {
    // Start animation after a longer delay to ensure component is mounted and visible
    const timer = setTimeout(() => {
      setAnimationStarted(true);
    }, 300); // Increased delay to 300ms for better visibility

    // Clean up timer
    return () => clearTimeout(timer);
  }, []);

  // Call the completion callback when animation ends
  const handleAnimationEnd = () => {
    if (onAnimationComplete) {
      onAnimationComplete();
    }
  };

  if (!cards || cards.length === 0) return null;

  return (
    <div className="card-animation-container">
      {cards.map((card, index) => {
        // Validate card data to prevent errors
        if (!card || typeof card !== 'object' || !card.id) {
          console.error('Invalid card data:', card);
          return null;
        }

        // Safely access card properties with fallbacks
        const value = card.value || 1;
        const vehicle = card.vehicle || 'camel';
        const vehicleImagePath = `/assets/images/vehicles/${value}/${vehicle}.png`;
        const delay = index * 360; // Increased stagger delay by 20% to 360ms for better visibility

        // Get the source position for this specific card
        const sourcePosition = (cardPositions && cardPositions[card.id]) ? cardPositions[card.id] : { x: 0, y: 0 };

        return (
          <div
            key={`anim-${card.id}`}
            className="animated-card"
            style={{
              position: 'fixed',
              left: sourcePosition.x,
              top: sourcePosition.y,
              width: '100px', // Increased size for better visibility
              height: '90px',
              backgroundColor: '#FFF8E1',
              borderRadius: '6px',
              border: '1px solid #1976d2',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              zIndex: 1000,
              transition: animationStarted ? 'all 1.8s cubic-bezier(0.2, 0.8, 0.2, 1.0)' : 'none', // Increased to 1.8s (20% slower)
              transitionDelay: `${delay}ms`,
              transform: animationStarted ? `translate(${targetPosition.x - sourcePosition.x}px, ${targetPosition.y - sourcePosition.y}px) scale(0.7)` : 'translate(0, 0) scale(1.2)', // Added initial scale up
              opacity: animationStarted ? 0 : 1,
              pointerEvents: 'none'
            }}
            onTransitionEnd={index === cards.length - 1 ? handleAnimationEnd : null}
          >
            <img
              src={vehicleImagePath}
              alt={`${card.vehicle} (${card.value})`}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain'
              }}
              onError={(e) => {
                // Fallback if image doesn't load
                e.target.style.display = 'none';
              }}
            />
          </div>
        );
      })}
    </div>
  );
}

export default CardPickAnimation;
