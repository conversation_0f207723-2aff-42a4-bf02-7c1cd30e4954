import React, { useState, useEffect } from 'react';
import './SimulationDashboard.css';

/**
 * SimulationDashboard component for visualizing simulation metrics and results
 */
const SimulationDashboard = () => {
  // State for simulation data
  const [simulations, setSimulations] = useState([]);
  const [metrics, setMetrics] = useState(null);
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSimulation, setSelectedSimulation] = useState(null);
  const [events, setEvents] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [simulationSpeed, setSimulationSpeed] = useState(10);
  const [strategies, setStrategies] = useState([]);
  const [selectedStrategies, setSelectedStrategies] = useState(['random', 'random']);
  const [runningSimulation, setRunningSimulation] = useState(false);
  const [gamesToRun, setGamesToRun] = useState(1);

  // Fetch initial data on component mount
  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchStatus, 2000);
    return () => clearInterval(interval);
  }, []);

  // Fetch all necessary data
  const fetchData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchMetrics(),
        fetchStatus(),
        fetchStrategies()
      ]);
      setLoading(false);
    } catch (err) {
      setError('Failed to load simulation data');
      setLoading(false);
    }
  };

  // Fetch simulation metrics
  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/metrics');
      const data = await response.json();
      setMetrics(data);
      return data;
    } catch (err) {
      console.error('Failed to fetch metrics:', err);
      setError('Failed to load metrics');
    }
  };

  // Fetch simulation status
  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/simulation/status');
      const data = await response.json();
      setStatus(data);
      setRunningSimulation(data.status === 'running');
      
      // Refresh metrics if simulation is not running
      if (data.status !== 'running' && runningSimulation) {
        fetchMetrics();
      }
      
      return data;
    } catch (err) {
      console.error('Failed to fetch status:', err);
    }
  };

  // Fetch available strategies
  const fetchStrategies = async () => {
    try {
      const response = await fetch('/api/simulation/strategies');
      const data = await response.json();
      setStrategies(data.strategies);
      return data;
    } catch (err) {
      console.error('Failed to fetch strategies:', err);
      setError('Failed to load strategies');
    }
  };

  // Fetch events for a specific simulation
  const fetchEvents = async (gameId, limit = 100) => {
    try {
      const response = await fetch(`/api/events?limit=${limit}`);
      const data = await response.json();
      setEvents(data.events);
      return data;
    } catch (err) {
      console.error('Failed to fetch events:', err);
      setError('Failed to load events');
    }
  };

  // Start a new simulation
  const startSimulation = async () => {
    try {
      // First, add bots with the selected strategies
      for (const strategy of selectedStrategies) {
        await fetch('/api/simulation/bot', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            name: `Bot-${Math.floor(Math.random() * 1000)}`,
            strategyName: strategy
          })
        });
      }

      // Configure the simulation
      await fetch('/api/simulation/configure', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          autoRestart: gamesToRun > 1,
          maxGames: gamesToRun,
          simulationSpeed: simulationSpeed,
          saveResults: true
        })
      });

      // Start the simulation
      await fetch('/api/simulation/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      setRunningSimulation(true);
    } catch (err) {
      console.error('Failed to start simulation:', err);
      setError('Failed to start simulation');
    }
  };

  // Stop the current simulation
  const stopSimulation = async () => {
    try {
      await fetch('/api/simulation/stop', {
        method: 'POST'
      });
      setRunningSimulation(false);
      fetchMetrics();
    } catch (err) {
      console.error('Failed to stop simulation:', err);
      setError('Failed to stop simulation');
    }
  };

  // View details for a specific simulation
  const viewSimulationDetails = (simulation) => {
    setSelectedSimulation(simulation);
    fetchEvents(simulation.gameNumber);
    setActiveTab('details');
  };

  // Handle strategy selection change
  const handleStrategyChange = (index, value) => {
    const newStrategies = [...selectedStrategies];
    newStrategies[index] = value;
    setSelectedStrategies(newStrategies);
  };

  // Add a bot strategy
  const addBotStrategy = () => {
    if (selectedStrategies.length < 6) {
      setSelectedStrategies([...selectedStrategies, 'random']);
    }
  };

  // Remove a bot strategy
  const removeBotStrategy = (index) => {
    if (selectedStrategies.length > 2) {
      const newStrategies = [...selectedStrategies];
      newStrategies.splice(index, 1);
      setSelectedStrategies(newStrategies);
    }
  };

  // Render loading state
  if (loading) {
    return <div className="simulation-dashboard loading">Loading simulation data...</div>;
  }

  // Render error state
  if (error) {
    return <div className="simulation-dashboard error">
      <h2>Error</h2>
      <p>{error}</p>
      <button onClick={fetchData}>Retry</button>
    </div>;
  }

  return (
    <div className="simulation-dashboard">
      <h1>Simulation Dashboard</h1>
      
      {/* Navigation tabs */}
      <div className="dashboard-nav">
        <button 
          className={activeTab === 'overview' ? 'active' : ''} 
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={activeTab === 'control' ? 'active' : ''} 
          onClick={() => setActiveTab('control')}
        >
          Control Panel
        </button>
        {selectedSimulation && (
          <button 
            className={activeTab === 'details' ? 'active' : ''} 
            onClick={() => setActiveTab('details')}
          >
            Simulation Details
          </button>
        )}
      </div>
      
      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="dashboard-overview">
          <div className="metrics-panel">
            <h2>Game Metrics</h2>
            {metrics && (
              <div className="metrics-grid">
                <div className="metric-card">
                  <h3>Total Games</h3>
                  <div className="metric-value">{metrics.gameCount || 0}</div>
                </div>
                <div className="metric-card">
                  <h3>Average Rounds</h3>
                  <div className="metric-value">{metrics.roundCount ? (metrics.roundCount).toFixed(1) : 'N/A'}</div>
                </div>
                <div className="metric-card">
                  <h3>Total Events</h3>
                  <div className="metric-value">{metrics.eventCounts?.total || 0}</div>
                </div>
                <div className="metric-card">
                  <h3>Current Status</h3>
                  <div className="metric-value status-indicator">
                    {status?.status || 'Unknown'}
                    {status?.status === 'running' && (
                      <span className="pulse-dot"></span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {metrics && metrics.playerStats && (
            <div className="player-stats-panel">
              <h2>Player Statistics</h2>
              <table className="player-stats-table">
                <thead>
                  <tr>
                    <th>Player</th>
                    <th>Wins</th>
                    <th>Average Score</th>
                    <th>Outer Score</th>
                    <th>Inner Score</th>
                    <th>OM Tokens</th>
                    <th>Energy Cubes</th>
                    <th>Journey Cards</th>
                    <th>Total Hops</th>
                  </tr>
                </thead>
                <tbody>
                  {metrics.playerStats.map((player, index) => (
                    <tr key={index}>
                      <td>{player.name}</td>
                      <td>{player.wins || 0}</td>
                      <td>{player.avgScore?.toFixed(1) || 0}</td>
                      <td>{player.avgOuterScore?.toFixed(1) || 0}</td>
                      <td>{player.avgInnerScore?.toFixed(1) || 0}</td>
                      <td>{player.avgOmTokens?.toFixed(1) || 0}</td>
                      <td>{player.avgEnergyCubes?.toFixed(1) || 0}</td>
                      <td>{player.avgJourneyCards?.toFixed(1) || 0}</td>
                      <td>{player.avgHops?.toFixed(1) || 0}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {metrics && metrics.eventCounts && (
            <div className="event-stats-panel">
              <h2>Event Distribution</h2>
              <div className="event-bars">
                {Object.entries(metrics.eventCounts)
                  .filter(([key]) => key !== 'total')
                  .sort((a, b) => b[1] - a[1])
                  .map(([eventType, count]) => (
                    <div className="event-bar-container" key={eventType}>
                      <div className="event-label">{eventType}</div>
                      <div className="event-bar-wrapper">
                        <div 
                          className="event-bar" 
                          style={{ 
                            width: `${(count / metrics.eventCounts.total) * 100}%` 
                          }}
                        ></div>
                        <span className="event-count">{count}</span>
                      </div>
                    </div>
                  ))
                }
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Control Panel Tab */}
      {activeTab === 'control' && (
        <div className="dashboard-control">
          <h2>Simulation Control Panel</h2>
          
          <div className="simulation-config">
            <div className="form-group">
              <label>Simulation Speed:</label>
              <input 
                type="range" 
                min="1" 
                max="50" 
                value={simulationSpeed} 
                onChange={(e) => setSimulationSpeed(Number(e.target.value))} 
                disabled={runningSimulation}
              />
              <span>{simulationSpeed}x</span>
            </div>
            
            <div className="form-group">
              <label>Number of Games:</label>
              <input 
                type="number" 
                min="1" 
                max="100" 
                value={gamesToRun} 
                onChange={(e) => setGamesToRun(Number(e.target.value))} 
                disabled={runningSimulation}
              />
            </div>
            
            <div className="form-group">
              <label>Bot Configuration:</label>
              <div className="bot-config">
                {selectedStrategies.map((strategy, index) => (
                  <div key={index} className="bot-strategy-selector">
                    <select 
                      value={strategy} 
                      onChange={(e) => handleStrategyChange(index, e.target.value)}
                      disabled={runningSimulation}
                    >
                      {strategies.map(strat => (
                        <option key={strat} value={strat}>{strat}</option>
                      ))}
                    </select>
                    {selectedStrategies.length > 2 && (
                      <button 
                        className="remove-bot" 
                        onClick={() => removeBotStrategy(index)}
                        disabled={runningSimulation}
                      >
                        ✕
                      </button>
                    )}
                  </div>
                ))}
                {selectedStrategies.length < 6 && (
                  <button 
                    className="add-bot" 
                    onClick={addBotStrategy}
                    disabled={runningSimulation}
                  >
                    + Add Bot
                  </button>
                )}
              </div>
            </div>
          </div>
          
          <div className="simulation-controls">
            {!runningSimulation ? (
              <button 
                className="start-simulation" 
                onClick={startSimulation}
              >
                Start Simulation
              </button>
            ) : (
              <button 
                className="stop-simulation" 
                onClick={stopSimulation}
              >
                Stop Simulation
              </button>
            )}
          </div>
          
          {status && status.status === 'running' && status.stats && status.stats.currentGame && (
            <div className="current-simulation">
              <h3>Current Simulation Progress</h3>
              <div className="progress-info">
                <div>Game: {status.stats.currentGame.gameNumber}</div>
                <div>Round: {status.stats.currentGame.roundCount || 0}</div>
                <div>Turn: {status.stats.currentGame.turnCount || 0}</div>
                <div>Speed: {status.stats.simulationSpeed}x</div>
                <div>Elapsed: {Math.floor((Date.now() - status.stats.currentGame.startTime) / 1000)}s</div>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Simulation Details Tab */}
      {activeTab === 'details' && selectedSimulation && (
        <div className="dashboard-details">
          <h2>Simulation Details</h2>
          
          <div className="simulation-header">
            <h3>Game #{selectedSimulation.gameNumber}</h3>
            <div className="simulation-meta">
              <div>Duration: {Math.floor(selectedSimulation.duration / 1000)}s</div>
              <div>Rounds: {selectedSimulation.rounds}</div>
              <div>Turns: {selectedSimulation.turns}</div>
            </div>
          </div>
          
          <div className="winner-panel">
            <h3>Game Result</h3>
            {selectedSimulation.winner ? (
              <div className="winner-card">
                <div className="winner-name">{selectedSimulation.winner.name}</div>
                <div className="winner-score">Score: {selectedSimulation.winner.totalScore}</div>
                <div className="winner-om">OM Tokens: {selectedSimulation.winner.omTotal}</div>
                <div className="winner-badge">Winner</div>
              </div>
            ) : (
              <div className="no-winner">No winner determined</div>
            )}
          </div>
          
          <div className="players-panel">
            <h3>All Players</h3>
            <div className="players-grid">
              {selectedSimulation.players.map((player, index) => (
                <div 
                  key={index} 
                  className={`player-card ${selectedSimulation.winner && player.id === selectedSimulation.winner.id ? 'winner' : ''}`}
                >
                  <div className="player-name">{player.name}</div>
                  <div className="player-score-breakdown">
                    <div>Outer: {player.outerScore}</div>
                    <div>Inner: {player.innerScore}</div>
                    <div>Total: {player.totalScore}</div>
                  </div>
                  <div className="player-om">OM: {player.omTotal}</div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="events-panel">
            <h3>Event Log</h3>
            <div className="event-filters">
              <button onClick={() => fetchEvents(selectedSimulation.gameNumber, 50)}>Last 50</button>
              <button onClick={() => fetchEvents(selectedSimulation.gameNumber, 100)}>Last 100</button>
              <button onClick={() => fetchEvents(selectedSimulation.gameNumber, 1000)}>All Events</button>
            </div>
            <div className="events-log">
              {events.length > 0 ? (
                <table className="events-table">
                  <thead>
                    <tr>
                      <th>Time</th>
                      <th>Type</th>
                      <th>Player</th>
                      <th>Details</th>
                    </tr>
                  </thead>
                  <tbody>
                    {events.map((event, index) => (
                      <tr key={index} className={`event-type-${event.type}`}>
                        <td>{new Date(event.timestamp).toLocaleTimeString()}</td>
                        <td>{event.type}</td>
                        <td>{event.player ? event.player.name : 'System'}</td>
                        <td>
                          {event.details && Object.entries(event.details)
                            .filter(([key]) => key !== 'id' && key !== 'timestamp')
                            .map(([key, value]) => (
                              <div key={key} className="event-detail">
                                <span className="detail-key">{key}:</span>
                                <span className="detail-value">
                                  {typeof value === 'object' ? JSON.stringify(value) : value.toString()}
                                </span>
                              </div>
                            ))
                          }
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="no-events">No events available</div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimulationDashboard; 