# Om: The Journey

A more complete React + Node.js + Socket.IO example implementing the core rules of **Om: The Journey**.

## Project Structure

- `boardgame-io/` - Contains the boardgame.io implementation
  - `src/game/` - Core game logic and boardgame.io server
- `client/` - React client application
- `server/` - Custom game server with Socket.IO

## Features

- 2-3 players
- Movement by exact hops using Travel cards (+ optional +1 from Event cards)
- Collecting energy cubes and OM tokens on locations
- Claiming Journey cards by discarding required cubes and having enough OM tokens allocated to the correct track slot
- Score tracks for Outer and Inner journeys
- Final round triggered when a player’s (outer+inner) ≥ 100 **or** they have ≥7 OM tokens

## Quick Start

1. **Install server**:
   ```bash
   cd server
   npm install
   npm run dev
   ```

2. **Run boardgame.io server** (in a separate terminal):
   ```bash
   npm run server
   ```

3. **Run client** (in a separate terminal):
   ```bash
   npm run client
   ```

4. **Or run everything together** (server + boardgame.io + client):
   ```bash
   npm run dev