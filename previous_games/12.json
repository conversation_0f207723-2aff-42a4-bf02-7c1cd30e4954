{"description": "KT won by a landslide using Om <PERSON> in a subtle way", "players": [{"id": "L4jh7b8f_-cQmdmqAAAF", "name": "KT", "position": 55, "hand": [], "energyCubes": ["gnana"], "omTemp": [1, 1, 1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 2, 0], "collectedJourneys": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "outerScore": 15, "innerScore": 71, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "A85jWFBRk9fVxvtXAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 47, "hand": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "energyCubes": ["karma", "bhakti", "gnana"], "omTemp": [1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "outerScore": 34, "innerScore": 44, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}}], "started": true, "turnIndex": 1, "roundCount": 13, "travelDeck": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "eventDeck": [{"id": "E3", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}, {"id": "E6", "type": "wildCube"}, {"id": "E7", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}], "travelDiscard": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "faceUpEvent": [{"id": "E2", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}, {"id": "E8", "type": "wildCube"}, {"id": "E5", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}], "faceUpJourneyOuter": [{"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "locationCubes": {"3": "artha", "7": "artha", "9": "gnana", "10": "artha", "12": "karma", "13": "bhakti", "14": "artha", "16": "bhakti", "19": "artha", "20": "bhakti", "24": "artha", "25": "gnana", "27": "karma", "30": "karma", "31": "artha", "32": "karma", "33": "gnana", "34": "karma", "36": "artha", "37": "bhakti", "38": "artha", "39": "bhakti", "43": "gnana", "44": "karma", "45": "karma", "46": "bhakti", "48": "gnana", "undefined": "karma"}, "locationOm": {"58": true}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1744764476193, "data": {"eventId": "global-event-20", "eventName": "Scenic Cruise", "eventEffect": "scenic_cruise_reward"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 0, "turnIndex": 0, "timestamp": 1744764845334, "data": {"cardType": "travel", "card": {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 0, "turnIndex": 0, "timestamp": 1744764845334, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744764845334, "data": {"cardType": "travel", "card": {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744764845334, "data": {"cardType": "travel", "card": {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 0, "turnIndex": 0, "timestamp": 1744765014308, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 0, "turnIndex": 0, "timestamp": 1744765021117, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 0, "turnIndex": 0, "timestamp": 1744765034942, "data": {"vehicle": "boat", "effect": "scenic_cruise_reward", "outerPoints": 5}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 0, "turnIndex": 0, "timestamp": 1744765034943, "data": {"path": [62, 11, 10, 49], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 0, "turnIndex": 1, "timestamp": 1744765039037, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1744765052598, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1744765053788, "data": {"cardType": "travel", "drawnCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1744765068500, "data": {"path": [61, 4, 3, 56], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1744765078124, "data": {"eventId": "global-event-21", "eventName": "Heavy Haul", "eventEffect": "heavy_haul_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1744765078124, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 1, "turnIndex": 0, "timestamp": 1744765242398, "data": {"path": [49, 10, 11, 7, 6], "travelCards": ["T9", "T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 1, "turnIndex": 0, "timestamp": 1744765268034, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 1, "turnIndex": 0, "timestamp": 1744765271260, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 1, "turnIndex": 1, "timestamp": 1744765278862, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1744765298489, "data": {"path": [56, 3, 4], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "trade", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1744765304467, "data": {"cubesTraded": ["karma"], "cubeReceived": "bhakti", "count": 1}}, {"type": "collectJourney", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1744765309665, "data": {"journeyType": "inner", "journeyCardId": "JI3", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1744765314328, "data": {"eventId": "global-event-13", "eventName": "<PERSON><PERSON> of Valor", "eventEffect": "steed_of_valor_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1744765314328, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 2, "turnIndex": 0, "timestamp": 1744765351738, "data": {"path": [6, 61, 63, 21], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 2, "turnIndex": 0, "timestamp": 1744765357933, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 1}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 2, "turnIndex": 1, "timestamp": 1744765366613, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744765436969, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744765446817, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744765456492, "data": {"vehicle": "horse", "effect": "steed_of_valor_reward", "outerPoints": 5}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744765456492, "data": {"path": [4, 61, 63, 24, 23, 59], "travelCards": ["T9", "T2", "T1"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1744765462057, "data": {"eventId": "global-event-23", "eventName": "Merchant's Midas", "eventEffect": "merchants_midas_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1744765462058, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 3, "turnIndex": 0, "timestamp": 1744765485304, "data": {"path": [21, 20, 51], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 3, "turnIndex": 0, "timestamp": 1744765512876, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 3, "turnIndex": 0, "timestamp": 1744765519472, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 3, "turnIndex": 1, "timestamp": 1744765522957, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1744765543447, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1744765553630, "data": {"path": [59, 23], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1744765556200, "data": {"eventId": "global-event-11", "eventName": "Pedal Power", "eventEffect": "pedal_power_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1744765556200, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 4, "turnIndex": 0, "timestamp": 1744765590677, "data": {"path": [51, 20, 21, 28], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 4, "turnIndex": 0, "timestamp": 1744765611049, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 4, "turnIndex": 0, "timestamp": 1744765632291, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 4, "turnIndex": 1, "timestamp": 1744765637439, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1744765647055, "data": {"path": [23, 22], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1744765660412, "data": {"journeyType": "outer", "journeyCardId": "JO10", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1744765665279, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1744765665279, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 5, "turnIndex": 0, "timestamp": 1744765682892, "data": {"path": [28, 18, 15], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 5, "turnIndex": 0, "timestamp": 1744765687816, "data": {"journeyType": "inner", "journeyCardId": "JI8", "omRequirement": 1}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 5, "turnIndex": 1, "timestamp": 1744765692708, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1744765793434, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1744765809070, "data": {"path": [22, 21, 28, 18], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1744765812950, "data": {"eventId": "global-event-15", "eventName": "Biker Gang", "eventEffect": "biker_gang_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1744765812950, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 6, "turnIndex": 0, "timestamp": 1744765838384, "data": {"vehicle": "motorbike", "effect": "biker_gang_reward", "outerPoints": 5}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 6, "turnIndex": 0, "timestamp": 1744765838384, "data": {"path": [15, 5, 52], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 6, "turnIndex": 0, "timestamp": 1744765850674, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 6, "turnIndex": 1, "timestamp": 1744765858202, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "PICK_DECK_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1744765869214, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1744765870241, "data": {"cardType": "travel", "drawnCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickFromTop": true}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1744765948717, "data": {"path": [18, 53], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1744765951480, "data": {"eventId": "global-event-16", "eventName": "<PERSON><PERSON> Rhapsody", "eventEffect": "rickshaw_rhapsody_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1744765951480, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 7, "turnIndex": 0, "timestamp": 1744766050874, "data": {"path": [52, 5], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 7, "turnIndex": 0, "timestamp": 1744766066236, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 7, "turnIndex": 0, "timestamp": 1744766089072, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 7, "turnIndex": 1, "timestamp": 1744766091538, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744766174163, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744766181444, "data": {"vehicle": "rickshaw", "effect": "rickshaw_rhapsody_reward", "outerPoints": 5}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744766181444, "data": {"path": [53, 18, 28, 29], "travelCards": ["T7", "T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744766188428, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1744766193081, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1744766193081, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 8, "turnIndex": 0, "timestamp": 1744766243668, "data": {"path": [5, 15, 64, 61, 6, 7, 54], "travelCards": ["T10", "T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 8, "turnIndex": 0, "timestamp": 1744766278823, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 8, "turnIndex": 0, "timestamp": 1744766283770, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 8, "turnIndex": 1, "timestamp": 1744766286605, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1744766350769, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1744766364304, "data": {"path": [29, 64, 61, 4, 3, 2, 1], "travelCards": ["T5", "T8", "T2", "T4"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1744766373589, "data": {"eventId": "global-event-26", "eventName": "Engineer's Precision", "eventEffect": "engineers_precision_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1744766373589, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 9, "turnIndex": 0, "timestamp": 1744766401192, "data": {"path": [54, 7, 11], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 9, "turnIndex": 0, "timestamp": 1744766456838, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 9, "turnIndex": 1, "timestamp": 1744766472157, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "collectJourney", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1744766477733, "data": {"journeyType": "inner", "journeyCardId": "JI1", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1744766484090, "data": {"eventId": "global-event-29", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1744766484090, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 10, "turnIndex": 0, "timestamp": 1744766535544, "data": {"path": [11, 12, 50], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 10, "turnIndex": 0, "timestamp": 1744766577837, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 10, "turnIndex": 1, "timestamp": 1744766583871, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1744766589320, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1744766597674, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1744766608690, "data": {"path": [1, 2], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1744766619427, "data": {"eventId": "global-event-24", "eventName": "Professor's Insight", "eventEffect": "professors_insight_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1744766619427, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 11, "turnIndex": 0, "timestamp": 1744766670407, "data": {"path": [50, 12, 11, 62, 66, 42], "travelCards": ["T9", "T1", "T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 11, "turnIndex": 0, "timestamp": 1744766678708, "data": {"journeyType": "inner", "journeyCardId": "JI21", "omRequirement": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 11, "turnIndex": 0, "timestamp": 1744766762057, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 11, "turnIndex": 0, "timestamp": 1744766767913, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 11, "turnIndex": 1, "timestamp": 1744766771990, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1744766861200, "data": {"path": [2, 3, 47, 57], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1744766871459, "data": {"eventId": "global-event-32", "eventName": "Central Heart", "eventEffect": "central_heart"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1744766871459, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 12, "turnIndex": 0, "timestamp": 1744766924798, "data": {"path": [42, 43, 60], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 12, "turnIndex": 0, "timestamp": 1744766942638, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 12, "turnIndex": 1, "timestamp": 1744766991164, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1744767043983, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1744767055317, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1744767072722, "data": {"path": [57, 47], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1744767076988, "data": {"eventId": "global-event-17", "eventName": "Top Gear", "eventEffect": "top_gear_reward"}}, {"type": "endTurn", "playerId": "A85jWFBRk9fVxvtXAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1744767076988, "data": {"nextPlayerId": "L4jh7b8f_-cQmdmqAAAF", "newRound": true, "roundCount": 13, "turnCount": null}}, {"type": "travel_card_reward", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 13, "turnIndex": 0, "timestamp": 1744767090257, "data": {"vehicle": "car", "effect": "top_gear_reward", "outerPoints": 5}}, {"type": "move", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 13, "turnIndex": 0, "timestamp": 1744767090257, "data": {"path": [60, 43, 42, 66, 65, 31, 30, 55], "travelCards": ["T12", "T6", "T8"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "L4jh7b8f_-cQmdmqAAAF", "playerName": "KT", "roundCount": 13, "turnIndex": 1, "timestamp": 1744767103633, "data": {"nextPlayerId": "A85jWFBRk9fVxvtXAAAD", "newRound": false, "roundCount": 13, "turnCount": null}}], "characterDeck": [{"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}], "energyCubePile": {"artha": 3, "karma": 4, "gnana": 6, "bhakti": 6}, "currentGlobalEvent": {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, "globalEventDeck": [{"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}], "globalEventDiscard": [{"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}], "nameMode": false}