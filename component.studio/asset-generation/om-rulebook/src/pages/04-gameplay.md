# Gameplay

Each player's turn follows a structured sequence. The game continues until one player's combined Outer + Inner score exceeds 100 points, triggering the final round.

## Turn Sequence

<div class="example-turn">
    <div class="turn-header">Your Turn (in order)</div>
    <div class="turn-step">1. Character Trading (Optional)</div>
    <div class="turn-step">2. Choose ONE Main Action</div>
    <div class="turn-step">3. Travelling (Optional)</div>
    <div class="turn-step">4. Collect Om Tokens (if applicable)</div>
    <div class="turn-step">5. Acquire Journey Card (if Action B chosen)</div>
    <div class="turn-step">6. End-of-Turn Cleanup</div>
</div>

## 1. Character Trading (Optional)

<div class="sidebar-left">
    <div class="callout-box">
        <h4>Character Abilities</h4>
        <p><strong>Engineer</strong> <span class="icon energy-cube karma"></span><br/>Trade any cube → 1 Karma</p>
        <p><strong>Professor</strong> <span class="icon energy-cube gnana"></span><br/>Trade any cube → 1 Gnana</p>
        <p><strong>Merchant</strong> <span class="icon energy-cube artha"></span><br/>Trade any cube → 1 Artha</p>
        <p><strong>Pilgrim</strong> <span class="icon energy-cube bhakti"></span><br/>Trade any cube → 1 Bhakti</p>
    </div>
</div>

<div class="main-content with-sidebar-left">

**Once per turn**, you may use your Character's ability to exchange Energy Cubes. Give any cube from your supply to the central pile and take 1 cube of your character's type.

**Important**: You cannot trade a cube of the same type you receive (e.g., Merchant cannot trade Artha for Artha).

This ability helps you obtain the specific cube types needed for Journey Cards while managing your cube collection efficiently.

</div>

## 2. Main Actions (Choose ONE)

You must choose exactly one of these actions each turn:

### Action A: Acquire Travel Cards

<div class="callout-box">
**Take any 2 Travel Cards** from the market and/or deck in any combination:
- Draw from the face-down deck
- Take from the market (then refill empty market slots)
- Combine both options

**No cost required** - Travel Cards are free to acquire.
</div>

### Action B: Acquire 1 Journey Card

<div class="callout-box important">
**Choose and pay for 1 Journey Card** from either market:
- **Pay the Energy Cube cost** (return cubes to central supply)
- **Pay the Om Token cost** based on your mat slot (1-1-2-3)
- **Place the card** in the next available slot of the correct track
- **Advance your score marker** by the card's point value
</div>

<div class="board-diagram">
    <img src="../assets/images/journey-card-examples.jpg" alt="Journey Card Examples" class="game-image" />
    <p class="text-center"><em>Examples of Outer and Inner Journey Cards with costs and rewards</em></p>
</div>

## 3. Travelling (Optional)

<div class="sidebar-right">
    <div class="callout-box setup">
        <h4>Movement Rules</h4>
        <ul>
            <li>Each card = exact number of hops</li>
            <li>Can't revisit locations during single card's movement</li>
            <li>Can chain multiple cards</li>
            <li>Movement is always optional</li>
        </ul>
    </div>
</div>

<div class="main-content with-sidebar-right">

Play any number of **Travel Cards** from your hand to move your traveller pawn across the board.

### Movement Mechanics
- **Exact Hops**: A card showing "3" requires exactly 3 steps along connected routes
- **No Backtracking**: Cannot revisit the same location during a single card's movement  
- **Chaining Cards**: Play multiple cards in sequence (e.g., "2" + "1" = 3 total hops)
- **Route Connections**: Follow the printed routes between locations

### Example Movement
<div class="example-turn">
    <div class="turn-header">Maya's Movement</div>
    <div class="turn-step">Maya plays a "2" Travel Card showing a Train</div>
    <div class="turn-step">She moves 2 hops: Airport → Delhi → Agra</div>
    <div class="turn-step">Then plays a "1" card showing Bus</div>
    <div class="turn-step">She moves 1 more hop: Agra → Jaipur</div>
    <div class="turn-step">Total movement: 3 hops using 2 cards</div>
</div>

</div>

## 4. Collecting Om Tokens

<div class="callout-box important">
**If you end movement on a Jyotirlinga location** with an Om Token <span class="icon om-token">ॐ</span> **and have space** (max 3 tokens), immediately:

1. **Take the Om Token** from the board
2. **Advance** your pawn on the Om Turn Track one space per token collected this turn
3. **Stack** on top if multiple pawns occupy the same space
</div>

**Note**: Om Tokens collected through Global Events also advance you on the Om Turn Track.

## 5. Acquiring Journey Cards (Action B only)

If you chose Action B this turn, now resolve the Journey Card acquisition:

<div class="example-turn">
    <div class="turn-header">Journey Card Acquisition Example</div>
    <div class="turn-step">Raj wants the "Taj Mahal" Outer Journey Card (Cost: 2 Karma + 1 Artha, Worth: 27 points)</div>
    <div class="turn-step">He pays 2 <span class="icon energy-cube karma"></span> + 1 <span class="icon energy-cube artha"></span> to the supply</div>
    <div class="turn-step">This goes in his 3rd Outer slot, so he pays 2 <span class="icon om-token">ॐ</span></div>
    <div class="turn-step">He places the card and advances his Outer score by 27 points</div>
    <div class="turn-step">The market slot is refilled from the Outer Journey deck</div>
</div>

## 6. End-of-Turn Steps

- **Refill** any empty market slots (Journey Cards only)
- **Pass play** clockwise to the next player

When all players have taken a turn, the **round ends**. Proceed to **Om Turn Track & Turn Order** (next section).

<div class="page-break"></div> 