// Test script to check if the server can be loaded
try {
  const OmJourney = require('./boardgame-io/src/game/Game');
  console.log('Successfully loaded Game.js');
  
  const { nodes, edges, airportNodes, jyotirlinga } = require('./boardgame-io/src/game/boardLayout');
  console.log('Successfully loaded boardLayout.js');
  
  const OmJourneyClient = require('./boardgame-io/src/game/OmJourneyClient');
  console.log('Successfully loaded OmJourneyClient.js');
  
  console.log('All modules loaded successfully!');
} catch (error) {
  console.error('Error loading modules:', error);
}
