# PDF Generation Fix Summary

## Problem
The original PDF generation was failing with "socket hang up" errors when using Puppeteer, preventing the creation of PDF versions of the Om: The Journey rulebook.

## Root Cause
The issue was primarily related to:
1. **Puppeteer compatibility issues** on macOS with the specific Node.js/system configuration
2. **Resource loading problems** with external CDN dependencies (Paged.js)
3. **File system access limitations** when using `file://` URLs
4. **Timeout issues** with complex page layouts

## Solutions Implemented

### 1. **Playwright Integration** (Primary Solution)
- Added Playwright as the primary PDF generation engine
- Playwright proved more stable and reliable than Puppeteer on this system
- Uses bundled Chromium for consistent behavior

### 2. **Local Resource Management**
- Downloaded Paged.js locally instead of relying on CDN
- Implemented inline CSS and JavaScript embedding
- Eliminated external resource dependencies

### 3. **Multiple Fallback Methods**
The build system now tries 4 different PDF generation approaches in order:
1. **Playwright-based generation** (most reliable)
2. **Enhanced Puppeteer with inline resources**
3. **HTTP server-based Puppeteer generation**
4. **Simple fallback without Paged.js**

### 4. **Improved Error Handling**
- Comprehensive retry logic for browser launches
- Graceful degradation when advanced features fail
- Detailed logging for troubleshooting

### 5. **Configuration Optimizations**
- Disabled timeouts where appropriate
- Added browser launch arguments for stability
- Improved page load detection

## Current Status
✅ **PDF generation is now working reliably**
- Primary method: Playwright with inline resources
- Output: `om-journey-rulebook.pdf` (564KB)
- Fallback methods available if needed

## Files Modified
- `scripts/build.js` - Added Playwright support and multiple generation methods
- `src/templates/base.hbs` - Updated to use local Paged.js
- `package.json` - Added Playwright dependency

## Usage
```bash
npm run build
```

The build process will automatically try the most reliable PDF generation method first, with fallbacks available if needed.

## Future Improvements
- Consider upgrading Puppeteer version for better compatibility
- Implement PDF optimization for smaller file sizes
- Add custom page layouts for better print formatting

# HTML-Based Solution

Rather than fixing the PDF generation issues, we've created a completely new HTML-based rulebook system.

## Why HTML Instead of PDF?

1. **Better Compatibility**: HTML works across all devices without needing special software
2. **Easier Maintenance**: HTML is simpler to update and modify
3. **Interactive Features**: Can add search, expandable sections, and other interactive elements
4. **Responsive Design**: Automatically adapts to different screen sizes
5. **No Rendering Issues**: Eliminates complex PDF rendering problems
6. **Simpler Development**: Removes complex dependencies like Puppeteer and PagedJS

## How to Use the HTML Rulebook

See the new `README-HTML.md` file for complete instructions, but in short:

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build production HTML
npm run build:html
```

The HTML rulebook output will be in the `output/rulebook.html` file.

## Migration Notes

For details on the migration process and things to be aware of, see `MIGRATION-NOTES.md`.

---

**Note**: The original PDF generation code is still available but should be considered deprecated. 