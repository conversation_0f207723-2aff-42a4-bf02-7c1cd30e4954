<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - Om: The Journey Rulebook</title>
    <link rel="stylesheet" href="om.css">
    <style>
        /* Fallback styles in case external resources don't load */
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
        }
        
        /* Responsive navigation */
        .rulebook-nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background-color: #205F80;
            color: white;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 1rem;
        }
        
        .nav-title {
            font-weight: bold;
            margin: 0;
        }
        
        .menu-button {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .nav-links li {
            margin-left: 1.5rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
        
        /* Content container */
        .rulebook-container {
            max-width: 1200px;
            margin: 80px auto 2rem;
            padding: 0 1rem;
        }
        
        /* Rulebook sections */
        .rulebook-section {
            margin-bottom: 3rem;
            padding-top: 1rem;
        }
        
        .section-title {
            color: #205F80;
            border-bottom: 2px solid #E87E1E;
            padding-bottom: 0.5rem;
        }
        
        /* Footer */
        .rulebook-footer {
            background-color: #F4EBDD;
            padding: 2rem 1rem;
            margin-top: 3rem;
            border-top: 4px solid #E87E1E;
        }
        
        .icon-legend {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .back-to-top {
            display: block;
            text-align: center;
            margin-top: 2rem;
            color: #205F80;
            text-decoration: none;
            font-weight: bold;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .menu-button {
                display: block;
            }
            
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: #205F80;
                flex-direction: column;
                padding: 1rem 0;
            }
            
            .nav-links.show {
                display: flex;
            }
            
            .nav-links li {
                margin: 0.5rem 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="rulebook-nav">
        <div class="nav-container">
            <h1 class="nav-title">Om: The Journey</h1>
            <button class="menu-button" id="menuToggle">☰</button>
            <ul class="nav-links" id="navLinks">
                <li><a href="#01-introduction">Introduction</a></li>
                <li><a href="#02-components">Components</a></li>
                <li><a href="#03-setup">Setup</a></li>
                <li><a href="#04-gameplay">Gameplay</a></li>
                <li><a href="#05-om-turn-track">Om Turn Track</a></li>
                <li><a href="#06-reference">Reference</a></li>
            </ul>
        </div>
    </nav>
    
    <!-- Main content -->
    <div class="rulebook-container">
        {{{content}}}
    </div>
    
    <!-- Footer with icon legend -->
    <footer class="rulebook-footer">
        <div class="icon-legend">
            <h3>Icon Reference</h3>
            <div class="legend-row">
                <div class="legend-item">
                    <div class="icon energy-cube artha"></div>
                    <span>Artha (Wealth)</span>
                </div>
                <div class="legend-item">
                    <div class="icon energy-cube karma"></div>
                    <span>Karma (Action)</span>
                </div>
                <div class="legend-item">
                    <div class="icon energy-cube gnana"></div>
                    <span>Gnana (Knowledge)</span>
                </div>
                <div class="legend-item">
                    <div class="icon energy-cube bhakti"></div>
                    <span>Bhakti (Devotion)</span>
                </div>
                <div class="legend-item">
                    <div class="icon om-token">ॐ</div>
                    <span>Om Token</span>
                </div>
            </div>
        </div>
        <a href="#" class="back-to-top">Back to Top</a>
    </footer>
    
    <!-- JavaScript for responsive navigation -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const navLinks = document.getElementById('navLinks');
            
            menuToggle.addEventListener('click', function() {
                navLinks.classList.toggle('show');
            });
            
            // Close menu when clicking a link
            const links = navLinks.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('click', function() {
                    navLinks.classList.remove('show');
                });
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.rulebook-nav')) {
                    navLinks.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html> 