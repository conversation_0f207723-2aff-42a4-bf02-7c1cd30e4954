# Bot Client

This directory contains a standalone bot client implementation that connects to the game server via websockets, just like human players.

## Overview

In this architecture, each bot runs as its own process and connects to the game server via websockets, exactly like a human player would through a browser. This approach:

1. Makes the system more consistent (all players connect via the same interface)
2. Simplifies the server code by removing special bot handling
3. Makes it easier to test and debug bots
4. Allows bots to run on separate machines if desired

## Running Bots

There are several ways to start bots:

### Starting a single bot

```bash
npm run start-bot
```

This will start a single bot with the default (defensive) strategy.

### Starting multiple bots

```bash
npm run start-bots
```

This will start 2 bots with the default strategy.

### Custom configurations

You can customize bot behavior with command line arguments:

```bash
# Start 3 bots with the 'defensive' strategy
node bot-client/start-bot.js --count=3 --strategy=defensive

# Start 2 bots with mixed strategies
node bot-client/start-bot.js --count=2 --strategy=random-mix

# Connect to a different server
node bot-client/start-bot.js --server=http://gameserver.example.com:4000
```

## Available Strategies

The following bot strategies are available:

- `defensive`: Conservative approach focused on building resources (default)
- `random`: Makes mostly random moves 
- `base`: Basic implementation with simple decision-making
- `random-mix`: Randomly assigns different strategies to each bot

## Playing Against Bots

1. Start the game server: `npm run server`
2. Start the client: `npm run client`
3. Start one or more bots: `npm run start-bot`
4. Join the game with your player name in the browser
5. Start the game when everyone has joined

## Architecture

The bot client architecture includes:

- `index.js`: Main BotClient class that connects to the server and handles gameplay
- `BotFactory.js`: Creates bot strategies for decision-making
- `start-bot.js`: Command-line interface for starting bots

Bots reuse the strategy implementations from the server-side code but run independently. 