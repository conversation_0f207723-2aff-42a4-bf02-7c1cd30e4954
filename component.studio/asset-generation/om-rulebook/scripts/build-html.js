const fs = require('fs');
const path = require('path');
const handlebars = require('handlebars');
const MarkdownIt = require('markdown-it');
const sass = require('sass');

const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true
});

// Paths
const srcDir = path.join(__dirname, '../src');
const outputDir = path.join(__dirname, '../output');
const pagesDir = path.join(srcDir, 'pages');
const templatesDir = path.join(srcDir, 'templates');
const stylesDir = path.join(srcDir, 'styles');
const assetsDir = path.join(__dirname, '../assets');
const outputAssetsDir = path.join(outputDir, 'assets');

// Ensure output directories exist
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

if (!fs.existsSync(outputAssetsDir)) {
    fs.mkdirSync(outputAssetsDir, { recursive: true });
}

async function compileStyles() {
    console.log('Compiling SCSS...');
    try {
        const result = sass.compile(path.join(stylesDir, 'om-web.scss'));
        fs.writeFileSync(path.join(outputDir, 'om.css'), result.css);
        console.log('✓ Styles compiled successfully');
    } catch (error) {
        console.error('Error compiling SCSS:', error);
        throw error;
    }
}

function loadTemplate(templateName) {
    try {
        const templatePath = path.join(templatesDir, `${templateName}.hbs`);
        if (!fs.existsSync(templatePath)) {
            console.error(`Template not found: ${templatePath}`);
            throw new Error(`Template ${templateName}.hbs not found`);
        }
        const templateContent = fs.readFileSync(templatePath, 'utf8');
        return handlebars.compile(templateContent);
    } catch (error) {
        console.error(`Error loading template ${templateName}:`, error);
        throw error;
    }
}

function loadMarkdownPages() {
    const pages = [];
    
    if (!fs.existsSync(pagesDir)) {
        console.error(`Pages directory not found: ${pagesDir}`);
        return pages;
    }
    
    const pageFiles = fs.readdirSync(pagesDir)
        .filter(file => file.endsWith('.md'))
        .sort();

    for (const file of pageFiles) {
        const filePath = path.join(pagesDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        const html = md.render(content);
        
        pages.push({
            filename: file,
            title: extractTitle(content),
            content: html
        });
    }

    return pages;
}

function extractTitle(markdown) {
    const match = markdown.match(/^#\s+(.+)$/m);
    return match ? match[1] : 'Om: The Journey';
}

async function generateHTML() {
    console.log('Generating HTML...');
    
    try {
        // Load templates
        console.log('Loading templates...');
        const baseTemplate = loadTemplate('base-html');
        const coverTemplate = loadTemplate('cover');
        const componentSpreadTemplate = loadTemplate('component-spread');
        
        // Load pages
        console.log('Loading content pages...');
        const pages = loadMarkdownPages();
        
        if (pages.length === 0) {
            console.warn('No markdown pages found!');
        }
        
        let fullContent = '';
        
        // Add cover page
        fullContent += coverTemplate({});
        
        // Add all content pages
        for (const page of pages) {
            fullContent += `<section id="${page.filename.replace('.md', '')}" class="rulebook-section">`;
            fullContent += `<h1 class="section-title">${page.title}</h1>`;
            fullContent += page.content;
            
            // Add component spread after components page
            if (page.filename.includes('02-components')) {
                fullContent += componentSpreadTemplate({});
            }
            
            fullContent += '</section>';
        }
        
        // Generate final HTML
        const finalHTML = baseTemplate({
            title: 'Om: The Journey Rulebook',
            content: fullContent
        });
        
        const htmlPath = path.join(outputDir, 'rulebook.html');
        fs.writeFileSync(htmlPath, finalHTML);
        console.log('✓ HTML generated successfully');
        
        return htmlPath;
    } catch (error) {
        console.error('Error generating HTML:', error);
        throw error;
    }
}

async function copyAssets() {
    console.log('Copying assets...');
    
    try {
        // Copy icons
        const iconsDir = path.join(assetsDir, 'icons');
        const outputIconsDir = path.join(outputAssetsDir, 'icons');
        
        if (fs.existsSync(iconsDir)) {
            if (!fs.existsSync(outputIconsDir)) {
                fs.mkdirSync(outputIconsDir, { recursive: true });
            }
            
            copyRecursive(iconsDir, outputIconsDir);
        } else {
            console.warn(`Icons directory not found: ${iconsDir}`);
        }
        
        // Copy images
        const imagesDir = path.join(assetsDir, 'images');
        const outputImagesDir = path.join(outputAssetsDir, 'images');
        
        if (fs.existsSync(imagesDir)) {
            if (!fs.existsSync(outputImagesDir)) {
                fs.mkdirSync(outputImagesDir, { recursive: true });
            }
            
            copyRecursive(imagesDir, outputImagesDir);
        } else {
            console.warn(`Images directory not found: ${imagesDir}`);
        }
        
        console.log('✓ Assets copied successfully');
    } catch (error) {
        console.error('Error copying assets:', error);
        throw error;
    }
}

function copyRecursive(src, dest) {
    if (!fs.existsSync(src)) {
        console.warn(`Source directory not found: ${src}`);
        return;
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
            if (!fs.existsSync(destPath)) {
                fs.mkdirSync(destPath, { recursive: true });
            }
            copyRecursive(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

async function main() {
    try {
        console.log('🚀 Building Om: The Journey HTML Rulebook...');
        
        // Step 1: Compile styles
        await compileStyles();
        
        // Step 2: Copy assets
        await copyAssets();
        
        // Step 3: Generate HTML
        await generateHTML();
        
        console.log('✅ HTML Rulebook generated successfully!');
        console.log(`📖 Open ${path.join(outputDir, 'rulebook.html')} to view the rulebook`);
    } catch (error) {
        console.error('❌ Build failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

// Export for use in other scripts
module.exports = { main }; 