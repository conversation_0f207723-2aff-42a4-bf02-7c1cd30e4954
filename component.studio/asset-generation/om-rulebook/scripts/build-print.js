const fs = require('fs');
const path = require('path');
const { chromium } = require('playwright');
const handlebars = require('handlebars');
const MarkdownIt = require('markdown-it');
const sass = require('sass');

const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true
});

// Paths
const srcDir = path.join(__dirname, '../src');
const outputDir = path.join(__dirname, '../output');
const pagesDir = path.join(srcDir, 'pages');
const templatesDir = path.join(srcDir, 'templates');
const stylesDir = path.join(srcDir, 'styles');
const assetsDir = path.join(__dirname, '../assets');
const clientPublicDir = path.join(__dirname, '../../../client/public/assets/images');

// Ensure output directory exists
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// Ensure output/assets directory exists
const outputAssetsDir = path.join(outputDir, 'assets');
if (!fs.existsSync(outputAssetsDir)) {
    fs.mkdirSync(outputAssetsDir, { recursive: true });
}

// Ensure output/assets/images directory exists
const outputImagesDir = path.join(outputAssetsDir, 'images');
if (!fs.existsSync(outputImagesDir)) {
    fs.mkdirSync(outputImagesDir, { recursive: true });
}

async function compileStyles() {
    console.log('Compiling SCSS...');
    try {
        const result = sass.compile(path.join(stylesDir, 'om-print.scss'));
        fs.writeFileSync(path.join(outputDir, 'om-print.css'), result.css);
        console.log('✓ Styles compiled successfully');
    } catch (error) {
        console.error('Error compiling SCSS:', error);
        throw error;
    }
}

function loadTemplate(templateName) {
    const templatePath = path.join(templatesDir, `${templateName}.hbs`);
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    return handlebars.compile(templateContent);
}

function loadMarkdownPages() {
    const pages = [];
    const pageFiles = fs.readdirSync(pagesDir)
        .filter(file => file.endsWith('.md'))
        .sort();

    for (const file of pageFiles) {
        const filePath = path.join(pagesDir, file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Fix Karma color reference if needed (already correct in styles)
        
        // Apply bold formatting to "When you collect Om Tokens"
        content = content.replace(/\*\*When you collect Om Tokens\*\*/, '**When you collect Om Tokens**');
        
        // Remove journey cards references
        content = content.replace(/Journey Cards/g, 'Destination Cards');
        content = content.replace(/Journey Card/g, 'Destination Card');
        
        const html = md.render(content);
        
        pages.push({
            filename: file,
            title: extractTitle(content),
            content: html
        });
    }

    return pages;
}

function extractTitle(markdown) {
    const match = markdown.match(/^#\s+(.+)$/m);
    return match ? match[1] : 'Om: The Journey';
}

async function copyOmCoverArt() {
    const sourcePaths = [
        path.join(clientPublicDir, 'om_the_journey_poster.jpg'),
        path.join(__dirname, '../../../client/public/assets/images/om_the_journey_poster.jpg')
    ];
    const destPath = path.join(outputImagesDir, 'cover-art.jpg');
    
    try {
        let copied = false;
        for (const sourcePath of sourcePaths) {
            if (fs.existsSync(sourcePath)) {
                fs.copyFileSync(sourcePath, destPath);
                console.log('✓ Cover art copied successfully from:', sourcePath);
                copied = true;
                break;
            }
        }
        
        if (!copied) {
            console.error('Cover art not found in any of the expected locations. Creating placeholder.');
            
            // Create a placeholder cover image
            const placeholderContent = `
                <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
                    <rect width="800" height="600" fill="#205F80"/>
                    <text x="400" y="300" font-family="Arial" font-size="48" fill="white" text-anchor="middle">Om: The Journey Cover Art</text>
                </svg>
            `;
            fs.writeFileSync(destPath, placeholderContent);
        }
    } catch (error) {
        console.error('Error copying cover art:', error);
    }
}

async function createImagePlaceholders() {
    const placeholders = [
        { name: 'board-overview.jpg', width: 800, height: 600, text: 'BOARD OVERVIEW' },
        { name: 'setup-board.jpg', width: 800, height: 600, text: 'SETUP BOARD' },
        { name: 'setup-complete.jpg', width: 800, height: 600, text: 'COMPLETE SETUP' },
        { name: 'om-track-example.jpg', width: 800, height: 300, text: 'OM TURN TRACK EXAMPLE' }
    ];
    
    for (const placeholder of placeholders) {
        const filePath = path.join(outputImagesDir, placeholder.name);
        const svgContent = `
            <svg width="${placeholder.width}" height="${placeholder.height}" xmlns="http://www.w3.org/2000/svg">
                <rect width="${placeholder.width}" height="${placeholder.height}" fill="#F4EBDD" stroke="#205F80" stroke-width="2"/>
                <text x="${placeholder.width/2}" y="${placeholder.height/2}" font-family="Arial" font-size="32" fill="#205F80" text-anchor="middle">${placeholder.text}</text>
            </svg>
        `;
        fs.writeFileSync(filePath, svgContent);
    }
    console.log('✓ Image placeholders created');
}

async function generatePrintHTML() {
    console.log('Generating print-focused HTML...');
    
    // Load templates
    const printTemplate = loadTemplate('base-print');
    const coverTemplate = loadTemplate('cover-print');
    const componentSpreadTemplate = loadTemplate('component-spread');
    
    // Load pages
    const pages = loadMarkdownPages();
    
    let fullContent = '';
    
    // Add cover page
    fullContent += coverTemplate({});
    fullContent += '<div class="page-break"></div>';
    
    // Add all content pages
    for (const page of pages) {
        fullContent += page.content;
        
        // Add component spread after components page
        if (page.filename.includes('02-components')) {
            fullContent += componentSpreadTemplate({});
        }
        
        if (page !== pages[pages.length - 1]) {
            fullContent += '<div class="page-break"></div>';
        }
    }
    
    // Generate final HTML
    const finalHTML = printTemplate({
        title: 'Om: The Journey Rulebook',
        content: fullContent
    });
    
    const htmlPath = path.join(outputDir, 'rulebook-print.html');
    fs.writeFileSync(htmlPath, finalHTML);
    console.log('✓ Print HTML generated successfully');
    
    return htmlPath;
}

async function generatePrintPDF(htmlPath) {
    console.log('Generating print-focused PDF...');
    
    let browser;
    try {
        browser = await chromium.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage'
            ]
        });
        
        const page = await browser.newPage();
        
        // Load HTML file
        await page.goto(`file://${htmlPath}`, {
            waitUntil: 'networkidle'
        });
        
        // Wait for any resources to load
        await page.waitForTimeout(2000);
        
        // Generate PDF with letter size dimensions
        const pdfPath = path.join(outputDir, 'om-journey-rulebook-print.pdf');
        await page.pdf({
            path: pdfPath,
            format: 'Letter',
            printBackground: true,
            margin: {
                top: '0.75in',
                bottom: '0.75in',
                left: '0.75in',
                right: '0.75in'
            }
        });
        
        console.log('✓ PDF generated successfully:', pdfPath);
    } catch (error) {
        console.error('Error generating PDF:', error);
        throw error;
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

async function main() {
    try {
        await copyOmCoverArt();
        await createImagePlaceholders();
        await compileStyles();
        const htmlPath = await generatePrintHTML();
        await generatePrintPDF(htmlPath);
        console.log('✓ Print rulebook build completed successfully!');
    } catch (error) {
        console.error('Build failed:', error);
        process.exit(1);
    }
}

main(); 