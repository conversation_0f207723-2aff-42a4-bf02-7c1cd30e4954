import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import App from './App';
import SimulationPage from './SimulationPage';
import './styles/main.css';

// Debug logs for asset paths
console.log('App starting up');
console.log('PUBLIC_URL:', process.env.PUBLIC_URL);
console.log('Character image path example:', `${process.env.PUBLIC_URL}/assets/images/characters/merchant.jpg`);

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <BrowserRouter>
    <Routes>
      <Route path="/" element={<App />} />
      <Route path="/simulation" element={<SimulationPage />} />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  </BrowserRouter>
);