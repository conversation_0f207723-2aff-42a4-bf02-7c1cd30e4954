{"name": "om-journey", "version": "1.0.0", "description": "Om: The Journey board game with boardgame.io framework", "main": "index.js", "scripts": {"start": "node server/index.js", "client": "cd client && npm start", "boardgame-server": "node boardgame-io/src/game/Server.js", "main-server": "node server/start_server.js", "server": "concurrently \"npm run boardgame-server\" \"npm run main-server\"", "dev": "concurrently \"npm run server\" \"npm run client\"", "start-bot": "node bot-client/start-bot.js", "start-bots": "node bot-client/start-bot.js --count=2", "render-cards": "node capture-journey-cards.js"}, "dependencies": {"boardgame.io": "^0.31.3", "canvas": "^3.1.0", "puppeteer": "^21.0.0", "serve-handler": "^6.1.5", "sharp": "^0.34.1"}, "devDependencies": {"concurrently": "^8.2.0"}}