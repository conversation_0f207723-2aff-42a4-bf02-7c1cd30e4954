# Om Turn Track & Turn Order

The **Om Turn Track** creates a dynamic turn order system that rewards strategic Om Token collection and adds tactical depth to your journey planning.

## How It Works

<div class="sidebar-right">
    <div class="callout-box">
        <h4>Key Points</h4>
        <ul>
            <li>Spaces 0-5 on the track</li>
            <li>Higher = earlier turn order</li>
            <li>Stack markers when tied</li>
            <li>Max advancement: space 5</li>
        </ul>
    </div>
</div>

<div class="main-content with-sidebar-right">

At the end of each round, turn order for the next round is determined by reading the Om Turn Track from **highest to lowest**:

1. **Highest pawn** (topmost on tallest stack) becomes **First Player**
2. **Second highest** becomes Second Player  
3. **Lowest** becomes Last Player

### Stacking Rules
When multiple pawns occupy the same space, they **stack vertically**. The pawn that **arrived most recently** goes on **top** of the stack.

</div>

## Advancing on the Track

<div class="callout-box important">
<strong>When you collect Om Tokens</strong> <span class="icon om-token">ॐ</span>, <strong>immediately advance</strong> your pawn on the Om Turn Track:

- <strong>1 Om Token</strong> = advance 1 space
- <strong>2 Om Tokens</strong> = advance 2 spaces  
- <strong>3 Om Tokens</strong> = advance 3 spaces

<strong>Maximum</strong>: Cannot advance beyond space 5, even with excess tokens.
</div>

### Sources of Om Tokens
- **Jyotirlinga Visits**: End movement on a Jyotirlinga location with a token
- **Global Events**: Some Event cards grant Om Tokens to players
- **Both Sources**: All Om Tokens collected in a turn advance you on the track

## Turn Order Examples

<div class="example-turn">
    <div class="turn-header">Round 1 → Round 2 Turn Order</div>
    <div class="turn-step"><strong>Start of Round 1:</strong> Maya (space 0), Raj (space 1), Anil (space 2)</div>
    <div class="turn-step"><strong>During Round 1:</strong> Maya collects 2 Om Tokens → advances to space 2</div>
    <div class="turn-step"><strong>Raj collects 1 Om Token</strong> → advances to space 2</div>
    <div class="turn-step"><strong>End of Round 1:</strong> Maya and Raj both on space 2, Anil on space 2</div>
    <div class="turn-step"><strong>Stacking:</strong> Raj arrived at space 2 most recently, so goes on top</div>
    <div class="turn-step"><strong>Round 2 Order:</strong> Raj (1st), Maya (2nd), Anil (3rd)</div>
</div>

<div class="board-diagram">
    <img src="../assets/images/om-track-example.jpg" alt="Om Turn Track Example" class="game-image" />
    <p class="text-center"><em>Example showing pawn positions and stacking on the Om Turn Track</em></p>
</div>

## Strategic Considerations

<div class="sidebar-left">
    <div class="callout-box setup">
        <h4>Tactical Tips</h4>
        <p><strong>Early Turns:</strong> Good for securing desired Journey Cards</p>
        <p><strong>Late Turns:</strong> Better reaction to Global Events and other players' moves</p>
        <p><strong>Denial:</strong> Sometimes leaving Om Tokens for others can be strategic</p>
    </div>
</div>

<div class="main-content with-sidebar-left">

### When to Prioritize Om Tokens
- **Powerful Global Events**: Going first lets you capitalize on beneficial events
- **Limited Journey Cards**: Early access to high-value or needed cards
- **Endgame Timing**: Controlling when the final round triggers

### When to Hold Back
- **Restrictive Events**: Sometimes going later is advantageous  
- **Resource Planning**: Other players' actions might create better opportunities
- **Psychological Factor**: Appearing less threatening can be valuable

The Om Turn Track transforms what could be a static player order into a dynamic, strategic element that's deeply integrated with the game's spiritual theme of collecting sacred tokens.

</div> 