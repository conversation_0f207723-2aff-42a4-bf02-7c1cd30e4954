#!/usr/bin/env python3
"""
Assemble 900 × 1500‑pixel tarot cards (JI*.png, JO*.png)
into 8.5 × 11‑inch sheets with 4 cards per sheet.

Run the script from the directory that holds the images.
Requires Pillow:  pip install pillow
"""

from pathlib import Path
import re
from PIL import Image

# ── CONFIG ────────────────────────────────────────────────────────────────
DPI            = 300               # output resolution
PAGE_W_PX      = int(8.5  * DPI)   # 2550
PAGE_H_PX      = int(11   * DPI)   # 3300
CARD_W_PX      = 900
CARD_H_PX      = 1500

# Centre the 2 × 2 grid on the page
MARGIN_X       = (PAGE_W_PX - 2 * CARD_W_PX) // 2   # 375 px  (≈1¼")
MARGIN_Y       = (PAGE_H_PX - 2 * CARD_H_PX) // 2   # 150 px  (≈½")

# Paste coordinates for the four slots
SLOTS = [
    (MARGI<PERSON>_X,                    MARGI<PERSON>_Y),                     # top‑left
    (MARGI<PERSON>_X + CARD_W_PX,        MARGI<PERSON>_Y),                     # top‑right
    (<PERSON><PERSON><PERSON><PERSON>_<PERSON>,                    MAR<PERSON>N_Y + CARD_H_PX),         # bottom‑left
    (MARGIN_X + CARD_W_PX,        MARGIN_Y + CARD_H_PX),         # bottom‑right
]
# ──────────────────────────────────────────────────────────────────────────

# Natural‑sort helper (JI2 before JI10)
def sort_key(p: Path):
    return [int(t) if t.isdigit() else t.lower()
            for t in re.split(r"(\d+)", p.stem)]

def main():
    # Gather files
    images = sorted(Path(".").glob("JI*.png")) + \
             sorted(Path(".").glob("JO*.png"))
    images.sort(key=sort_key)

    if not images:
        print("No JI*.png or JO*.png files found.")
        return

    # Process 4 at a time
    for page_idx in range(0, len(images), 4):
        sheet = Image.new("RGB", (PAGE_W_PX, PAGE_H_PX), "white")
        for slot_idx, img_path in enumerate(images[page_idx:page_idx + 4]):
            card = Image.open(img_path).convert("RGB")
            # Safety resize (keeps aspect if someone slipped a wrong size in)
            card = card.resize((CARD_W_PX, CARD_H_PX), Image.LANCZOS)
            sheet.paste(card, SLOTS[slot_idx])

        out_name = f"page_{page_idx//4 + 1:02}.png"
        sheet.save(out_name, dpi=(DPI, DPI))
        print(f"Wrote {out_name}")

    print("✅  All pages created. Ready to print!")

if __name__ == "__main__":
    main()