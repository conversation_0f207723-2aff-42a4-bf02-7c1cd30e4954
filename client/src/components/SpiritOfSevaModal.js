import React from 'react';

function SpiritOfSevaModal({ data, onClose }) {
  const { donations, eventName, eventEffect } = data;
  
  const imagePath = `${process.env.PUBLIC_URL}/assets/images/global_event_cards/39.png`;
  
  // Helper to render donation info
  const renderDonation = (track, trackName) => {
    const donation = donations[track];
    
    if (!donation.from || !donation.to) {
      return (
        <div style={{ margin: '8px 0', fontStyle: 'italic' }}>
          No donation on the {trackName} track (same player is leader and lowest)
        </div>
      );
    }
    
    const donor = donation.from;
    const recipient = donation.to;
    
    return (
      <div style={{ margin: '12px 0', padding: '8px', border: '1px solid #e0e0e0', borderRadius: '4px', backgroundColor: track === 'inner' ? '#e8eaf6' : '#fff3e0' }}>
        <div style={{ marginBottom: '8px', fontWeight: 'bold', color: track === 'inner' ? '#3f51b5' : '#f57c00' }}>
          {trackName} Track Donation
        </div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
          <div style={{ fontWeight: 'bold', flex: 1 }}>{donor.name}</div>
          <div style={{ margin: '0 10px' }}>→</div>
          <div style={{ fontWeight: 'bold', flex: 1 }}>{recipient.name}</div>
        </div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ flex: 1 }}>
            {donor.previousScore} → {donor.newScore} 
            <span style={{ color: '#e53935', marginLeft: '4px' }}>(-3)</span>
          </div>
          <div style={{ margin: '0 10px' }}></div>
          <div style={{ flex: 1 }}>
            {recipient.previousScore} → {recipient.newScore}
            <span style={{ color: '#43a047', marginLeft: '4px' }}>(+3)</span>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: '#ffffff',
        borderRadius: '8px',
        padding: '20px',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'auto',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}>
          <h2 style={{
            color: '#e65100',
            textAlign: 'center',
            margin: '0 0 16px 0'
          }}>
            {eventName}
          </h2>
          
          <div style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'flex-start',
            width: '100%',
            gap: '20px'
          }}>
            {/* Event Card Image */}
            <div style={{
              width: '180px',
              height: '250px',
              borderRadius: '8px',
              overflow: 'hidden',
              border: '1px solid #f57c00',
              flexShrink: 0
            }}>
              <img
                src={imagePath}
                alt={eventName}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
                onError={(e) => {
                  console.error(`Failed to load event image: ${imagePath}`);
                  e.target.style.display = 'none';
                  e.target.parentNode.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; text-align: center; padding: 10px; color: #d84315; font-style: italic;">Event Image<br/>Not Available</div>';
                }}
              />
            </div>
            
            {/* Donation Details */}
            <div style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column'
            }}>
              <div style={{
                marginBottom: '16px',
                fontStyle: 'italic',
                color: '#5d4037'
              }}>
                Leader on each track donates 3 points to player with lowest score on that track
              </div>
              
              {renderDonation('inner', 'Inner')}
              {renderDonation('outer', 'Outer')}
            </div>
          </div>
        </div>
        
        <div style={{
          marginTop: '20px',
          textAlign: 'center'
        }}>
          <button
            onClick={onClose}
            style={{
              padding: '8px 16px',
              backgroundColor: '#f57c00',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}

export default SpiritOfSevaModal; 