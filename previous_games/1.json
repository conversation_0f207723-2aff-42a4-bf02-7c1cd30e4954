{"players": [{"id": "35F0_l2tpIdsmb6UAAAB", "name": "<PERSON><PERSON>", "position": 49, "hand": [{"id": "E8", "type": "wildCube"}, {"id": "T9", "type": "travel", "value": 2}], "energyCubes": ["artha"], "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "outerScore": 47, "innerScore": 30, "collectedJourneys": [{"id": "JO11", "locationId": 25, "reward": {"outer": 20}}, {"id": "JI14", "locationId": 24, "reward": {"inner": 30}}, {"id": "JO12", "locationId": 27, "reward": {"outer": 27}}], "didMoveThisTurn": true, "didSelectionActionThisTurn": true}, {"id": "nKpQ7pDJvZN_zkoLAAAD", "name": "<PERSON><PERSON>", "position": 30, "hand": [{"id": "T6", "type": "travel", "value": 2}], "energyCubes": ["artha", "artha"], "omTemp": 0, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "outerScore": 51, "innerScore": 54, "collectedJourneys": [{"id": "JI5", "locationId": 8, "reward": {"inner": 30}}, {"id": "JI7", "locationId": 14, "reward": {"inner": 24}}, {"id": "JO22", "locationId": 43, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 30, "reward": {"outer": 24}}], "didMoveThisTurn": true, "didSelectionActionThisTurn": true}], "started": true, "turnIndex": 1, "roundCount": 14, "travelDeck": [{"id": "T4", "type": "travel", "value": 1}, {"id": "T7", "type": "travel", "value": 2}, {"id": "T2", "type": "travel", "value": 1}, {"id": "T13", "type": "travel", "value": 3}], "eventDeck": [{"id": "E1", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}], "travelDiscard": [{"id": "T3", "type": "travel", "value": 1}, {"id": "T14", "type": "travel", "value": 3}, {"id": "T15", "type": "travel", "value": 3}, {"id": "T8", "type": "travel", "value": 2}, {"id": "T11", "type": "travel", "value": 3}], "eventDiscard": [], "journeyInnerDiscard": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "journeyOuterDiscard": [{"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "faceUpTravel": [{"id": "T10", "type": "travel", "value": 2}, {"id": "T5", "type": "travel", "value": 1}, {"id": "T12", "type": "travel", "value": 3}, {"id": "T1", "type": "travel", "value": 1}], "faceUpEvent": [{"id": "E5", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "faceUpJourneyOuter": [{"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}], "locationCubes": {"1": null, "2": "gnana", "3": null, "4": null, "5": null, "6": "karma", "7": null, "8": null, "9": "karma", "10": "artha", "11": null, "12": null, "13": "bhakti", "14": null, "15": "bhakti", "16": "artha", "17": "artha", "18": null, "19": "karma", "20": null, "21": "karma", "22": null, "23": "artha", "24": null, "25": null, "26": null, "27": null, "28": "bhakti", "29": "karma", "30": null, "31": "gnana", "32": "gnana", "33": "karma", "34": "karma", "35": "gnana", "36": "gnana", "37": "gnana", "38": "karma", "39": "bhakti", "40": "gnana", "41": "artha", "42": "gnana", "43": null, "44": "bhakti", "45": "bhakti", "46": "gnana", "47": "bhakti", "48": null, "undefined": "karma"}, "locationOm": {"49": false, "50": false, "51": false, "52": false, "53": true, "54": false, "55": true, "56": false, "57": true, "58": true, "59": false, "60": false}, "finalRound": true, "finalRoundStarter": 1, "finalRoundEnd": 0, "gameEvents": [{"type": "DEAL_INITIAL_CARD", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1742844703600, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1742844703600, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 2}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1742844703600, "data": {"cardType": "travel", "card": {"id": "T11", "type": "travel", "value": 3}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1742844703600, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1742844716225, "data": {"cardType": "event", "pickedCards": [{"id": "E9", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1742844790534, "data": {"path": [64, 15, 5], "travelCardIds": ["T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1742845230208, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1742845282319, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1}, {"id": "T4", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1742845287845, "data": {"path": [61, 4], "travelCardIds": ["T3"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1742845290309, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1742845290310, "data": {"roundNumber": 1, "playerCount": 2, "playerStats": [{"id": "gRASI_4xtvkuw96tAAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 0}, {"id": "qxkAkmLpwDtvAXvZAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1742845357098, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1}, {"id": "T12", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1742845362139, "data": {"location": 52}}, {"type": "move", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1742845362139, "data": {"path": [5, 52], "travelCardIds": ["T2"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1742845363880, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1742845379427, "data": {"cardType": "event", "pickedCards": [{"id": "E6", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1742845390811, "data": {"path": [4, 3], "travelCardIds": ["T4"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1742845393504, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1742845393504, "data": {"roundNumber": 2, "playerCount": 2, "playerStats": [{"id": "gRASI_4xtvkuw96tAAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 0}, {"id": "qxkAkmLpwDtvAXvZAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1742845420602, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3}, {"id": "T1", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1742845433241, "data": {"path": [52, 5, 15, 18], "travelCardIds": ["T12"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1742845435296, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1742845447525, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 1}, {"id": "T10", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1742845451413, "data": {"location": 56}}, {"type": "move", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1742845451413, "data": {"path": [3, 56], "travelCardIds": ["T5"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1742845576760, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1742845576760, "data": {"roundNumber": 3, "playerCount": 2, "playerStats": [{"id": "gRASI_4xtvkuw96tAAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 0}, {"id": "qxkAkmLpwDtvAXvZAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 0}]}}, {"type": "END_TURN", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1742848090483, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1742848196543, "data": {"path": [56, 3, 2, 1], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "qxkAkmLpwDtvAXvZAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1742848239629, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1742848239629, "data": {"roundNumber": 4, "playerCount": 2, "playerStats": [{"id": "gRASI_4xtvkuw96tAAAC", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 0}, {"id": "qxkAkmLpwDtvAXvZAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 0}]}}, {"type": "move", "playerId": "gRASI_4xtvkuw96tAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1742849122042, "data": {"path": [18, 15, 64, 63, 25], "travelCardIds": ["T13", "T1"], "extraHopCount": 0}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1742849227622, "data": {"timestamp": 1742849227621, "loadedStateRoundCount": 4, "playerCount": 2}}, {"type": "COLLECT_JOURNEY", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1742849244502, "data": {"journeyCard": {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, "journeyType": "outer", "location": {"id": 25, "name": "Backwaters of Kerala", "region": "South", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["artha", "karma", "artha"], "energyCubesAfter": ["artha", "artha", "gnana", "bhakti"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 20, "innerScore": 0}}}}, {"type": "END_TURN", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1742849258817, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1742849282449, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3}, {"id": "T15", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1742849341591, "data": {"location": 54}}, {"type": "move", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1742849341592, "data": {"path": [1, 9, 54], "travelCardIds": ["T7"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1742849361577, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1742849361577, "data": {"roundNumber": 5, "playerCount": 2, "playerStats": [{"id": "FxjUGwuTfKuRPdGqAAAB", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 2, "energyCubesCount": 2, "collectedJourneysCount": 1}, {"id": "XmmwnfFABHETwiYtAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 4, "energyCubesCount": 3, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1742849605288, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2}, {"id": "T3", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1742849611985, "data": {"path": [25, 26, 27], "travelCardIds": ["T9"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1742849617749, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1742849663711, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1742849681594, "data": {"path": [54, 8, 7], "travelCardIds": ["T10"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1742849684770, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1742849684770, "data": {"roundNumber": 6, "playerCount": 2, "playerStats": [{"id": "FxjUGwuTfKuRPdGqAAAB", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 1}, {"id": "XmmwnfFABHETwiYtAAAD", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 4, "energyCubesCount": 4, "collectedJourneysCount": 0}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1742849707873, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2}, {"id": "T4", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1742849739762, "data": {"path": [27, 26, 25, 24], "travelCardIds": ["T8", "T3"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "FxjUGwuTfKuRPdGqAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1742849751685, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "XmmwnfFABHETwiYtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1742849766345, "data": {"path": [7, 8], "travelCardIds": ["T1"], "extraHopCount": 0}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 1, "timestamp": 1742849807635, "data": {"timestamp": 1742849807635, "loadedStateRoundCount": 6, "playerCount": 2}}, {"type": "COLLECT_JOURNEY", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1742849841247, "data": {"journeyCard": {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, "journeyType": "inner", "location": {"id": 8, "name": "Badrinath Temple", "region": "North", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["bhakti", "bhakti", "gnana", "artha", "gnana"], "energyCubesAfter": ["artha", "bhakti"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 0, "innerScore": 30}}}}, {"type": "END_TURN", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1742850360818, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1742850360818, "data": {"roundNumber": 7, "playerCount": 2, "playerStats": [{"id": "Usf1KCSPvHH3ZgNxAAAD", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 0, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 3, "energyCubesCount": 4, "collectedJourneysCount": 1}, {"id": "A27HDCXKzTIuvO8JAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1742850409058, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}, {"id": "T2", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1742850471528, "data": {"location": 59}}, {"type": "move", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1742850471528, "data": {"path": [24, 23, 59], "travelCardIds": ["T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1742850475026, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1742850568313, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 1}, {"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1742850588375, "data": {"path": [8, 7, 11, 12], "travelCardIds": ["T14"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1742850596921, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1742850596921, "data": {"roundNumber": 8, "playerCount": 2, "playerStats": [{"id": "Usf1KCSPvHH3ZgNxAAAD", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 4, "energyCubesCount": 4, "collectedJourneysCount": 1}, {"id": "A27HDCXKzTIuvO8JAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1742850614207, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1742850620064, "data": {"path": [59, 23, 22], "travelCardIds": ["T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1742850622434, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1742850685634, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1742850695636, "data": {"location": 50}}, {"type": "move", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1742850695636, "data": {"path": [12, 50], "travelCardIds": ["T5"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "A27HDCXKzTIuvO8JAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1742850697990, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1742850697991, "data": {"roundNumber": 9, "playerCount": 2, "playerStats": [{"id": "Usf1KCSPvHH3ZgNxAAAD", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "handSize": 4, "energyCubesCount": 5, "collectedJourneysCount": 1}, {"id": "A27HDCXKzTIuvO8JAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 30, "omTemp": 2, "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1742850719719, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Usf1KCSPvHH3ZgNxAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1742850725230, "data": {"path": [22, 23, 24], "travelCardIds": ["T10"], "extraHopCount": 0}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1742925771100, "data": {"timestamp": 1742925771100, "loadedStateRoundCount": 9, "playerCount": 2}}, {"type": "COLLECT_JOURNEY", "playerId": "4vyRc6ytv-dlvN2jAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1742925791313, "data": {"journeyCard": {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, "journeyType": "inner", "location": {"id": 24, "name": "Padmanabhaswamy Temple", "region": "South", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["artha", "artha", "gnana", "bhakti", "bhakti"], "energyCubesAfter": ["artha", "artha"], "wildCubesUsed": 1, "scoreChange": {"previousScore": {"outerScore": 20, "innerScore": 0}, "newScore": {"outerScore": 20, "innerScore": 30}}}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1742925869653, "data": {"timestamp": 1742925869653, "loadedStateRoundCount": 9, "playerCount": 2}}, {"type": "END_TURN", "playerId": "DuTATA4sdSaL0-cOAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1742925878458, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "jZOqyyoQr7KAWwJNAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1742925913858, "data": {"cardType": "event", "pickedCards": [{"id": "E7", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "jZOqyyoQr7KAWwJNAAAC", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1742925918777, "data": {"path": [50, 12, 11], "travelCardIds": ["T8"], "extraHopCount": 0}}, {"type": "move", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1742926512367, "data": {"path": [11, 12, 13, 14], "travelCardIds": ["T15"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1742926515522, "data": {"journeyCard": {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, "journeyType": "inner", "location": {"id": 14, "name": "Brahma Temple, Pushkar", "region": "West", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["artha", "bhakti", "gnana", "artha"], "energyCubesAfter": ["artha"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 30}, "newScore": {"outerScore": 0, "innerScore": 54}}}}, {"type": "END_TURN", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1742926707091, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1742926707092, "data": {"roundNumber": 10, "playerCount": 2, "playerStats": [{"id": "8TiSUREf6lsoIe9UAAAD", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 30, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 2}, {"id": "LARagmhbkG1Pn7aMAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8TiSUREf6lsoIe9UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1742926788638, "data": {"cardType": "event", "pickedCards": [{"id": "E8", "type": "wildCube"}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "8TiSUREf6lsoIe9UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1742926799027, "data": {"location": 51}}, {"type": "move", "playerId": "8TiSUREf6lsoIe9UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1742926799027, "data": {"path": [24, 63, 20, 51], "travelCardIds": ["T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "8TiSUREf6lsoIe9UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1742926801146, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1742927049438, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3}, {"id": "T7", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1742928676773, "data": {"path": [14, 13, 62, 66], "travelCardIds": ["T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1742928719640, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1742928719640, "data": {"roundNumber": 11, "playerCount": 2, "playerStats": [{"id": "8TiSUREf6lsoIe9UAAAD", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 2}, {"id": "LARagmhbkG1Pn7aMAAAB", "name": "<PERSON><PERSON>", "outerScore": 0, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "8TiSUREf6lsoIe9UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1742928806705, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 2}, {"id": "T3", "type": "travel", "value": 1}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "8TiSUREf6lsoIe9UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1742928810735, "data": {"path": [51, 20], "travelCardIds": ["T4"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "8TiSUREf6lsoIe9UAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1742928812521, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1742928846651, "data": {"path": [66, 42, 43], "travelCardIds": ["T7"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "LARagmhbkG1Pn7aMAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1742928857700, "data": {"journeyCard": {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, "journeyType": "outer", "location": {"id": 43, "name": "Living Root Bridge", "region": "Northeast", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["artha", "artha", "karma"], "energyCubesAfter": ["artha"], "wildCubesUsed": 1, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 54}, "newScore": {"outerScore": 27, "innerScore": 54}}}}, {"type": "END_TURN", "playerId": "LBZroVVNadyeHM-QAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1742929085641, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1742929085641, "data": {"roundNumber": 12, "playerCount": 2, "playerStats": [{"id": "vva133yxlyEfkAQcAAAB", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 4, "energyCubesCount": 3, "collectedJourneysCount": 2}, {"id": "LBZroVVNadyeHM-QAAAD", "name": "<PERSON><PERSON>", "outerScore": 27, "innerScore": 54, "omTemp": 0, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "vva133yxlyEfkAQcAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1742929127026, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "vva133yxlyEfkAQcAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1742929143275, "data": {"path": [20, 63, 25, 26], "travelCardIds": ["T13"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "vva133yxlyEfkAQcAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1742929145422, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "LBZroVVNadyeHM-QAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1742929218915, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1}, {"id": "T8", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "LBZroVVNadyeHM-QAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1742929222747, "data": {"location": 60}}, {"type": "move", "playerId": "LBZroVVNadyeHM-QAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1742929222747, "data": {"path": [43, 60], "travelCardIds": ["T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "LBZroVVNadyeHM-QAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1742929226103, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1742929226104, "data": {"roundNumber": 13, "playerCount": 2, "playerStats": [{"id": "vva133yxlyEfkAQcAAAB", "name": "<PERSON><PERSON>", "outerScore": 20, "innerScore": 30, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 4, "energyCubesCount": 4, "collectedJourneysCount": 2}, {"id": "LBZroVVNadyeHM-QAAAD", "name": "<PERSON><PERSON>", "outerScore": 27, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 3}]}}, {"type": "move", "playerId": "vva133yxlyEfkAQcAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1742929242577, "data": {"path": [26, 27], "travelCardIds": ["T2"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "vva133yxlyEfkAQcAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1742929246451, "data": {"journeyCard": {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, "journeyType": "outer", "location": {"id": 27, "name": "<PERSON><PERSON>", "region": "South", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["artha", "artha", "karma", "karma"], "energyCubesAfter": ["artha"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 20, "innerScore": 30}, "newScore": {"outerScore": 47, "innerScore": 30}}}}, {"type": "END_TURN", "playerId": "vva133yxlyEfkAQcAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1742929267618, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "xPh3wycv7GrBne_EAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1742930767820, "data": {"path": [60, 43, 42, 48], "travelCardIds": ["T12"], "extraHopCount": 0}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xPh3wycv7GrBne_EAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1742930776219, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3}, {"id": "T6", "type": "travel", "value": 2}], "pickedFromFaceUp": true}}, {"type": "END_TURN", "playerId": "xPh3wycv7GrBne_EAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1742930777893, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1742930777893, "data": {"roundNumber": 14, "playerCount": 2, "playerStats": [{"id": "EhIHnaEbnE2PBhduAAAD", "name": "<PERSON><PERSON>", "outerScore": 47, "innerScore": 30, "omTemp": 0, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 3}, {"id": "xPh3wycv7GrBne_EAAAB", "name": "<PERSON><PERSON>", "outerScore": 27, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 2, "collectedJourneysCount": 3}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EhIHnaEbnE2PBhduAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1742931057951, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3}, {"id": "T15", "type": "travel", "value": 3}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "EhIHnaEbnE2PBhduAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1742931074182, "data": {"location": 49}}, {"type": "move", "playerId": "EhIHnaEbnE2PBhduAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1742931074182, "data": {"path": [27, 16, 14, 13, 12, 11, 10, 49], "travelCardIds": ["T3", "T14", "T15"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "EhIHnaEbnE2PBhduAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1742931089900, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 1, "timestamp": 1742933315154, "data": {"timestamp": 1742933315154, "loadedStateRoundCount": 14, "playerCount": 2}}, {"type": "move", "playerId": "nKpQ7pDJvZN_zkoLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1742933483237, "data": {"path": [48, 42, 66, 65, 31, 30], "travelCardIds": ["T8", "T11"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "nKpQ7pDJvZN_zkoLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1742933486387, "data": {"journeyCard": {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, "journeyType": "outer", "location": {"id": 30, "name": "Charminar", "region": "East", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["artha", "artha", "artha"], "energyCubesAfter": ["artha", "artha"], "wildCubesUsed": 1, "scoreChange": {"previousScore": {"outerScore": 27, "innerScore": 54}, "newScore": {"outerScore": 51, "innerScore": 54}}}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "nKpQ7pDJvZN_zkoLAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1742933486388, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 105, "playerOmTotal": 4, "finalRoundStarter": 1, "finalRoundEnd": 0}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 1, "timestamp": 1742933489693, "data": {"winner": {"id": "nKpQ7pDJvZN_zkoLAAAD", "name": "<PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}, "totalRounds": 14, "players": [{"id": "35F0_l2tpIdsmb6UAAAB", "name": "<PERSON><PERSON>", "outerScore": 47, "innerScore": 30, "totalScore": 77, "omTotal": 4}, {"id": "nKpQ7pDJvZN_zkoLAAAD", "name": "<PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}]}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 1, "timestamp": 1742933489695, "data": {"winner": {"id": "nKpQ7pDJvZN_zkoLAAAD", "name": "<PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}, "totalRounds": 14, "players": [{"id": "35F0_l2tpIdsmb6UAAAB", "name": "<PERSON><PERSON>", "outerScore": 47, "innerScore": 30, "totalScore": 77, "omTotal": 4}, {"id": "nKpQ7pDJvZN_zkoLAAAD", "name": "<PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}]}}]}