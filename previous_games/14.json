{"description": "First game with 3 players. First player took 5 om tokens and had no more om tokens left to collect next journey card and couldnt reach convergence.", "players": [{"id": "RzBEyN5MbjCX48YCAAAB", "name": "First", "position": 43, "hand": [], "energyCubes": [], "omTemp": [], "omSlotsOuter": [1, 1, 2, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}], "outerScore": 71, "innerScore": 24, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "Bxn5wbNXbv6ISarcAAAD", "name": "MightyWayfarer", "position": 4, "hand": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "energyCubes": ["artha", "karma", "artha"], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}], "outerScore": 0, "innerScore": 51, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "7ysJv8u0gOYHAymPAAAF", "name": "SwiftPathfinder", "position": 32, "hand": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "energyCubes": ["bhakti", "gnana", "gnana", "karma"], "omTemp": [1, 1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "outerScore": 20, "innerScore": 30, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}], "started": true, "turnIndex": 0, "roundCount": 14, "travelDeck": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "eventDeck": [{"id": "E7", "type": "wildCube"}, {"id": "E6", "type": "wildCube"}, {"id": "E3", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "travelDiscard": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "faceUpEvent": [{"id": "E8", "type": "wildCube"}, {"id": "E4", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}], "faceUpJourneyOuter": [{"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "locationCubes": {"1": "karma", "9": "artha", "14": "artha", "19": "artha", "20": "karma", "21": "karma", "23": "artha", "24": "artha", "27": "bhakti", "31": "artha", "39": "karma", "45": "bhakti", "46": "bhakti", "47": "karma", "48": "gnana", "undefined": "karma"}, "locationOm": {}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897094076, "data": {"eventId": "global-event-5", "eventName": "Bountiful Bhandara", "eventEffect": "draw_2_cubes_bonus_5_outer"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897179835, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897179835, "data": {"cardType": "travel", "card": {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897179835, "data": {"cardType": "travel", "card": {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897179835, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897179835, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897179835, "data": {"cardType": "travel", "card": {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897224581, "data": {"path": [63, 21, 20, 51], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 0, "turnIndex": 0, "timestamp": 1744897236927, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 0, "turnIndex": 1, "timestamp": 1744897253658, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 0, "turnIndex": 1, "timestamp": 1744897255950, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 0, "turnIndex": 1, "timestamp": 1744897258955, "data": {"path": [62, 11, 10, 49], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 0, "turnIndex": 2, "timestamp": 1744897261958, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 0, "turnIndex": 2, "timestamp": 1744897263824, "data": {"path": [65, 31, 30, 55], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 0, "turnIndex": 2, "timestamp": 1744897266829, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1744897269830, "data": {"eventId": "global-event-20", "eventName": "Scenic Cruise", "eventEffect": "scenic_cruise_reward"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 1, "turnIndex": 0, "timestamp": 1744897269830, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 1, "turnIndex": 0, "timestamp": 1744897334803, "data": {"path": [51, 20, 21, 28, 29, 33], "travelCards": ["T11", "T7"], "extraHopCards": []}}, {"type": "trade", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 1, "turnIndex": 0, "timestamp": 1744897341185, "data": {"cubesTraded": ["gnana"], "cubeReceived": "karma", "count": 1}}, {"type": "collectJourney", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 1, "turnIndex": 0, "timestamp": 1744897344067, "data": {"journeyType": "outer", "journeyCardId": "JO17", "omRequirement": 1}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 1, "turnIndex": 1, "timestamp": 1744897347433, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 1, "turnIndex": 1, "timestamp": 1744897349832, "data": {"path": [49, 10], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 1, "turnIndex": 1, "timestamp": 1744897352836, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 1, "turnIndex": 2, "timestamp": 1744897355837, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 1, "turnIndex": 2, "timestamp": 1744897357389, "data": {"path": [55, 30], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 1, "turnIndex": 2, "timestamp": 1744897360392, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1744897363395, "data": {"eventId": "global-event-16", "eventName": "<PERSON><PERSON> Rhapsody", "eventEffect": "rickshaw_rhapsody_reward"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 2, "turnIndex": 0, "timestamp": 1744897363395, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 2, "turnIndex": 0, "timestamp": 1744897435953, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 2, "turnIndex": 0, "timestamp": 1744897453860, "data": {"path": [33, 29, 28, 18, 53], "travelCards": ["T11", "T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 2, "turnIndex": 1, "timestamp": 1744897456405, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 2, "turnIndex": 1, "timestamp": 1744897458340, "data": {"path": [10, 11, 7], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 2, "turnIndex": 1, "timestamp": 1744897461344, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 2, "turnIndex": 2, "timestamp": 1744897464347, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 2, "turnIndex": 2, "timestamp": 1744897466120, "data": {"path": [30, 31, 65, 63, 25], "travelCards": ["T6", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 2, "turnIndex": 2, "timestamp": 1744897469122, "data": {"journeyType": "outer", "journeyCardId": "JO11", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1744897472127, "data": {"eventId": "global-event-4", "eventName": "Drought of Spirits", "eventEffect": "no_inner_journey_cards"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 3, "turnIndex": 0, "timestamp": 1744897472127, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 3, "turnIndex": 0, "timestamp": 1744897553583, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 3, "turnIndex": 0, "timestamp": 1744897564554, "data": {"path": [53, 18, 28, 37], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 3, "turnIndex": 1, "timestamp": 1744897566882, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 3, "turnIndex": 1, "timestamp": 1744897568606, "data": {"path": [7, 11, 12, 13, 34], "travelCards": ["T4", "T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 3, "turnIndex": 1, "timestamp": 1744897571609, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 3, "turnIndex": 2, "timestamp": 1744897574610, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 3, "turnIndex": 2, "timestamp": 1744897576512, "data": {"path": [25, 24, 23, 59], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 3, "turnIndex": 2, "timestamp": 1744897579514, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1744897582517, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 4, "turnIndex": 0, "timestamp": 1744897582517, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 4, "turnIndex": 0, "timestamp": 1744897599296, "data": {"path": [37, 38], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 4, "turnIndex": 0, "timestamp": 1744897602586, "data": {"journeyType": "inner", "journeyCardId": "JI18", "omRequirement": 1}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 4, "turnIndex": 1, "timestamp": 1744897606234, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 4, "turnIndex": 1, "timestamp": 1744897608150, "data": {"path": [34, 13, 62, 61, 4, 3], "travelCards": ["T12", "T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 4, "turnIndex": 1, "timestamp": 1744897611153, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 4, "turnIndex": 2, "timestamp": 1744897614154, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 4, "turnIndex": 2, "timestamp": 1744897616583, "data": {"path": [59, 23, 22], "travelCards": ["T1", "T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 4, "turnIndex": 2, "timestamp": 1744897619589, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1744897622591, "data": {"eventId": "global-event-8", "eventName": "Triathlon", "eventEffect": "triathlon_bonus"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 5, "turnIndex": 0, "timestamp": 1744897622591, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 5, "turnIndex": 0, "timestamp": 1744897818035, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 5, "turnIndex": 0, "timestamp": 1744897827872, "data": {"path": [38, 37, 28, 29], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 5, "turnIndex": 1, "timestamp": 1744897832881, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 5, "turnIndex": 1, "timestamp": 1744897834455, "data": {"path": [3, 4, 6, 7, 11, 10], "travelCards": ["T9", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 5, "turnIndex": 1, "timestamp": 1744897837458, "data": {"journeyType": "inner", "journeyCardId": "JI6", "omRequirement": 1}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 5, "turnIndex": 2, "timestamp": 1744897840459, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 5, "turnIndex": 2, "timestamp": 1744897842813, "data": {"path": [22, 21, 28], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 5, "turnIndex": 2, "timestamp": 1744897845817, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1744897848818, "data": {"eventId": "global-event-9", "eventName": "Riots", "eventEffect": "riots_discard"}}, {"type": "riots_discard_cube", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 6, "turnIndex": 0, "timestamp": 1744897848819, "data": {"cubeType": "bhakti"}}, {"type": "riots_discard_cube", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 6, "turnIndex": 0, "timestamp": 1744897848820, "data": {"cubeType": "bhakti"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 6, "turnIndex": 0, "timestamp": 1744897848820, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 6, "turnIndex": 0, "timestamp": 1744897878262, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 6, "turnIndex": 0, "timestamp": 1744897894846, "data": {"path": [29, 64, 15, 5, 52], "travelCards": ["T9", "T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 6, "turnIndex": 1, "timestamp": 1744897898183, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 6, "turnIndex": 1, "timestamp": 1744897899720, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 6, "turnIndex": 2, "timestamp": 1744897902722, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 6, "turnIndex": 2, "timestamp": 1744897904416, "data": {"path": [28, 18], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 6, "turnIndex": 2, "timestamp": 1744897907418, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1744897910420, "data": {"eventId": "global-event-15", "eventName": "Biker Gang", "eventEffect": "biker_gang_reward"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 7, "turnIndex": 0, "timestamp": 1744897910420, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 7, "turnIndex": 0, "timestamp": 1744897952597, "data": {"path": [52, 5], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "trade", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 7, "turnIndex": 0, "timestamp": 1744897958927, "data": {"cubesTraded": ["gnana"], "cubeReceived": "karma", "count": 1}}, {"type": "collectJourney", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 7, "turnIndex": 0, "timestamp": 1744897961261, "data": {"journeyType": "outer", "journeyCardId": "JO2", "omRequirement": 1}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 7, "turnIndex": 1, "timestamp": 1744897969615, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 7, "turnIndex": 1, "timestamp": 1744897972119, "data": {"path": [10, 11, 12, 50], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 7, "turnIndex": 1, "timestamp": 1744897975122, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 7, "turnIndex": 2, "timestamp": 1744897978122, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 7, "turnIndex": 2, "timestamp": 1744897980592, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 7, "turnIndex": 2, "timestamp": 1744897983595, "data": {"path": [18, 15, 64, 61, 6], "travelCards": ["T11", "T1"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1744897986597, "data": {"eventId": "global-event-19", "eventName": "Bullet Train", "eventEffect": "bullet_train_reward"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 8, "turnIndex": 0, "timestamp": 1744897986597, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 8, "turnIndex": 0, "timestamp": 1744898046593, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 8, "turnIndex": 0, "timestamp": 1744898073787, "data": {"path": [5, 15, 64, 66, 42, 43, 60], "travelCards": ["T7", "T12", "T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 8, "turnIndex": 1, "timestamp": 1744898078337, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 8, "turnIndex": 1, "timestamp": 1744898080475, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 8, "turnIndex": 1, "timestamp": 1744898083478, "data": {"path": [50, 12], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 8, "turnIndex": 2, "timestamp": 1744898086480, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 8, "turnIndex": 2, "timestamp": 1744898088823, "data": {"path": [6, 61, 62, 13, 14, 16], "travelCards": ["T5", "T10"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 8, "turnIndex": 2, "timestamp": 1744898091826, "data": {"journeyType": "inner", "journeyCardId": "JI9", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1744898094827, "data": {"eventId": "global-event-32", "eventName": "Central Heart", "eventEffect": "central_heart"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 9, "turnIndex": 0, "timestamp": 1744898094827, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 9, "turnIndex": 0, "timestamp": 1744898129912, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 9, "turnIndex": 0, "timestamp": 1744898133900, "data": {"cardType": "travel", "drawnCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 9, "turnIndex": 1, "timestamp": 1744898135988, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 9, "turnIndex": 1, "timestamp": 1744898138305, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 9, "turnIndex": 1, "timestamp": 1744898141307, "data": {"path": [12, 11], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 9, "turnIndex": 2, "timestamp": 1744898144308, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 9, "turnIndex": 2, "timestamp": 1744898146072, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1744898149073, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 10, "turnIndex": 0, "timestamp": 1744898149073, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 10, "turnIndex": 0, "timestamp": 1744898168846, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 10, "turnIndex": 0, "timestamp": 1744898176280, "data": {"path": [60, 43, 44], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 10, "turnIndex": 1, "timestamp": 1744898178807, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 10, "turnIndex": 1, "timestamp": 1744898180746, "data": {"path": [11, 62, 64, 15], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 10, "turnIndex": 1, "timestamp": 1744898183748, "data": {"journeyType": "inner", "journeyCardId": "JI8", "omRequirement": 1}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 10, "turnIndex": 2, "timestamp": 1744898186750, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 10, "turnIndex": 2, "timestamp": 1744898188546, "data": {"path": [16, 14, 13, 12, 11, 7, 54], "travelCards": ["T11", "T12"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 10, "turnIndex": 2, "timestamp": 1744898191549, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1744898194550, "data": {"eventId": "global-event-30", "eventName": "Breezy East", "eventEffect": "breezy_east"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 11, "turnIndex": 0, "timestamp": 1744898194550, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 11, "turnIndex": 0, "timestamp": 1744898222508, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 11, "turnIndex": 0, "timestamp": 1744898230179, "data": {"path": [44, 43, 42], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 11, "turnIndex": 1, "timestamp": 1744898237876, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 11, "turnIndex": 1, "timestamp": 1744898240388, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 11, "turnIndex": 1, "timestamp": 1744898243391, "data": {"path": [15, 64, 61, 4, 3, 56], "travelCards": ["T8", "T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 11, "turnIndex": 2, "timestamp": 1744898246392, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 11, "turnIndex": 2, "timestamp": 1744898248488, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 11, "turnIndex": 2, "timestamp": 1744898251491, "data": {"path": [54, 7, 11, 12, 13], "travelCards": ["T4", "T11"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1744898254493, "data": {"eventId": "global-event-21", "eventName": "Heavy Haul", "eventEffect": "heavy_haul_reward"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 12, "turnIndex": 0, "timestamp": 1744898254493, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 12, "turnIndex": 0, "timestamp": 1744898274032, "data": {"path": [42, 48, 57], "travelCards": ["T1", "T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 12, "turnIndex": 1, "timestamp": 1744898277036, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 12, "turnIndex": 1, "timestamp": 1744898279553, "data": {"path": [56, 3, 2], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "region_based_penalty", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 12, "turnIndex": 1, "timestamp": 1744898279558, "data": {"region": "West", "effect": "sandy_west", "discardedCards": ["T3"]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 12, "turnIndex": 1, "timestamp": 1744898282556, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 12, "turnIndex": 2, "timestamp": 1744898285558, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 12, "turnIndex": 2, "timestamp": 1744898287302, "data": {"path": [13, 62, 65, 31, 32, 36], "travelCards": ["T5", "T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 12, "turnIndex": 2, "timestamp": 1744898290306, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1744898293308, "data": {"eventId": "global-event-10", "eventName": "Om Meditation", "eventEffect": "om_meditation"}}, {"type": "om_meditation_gain", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 13, "turnIndex": 0, "timestamp": 1744898293310, "data": {"jyotirlinga": 58, "distance": 2}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 13, "turnIndex": 0, "timestamp": 1744898293310, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 13, "turnIndex": 0, "timestamp": 1744898329301, "data": {"path": [57, 48, 42, 43], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "trade", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 13, "turnIndex": 0, "timestamp": 1744898339205, "data": {"cubesTraded": ["bhakti"], "cubeReceived": "karma", "count": 1}}, {"type": "collectJourney", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 13, "turnIndex": 0, "timestamp": 1744898342081, "data": {"journeyType": "outer", "journeyCardId": "JO22", "omRequirement": 2}}, {"type": "endTurn", "playerId": "RzBEyN5MbjCX48YCAAAB", "playerName": "First", "roundCount": 13, "turnIndex": 1, "timestamp": 1744898347186, "data": {"nextPlayerId": "Bxn5wbNXbv6ISarcAAAD", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 13, "turnIndex": 1, "timestamp": 1744898348786, "data": {"path": [2, 3, 4], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 13, "turnIndex": 1, "timestamp": 1744898351788, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "Bxn5wbNXbv6ISarcAAAD", "playerName": "MightyWayfarer", "roundCount": 13, "turnIndex": 2, "timestamp": 1744898354790, "data": {"nextPlayerId": "7ysJv8u0gOYHAymPAAAF", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "move", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 13, "turnIndex": 2, "timestamp": 1744898357192, "data": {"path": [36, 32], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 13, "turnIndex": 2, "timestamp": 1744898360195, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1744898363197, "data": {"eventId": "global-event-1", "eventName": "Drizzle of Delay", "eventEffect": "max_moves_2_and_cost_artha_north_east"}}, {"type": "endTurn", "playerId": "7ysJv8u0gOYHAymPAAAF", "playerName": "SwiftPathfinder", "roundCount": 14, "turnIndex": 0, "timestamp": 1744898363197, "data": {"nextPlayerId": "RzBEyN5MbjCX48YCAAAB", "newRound": true, "roundCount": 14, "turnCount": null}}], "characterDeck": [{"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}], "energyCubePile": {"artha": 6, "karma": 2, "gnana": 10, "bhakti": 10}, "currentGlobalEvent": {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, "globalEventDeck": [{"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}], "globalEventDiscard": [{"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}], "nameMode": false}