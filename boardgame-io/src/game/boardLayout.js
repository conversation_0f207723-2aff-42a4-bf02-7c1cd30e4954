// Comprehensive boardLayout.js for Om: The Journey
// Using a mathematical approach to ensure balanced spacing and readability
// Designed to utilize full 1800×1400 viewbox with proper margins
//
// Each region is laid out in clearly defined areas with balanced spacing:
// - North: Top left quadrant
// - Northeast: Top right quadrant
// - West: Far left
// - Central: Center
// - East: Far right
// - South: Bottom section spanning width
//
// Jyotirlingas and airports positioned for clear visibility

// ----------------------------------------------------------------
//  Node Positions - Organized by region
// ----------------------------------------------------------------
const nodes = {
  // ----------------------------------------------------------------
  //  NORTH REGION (Top-Left) - IDs: 1,2,3,4,6,7,8,9
  // ----------------------------------------------------------------
  1:  { x: 350, y: 220 },  // Vaishno Devi
  2:  { x: 510, y: 200 },  // Chail
  3:  { x: 710, y: 310 },  // Golden Temple
  4:  { x: 610, y: 410 },  // Durgiana Temple
  6:  { x: 470, y: 530 },  // Taj Mahal
  7:  { x: 250, y: 380 },  // Haridwar
  9:  { x: 430, y: 100 },  // Valley of Flowers

  // Jyotirlingas: 54,56
  54: { x: 250, y: 120 },  // Kedarnath
  56: { x: 650, y: 140 },  // Viswanath

  // Airport: 61
  61: { x: 420, y: 400 },  // North Airport

  // ----------------------------------------------------------------
  //  WEST REGION (Far Left) - IDs: 10,11,12,13,14,16,17,19
  // ----------------------------------------------------------------
  10: { x: 120, y: 730 },  // Ambaji Temple (adjusted for better spacing)
  11: { x: 260, y: 530 },  // Rann of Kutch (adjusted spacing)
  12: { x: 390, y: 735 },  // Jaisalmer Fort (adjusted spacing)
  13: { x: 380, y: 860 },  // Hawa Mahal (adjusted spacing)
  14: { x: 240, y: 900 },  // Brahma Temple, Pushkar (adjusted spacing)
  16: { x: 180, y: 1050 },  // Siddhivinayak Temple (adjusted spacing)
  19: { x: 100, y: 830 },  // Basilica of Bom Jesus (adjusted spacing)

  // Jyotirlingas: 49,50
  49: { x: 90, y: 600 },  // Somnath (adjusted spacing)
  50: { x: 390, y: 635 },  // Nageswar (adjusted spacing)

  // Airport: 62
  62: { x: 210, y: 780 },  // West Airport (adjusted spacing)

  // ----------------------------------------------------------------
  //  SOUTH REGION (Far Bottom) - IDs: 20..27
  // ----------------------------------------------------------------
  20: { x: 580, y: 1200 },  // Meenakshi Temple (slight adjustment)
  21: { x: 715, y: 1135 },  // Annamalaiyar Temple
  22: { x: 875, y: 1180 },  // Nilgiri Hills
  23: { x: 1150, y: 1210 },  // Sabarimala
  24: { x: 900, y: 1355 },  // Padmanabhaswamy Temple (moved more south)
  25: { x: 720, y: 1355 },  // Backwaters of Kerala (adjusted)
  27: { x: 450, y: 1270 },  // Hampi

  // Jyotirlingas: 51,59
  51: { x: 470, y: 1150 },  // Mallikarjuna
  59: { x: 1100, y: 1355 },  // Rameshwar

  // Airport: 63
  63: { x: 740, y: 1255 },  // South Airport

  // ----------------------------------------------------------------
  //  CENTRAL REGION (Center) - IDs: 5,15,18,28,29,33,34,35
  // ----------------------------------------------------------------
  5:  { x: 550, y: 680 },  // Kurukshetra (slight adjustment)
  15: { x: 780, y: 550 },  // Shirdi Sai Baba Temple (slight adjustment)
  18: { x: 950, y: 700 },  // Ajanta and Ellora Caves (slight adjustment)
  28: { x: 1000, y: 850 },  // Mysore Palace
  29: { x: 770, y: 850 },  // Coorg
  33: { x: 600, y: 810 },  // Khajuraho Temples
  34: { x: 510, y: 900 },  // Pachmarhi

  // Jyotirlingas: 52,53
  52: { x: 630, y: 580 },  // Mahakaleswar (moved up a bit)
  53: { x: 950, y: 550 },  // Omkareshwar (moved up a bit)

  // Airport: 64
  64: { x: 780, y: 700 },  // Central Airport

  // ----------------------------------------------------------------
  //  EAST REGION (Far Right & Down) - IDs: 30..32,36..40
  // ----------------------------------------------------------------
  30: { x: 1160, y: 750 },  // Charminar (moved southeast)
  31: { x: 1360, y: 600 },  // Ramoji Film City (moved southeast)
  32: { x: 1430, y: 700 },  // Tirumala Venkateswara Temple (moved southeast)
  36: { x: 1400, y: 900 },  // Jagannath Temple (moved southeast)
  37: { x: 1250, y: 950 },  // Chilika Lake (moved southeast)
  38: { x: 1150, y: 1050 },  // Tarapith (moved southeast)
  39: { x: 1300, y: 1100 },  // Betla National Park (moved southeast)

  // Jyotirlingas: 55,58
  55: { x: 1210, y: 600 },  // Bhimashankar (moved southeast)
  58: { x: 1530, y: 810 },  // Vaidyanath (moved southeast)

  // Airport: 65
  65: { x: 1300, y: 850 },  // East Airport (moved southeast)

  // ----------------------------------------------------------------
  //  NORTHEAST REGION (Top-Right) - IDs: 41..48
  // ----------------------------------------------------------------
  42: { x: 1280, y: 200 },  // Kamakhya Temple
  43: { x: 1480, y: 300 },  // Living Root Bridge
  44: { x: 1350, y: 400 },  // Ujjayanta Palace
  45: { x: 1210, y: 500 },  // Kangla Fort
  46: { x: 1000, y: 400 },  // Phawngpui Blue Mountain
  47: { x: 900, y: 300 },  // Dzükou Valley (moved slightly left)
  48: { x: 1320, y:  80 },  // Rumtek Monastery

  // Jyotirlingas: 57,60
  57: { x: 1000, y: 160 },  // Triambakeshwar (moved left a bit)
  60: { x: 1420, y: 150 },  // Grishneshwar

  // Airport: 66
  66: { x: 1200, y: 300 },  // Northeast Airport
};

// --------------------------------------------------------------------
//  EDGES
//  Within each region: main ring + 2 Jyotirlingas + 1 airport
//  PLUS inter-regional edges so each region is reachable
// --------------------------------------------------------------------
const edges = [
  //
  // ─────────────────────────────────────────────────────────────────
  //   N O R T H   R E G I O N
  //   (nodes 1,2,3,4,6,7,8,9; jyotirlingas 54,56; airport 61)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main North nodes:
  { from: 1, to: 2 },
  { from: 2, to: 3 },
  { from: 3, to: 4 },
  { from: 4, to: 6 },
  { from: 6, to: 7 },
  { from: 1, to: 9 },
  // Jyotirlingas (1 connection each):
  { from: 9, to: 54 },  // Kedarnath
  { from: 3, to: 56 },  // Viswanath
  { from: 7, to: 54 },  // New connection: Haridwar to Kedarnath
  // Airport (2 connections):
  { from: 4, to: 61 },
  { from: 6, to: 61 },
  // Inter‐regional edges:
  { from: 7, to: 11 },  // North <-> West
  { from: 3, to: 47 },  // North <-> Northeast

  //
  // ─────────────────────────────────────────────────────────────────
  //   W E S T   R E G I O N
  //   (nodes 10,11,12,13,14,16,17,19; jyotirlingas 49,50; airport 62)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main West nodes:
  { from: 10, to: 11 },
  { from: 11, to: 12 },
  { from: 12, to: 13 },
  { from: 13, to: 14 },
  { from: 14, to: 16 },
  { from: 14, to: 19 },
  { from: 13, to: 34 },  // New connection: Hawa Mahal to Pachmarhi (West to Central)
  // Jyotirlingas (1 connection each):
  { from: 10, to: 49 }, // Somnath
  { from: 12, to: 50 }, // Nageswar
  // Airport (connected to 3 West nodes so none is left with only 1 edge):
  { from: 11, to: 62 },
  { from: 13, to: 62 },
  { from: 19, to: 62 },
  // Inter‐regional edges:
  // (North <-> West is already from 7->14)
  { from: 16, to: 27 }, // West <-> South

  //
  // ─────────────────────────────────────────────────────────────────
  //   S O U T H   R E G I O N
  //   (nodes 20..27; jyotirlingas 51,59; airport 63)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main South nodes:
  { from: 20, to: 21 },
  { from: 21, to: 22 },
  { from: 22, to: 23 },
  { from: 23, to: 24 },
  { from: 24, to: 25 },
  { from: 25, to: 27 },
  // Jyotirlingas (1 connection each):
  { from: 20, to: 51 }, // Mallikarjuna
  { from: 23, to: 59 }, // Rameshwar
  // Airport (2 connections):
  { from: 24, to: 63 },
  { from: 25, to: 63 },
  { from: 21, to: 63 },
  // Inter‐regional edges:
  { from: 21, to: 28 }, // South <-> Central
  // (West <-> South is 16->27 above)

  //
  // ─────────────────────────────────────────────────────────────────
  //   C E N T R A L   R E G I O N
  //   (nodes 5,15,18,28,29,33,34,35; jyotirlingas 52,53; airport 64)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main Central nodes:
  { from: 5,  to: 15 },
  { from: 15, to: 18 },
  { from: 18, to: 28 },
  { from: 28, to: 29 },
  { from: 29, to: 33 },
  { from: 33, to: 34 },
  { from: 28, to: 37 },  // New connection: Mysore Palace to Chilika Lake (Central to East)
  // Jyotirlingas (1 connection each):
  { from: 5,  to: 52 }, // Mahakaleswar
  { from: 18, to: 53 }, // Omkareshwar
  // Airport (3 connections so endpoints aren't stuck at 1 edge):
  { from: 15, to: 64 },
  { from: 29, to: 64 },
  // Inter‐regional edges:
  { from: 21, to: 28 }, // South <-> Central (above)

  //
  // ─────────────────────────────────────────────────────────────────
  //   E A S T   R E G I O N
  //   (nodes 30..32,36..40; jyotirlingas 55,58; airport 65)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main East nodes:
  { from: 30, to: 31 },
  { from: 31, to: 32 },
  { from: 32, to: 36 },
  { from: 65, to: 37 },
  { from: 37, to: 38 },
  { from: 38, to: 39 },
  // Jyotirlingas (1 connection each):
  { from: 30, to: 55 }, // Bhimashankar
  { from: 32, to: 58 }, // Vaidyanath
  // Airport (2 connections):
  { from: 31, to: 65 },
  { from: 39, to: 65 },
  // Inter‐regional edges:
  { from: 23, to: 38 }, // South <-> East (above)
  { from: 31, to: 45 }, // East <-> Northeast (below)

  //
  // ─────────────────────────────────────────────────────────────────
  //   N O R T H E A S T   R E G I O N
  //   (nodes 41..48; jyotirlingas 57,60; airport 66)
  // ─────────────────────────────────────────────────────────────────
  // Chain of 8 main Northeast nodes:
  { from: 42, to: 43 },
  { from: 43, to: 44 },
  { from: 44, to: 45 },
  { from: 45, to: 46 },
  { from: 46, to: 47 },
  { from: 42, to: 48 },
  // Jyotirlingas (1 connection each):
  { from: 47, to: 57 }, // Triambakeshwar
  { from: 48, to: 60 }, // Grishneshwar
  // Airport (3 connections):
  { from: 42, to: 66 },
  { from: 44, to: 66 },
  // Inter‐regional edges:
  { from: 3, to: 47 },  // North <-> Northeast (above)
  { from: 31, to: 45 }, // East <-> Northeast (above)
];

// ----------------------------------------------------------------
//  Node collections by type for rendering
// ----------------------------------------------------------------

// Airport nodes for special rendering
const airportNodes = [61, 62, 63, 64, 65, 66];

// Jyotirlinga nodes for special rendering
const jyotirlinga = [49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60];

// Export all data for use in the game
module.exports = {
  nodes,
  edges,
  airportNodes,
  jyotirlinga
};