{"description": "local testing by <PERSON><PERSON> only. 1 won by a landslide, picked cheapest cards, got some event benefits", "players": [{"id": "pyPtV-dnCFK5l7XNAAAB", "name": "1", "position": 7, "hand": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "energyCubes": ["karma"], "omTemp": [1], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 0, 0, 0], "collectedJourneys": [{"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "outerScore": 69, "innerScore": 32, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "SANRJdl3bseOwvUeAAAD", "name": "2", "position": 32, "hand": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "energyCubes": ["artha"], "omTemp": [1, 1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "outerScore": 29, "innerScore": 59, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}}], "started": true, "turnIndex": 0, "roundCount": 12, "travelDeck": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "eventDeck": [{"id": "E2", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}, {"id": "E6", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "journeyDeckOuter": [{"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "travelDiscard": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "faceUpEvent": [{"id": "E5", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}, {"id": "E8", "type": "wildCube"}, {"id": "E3", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "faceUpJourneyOuter": [{"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "locationCubes": {"3": "karma", "4": "karma", "6": "artha", "10": "gnana", "11": "artha", "12": "artha", "13": "karma", "15": "bhakti", "16": "karma", "17": "karma", "18": "karma", "19": "artha", "21": "gnana", "25": "artha", "26": "bhakti", "27": "gnana", "28": "gnana", "29": "karma", "31": "bhakti", "32": "gnana", "33": "artha", "34": "karma", "35": "artha", "36": "bhakti", "37": "bhakti", "38": "artha", "40": "karma", "41": "artha", "42": "bhakti", "43": "bhakti", "44": "bhakti", "45": "gnana", "46": "gnana", "47": "artha", "48": "gnana", "undefined": "karma"}, "locationOm": {"49": true, "53": true, "55": false, "60": true}, "finalRound": true, "finalRoundStarter": 0, "finalRoundEnd": 1, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221635170, "data": {"eventId": "global-event-15", "eventName": "Biker Gang", "eventEffect": "biker_gang_reward"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221657709, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221657709, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221657709, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221657709, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "PICK_DECK_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221734734, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221735368, "data": {"cardType": "travel", "drawnCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221762632, "data": {"path": [64, 15, 5, 52], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 0, "turnIndex": 0, "timestamp": 1744221766778}, {"type": "PICK_DECK_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 0, "turnIndex": 1, "timestamp": 1744221773706, "data": {"cardType": "travel", "drawnCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 0, "turnIndex": 1, "timestamp": 1744221774128, "data": {"cardType": "travel", "drawnCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickFromTop": true}}, {"type": "travel_card_reward", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 0, "turnIndex": 1, "timestamp": 1744221786900, "data": {"vehicle": "motorbike", "effect": "biker_gang_reward", "outerPoints": 5}}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 0, "turnIndex": 1, "timestamp": 1744221786900, "data": {"path": [61, 63, 20, 51], "travelCards": ["T8", "T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 0, "turnIndex": 1, "timestamp": 1744221791813}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1744221791814, "data": {"eventId": "global-event-4", "eventName": "Drought of Spirits", "eventEffect": "no_inner_journey_cards"}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 1, "turnIndex": 0, "timestamp": 1744221887672, "data": {"path": [52, 5], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "trade", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 1, "turnIndex": 0, "timestamp": 1744222082355, "data": {"cubesTraded": ["bhakti"], "cubeReceived": "karma", "count": 1}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 1, "turnIndex": 0, "timestamp": 1744222084709}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 1, "turnIndex": 1, "timestamp": 1744223486688, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 1, "turnIndex": 1, "timestamp": 1744223502501, "data": {"path": [51, 20], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 1, "turnIndex": 1, "timestamp": 1744223505315}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1744223505316, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 2, "turnIndex": 0, "timestamp": 1744223523643, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 2, "turnIndex": 0, "timestamp": 1744223708561, "data": {"vehicle": "camel", "effect": "desert_caravan_reward", "outerPoints": 5}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 2, "turnIndex": 0, "timestamp": 1744223708561, "data": {"path": [5, 15, 64, 61, 4, 3, 2, 1, 9], "travelCards": ["T11", "T6", "T1", "T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 2, "turnIndex": 0, "timestamp": 1744224302783, "data": {"journeyType": "outer", "journeyCardId": "JO4", "omRequirement": 1}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 2, "turnIndex": 0, "timestamp": 1744224330209}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 2, "turnIndex": 1, "timestamp": 1744224501906, "data": {"path": [20, 63, 61, 62, 13, 14], "travelCards": ["T7", "T10"], "extraHopCards": []}}, {"type": "trade", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 2, "turnIndex": 1, "timestamp": 1744224510328, "data": {"cubesTraded": ["karma"], "cubeReceived": "bhakti", "count": 1}}, {"type": "collectJourney", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 2, "turnIndex": 1, "timestamp": 1744224512593, "data": {"journeyType": "inner", "journeyCardId": "JI7", "omRequirement": 1}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 2, "turnIndex": 1, "timestamp": 1744225065015}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1744225065016, "data": {"eventId": "global-event-26", "eventName": "Engineer's Precision", "eventEffect": "engineers_precision_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 3, "turnIndex": 0, "timestamp": 1744225108627, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 3, "turnIndex": 0, "timestamp": 1744225116850, "data": {"path": [9, 1], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "character_trade_reward", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 3, "turnIndex": 0, "timestamp": 1744225125838, "data": {"character": "engineer", "cubeReceived": "karma", "effect": "engineers_precision_reward", "outerPoints": 7}}, {"type": "trade", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 3, "turnIndex": 0, "timestamp": 1744225125838, "data": {"cubesTraded": ["gnana"], "cubeReceived": "karma", "count": 1}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 3, "turnIndex": 0, "timestamp": 1744225130241}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 3, "turnIndex": 1, "timestamp": 1744225211810, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 3, "turnIndex": 1, "timestamp": 1744225220291, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 3, "turnIndex": 1, "timestamp": 1744225228264, "data": {"path": [14, 13, 12, 50], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 3, "turnIndex": 1, "timestamp": 1744225230817}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1744225230817, "data": {"eventId": "global-event-32", "eventName": "Central Heart", "eventEffect": "central_heart"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 4, "turnIndex": 0, "timestamp": 1744225254329, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 4, "turnIndex": 0, "timestamp": 1744225261204, "data": {"path": [1, 9, 54, 8, 7], "travelCards": ["T8", "T7"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 4, "turnIndex": 0, "timestamp": 1744225266696}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 4, "turnIndex": 1, "timestamp": 1744225296065, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 4, "turnIndex": 1, "timestamp": 1744225304627, "data": {"path": [50, 12, 11, 62, 63, 24], "travelCards": ["T9", "T5"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 4, "turnIndex": 1, "timestamp": 1744225317217}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1744225317217, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 5, "turnIndex": 0, "timestamp": 1744225371421, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 5, "turnIndex": 0, "timestamp": 1744225390046, "data": {"path": [7, 8, 54], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 5, "turnIndex": 0, "timestamp": 1744225393860}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 5, "turnIndex": 1, "timestamp": 1744225410969, "data": {"path": [24, 63, 20, 21, 22], "travelCards": ["T2", "T11"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 5, "turnIndex": 1, "timestamp": 1744225415610, "data": {"journeyType": "outer", "journeyCardId": "JO10", "omRequirement": 1}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 5, "turnIndex": 1, "timestamp": 1744225417740}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1744225417741, "data": {"eventId": "global-event-5", "eventName": "Bountiful Bhandara", "eventEffect": "draw_2_cubes_bonus_5_outer"}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 6, "turnIndex": 0, "timestamp": 1744225436599, "data": {"path": [54, 9, 1, 2], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 6, "turnIndex": 0, "timestamp": 1744225439999, "data": {"journeyType": "outer", "journeyCardId": "JO1", "omRequirement": 1}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 6, "turnIndex": 0, "timestamp": 1744225442383}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 6, "turnIndex": 1, "timestamp": 1744225537052, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 6, "turnIndex": 1, "timestamp": 1744225539703, "data": {"cardType": "travel", "drawnCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 6, "turnIndex": 1, "timestamp": 1744225545708, "data": {"path": [22, 23, 59], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 6, "turnIndex": 1, "timestamp": 1744225548304}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1744225548304, "data": {"eventId": "global-event-19", "eventName": "Bullet Train", "eventEffect": "bullet_train_reward"}}, {"type": "PICK_DECK_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 7, "turnIndex": 0, "timestamp": 1744227134329, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 7, "turnIndex": 0, "timestamp": 1744227146371, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 7, "turnIndex": 0, "timestamp": 1744227158986, "data": {"vehicle": "train", "effect": "bullet_train_reward", "outerPoints": 5}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 7, "turnIndex": 0, "timestamp": 1744227158986, "data": {"path": [2, 3, 47, 57], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 7, "turnIndex": 0, "timestamp": 1744227161348}, {"type": "PICK_DECK_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 7, "turnIndex": 1, "timestamp": 1744227233406, "data": {"cardType": "travel", "drawnCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 7, "turnIndex": 1, "timestamp": 1744227239138, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 7, "turnIndex": 1, "timestamp": 1744227247024, "data": {"path": [59, 23], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 7, "turnIndex": 1, "timestamp": 1744227249002}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1744227249002, "data": {"eventId": "global-event-29", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 8, "turnIndex": 0, "timestamp": 1744227262194, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 8, "turnIndex": 0, "timestamp": 1744227267364, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 8, "turnIndex": 0, "timestamp": 1744227274431, "data": {"path": [57, 47, 3, 2], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 8, "turnIndex": 0, "timestamp": 1744227286936}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 8, "turnIndex": 1, "timestamp": 1744227318775, "data": {"path": [23, 38, 39], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 8, "turnIndex": 1, "timestamp": 1744227329826, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 8, "turnIndex": 1, "timestamp": 1744227335848, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 8, "turnIndex": 1, "timestamp": 1744227337660}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1744227337660, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "move", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 9, "turnIndex": 0, "timestamp": 1744227356850, "data": {"path": [2, 3, 56], "travelCards": ["T8"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 9, "turnIndex": 0, "timestamp": 1744227364143, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 9, "turnIndex": 0, "timestamp": 1744227366012, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "BkdRK2GzVOBEBK4fAAAB", "playerName": "1", "roundCount": 9, "turnIndex": 0, "timestamp": 1744227367488}, {"type": "move", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 9, "turnIndex": 1, "timestamp": 1744227382641, "data": {"path": [39, 65, 31, 32], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 9, "turnIndex": 1, "timestamp": 1744227396781}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1744227396782, "data": {"eventId": "global-event-10", "eventName": "Om Meditation", "eventEffect": "om_meditation"}}, {"type": "om_meditation_gain", "playerId": "dummLRDn9r2QTVFtAAAD", "playerName": "2", "roundCount": 10, "turnIndex": 0, "timestamp": 1744227396783, "data": {"jyotirlinga": 58, "distance": 1}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1744227653999, "data": {"timestamp": 1744227653999, "loadedStateRoundCount": 10, "playerCount": 2}}, {"type": "move", "playerId": "1r2iIKMXiTLPwSJ9AAAC", "playerName": "1", "roundCount": 10, "turnIndex": 0, "timestamp": 1744227682170, "data": {"path": [56, 3, 2, 1, 9, 54, 8], "travelCards": ["T9", "T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1r2iIKMXiTLPwSJ9AAAC", "playerName": "1", "roundCount": 10, "turnIndex": 0, "timestamp": 1744227686746, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "1r2iIKMXiTLPwSJ9AAAC", "playerName": "1", "roundCount": 10, "turnIndex": 0, "timestamp": 1744227689380, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "1r2iIKMXiTLPwSJ9AAAC", "playerName": "1", "roundCount": 10, "turnIndex": 0, "timestamp": 1744227700993}, {"type": "move", "playerId": "FaDzs9o74vZ34cPCAAAD", "playerName": "2", "roundCount": 10, "turnIndex": 1, "timestamp": 1744227726544, "data": {"path": [32, 31, 30], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "FaDzs9o74vZ34cPCAAAD", "playerName": "2", "roundCount": 10, "turnIndex": 1, "timestamp": 1744227730882, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "FaDzs9o74vZ34cPCAAAD", "playerName": "2", "roundCount": 10, "turnIndex": 1, "timestamp": 1744227736317}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1744227736319, "data": {"eventId": "global-event-6", "eventName": "Turbulent Skies", "eventEffect": "no_airport_travel"}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1744227750468, "data": {"timestamp": 1744227750468, "loadedStateRoundCount": 11, "playerCount": 2}}, {"type": "move", "playerId": "pyPtV-dnCFK5l7XNAAAB", "playerName": "1", "roundCount": 11, "turnIndex": 0, "timestamp": 1744227768725, "data": {"path": [8, 7], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "pyPtV-dnCFK5l7XNAAAB", "playerName": "1", "roundCount": 11, "turnIndex": 0, "timestamp": 1744227773043, "data": {"journeyType": "inner", "journeyCardId": "JI4", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "pyPtV-dnCFK5l7XNAAAB", "playerName": "1", "roundCount": 11, "turnIndex": 0, "timestamp": 1744227773043, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 101, "playerOmTotal": 4, "finalRoundStarter": 0, "finalRoundEnd": 1}}, {"type": "endTurn", "playerId": "pyPtV-dnCFK5l7XNAAAB", "playerName": "1", "roundCount": 11, "turnIndex": 0, "timestamp": 1744227793068}, {"type": "move", "playerId": "SANRJdl3bseOwvUeAAAD", "playerName": "2", "roundCount": 11, "turnIndex": 1, "timestamp": 1744227848587, "data": {"path": [30, 31, 32], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "SANRJdl3bseOwvUeAAAD", "playerName": "2", "roundCount": 11, "turnIndex": 1, "timestamp": 1744227852011, "data": {"journeyType": "inner", "journeyCardId": "JI16", "omRequirement": 1}}, {"type": "endTurn", "playerId": "SANRJdl3bseOwvUeAAAD", "playerName": "2", "roundCount": 11, "turnIndex": 1, "timestamp": 1744228037898}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1744228037899, "data": {"eventId": "global-event-17", "eventName": "Top Gear", "eventEffect": "top_gear_reward"}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1744228037901, "data": {"winner": {"id": "pyPtV-dnCFK5l7XNAAAB", "name": "1", "outerScore": 69, "innerScore": 32, "totalScore": 101, "omTotal": 4}, "totalRounds": 12, "players": [{"id": "pyPtV-dnCFK5l7XNAAAB", "name": "1", "outerScore": 69, "innerScore": 32, "totalScore": 101, "omTotal": 4}, {"id": "SANRJdl3bseOwvUeAAAD", "name": "2", "outerScore": 29, "innerScore": 59, "totalScore": 88, "omTotal": 5}]}}], "characterDeck": [{"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}], "energyCubePile": {"artha": 3, "karma": 4, "gnana": 6, "bhakti": 6}, "currentGlobalEvent": {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, "globalEventDeck": [{"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}], "globalEventDiscard": [{"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}], "nameMode": false}