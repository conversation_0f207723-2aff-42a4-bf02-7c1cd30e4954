{
  "description": "<PERSON><PERSON> planned last 2 turns quite well and won despite travel election challenge",,
  "players": [
    {
      "id": "qfblzx7ZaXXW96yxAAAF",
      "name": "MysticWayfarer",
      "position": 19,
      "hand": [
        {
          "id": "T11",
          "type": "travel",
          "value": 3,
          "vehicle": "train"
        },
        {
          "id": "T4",
          "type": "travel",
          "value": 1,
          "vehicle": "cycle"
        },
        {
          "id": "T6",
          "type": "travel",
          "value": 2,
          "vehicle": "car"
        }
      ],
      "energyCubes": [
        "artha",
        "bhakti",
        "gnana",
        "gnana",
        "bhakti"
      ],
      "omTemp": [
        1,
        1,
        1
      ],
      "omSlotsOuter": [
        1,
        0,
        0,
        0
      ],
      "omSlotsInner": [
        0,
        0,
        0,
        0
      ],
      "collectedJourneys": [
        {
          "id": "JO2",
          "locationId": 5,
          "required": {
            "karma": 1,
            "artha": 1
          },
          "reward": {
            "outer": 24
          }
        }
      ],
      "outerScore": 24,
      "innerScore": 0,
      "didMoveThisTurn": false,
      "didSelectionActionThisTurn": false,
      "didTradeThisTurn": false,
      "character": {
        "id": "pilgrim2",
        "type": "Pilgrim",
        "ability": {
          "gives": "bhakti",
          "takes": [
            "gnana",
            "artha",
            "karma"
          ]
        },
        "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"
      }
    },
    {
      "id": "2dRRyh91WGmbpjEYAAAD",
      "name": "Yatri",
      "position": 15,
      "hand": [
        {
          "id": "T8",
          "type": "travel",
          "value": 2,
          "vehicle": "motorbike"
        }
      ],
      "energyCubes": [
        "bhakti"
      ],
      "omTemp": [],
      "omSlotsOuter": [
        1,
        1,
        0,
        0
      ],
      "omSlotsInner": [
        1,
        1,
        0,
        0
      ],
      "collectedJourneys": [
        {
          "id": "JI18",
          "locationId": 38,
          "required": {
            "bhakti": 1,
            "gnana": 1
          },
          "reward": {
            "inner": 24
          }
        },
        {
          "id": "JO7",
          "locationId": 13,
          "required": {
            "artha": 1
          },
          "reward": {
            "outer": 20
          }
        },
        {
          "id": "JO10",
          "locationId": 22,
          "required": {
            "karma": 1,
            "artha": 1
          },
          "reward": {
            "outer": 24
          }
        },
        {
          "id": "JI8",
          "locationId": 15,
          "required": {
            "bhakti": 1,
            "gnana": 2
          },
          "reward": {
            "inner": 27
          }
        }
      ],
      "outerScore": 44,
      "innerScore": 56,
      "didMoveThisTurn": false,
      "didSelectionActionThisTurn": false,
      "didTradeThisTurn": false,
      "character": {
        "id": "professor2",
        "type": "Professor",
        "ability": {
          "gives": "gnana",
          "takes": [
            "bhakti",
            "artha",
            "karma"
          ]
        },
        "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"
      }
    },
    {
      "id": "8IpCgxVeBqhh5cyIAAAB",
      "name": "Rudra",
      "position": 14,
      "hand": [
        {
          "id": "T18",
          "type": "travel",
          "value": 2,
          "vehicle": "car"
        }
      ],
      "energyCubes": [
        "bhakti",
        "bhakti",
        "artha"
      ],
      "omTemp": [
        1
      ],
      "omSlotsOuter": [
        1,
        1,
        0,
        0
      ],
      "omSlotsInner": [
        1,
        0,
        0,
        0
      ],
      "collectedJourneys": [
        {
          "id": "JO4",
          "locationId": 9,
          "required": {
            "artha": 1
          },
          "reward": {
            "outer": 20
          }
        },
        {
          "id": "JI1",
          "locationId": 1,
          "required": {
            "bhakti": 1,
            "gnana": 1
          },
          "reward": {
            "inner": 24
          }
        },
        {
          "id": "JO26",
          "locationId": 47,
          "required": {
            "karma": 2,
            "artha": 1
          },
          "reward": {
            "outer": 27
          }
        }
      ],
      "outerScore": 52,
      "innerScore": 24,
      "didMoveThisTurn": false,
      "didSelectionActionThisTurn": false,
      "didTradeThisTurn": false,
      "character": {
        "id": "merchant1",
        "type": "Merchant",
        "ability": {
          "gives": "artha",
          "takes": [
            "gnana",
            "bhakti",
            "karma"
          ]
        },
        "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"
      }
    }
  ],
  "started": true,
  "turnIndex": 1,
  "roundCount": 13,
  "travelDeck": [
    {
      "id": "T13",
      "type": "travel",
      "value": 1,
      "vehicle": "camel"
    },
    {
      "id": "T3",
      "type": "travel",
      "value": 1,
      "vehicle": "trek"
    },
    {
      "id": "T16",
      "type": "travel",
      "value": 1,
      "vehicle": "cycle"
    }
  ],
  "eventDeck": [
    {
      "id": "E7",
      "type": "wildCube"
    },
    {
      "id": "E3",
      "type": "extraHop"
    },
    {
      "id": "E5",
      "type": "extraHop"
    },
    {
      "id": "E8",
      "type": "wildCube"
    },
    {
      "id": "E4",
      "type": "extraHop"
    }
  ],
  "journeyDeckInner": [
    {
      "id": "JI17",
      "locationId": 36,
      "required": {
        "bhakti": 2,
        "gnana": 2
      },
      "reward": {
        "inner": 30
      }
    },
    {
      "id": "JI21",
      "locationId": 42,
      "required": {
        "bhakti": 1,
        "gnana": 1
      },
      "reward": {
        "inner": 24
      }
    },
    {
      "id": "JI3",
      "locationId": 4,
      "required": {
        "gnana": 1
      },
      "reward": {
        "inner": 20
      }
    },
    {
      "id": "JI10",
      "locationId": 19,
      "required": {
        "bhakti": 2,
        "gnana": 1
      },
      "reward": {
        "inner": 27
      }
    },
    {
      "id": "JI14",
      "locationId": 24,
      "required": {
        "bhakti": 2,
        "gnana": 2
      },
      "reward": {
        "inner": 30
      }
    },
    {
      "id": "JI9",
      "locationId": 16,
      "required": {
        "bhakti": 2,
        "gnana": 2
      },
      "reward": {
        "inner": 30
      }
    },
    {
      "id": "JI22",
      "locationId": 48,
      "required": {
        "bhakti": 2,
        "gnana": 2
      },
      "reward": {
        "inner": 30
      }
    },
    {
      "id": "JI6",
      "locationId": 10,
      "required": {
        "bhakti": 1,
        "gnana": 1
      },
      "reward": {
        "inner": 24
      }
    },
    {
      "id": "JI13",
      "locationId": 23,
      "required": {
        "bhakti": 1,
        "gnana": 2
      },
      "reward": {
        "inner": 27
      }
    },
    {
      "id": "JI11",
      "locationId": 20,
      "required": {
        "bhakti": 2,
        "gnana": 2
      },
      "reward": {
        "inner": 30
      }
    },
    {
      "id": "JI4",
      "locationId": 7,
      "required": {
        "bhakti": 2,
        "gnana": 1
      },
      "reward": {
        "inner": 27
      }
    }
  ],
  "journeyDeckOuter": [
    {
      "id": "JO12",
      "locationId": 27,
      "required": {
        "karma": 2,
        "artha": 1
      },
      "reward": {
        "outer": 27
      }
    },
    {
      "id": "JO11",
      "locationId": 25,
      "required": {
        "artha": 1
      },
      "reward": {
        "outer": 20
      }
    },
    {
      "id": "JO1",
      "locationId": 2,
      "required": {
        "karma": 1,
        "artha": 2
      },
      "reward": {
        "outer": 27
      }
    },
    {
      "id": "JO5",
      "locationId": 11,
      "required": {
        "karma": 1,
        "artha": 2
      },
      "reward": {
        "outer": 27
      }
    },
    {
      "id": "JO25",
      "locationId": 46,
      "required": {
        "karma": 1,
        "artha": 1
      },
      "reward": {
        "outer": 24
      }
    },
    {
      "id": "JO23",
      "locationId": 44,
      "required": {
        "karma": 2,
        "artha": 2
      },
      "reward": {
        "outer": 30
      }
    },
    {
      "id": "JO21",
      "locationId": 39,
      "required": {
        "karma": 2,
        "artha": 1
      },
      "reward": {
        "outer": 27
      }
    },
    {
      "id": "JO22",
      "locationId": 43,
      "required": {
        "karma": 1,
        "artha": 2
      },
      "reward": {
        "outer": 27
      }
    },
    {
      "id": "JO24",
      "locationId": 45,
      "required": {
        "karma": 1
      },
      "reward": {
        "outer": 20
      }
    },
    {
      "id": "JO20",
      "locationId": 37,
      "required": {
        "karma": 1
      },
      "reward": {
        "outer": 20
      }
    },
    {
      "id": "JO13",
      "locationId": 28,
      "required": {
        "karma": 2,
        "artha": 2
      },
      "reward": {
        "outer": 30
      }
    },
    {
      "id": "JO17",
      "locationId": 33,
      "required": {
        "karma": 1
      },
      "reward": {
        "outer": 20
      }
    },
    {
      "id": "JO18",
      "locationId": 34,
      "required": {
        "karma": 1,
        "artha": 1
      },
      "reward": {
        "outer": 24
      }
    },
    {
      "id": "JO14",
      "locationId": 29,
      "required": {
        "artha": 1
      },
      "reward": {
        "outer": 20
      }
    },
    {
      "id": "JO15",
      "locationId": 30,
      "required": {
        "karma": 1,
        "artha": 1
      },
      "reward": {
        "outer": 24
      }
    }
  ],
  "travelDiscard": [
    {
      "id": "T9",
      "type": "travel",
      "value": 3,
      "vehicle": "helicopter"
    },
    {
      "id": "T20",
      "type": "travel",
      "value": 2,
      "vehicle": "motorbike"
    },
    {
      "id": "T19",
      "type": "travel",
      "value": 2,
      "vehicle": "rickshaw"
    },
    {
      "id": "T7",
      "type": "travel",
      "value": 2,
      "vehicle": "rickshaw"
    },
    {
      "id": "T17",
      "type": "travel",
      "value": 2,
      "vehicle": "bus"
    },
    {
      "id": "T23",
      "type": "travel",
      "value": 3,
      "vehicle": "train"
    },
    {
      "id": "T2",
      "type": "travel",
      "value": 1,
      "vehicle": "horse"
    },
    {
      "id": "T22",
      "type": "travel",
      "value": 3,
      "vehicle": "boat"
    },
    {
      "id": "T15",
      "type": "travel",
      "value": 1,
      "vehicle": "trek"
    },
    {
      "id": "T12",
      "type": "travel",
      "value": 3,
      "vehicle": "truck"
    },
    {
      "id": "T5",
      "type": "travel",
      "value": 2,
      "vehicle": "bus"
    },
    {
      "id": "T21",
      "type": "travel",
      "value": 3,
      "vehicle": "helicopter"
    }
  ],
  "eventDiscard": [],
  "journeyInnerDiscard": [],
  "journeyOuterDiscard": [],
  "faceUpTravel": [
    {
      "id": "T14",
      "type": "travel",
      "value": 1,
      "vehicle": "horse"
    },
    {
      "id": "T1",
      "type": "travel",
      "value": 1,
      "vehicle": "camel"
    },
    {
      "id": "T10",
      "type": "travel",
      "value": 3,
      "vehicle": "boat"
    },
    {
      "id": "T24",
      "type": "travel",
      "value": 3,
      "vehicle": "truck"
    }
  ],
  "faceUpEvent": [
    {
      "id": "E1",
      "type": "extraHop"
    },
    {
      "id": "E6",
      "type": "wildCube"
    },
    {
      "id": "E9",
      "type": "wildCube"
    },
    {
      "id": "E2",
      "type": "extraHop"
    }
  ],
  "faceUpJourneyInner": [
    {
      "id": "JI16",
      "locationId": 32,
      "required": {
        "bhakti": 2,
        "gnana": 2
      },
      "reward": {
        "inner": 30
      }
    },
    {
      "id": "JI2",
      "locationId": 3,
      "required": {
        "bhakti": 2,
        "gnana": 2
      },
      "reward": {
        "inner": 30
      }
    },
    {
      "id": "JI7",
      "locationId": 14,
      "required": {
        "bhakti": 1,
        "gnana": 1
      },
      "reward": {
        "inner": 24
      }
    },
    {
      "id": "JI12",
      "locationId": 21,
      "required": {
        "bhakti": 1
      },
      "reward": {
        "inner": 20
      }
    }
  ],
  "faceUpJourneyOuter": [
    {
      "id": "JO3",
      "locationId": 6,
      "required": {
        "karma": 2,
        "artha": 1
      },
      "reward": {
        "outer": 27
      }
    },
    {
      "id": "JO6",
      "locationId": 12,
      "required": {
        "karma": 2,
        "artha": 2
      },
      "reward": {
        "outer": 30
      }
    },
    {
      "id": "JO16",
      "locationId": 31,
      "required": {
        "karma": 1,
        "artha": 2
      },
      "reward": {
        "outer": 27
      }
    },
    {
      "id": "JO9",
      "locationId": 18,
      "required": {
        "karma": 1,
        "artha": 1
      },
      "reward": {
        "outer": 24
      }
    }
  ],
  "locationCubes": {
    "2": "bhakti",
    "6": "gnana",
    "10": "karma",
    "16": "gnana",
    "18": "bhakti",
    "20": "artha",
    "21": "bhakti",
    "24": "gnana",
    "25": "artha",
    "27": "artha",
    "29": "bhakti",
    "32": "artha",
    "33": "karma",
    "34": "gnana",
    "36": "gnana",
    "37": "bhakti",
    "39": "gnana",
    "42": "gnana",
    "43": "karma",
    "45": "karma",
    "46": "gnana",
    "48": "artha",
    "undefined": "karma"
  },
  "locationOm": {},
  "finalRound": true,
  "finalRoundStarter": 1,
  "finalRoundEnd": 0,
  "omTokenVictory": false,
  "omTokenVictor": null,
  "gameEvents": [
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370532754,
      "data": {
        "eventId": "global-event-26",
        "eventName": "Engineer's Precision",
        "eventEffect": "engineers_precision_reward"
      }
    },
    {
      "type": "DEAL_INITIAL_CARD",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370558361,
      "data": {
        "cardType": "travel",
        "card": {
          "id": "T5",
          "type": "travel",
          "value": 2,
          "vehicle": "bus"
        }
      }
    },
    {
      "type": "DEAL_INITIAL_CARD",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370558361,
      "data": {
        "cardType": "travel",
        "card": {
          "id": "T1",
          "type": "travel",
          "value": 1,
          "vehicle": "camel"
        }
      }
    },
    {
      "type": "DEAL_INITIAL_CARD",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370558361,
      "data": {
        "cardType": "travel",
        "card": {
          "id": "T24",
          "type": "travel",
          "value": 3,
          "vehicle": "truck"
        }
      }
    },
    {
      "type": "DEAL_INITIAL_CARD",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370558361,
      "data": {
        "cardType": "travel",
        "card": {
          "id": "T2",
          "type": "travel",
          "value": 1,
          "vehicle": "horse"
        }
      }
    },
    {
      "type": "DEAL_INITIAL_CARD",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370558361,
      "data": {
        "cardType": "travel",
        "card": {
          "id": "T23",
          "type": "travel",
          "value": 3,
          "vehicle": "train"
        }
      }
    },
    {
      "type": "DEAL_INITIAL_CARD",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370558361,
      "data": {
        "cardType": "travel",
        "card": {
          "id": "T7",
          "type": "travel",
          "value": 2,
          "vehicle": "rickshaw"
        }
      }
    },
    {
      "type": "move",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370560233,
      "data": {
        "path": [
          64,
          15,
          5,
          52
        ],
        "travelCards": [
          "T5",
          "T1"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 0,
      "turnIndex": 0,
      "timestamp": 1745370563239,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T22",
            "type": "travel",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T8",
            "type": "travel",
            "value": 2,
            "vehicle": "motorbike"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 0,
      "turnIndex": 1,
      "timestamp": 1745370566239,
      "data": {
        "nextPlayerId": "uAhJlpzCmAYloGEHAAAD",
        "newRound": false,
        "roundCount": 0,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 0,
      "turnIndex": 1,
      "timestamp": 1745370638146,
      "data": {
        "path": [
          65,
          31,
          30,
          55
        ],
        "travelCards": [
          "T24"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 0,
      "turnIndex": 1,
      "timestamp": 1745370651059,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T17",
            "type": "travel",
            "value": 2,
            "vehicle": "bus"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 0,
      "turnIndex": 2,
      "timestamp": 1745370662449,
      "data": {
        "nextPlayerId": "NxoCqjvAdOv4zytHAAAB",
        "newRound": false,
        "roundCount": 0,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 0,
      "turnIndex": 2,
      "timestamp": 1745370710661,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T3",
            "type": "travel",
            "value": 1,
            "vehicle": "trek"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 0,
      "turnIndex": 2,
      "timestamp": 1745370718165,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T15",
            "type": "travel",
            "value": 1,
            "vehicle": "trek"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "move",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 0,
      "turnIndex": 2,
      "timestamp": 1745370728654,
      "data": {
        "path": [
          63,
          61,
          6,
          7,
          54
        ],
        "travelCards": [
          "T23",
          "T3"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 1,
      "turnIndex": 0,
      "timestamp": 1745370732670,
      "data": {
        "eventId": "global-event-25",
        "eventName": "Pilgrim's Grace",
        "eventEffect": "pilgrims_grace_reward"
      }
    },
    {
      "type": "endTurn",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 1,
      "turnIndex": 0,
      "timestamp": 1745370732670,
      "data": {
        "nextPlayerId": "1bYYOhbOLDCYcw7XAAAF",
        "newRound": true,
        "roundCount": 1,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 1,
      "turnIndex": 0,
      "timestamp": 1745370734972,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T13",
            "type": "travel",
            "value": 1,
            "vehicle": "camel"
          },
          {
            "id": "T16",
            "type": "travel",
            "value": 1,
            "vehicle": "cycle"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 1,
      "turnIndex": 0,
      "timestamp": 1745370737973,
      "data": {
        "path": [
          52,
          5
        ],
        "travelCards": [
          "T13"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 1,
      "turnIndex": 1,
      "timestamp": 1745370740975,
      "data": {
        "nextPlayerId": "uAhJlpzCmAYloGEHAAAD",
        "newRound": false,
        "roundCount": 1,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 1,
      "turnIndex": 1,
      "timestamp": 1745370818822,
      "data": {
        "path": [
          55,
          30,
          31
        ],
        "travelCards": [
          "T17"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 1,
      "turnIndex": 1,
      "timestamp": 1745370838815,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T9",
            "type": "travel",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 1,
      "turnIndex": 1,
      "timestamp": 1745370849122,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T21",
            "type": "travel",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 1,
      "turnIndex": 2,
      "timestamp": 1745370859124,
      "data": {
        "nextPlayerId": "NxoCqjvAdOv4zytHAAAB",
        "newRound": false,
        "roundCount": 1,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 1,
      "turnIndex": 2,
      "timestamp": 1745370872144,
      "data": {
        "path": [
          54,
          9
        ],
        "travelCards": [
          "T15"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "collectJourney",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 1,
      "turnIndex": 2,
      "timestamp": 1745370877428,
      "data": {
        "journeyType": "outer",
        "journeyCardId": "JO4",
        "omRequirement": 1
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 2,
      "turnIndex": 0,
      "timestamp": 1745370882803,
      "data": {
        "eventId": "global-event-19",
        "eventName": "Bullet Train",
        "eventEffect": "bullet_train_reward"
      }
    },
    {
      "type": "endTurn",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 2,
      "turnIndex": 0,
      "timestamp": 1745370882803,
      "data": {
        "nextPlayerId": "1bYYOhbOLDCYcw7XAAAF",
        "newRound": true,
        "roundCount": 2,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 2,
      "turnIndex": 0,
      "timestamp": 1745370885175,
      "data": {
        "path": [
          5,
          15,
          64,
          61,
          4
        ],
        "travelCards": [
          "T22",
          "T16"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 2,
      "turnIndex": 0,
      "timestamp": 1745370888176,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T10",
            "type": "travel",
            "value": 3,
            "vehicle": "boat"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 2,
      "turnIndex": 1,
      "timestamp": 1745370891178,
      "data": {
        "nextPlayerId": "uAhJlpzCmAYloGEHAAAD",
        "newRound": false,
        "roundCount": 2,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 2,
      "turnIndex": 1,
      "timestamp": 1745370951862,
      "data": {
        "path": [
          31,
          65,
          37,
          38
        ],
        "travelCards": [
          "T9"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "trade",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 2,
      "turnIndex": 1,
      "timestamp": 1745370969019,
      "data": {
        "cubesTraded": [
          "karma"
        ],
        "cubeReceived": "gnana",
        "count": 1
      }
    },
    {
      "type": "collectJourney",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 2,
      "turnIndex": 1,
      "timestamp": 1745370971666,
      "data": {
        "journeyType": "inner",
        "journeyCardId": "JI18",
        "omRequirement": 1
      }
    },
    {
      "type": "endTurn",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 2,
      "turnIndex": 2,
      "timestamp": 1745370976164,
      "data": {
        "nextPlayerId": "NxoCqjvAdOv4zytHAAAB",
        "newRound": false,
        "roundCount": 2,
        "turnCount": null
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 2,
      "turnIndex": 2,
      "timestamp": 1745370990405,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T19",
            "type": "travel",
            "value": 2,
            "vehicle": "rickshaw"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 2,
      "turnIndex": 2,
      "timestamp": 1745370991356,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T20",
            "type": "travel",
            "value": 2,
            "vehicle": "motorbike"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "move",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 2,
      "turnIndex": 2,
      "timestamp": 1745371035130,
      "data": {
        "path": [
          9,
          1,
          2,
          3,
          56
        ],
        "travelCards": [
          "T7",
          "T19"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 3,
      "turnIndex": 0,
      "timestamp": 1745371040100,
      "data": {
        "eventId": "global-event-31",
        "eventName": "Himalayan NE",
        "eventEffect": "himalayan_ne"
      }
    },
    {
      "type": "endTurn",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 3,
      "turnIndex": 0,
      "timestamp": 1745371040100,
      "data": {
        "nextPlayerId": "1bYYOhbOLDCYcw7XAAAF",
        "newRound": true,
        "roundCount": 3,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 3,
      "turnIndex": 0,
      "timestamp": 1745371041788,
      "data": {
        "path": [
          4,
          61,
          62,
          13
        ],
        "travelCards": [
          "T10"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 3,
      "turnIndex": 0,
      "timestamp": 1745371044789,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T4",
            "type": "travel",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T14",
            "type": "travel",
            "value": 1,
            "vehicle": "horse"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 3,
      "turnIndex": 1,
      "timestamp": 1745371047791,
      "data": {
        "nextPlayerId": "uAhJlpzCmAYloGEHAAAD",
        "newRound": false,
        "roundCount": 3,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 3,
      "turnIndex": 1,
      "timestamp": 1745371117254,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T18",
            "type": "travel",
            "value": 2,
            "vehicle": "car"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 3,
      "turnIndex": 1,
      "timestamp": 1745371145852,
      "data": {
        "path": [
          38,
          23,
          59
        ],
        "travelCards": [
          "T18"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 3,
      "turnIndex": 2,
      "timestamp": 1745371149575,
      "data": {
        "nextPlayerId": "NxoCqjvAdOv4zytHAAAB",
        "newRound": false,
        "roundCount": 3,
        "turnCount": null
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 3,
      "turnIndex": 2,
      "timestamp": 1745371172877,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T24",
            "type": "travel",
            "value": 3,
            "vehicle": "truck"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 3,
      "turnIndex": 2,
      "timestamp": 1745371174327,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T9",
            "type": "travel",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "move",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 3,
      "turnIndex": 2,
      "timestamp": 1745371234365,
      "data": {
        "path": [
          56,
          3,
          47,
          57
        ],
        "travelCards": [
          "T9"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 4,
      "turnIndex": 0,
      "timestamp": 1745371239858,
      "data": {
        "eventId": "global-event-9",
        "eventName": "Riots",
        "eventEffect": "riots_discard"
      }
    },
    {
      "type": "riots_discard_cube",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 4,
      "turnIndex": 0,
      "timestamp": 1745371239859,
      "data": {
        "cubeType": "artha"
      }
    },
    {
      "type": "riots_discard_om",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 4,
      "turnIndex": 0,
      "timestamp": 1745371239859,
      "data": {
        "jyotirlinga": 57
      }
    },
    {
      "type": "endTurn",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 4,
      "turnIndex": 0,
      "timestamp": 1745371239859,
      "data": {
        "nextPlayerId": "1bYYOhbOLDCYcw7XAAAF",
        "newRound": true,
        "roundCount": 4,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 4,
      "turnIndex": 0,
      "timestamp": 1745371241876,
      "data": {
        "path": [
          13,
          62,
          64,
          15,
          5
        ],
        "travelCards": [
          "T8",
          "T4",
          "T14"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "collectJourney",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 4,
      "turnIndex": 0,
      "timestamp": 1745371244879,
      "data": {
        "journeyType": "outer",
        "journeyCardId": "JO2",
        "omRequirement": 1
      }
    },
    {
      "type": "endTurn",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 4,
      "turnIndex": 1,
      "timestamp": 1745371247880,
      "data": {
        "nextPlayerId": "uAhJlpzCmAYloGEHAAAD",
        "newRound": false,
        "roundCount": 4,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 4,
      "turnIndex": 1,
      "timestamp": 1745371339898,
      "data": {
        "path": [
          59,
          23
        ],
        "travelCards": [
          "T2"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 4,
      "turnIndex": 1,
      "timestamp": 1745371355053,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T19",
            "type": "travel",
            "value": 2,
            "vehicle": "rickshaw"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 4,
      "turnIndex": 1,
      "timestamp": 1745371358328,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T15",
            "type": "travel",
            "value": 1,
            "vehicle": "trek"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 4,
      "turnIndex": 2,
      "timestamp": 1745371360684,
      "data": {
        "nextPlayerId": "NxoCqjvAdOv4zytHAAAB",
        "newRound": false,
        "roundCount": 4,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 4,
      "turnIndex": 2,
      "timestamp": 1745371372948,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T17",
            "type": "travel",
            "value": 2,
            "vehicle": "bus"
          },
          {
            "id": "T6",
            "type": "travel",
            "value": 2,
            "vehicle": "car"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 4,
      "turnIndex": 2,
      "timestamp": 1745371380906,
      "data": {
        "path": [
          57,
          47,
          3
        ],
        "travelCards": [
          "T17"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 5,
      "turnIndex": 0,
      "timestamp": 1745371382719,
      "data": {
        "eventId": "global-event-12",
        "eventName": "Footpath Reverie",
        "eventEffect": "footpath_reverie_reward"
      }
    },
    {
      "type": "endTurn",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 5,
      "turnIndex": 0,
      "timestamp": 1745371382719,
      "data": {
        "nextPlayerId": "1bYYOhbOLDCYcw7XAAAF",
        "newRound": true,
        "roundCount": 5,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 5,
      "turnIndex": 0,
      "timestamp": 1745371385166,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T3",
            "type": "travel",
            "value": 1,
            "vehicle": "trek"
          },
          {
            "id": "T13",
            "type": "travel",
            "value": 1,
            "vehicle": "camel"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 5,
      "turnIndex": 1,
      "timestamp": 1745371388168,
      "data": {
        "nextPlayerId": "uAhJlpzCmAYloGEHAAAD",
        "newRound": false,
        "roundCount": 5,
        "turnCount": null
      }
    },
    {
      "type": "travel_card_reward",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 5,
      "turnIndex": 1,
      "timestamp": 1745371501314,
      "data": {
        "vehicle": "trek",
        "effect": "footpath_reverie_reward",
        "innerPoints": 5
      }
    },
    {
      "type": "move",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 5,
      "turnIndex": 1,
      "timestamp": 1745371501314,
      "data": {
        "path": [
          23,
          22,
          21,
          63,
          61,
          62,
          13
        ],
        "travelCards": [
          "T21",
          "T19",
          "T15"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "collectJourney",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 5,
      "turnIndex": 1,
      "timestamp": 1745371506748,
      "data": {
        "journeyType": "outer",
        "journeyCardId": "JO7",
        "omRequirement": 1
      }
    },
    {
      "type": "endTurn",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 5,
      "turnIndex": 2,
      "timestamp": 1745371511010,
      "data": {
        "nextPlayerId": "NxoCqjvAdOv4zytHAAAB",
        "newRound": false,
        "roundCount": 5,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 5,
      "turnIndex": 2,
      "timestamp": 1745371522350,
      "data": {
        "path": [
          3,
          2,
          1
        ],
        "travelCards": [
          "T20"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "collectJourney",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 5,
      "turnIndex": 2,
      "timestamp": 1745371527125,
      "data": {
        "journeyType": "inner",
        "journeyCardId": "JI1",
        "omRequirement": 1
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 6,
      "turnIndex": 0,
      "timestamp": 1745371528758,
      "data": {
        "eventId": "global-event-5",
        "eventName": "Bountiful Bhandara",
        "eventEffect": "draw_2_cubes_bonus_5_outer"
      }
    },
    {
      "type": "endTurn",
      "playerId": "NxoCqjvAdOv4zytHAAAB",
      "playerName": "Rudra",
      "roundCount": 6,
      "turnIndex": 0,
      "timestamp": 1745371528759,
      "data": {
        "nextPlayerId": "1bYYOhbOLDCYcw7XAAAF",
        "newRound": true,
        "roundCount": 6,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 6,
      "turnIndex": 0,
      "timestamp": 1745371530906,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T12",
            "type": "travel",
            "value": 3,
            "vehicle": "truck"
          },
          {
            "id": "T11",
            "type": "travel",
            "value": 3,
            "vehicle": "train"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 6,
      "turnIndex": 0,
      "timestamp": 1745371533909,
      "data": {
        "path": [
          5,
          15,
          18,
          53
        ],
        "travelCards": [
          "T12"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "1bYYOhbOLDCYcw7XAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 6,
      "turnIndex": 1,
      "timestamp": 1745371536911,
      "data": {
        "nextPlayerId": "uAhJlpzCmAYloGEHAAAD",
        "newRound": false,
        "roundCount": 6,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 6,
      "turnIndex": 1,
      "timestamp": 1745371607122,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T7",
            "type": "travel",
            "value": 2,
            "vehicle": "rickshaw"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 6,
      "turnIndex": 1,
      "timestamp": 1745371642914,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T23",
            "type": "travel",
            "value": 3,
            "vehicle": "train"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "uAhJlpzCmAYloGEHAAAD",
      "playerName": "Yatri",
      "roundCount": 6,
      "turnIndex": 2,
      "timestamp": 1745371644542,
      "data": {
        "nextPlayerId": "NxoCqjvAdOv4zytHAAAB",
        "newRound": false,
        "roundCount": 6,
        "turnCount": null
      }
    },
    {
      "type": "GAME_RESUMED",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 6,
      "turnIndex": 1,
      "timestamp": 1745371901412,
      "data": {
        "timestamp": 1745371901412,
        "loadedStateRoundCount": 6,
        "playerCount": 3
      }
    },
    {
      "type": "move",
      "playerId": "3bMxuQUTzs0_ienKAAAB",
      "playerName": "Yatri",
      "roundCount": 6,
      "turnIndex": 1,
      "timestamp": 1745371917446,
      "data": {
        "path": [
          13,
          12,
          50
        ],
        "travelCards": [
          "T7"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "3bMxuQUTzs0_ienKAAAB",
      "playerName": "Yatri",
      "roundCount": 6,
      "turnIndex": 2,
      "timestamp": 1745371919695,
      "data": {
        "nextPlayerId": "hLrmQIZWfMJiPRRSAAAD",
        "newRound": false,
        "roundCount": 6,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "hLrmQIZWfMJiPRRSAAAD",
      "playerName": "Rudra",
      "roundCount": 6,
      "turnIndex": 2,
      "timestamp": 1745371927056,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T16",
            "type": "travel",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T1",
            "type": "travel",
            "value": 1,
            "vehicle": "camel"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "hLrmQIZWfMJiPRRSAAAD",
      "playerName": "Rudra",
      "roundCount": 6,
      "turnIndex": 2,
      "timestamp": 1745371935149,
      "data": {
        "path": [
          1,
          2,
          3,
          47,
          57
        ],
        "travelCards": [
          "T24",
          "T16"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 7,
      "turnIndex": 0,
      "timestamp": 1745371939374,
      "data": {
        "eventId": "global-event-6",
        "eventName": "Turbulent Skies",
        "eventEffect": "no_airport_travel"
      }
    },
    {
      "type": "endTurn",
      "playerId": "hLrmQIZWfMJiPRRSAAAD",
      "playerName": "Rudra",
      "roundCount": 7,
      "turnIndex": 0,
      "timestamp": 1745371939374,
      "data": {
        "nextPlayerId": "1bYYOhbOLDCYcw7XAAAF",
        "newRound": true,
        "roundCount": 7,
        "turnCount": null
      }
    },
    {
      "type": "GAME_RESUMED",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 7,
      "turnIndex": 0,
      "timestamp": 1745372082063,
      "data": {
        "timestamp": 1745372082063,
        "loadedStateRoundCount": 7,
        "playerCount": 3
      }
    },
    {
      "type": "move",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 7,
      "turnIndex": 0,
      "timestamp": 1745372084077,
      "data": {
        "path": [
          53,
          18,
          28,
          21,
          20,
          51
        ],
        "travelCards": [
          "T3",
          "T13",
          "T11"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 7,
      "turnIndex": 0,
      "timestamp": 1745372087080,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T5",
            "type": "travel",
            "value": 2,
            "vehicle": "bus"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 7,
      "turnIndex": 1,
      "timestamp": 1745372090082,
      "data": {
        "nextPlayerId": "2dRRyh91WGmbpjEYAAAD",
        "newRound": false,
        "roundCount": 7,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 7,
      "turnIndex": 1,
      "timestamp": 1745372151563,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T14",
            "type": "travel",
            "value": 1,
            "vehicle": "horse"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 7,
      "turnIndex": 1,
      "timestamp": 1745372165766,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T15",
            "type": "travel",
            "value": 1,
            "vehicle": "trek"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 7,
      "turnIndex": 1,
      "timestamp": 1745372174674,
      "data": {
        "path": [
          50,
          12
        ],
        "travelCards": [
          "T14"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 7,
      "turnIndex": 2,
      "timestamp": 1745372177122,
      "data": {
        "nextPlayerId": "8IpCgxVeBqhh5cyIAAAB",
        "newRound": false,
        "roundCount": 7,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 7,
      "turnIndex": 2,
      "timestamp": 1745372186181,
      "data": {
        "path": [
          57,
          47
        ],
        "travelCards": [
          "T1"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "collectJourney",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 7,
      "turnIndex": 2,
      "timestamp": 1745372192138,
      "data": {
        "journeyType": "outer",
        "journeyCardId": "JO26",
        "omRequirement": 1
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 8,
      "turnIndex": 0,
      "timestamp": 1745372195630,
      "data": {
        "eventId": "global-event-29",
        "eventName": "Solar South",
        "eventEffect": "solar_south"
      }
    },
    {
      "type": "endTurn",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 8,
      "turnIndex": 0,
      "timestamp": 1745372195630,
      "data": {
        "nextPlayerId": "qfblzx7ZaXXW96yxAAAF",
        "newRound": true,
        "roundCount": 8,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 8,
      "turnIndex": 0,
      "timestamp": 1745372197694,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T10",
            "type": "travel",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T8",
            "type": "travel",
            "value": 2,
            "vehicle": "motorbike"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 8,
      "turnIndex": 0,
      "timestamp": 1745372200699,
      "data": {
        "path": [
          51,
          20,
          21,
          63,
          62,
          11,
          10,
          49
        ],
        "travelCards": [
          "T5",
          "T10",
          "T8"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 8,
      "turnIndex": 1,
      "timestamp": 1745372203700,
      "data": {
        "nextPlayerId": "2dRRyh91WGmbpjEYAAAD",
        "newRound": false,
        "roundCount": 8,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 8,
      "turnIndex": 1,
      "timestamp": 1745372435697,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T21",
            "type": "travel",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 8,
      "turnIndex": 1,
      "timestamp": 1745372461286,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T17",
            "type": "travel",
            "value": 2,
            "vehicle": "bus"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 8,
      "turnIndex": 1,
      "timestamp": 1745372491740,
      "data": {
        "path": [
          12,
          11,
          62,
          65,
          31,
          32,
          58
        ],
        "travelCards": [
          "T23",
          "T21"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 8,
      "turnIndex": 2,
      "timestamp": 1745372498512,
      "data": {
        "nextPlayerId": "8IpCgxVeBqhh5cyIAAAB",
        "newRound": false,
        "roundCount": 8,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 8,
      "turnIndex": 2,
      "timestamp": 1745372512517,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T12",
            "type": "travel",
            "value": 3,
            "vehicle": "truck"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 8,
      "turnIndex": 2,
      "timestamp": 1745372538441,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T19",
            "type": "travel",
            "value": 2,
            "vehicle": "rickshaw"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 8,
      "turnIndex": 2,
      "timestamp": 1745372543819,
      "data": {
        "path": [
          47,
          46,
          45,
          44,
          43,
          60
        ],
        "travelCards": [
          "T6",
          "T12"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 9,
      "turnIndex": 0,
      "timestamp": 1745372552882,
      "data": {
        "eventId": "global-event-14",
        "eventName": "Desert Caravan",
        "eventEffect": "desert_caravan_reward"
      }
    },
    {
      "type": "endTurn",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 9,
      "turnIndex": 0,
      "timestamp": 1745372552882,
      "data": {
        "nextPlayerId": "qfblzx7ZaXXW96yxAAAF",
        "newRound": true,
        "roundCount": 9,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 9,
      "turnIndex": 0,
      "timestamp": 1745372554815,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T2",
            "type": "travel",
            "value": 1,
            "vehicle": "horse"
          },
          {
            "id": "T7",
            "type": "travel",
            "value": 2,
            "vehicle": "rickshaw"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 9,
      "turnIndex": 1,
      "timestamp": 1745372557818,
      "data": {
        "nextPlayerId": "2dRRyh91WGmbpjEYAAAD",
        "newRound": false,
        "roundCount": 9,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 9,
      "turnIndex": 1,
      "timestamp": 1745372731805,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T9",
            "type": "travel",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 9,
      "turnIndex": 1,
      "timestamp": 1745372751771,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T20",
            "type": "travel",
            "value": 2,
            "vehicle": "motorbike"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "move",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 9,
      "turnIndex": 1,
      "timestamp": 1745372762034,
      "data": {
        "path": [
          58,
          32,
          31,
          65,
          37,
          28
        ],
        "travelCards": [
          "T9",
          "T20"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "endTurn",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 9,
      "turnIndex": 2,
      "timestamp": 1745372767979,
      "data": {
        "nextPlayerId": "8IpCgxVeBqhh5cyIAAAB",
        "newRound": false,
        "roundCount": 9,
        "turnCount": null
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 9,
      "turnIndex": 2,
      "timestamp": 1745372792343,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T23",
            "type": "travel",
            "value": 3,
            "vehicle": "train"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "PICK_DECK_CARDS",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 9,
      "turnIndex": 2,
      "timestamp": 1745372793490,
      "data": {
        "cardType": "travel",
        "drawnCards": [
          {
            "id": "T12",
            "type": "travel",
            "value": 3,
            "vehicle": "truck"
          }
        ],
        "pickFromTop": true
      }
    },
    {
      "type": "move",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 9,
      "turnIndex": 2,
      "timestamp": 1745372801492,
      "data": {
        "path": [
          60,
          43,
          44
        ],
        "travelCards": [
          "T19"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 10,
      "turnIndex": 0,
      "timestamp": 1745372804913,
      "data": {
        "eventId": "global-event-7",
        "eventName": "Election Campaigns",
        "eventEffect": "double_trade_no_travel"
      }
    },
    {
      "type": "endTurn",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 10,
      "turnIndex": 0,
      "timestamp": 1745372804913,
      "data": {
        "nextPlayerId": "qfblzx7ZaXXW96yxAAAF",
        "newRound": true,
        "roundCount": 10,
        "turnCount": null
      }
    },
    {
      "type": "endTurn",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 10,
      "turnIndex": 1,
      "timestamp": 1745372806628,
      "data": {
        "nextPlayerId": "2dRRyh91WGmbpjEYAAAD",
        "newRound": false,
        "roundCount": 10,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 10,
      "turnIndex": 1,
      "timestamp": 1745372896485,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T22",
            "type": "travel",
            "value": 3,
            "vehicle": "boat"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 10,
      "turnIndex": 1,
      "timestamp": 1745372927261,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T8",
            "type": "travel",
            "value": 2,
            "vehicle": "motorbike"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 10,
      "turnIndex": 2,
      "timestamp": 1745372929578,
      "data": {
        "nextPlayerId": "8IpCgxVeBqhh5cyIAAAB",
        "newRound": false,
        "roundCount": 10,
        "turnCount": null
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 10,
      "turnIndex": 2,
      "timestamp": 1745372936857,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T18",
            "type": "travel",
            "value": 2,
            "vehicle": "car"
          },
          {
            "id": "T5",
            "type": "travel",
            "value": 2,
            "vehicle": "bus"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 11,
      "turnIndex": 0,
      "timestamp": 1745372950644,
      "data": {
        "eventId": "global-event-4",
        "eventName": "Drought of Spirits",
        "eventEffect": "no_inner_journey_cards"
      }
    },
    {
      "type": "endTurn",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 11,
      "turnIndex": 0,
      "timestamp": 1745372950644,
      "data": {
        "nextPlayerId": "qfblzx7ZaXXW96yxAAAF",
        "newRound": true,
        "roundCount": 11,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 11,
      "turnIndex": 0,
      "timestamp": 1745372952532,
      "data": {
        "path": [
          49,
          10,
          11
        ],
        "travelCards": [
          "T7"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 11,
      "turnIndex": 0,
      "timestamp": 1745372955535,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T21",
            "type": "travel",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 11,
      "turnIndex": 1,
      "timestamp": 1745372958536,
      "data": {
        "nextPlayerId": "2dRRyh91WGmbpjEYAAAD",
        "newRound": false,
        "roundCount": 11,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 11,
      "turnIndex": 1,
      "timestamp": 1745372994483,
      "data": {
        "path": [
          28,
          21,
          22
        ],
        "travelCards": [
          "T17"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "collectJourney",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 11,
      "turnIndex": 1,
      "timestamp": 1745372998477,
      "data": {
        "journeyType": "outer",
        "journeyCardId": "JO10",
        "omRequirement": 1
      }
    },
    {
      "type": "endTurn",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 11,
      "turnIndex": 2,
      "timestamp": 1745373002100,
      "data": {
        "nextPlayerId": "8IpCgxVeBqhh5cyIAAAB",
        "newRound": false,
        "roundCount": 11,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 11,
      "turnIndex": 2,
      "timestamp": 1745373015984,
      "data": {
        "path": [
          44,
          45,
          31,
          30
        ],
        "travelCards": [
          "T23"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 12,
      "turnIndex": 0,
      "timestamp": 1745373025940,
      "data": {
        "eventId": "global-event-18",
        "eventName": "Hop on Hop off",
        "eventEffect": "hop_on_hop_off_reward"
      }
    },
    {
      "type": "endTurn",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 12,
      "turnIndex": 0,
      "timestamp": 1745373025940,
      "data": {
        "nextPlayerId": "qfblzx7ZaXXW96yxAAAF",
        "newRound": true,
        "roundCount": 12,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 12,
      "turnIndex": 0,
      "timestamp": 1745373027570,
      "data": {
        "path": [
          11,
          7
        ],
        "travelCards": [
          "T2"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 12,
      "turnIndex": 0,
      "timestamp": 1745373030572,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T11",
            "type": "travel",
            "value": 3,
            "vehicle": "train"
          },
          {
            "id": "T4",
            "type": "travel",
            "value": 1,
            "vehicle": "cycle"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 12,
      "turnIndex": 1,
      "timestamp": 1745373033574,
      "data": {
        "nextPlayerId": "2dRRyh91WGmbpjEYAAAD",
        "newRound": false,
        "roundCount": 12,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 12,
      "turnIndex": 1,
      "timestamp": 1745373045107,
      "data": {
        "path": [
          22,
          21,
          63,
          64,
          15
        ],
        "travelCards": [
          "T22",
          "T15"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "collectJourney",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 12,
      "turnIndex": 1,
      "timestamp": 1745373069338,
      "data": {
        "journeyType": "inner",
        "journeyCardId": "JI8",
        "omRequirement": 1
      }
    },
    {
      "type": "FINAL_ROUND_TRIGGERED",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 12,
      "turnIndex": 1,
      "timestamp": 1745373069338,
      "data": {
        "winCondition": "SCORE_THRESHOLD",
        "playerScore": 100,
        "finalRoundStarter": 1,
        "finalRoundEnd": 0
      }
    },
    {
      "type": "endTurn",
      "playerId": "2dRRyh91WGmbpjEYAAAD",
      "playerName": "Yatri",
      "roundCount": 12,
      "turnIndex": 2,
      "timestamp": 1745373085213,
      "data": {
        "nextPlayerId": "8IpCgxVeBqhh5cyIAAAB",
        "newRound": false,
        "roundCount": 12,
        "turnCount": null
      }
    },
    {
      "type": "travel_card_reward",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 12,
      "turnIndex": 2,
      "timestamp": 1745*********,
      "data": {
        "vehicle": "bus",
        "effect": "hop_on_hop_off_reward",
        "outerPoints": 5
      }
    },
    {
      "type": "move",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 12,
      "turnIndex": 2,
      "timestamp": 1745*********,
      "data": {
        "path": [
          30,
          31,
          65,
          62,
          13,
          14
        ],
        "travelCards": [
          "T12",
          "T5"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "trade",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 12,
      "turnIndex": 2,
      "timestamp": 1745373174863,
      "data": {
        "cubesTraded": [
          "karma"
        ],
        "cubeReceived": "artha",
        "count": 1
      }
    },
    {
      "type": "global_event_drawn",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 13,
      "turnIndex": 0,
      "timestamp": 1745373196793,
      "data": {
        "eventId": "global-event-21",
        "eventName": "Heavy Haul",
        "eventEffect": "heavy_haul_reward"
      }
    },
    {
      "type": "endTurn",
      "playerId": "8IpCgxVeBqhh5cyIAAAB",
      "playerName": "Rudra",
      "roundCount": 13,
      "turnIndex": 0,
      "timestamp": 1745373196793,
      "data": {
        "nextPlayerId": "qfblzx7ZaXXW96yxAAAF",
        "newRound": true,
        "roundCount": 13,
        "turnCount": null
      }
    },
    {
      "type": "move",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 13,
      "turnIndex": 0,
      "timestamp": 1745373199184,
      "data": {
        "path": [
          7,
          11,
          62,
          19
        ],
        "travelCards": [
          "T21"
        ],
        "extraHopCards": []
      }
    },
    {
      "type": "PICK_FACE_UP_CARDS",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 13,
      "turnIndex": 0,
      "timestamp": 1745373202187,
      "data": {
        "cardType": "travel",
        "pickedCards": [
          {
            "id": "T6",
            "type": "travel",
            "value": 2,
            "vehicle": "car"
          }
        ],
        "pickedFromFaceUp": true
      }
    },
    {
      "type": "endTurn",
      "playerId": "qfblzx7ZaXXW96yxAAAF",
      "playerName": "MysticWayfarer",
      "roundCount": 13,
      "turnIndex": 1,
      "timestamp": 1745373205188,
      "data": {
        "nextPlayerId": "2dRRyh91WGmbpjEYAAAD",
        "newRound": false,
        "roundCount": 13,
        "turnCount": null
      }
    },
    {
      "type": "GAME_OVER",
      "playerId": "system",
      "playerName": "System",
      "roundCount": 13,
      "turnIndex": 1,
      "timestamp": 1745373205191,
      "data": {
        "winner": {
          "id": "2dRRyh91WGmbpjEYAAAD",
          "name": "Yatri",
          "outerScore": 44,
          "innerScore": 56,
          "totalScore": 100,
          "omTotal": 4,
          "winByOm": false,
          "winByScore": true
        },
        "winCondition": "SCORE_THRESHOLD",
        "totalRounds": 13,
        "players": [
          {
            "id": "qfblzx7ZaXXW96yxAAAF",
            "name": "MysticWayfarer",
            "outerScore": 24,
            "innerScore": 0,
            "totalScore": 24,
            "omTotal": 4
          },
          {
            "id": "2dRRyh91WGmbpjEYAAAD",
            "name": "Yatri",
            "outerScore": 44,
            "innerScore": 56,
            "totalScore": 100,
            "omTotal": 4
          },
          {
            "id": "8IpCgxVeBqhh5cyIAAAB",
            "name": "Rudra",
            "outerScore": 52,
            "innerScore": 24,
            "totalScore": 76,
            "omTotal": 4
          }
        ]
      }
    }
  ],
  "roundSummaries": [
    {
      "roundNumber": 1,
      "timestamp": 1745370732669,
      "players": [
        {
          "id": "1bYYOhbOLDCYcw7XAAAF",
          "name": "MysticWayfarer",
          "position": 52,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 0,
            "inner": 0,
            "total": 0
          },
          "collectedJourneys": 0,
          "gained": null
        },
        {
          "id": "uAhJlpzCmAYloGEHAAAD",
          "name": "Yatri",
          "position": 55,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 0,
            "inner": 0,
            "total": 0
          },
          "collectedJourneys": 0,
          "gained": null
        },
        {
          "id": "NxoCqjvAdOv4zytHAAAB",
          "name": "Rudra",
          "position": 54,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 0,
            "inner": 0,
            "total": 0
          },
          "collectedJourneys": 0,
          "gained": null
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T13",
            "value": 1,
            "vehicle": "camel"
          },
          {
            "id": "T10",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T16",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T4",
            "value": 1,
            "vehicle": "cycle"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO4",
            "locationId": 9,
            "requiredCubes": {},
            "points": 20
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO2",
            "locationId": 5,
            "requiredCubes": {},
            "points": 24
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI18",
            "locationId": 38,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-26",
        "name": "Engineer's Precision",
        "effect": "engineers_precision_reward"
      }
    },
    {
      "roundNumber": 2,
      "timestamp": 1745370882802,
      "players": [
        {
          "id": "1bYYOhbOLDCYcw7XAAAF",
          "name": "MysticWayfarer",
          "position": 5,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 0
          },
          "energyCubes": {
            "total": 1,
            "bhakti": 0,
            "gnana": 0,
            "karma": 1,
            "artha": 0
          },
          "scores": {
            "outer": 0,
            "inner": 0,
            "total": 0
          },
          "collectedJourneys": 0,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": null,
              "karma": 1,
              "artha": null
            },
            "journeyCards": null
          }
        },
        {
          "id": "uAhJlpzCmAYloGEHAAAD",
          "name": "Yatri",
          "position": 31,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 0
          },
          "energyCubes": {
            "total": 1,
            "bhakti": 0,
            "gnana": 0,
            "karma": 1,
            "artha": 0
          },
          "scores": {
            "outer": 0,
            "inner": 0,
            "total": 0
          },
          "collectedJourneys": 0,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": null,
              "karma": 1,
              "artha": null
            },
            "journeyCards": null
          }
        },
        {
          "id": "NxoCqjvAdOv4zytHAAAB",
          "name": "Rudra",
          "position": 9,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 0,
            "total": 20
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": null,
            "energyCubes": null,
            "journeyCards": 1
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T10",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T4",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T14",
            "value": 1,
            "vehicle": "horse"
          },
          {
            "id": "T12",
            "value": 3,
            "vehicle": "truck"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO2",
            "locationId": 5,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JO26",
            "locationId": 47,
            "requiredCubes": {},
            "points": 27
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI18",
            "locationId": 38,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-25",
        "name": "Pilgrim's Grace",
        "effect": "pilgrims_grace_reward"
      }
    },
    {
      "roundNumber": 3,
      "timestamp": 1745371040099,
      "players": [
        {
          "id": "1bYYOhbOLDCYcw7XAAAF",
          "name": "MysticWayfarer",
          "position": 4,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 0
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 0,
            "gnana": 0,
            "karma": 1,
            "artha": 1
          },
          "scores": {
            "outer": 0,
            "inner": 0,
            "total": 0
          },
          "collectedJourneys": 0,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": null,
              "karma": null,
              "artha": 1
            },
            "journeyCards": null
          }
        },
        {
          "id": "uAhJlpzCmAYloGEHAAAD",
          "name": "Yatri",
          "position": 38,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 0,
            "inner": 1
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 0,
            "inner": 24,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": null,
            "energyCubes": null,
            "journeyCards": 1
          }
        },
        {
          "id": "NxoCqjvAdOv4zytHAAAB",
          "name": "Rudra",
          "position": 56,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 0,
            "total": 20
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": 1,
            "energyCubes": null,
            "journeyCards": null
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T4",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T14",
            "value": 1,
            "vehicle": "horse"
          },
          {
            "id": "T12",
            "value": 3,
            "vehicle": "truck"
          },
          {
            "id": "T18",
            "value": 2,
            "vehicle": "car"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO2",
            "locationId": 5,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JO26",
            "locationId": 47,
            "requiredCubes": {},
            "points": 27
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI1",
            "locationId": 1,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-19",
        "name": "Bullet Train",
        "effect": "bullet_train_reward"
      }
    },
    {
      "roundNumber": 4,
      "timestamp": 1745371239858,
      "players": [
        {
          "id": "1bYYOhbOLDCYcw7XAAAF",
          "name": "MysticWayfarer",
          "position": 13,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 0
          },
          "energyCubes": {
            "total": 3,
            "bhakti": 0,
            "gnana": 0,
            "karma": 1,
            "artha": 2
          },
          "scores": {
            "outer": 0,
            "inner": 0,
            "total": 0
          },
          "collectedJourneys": 0,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": null,
              "karma": null,
              "artha": 1
            },
            "journeyCards": null
          }
        },
        {
          "id": "uAhJlpzCmAYloGEHAAAD",
          "name": "Yatri",
          "position": 59,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 1
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 0,
            "inner": 24,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": 1,
            "energyCubes": null,
            "journeyCards": null
          }
        },
        {
          "id": "NxoCqjvAdOv4zytHAAAB",
          "name": "Rudra",
          "position": 57,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 2,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 0,
            "total": 20
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": 1,
            "energyCubes": null,
            "journeyCards": null
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T12",
            "value": 3,
            "vehicle": "truck"
          },
          {
            "id": "T6",
            "value": 2,
            "vehicle": "car"
          },
          {
            "id": "T11",
            "value": 3,
            "vehicle": "train"
          },
          {
            "id": "T19",
            "value": 2,
            "vehicle": "rickshaw"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO2",
            "locationId": 5,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JO26",
            "locationId": 47,
            "requiredCubes": {},
            "points": 27
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI1",
            "locationId": 1,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-31",
        "name": "Himalayan NE",
        "effect": "himalayan_ne"
      }
    },
    {
      "roundNumber": 5,
      "timestamp": 1745371382719,
      "players": [
        {
          "id": "1bYYOhbOLDCYcw7XAAAF",
          "name": "MysticWayfarer",
          "position": 5,
          "hand": {
            "total": 0,
            "travel": 0
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": null,
            "energyCubes": null,
            "journeyCards": 1
          }
        },
        {
          "id": "uAhJlpzCmAYloGEHAAAD",
          "name": "Yatri",
          "position": 23,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 0,
            "inner": 1
          },
          "energyCubes": {
            "total": 1,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 0,
            "inner": 24,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": null,
              "karma": null,
              "artha": 1
            },
            "journeyCards": null
          }
        },
        {
          "id": "NxoCqjvAdOv4zytHAAAB",
          "name": "Rudra",
          "position": 3,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 1,
            "bhakti": 0,
            "gnana": 1,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 0,
            "total": 20
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": 1,
              "karma": null,
              "artha": null
            },
            "journeyCards": null
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T12",
            "value": 3,
            "vehicle": "truck"
          },
          {
            "id": "T11",
            "value": 3,
            "vehicle": "train"
          },
          {
            "id": "T3",
            "value": 1,
            "vehicle": "trek"
          },
          {
            "id": "T13",
            "value": 1,
            "vehicle": "camel"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO26",
            "locationId": 47,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO7",
            "locationId": 13,
            "requiredCubes": {},
            "points": 20
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI1",
            "locationId": 1,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-9",
        "name": "Riots",
        "effect": "riots_discard"
      }
    },
    {
      "roundNumber": 6,
      "timestamp": 1745371528757,
      "players": [
        {
          "id": "1bYYOhbOLDCYcw7XAAAF",
          "name": "MysticWayfarer",
          "position": 5,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": null
        },
        {
          "id": "uAhJlpzCmAYloGEHAAAD",
          "name": "Yatri",
          "position": 13,
          "hand": {
            "total": 0,
            "travel": 0
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 29,
            "total": 49
          },
          "collectedJourneys": 2,
          "gained": {
            "omTemp": null,
            "energyCubes": null,
            "journeyCards": 1
          }
        },
        {
          "id": "NxoCqjvAdOv4zytHAAAB",
          "name": "Rudra",
          "position": 1,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 24,
            "total": 44
          },
          "collectedJourneys": 2,
          "gained": {
            "omTemp": null,
            "energyCubes": null,
            "journeyCards": 1
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T12",
            "value": 3,
            "vehicle": "truck"
          },
          {
            "id": "T11",
            "value": 3,
            "vehicle": "train"
          },
          {
            "id": "T23",
            "value": 3,
            "vehicle": "train"
          },
          {
            "id": "T1",
            "value": 1,
            "vehicle": "camel"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO26",
            "locationId": 47,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO10",
            "locationId": 22,
            "requiredCubes": {},
            "points": 24
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-12",
        "name": "Footpath Reverie",
        "effect": "footpath_reverie_reward"
      }
    },
    {
      "roundNumber": 7,
      "timestamp": 1745371939373,
      "players": [
        {
          "id": "1bYYOhbOLDCYcw7XAAAF",
          "name": "MysticWayfarer",
          "position": 53,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": 1,
            "energyCubes": {
              "total": 2,
              "bhakti": 1,
              "gnana": null,
              "karma": null,
              "artha": 1
            },
            "journeyCards": null
          }
        },
        {
          "id": "3bMxuQUTzs0_ienKAAAB",
          "name": "Yatri",
          "position": 50,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 0,
            "gnana": 2,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 29,
            "total": 49
          },
          "collectedJourneys": 2,
          "gained": null
        },
        {
          "id": "hLrmQIZWfMJiPRRSAAAD",
          "name": "Rudra",
          "position": 57,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 0,
            "gnana": 0,
            "karma": 2,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 24,
            "total": 44
          },
          "collectedJourneys": 2,
          "gained": null
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T10",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T22",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T5",
            "value": 2,
            "vehicle": "bus"
          },
          {
            "id": "T21",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO26",
            "locationId": 47,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO10",
            "locationId": 22,
            "requiredCubes": {},
            "points": 24
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-5",
        "name": "Bountiful Bhandara",
        "effect": "draw_2_cubes_bonus_5_outer"
      }
    },
    {
      "roundNumber": 8,
      "timestamp": 1745372195629,
      "players": [
        {
          "id": "qfblzx7ZaXXW96yxAAAF",
          "name": "MysticWayfarer",
          "position": 51,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 2,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": null
        },
        {
          "id": "2dRRyh91WGmbpjEYAAAD",
          "name": "Yatri",
          "position": 12,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 3,
            "bhakti": 1,
            "gnana": 2,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 29,
            "total": 49
          },
          "collectedJourneys": 2,
          "gained": null
        },
        {
          "id": "8IpCgxVeBqhh5cyIAAAB",
          "name": "Rudra",
          "position": 47,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 2,
            "inner": 1
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 47,
            "inner": 24,
            "total": 71
          },
          "collectedJourneys": 3,
          "gained": null
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T10",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T22",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T21",
            "value": 3,
            "vehicle": "helicopter"
          },
          {
            "id": "T8",
            "value": 2,
            "vehicle": "motorbike"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO10",
            "locationId": 22,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JO16",
            "locationId": 31,
            "requiredCubes": {},
            "points": 27
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-6",
        "name": "Turbulent Skies",
        "effect": "no_airport_travel"
      }
    },
    {
      "roundNumber": 9,
      "timestamp": 1745372552881,
      "players": [
        {
          "id": "qfblzx7ZaXXW96yxAAAF",
          "name": "MysticWayfarer",
          "position": 49,
          "hand": {
            "total": 0,
            "travel": 0
          },
          "omTemp": 3,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": 1,
            "energyCubes": null,
            "journeyCards": null
          }
        },
        {
          "id": "2dRRyh91WGmbpjEYAAAD",
          "name": "Yatri",
          "position": 58,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 2,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 3,
            "bhakti": 1,
            "gnana": 2,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 29,
            "total": 49
          },
          "collectedJourneys": 2,
          "gained": {
            "omTemp": 1,
            "energyCubes": null,
            "journeyCards": null
          }
        },
        {
          "id": "8IpCgxVeBqhh5cyIAAAB",
          "name": "Rudra",
          "position": 60,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 2,
            "inner": 1
          },
          "energyCubes": {
            "total": 0,
            "bhakti": 0,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 47,
            "inner": 24,
            "total": 71
          },
          "collectedJourneys": 3,
          "gained": {
            "omTemp": 1,
            "energyCubes": null,
            "journeyCards": null
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T22",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T2",
            "value": 1,
            "vehicle": "horse"
          },
          {
            "id": "T9",
            "value": 3,
            "vehicle": "helicopter"
          },
          {
            "id": "T7",
            "value": 2,
            "vehicle": "rickshaw"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO10",
            "locationId": 22,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JO16",
            "locationId": 31,
            "requiredCubes": {},
            "points": 27
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-29",
        "name": "Solar South",
        "effect": "solar_south"
      }
    },
    {
      "roundNumber": 10,
      "timestamp": 1745372804912,
      "players": [
        {
          "id": "qfblzx7ZaXXW96yxAAAF",
          "name": "MysticWayfarer",
          "position": 49,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 3,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": null
        },
        {
          "id": "2dRRyh91WGmbpjEYAAAD",
          "name": "Yatri",
          "position": 28,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 2,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 4,
            "bhakti": 1,
            "gnana": 2,
            "karma": 1,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 29,
            "total": 49
          },
          "collectedJourneys": 2,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": null,
              "karma": 1,
              "artha": null
            },
            "journeyCards": null
          }
        },
        {
          "id": "8IpCgxVeBqhh5cyIAAAB",
          "name": "Rudra",
          "position": 44,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 2,
            "inner": 1
          },
          "energyCubes": {
            "total": 1,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 47,
            "inner": 24,
            "total": 71
          },
          "collectedJourneys": 3,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": 1,
              "gnana": null,
              "karma": null,
              "artha": null
            },
            "journeyCards": null
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T22",
            "value": 3,
            "vehicle": "boat"
          },
          {
            "id": "T4",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T18",
            "value": 2,
            "vehicle": "car"
          },
          {
            "id": "T5",
            "value": 2,
            "vehicle": "bus"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO10",
            "locationId": 22,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JO16",
            "locationId": 31,
            "requiredCubes": {},
            "points": 27
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-14",
        "name": "Desert Caravan",
        "effect": "desert_caravan_reward"
      }
    },
    {
      "roundNumber": 11,
      "timestamp": 1745372950643,
      "players": [
        {
          "id": "qfblzx7ZaXXW96yxAAAF",
          "name": "MysticWayfarer",
          "position": 49,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 3,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": null
        },
        {
          "id": "2dRRyh91WGmbpjEYAAAD",
          "name": "Yatri",
          "position": 28,
          "hand": {
            "total": 4,
            "travel": 4
          },
          "omTemp": 2,
          "omSlots": {
            "outer": 1,
            "inner": 1
          },
          "energyCubes": {
            "total": 4,
            "bhakti": 1,
            "gnana": 2,
            "karma": 1,
            "artha": 0
          },
          "scores": {
            "outer": 20,
            "inner": 29,
            "total": 49
          },
          "collectedJourneys": 2,
          "gained": null
        },
        {
          "id": "8IpCgxVeBqhh5cyIAAAB",
          "name": "Rudra",
          "position": 44,
          "hand": {
            "total": 4,
            "travel": 4
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 2,
            "inner": 1
          },
          "energyCubes": {
            "total": 1,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 47,
            "inner": 24,
            "total": 71
          },
          "collectedJourneys": 3,
          "gained": null
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T4",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T14",
            "value": 1,
            "vehicle": "horse"
          },
          {
            "id": "T1",
            "value": 1,
            "vehicle": "camel"
          },
          {
            "id": "T21",
            "value": 3,
            "vehicle": "helicopter"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO10",
            "locationId": 22,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JO16",
            "locationId": 31,
            "requiredCubes": {},
            "points": 27
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-7",
        "name": "Election Campaigns",
        "effect": "double_trade_no_travel"
      }
    },
    {
      "roundNumber": 12,
      "timestamp": 1745373025939,
      "players": [
        {
          "id": "qfblzx7ZaXXW96yxAAAF",
          "name": "MysticWayfarer",
          "position": 11,
          "hand": {
            "total": 2,
            "travel": 2
          },
          "omTemp": 3,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 3,
            "bhakti": 1,
            "gnana": 1,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": 1,
              "karma": null,
              "artha": null
            },
            "journeyCards": null
          }
        },
        {
          "id": "2dRRyh91WGmbpjEYAAAD",
          "name": "Yatri",
          "position": 22,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 2,
            "inner": 1
          },
          "energyCubes": {
            "total": 3,
            "bhakti": 1,
            "gnana": 2,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 44,
            "inner": 29,
            "total": 73
          },
          "collectedJourneys": 3,
          "gained": {
            "omTemp": null,
            "energyCubes": null,
            "journeyCards": 1
          }
        },
        {
          "id": "8IpCgxVeBqhh5cyIAAAB",
          "name": "Rudra",
          "position": 30,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 2,
            "inner": 1
          },
          "energyCubes": {
            "total": 2,
            "bhakti": 2,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 47,
            "inner": 24,
            "total": 71
          },
          "collectedJourneys": 3,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": 1,
              "gnana": null,
              "karma": null,
              "artha": null
            },
            "journeyCards": null
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T4",
            "value": 1,
            "vehicle": "cycle"
          },
          {
            "id": "T14",
            "value": 1,
            "vehicle": "horse"
          },
          {
            "id": "T1",
            "value": 1,
            "vehicle": "camel"
          },
          {
            "id": "T11",
            "value": 3,
            "vehicle": "train"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO16",
            "locationId": 31,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO9",
            "locationId": 18,
            "requiredCubes": {},
            "points": 24
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI8",
            "locationId": 15,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-4",
        "name": "Drought of Spirits",
        "effect": "no_inner_journey_cards"
      }
    },
    {
      "roundNumber": 13,
      "timestamp": 1745373196793,
      "players": [
        {
          "id": "qfblzx7ZaXXW96yxAAAF",
          "name": "MysticWayfarer",
          "position": 7,
          "hand": {
            "total": 3,
            "travel": 3
          },
          "omTemp": 3,
          "omSlots": {
            "outer": 1,
            "inner": 0
          },
          "energyCubes": {
            "total": 4,
            "bhakti": 1,
            "gnana": 2,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 24,
            "inner": 0,
            "total": 24
          },
          "collectedJourneys": 1,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": 1,
              "karma": null,
              "artha": null
            },
            "journeyCards": null
          }
        },
        {
          "id": "2dRRyh91WGmbpjEYAAAD",
          "name": "Yatri",
          "position": 15,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 0,
          "omSlots": {
            "outer": 2,
            "inner": 2
          },
          "energyCubes": {
            "total": 1,
            "bhakti": 1,
            "gnana": 0,
            "karma": 0,
            "artha": 0
          },
          "scores": {
            "outer": 44,
            "inner": 56,
            "total": 100
          },
          "collectedJourneys": 4,
          "gained": {
            "omTemp": null,
            "energyCubes": null,
            "journeyCards": 1
          }
        },
        {
          "id": "8IpCgxVeBqhh5cyIAAAB",
          "name": "Rudra",
          "position": 14,
          "hand": {
            "total": 1,
            "travel": 1
          },
          "omTemp": 1,
          "omSlots": {
            "outer": 2,
            "inner": 1
          },
          "energyCubes": {
            "total": 3,
            "bhakti": 2,
            "gnana": 0,
            "karma": 0,
            "artha": 1
          },
          "scores": {
            "outer": 52,
            "inner": 24,
            "total": 76
          },
          "collectedJourneys": 3,
          "gained": {
            "omTemp": null,
            "energyCubes": {
              "total": 1,
              "bhakti": null,
              "gnana": null,
              "karma": null,
              "artha": 1
            },
            "journeyCards": null
          }
        }
      ],
      "faceUpCards": {
        "travel": [
          {
            "id": "T14",
            "value": 1,
            "vehicle": "horse"
          },
          {
            "id": "T1",
            "value": 1,
            "vehicle": "camel"
          },
          {
            "id": "T6",
            "value": 2,
            "vehicle": "car"
          },
          {
            "id": "T10",
            "value": 3,
            "vehicle": "boat"
          }
        ],
        "journeyOuter": [
          {
            "id": "JO3",
            "locationId": 6,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO6",
            "locationId": 12,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JO16",
            "locationId": 31,
            "requiredCubes": {},
            "points": 27
          },
          {
            "id": "JO9",
            "locationId": 18,
            "requiredCubes": {},
            "points": 24
          }
        ],
        "journeyInner": [
          {
            "id": "JI16",
            "locationId": 32,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI2",
            "locationId": 3,
            "requiredCubes": {},
            "points": 30
          },
          {
            "id": "JI7",
            "locationId": 14,
            "requiredCubes": {},
            "points": 24
          },
          {
            "id": "JI12",
            "locationId": 21,
            "requiredCubes": {},
            "points": 20
          }
        ]
      },
      "globalEvent": {
        "id": "global-event-18",
        "name": "Hop on Hop off",
        "effect": "hop_on_hop_off_reward"
      }
    }
  ],
  "characterDeck": [
    {
      "id": "engineer1",
      "type": "Engineer",
      "ability": {
        "gives": "karma",
        "takes": [
          "bhakti",
          "artha",
          "gnana"
        ]
      },
      "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"
    },
    {
      "id": "merchant2",
      "type": "Merchant",
      "ability": {
        "gives": "artha",
        "takes": [
          "gnana",
          "bhakti",
          "karma"
        ]
      },
      "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"
    },
    {
      "id": "pilgrim1",
      "type": "Pilgrim",
      "ability": {
        "gives": "bhakti",
        "takes": [
          "gnana",
          "artha",
          "karma"
        ]
      },
      "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"
    },
    {
      "id": "engineer2",
      "type": "Engineer",
      "ability": {
        "gives": "karma",
        "takes": [
          "bhakti",
          "artha",
          "gnana"
        ]
      },
      "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"
    },
    {
      "id": "professor1",
      "type": "Professor",
      "ability": {
        "gives": "gnana",
        "takes": [
          "bhakti",
          "artha",
          "karma"
        ]
      },
      "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"
    }
  ],
  "energyCubePile": {
    "artha": 6,
    "karma": 6,
    "gnana": 3,
    "bhakti": 4
  },
  "currentGlobalEvent": {
    "id": "global-event-21",
    "name": "Heavy Haul",
    "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes",
    "effect": "heavy_haul_reward"
  },
  "globalEventDeck": [
    {
      "id": "global-event-2",
      "name": "Diwali Distraction",
      "text": "All gain +5 inner pts but no cube pickup.",
      "effect": "gain_5_inner_no_cube_pickup"
    },
    {
      "id": "global-event-10",
      "name": "Om Meditation",
      "text": "Gain 1 om token from nearest jyotirlinga in your current region",
      "effect": "om_meditation"
    },
    {
      "id": "global-event-27",
      "name": "Frozen North",
      "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points",
      "effect": "frozen_north"
    },
    {
      "id": "global-event-32",
      "name": "Central Heart",
      "text": "If starting in Central; travel to gain 5 outer points",
      "effect": "central_heart"
    },
    {
      "id": "global-event-8",
      "name": "Triathlon",
      "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round",
      "effect": "triathlon_bonus"
    },
    {
      "id": "global-event-30",
      "name": "Breezy East",
      "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card",
      "effect": "breezy_east"
    },
    {
      "id": "global-event-11",
      "name": "Pedal Power",
      "text": "Use the Cycle travel card for 5 outer points",
      "effect": "pedal_power_reward"
    },
    {
      "id": "global-event-17",
      "name": "Top Gear",
      "text": "Use the Car travel card for 5 outer points",
      "effect": "top_gear_reward"
    },
    {
      "id": "global-event-28",
      "name": "Sandy West",
      "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card",
      "effect": "sandy_west"
    },
    {
      "id": "global-event-23",
      "name": "Merchant's Midas",
      "text": "Gain 7 outer points if a merchant trades for artha",
      "effect": "merchants_midas_reward"
    },
    {
      "id": "global-event-13",
      "name": "Steed of Valor",
      "text": "Use the Horse travel card for 5 outer points",
      "effect": "steed_of_valor_reward"
    },
    {
      "id": "global-event-15",
      "name": "Biker Gang",
      "text": "Use the Motorbike travel card for 5 outer points",
      "effect": "biker_gang_reward"
    },
    {
      "id": "global-event-20",
      "name": "Scenic Cruise",
      "text": "Use the Boat travel card for 5 outer points",
      "effect": "scenic_cruise_reward"
    },
    {
      "id": "global-event-1",
      "name": "Drizzle of Delay",
      "text": "Max 2 moves; ending in North or East costs 1 Artha.",
      "effect": "max_moves_2_and_cost_artha_north_east"
    },
    {
      "id": "global-event-24",
      "name": "Professor's Insight",
      "text": "Gain 7 inner points if a Professor trades for gnana",
      "effect": "professors_insight_reward"
    },
    {
      "id": "global-event-3",
      "name": "Maha Kumbh",
      "text": "Visit any Jyotirlinga for 7 inner pts; skip for 1 bonus cube.",
      "effect": "jyotirlinga_7_inner_or_bonus_cube"
    },
    {
      "id": "global-event-16",
      "name": "Rickshaw Rhapsody",
      "text": "Use the Rickshaw travel card for 5 outer points",
      "effect": "rickshaw_rhapsody_reward"
    },
    {
      "id": "global-event-22",
      "name": "Up and Over",
      "text": "Use the Helicopter travel card for 5 outer points",
      "effect": "up_and_over_reward"
    }
  ],
  "globalEventDiscard": [
    {
      "id": "global-event-26",
      "name": "Engineer's Precision",
      "text": "Gain 7 outer points if an Engineer trades for karma",
      "effect": "engineers_precision_reward"
    },
    {
      "id": "global-event-25",
      "name": "Pilgrim's Grace",
      "text": "Gain 7 inner points if a Pilgrim trades for bhakti",
      "effect": "pilgrims_grace_reward"
    },
    {
      "id": "global-event-19",
      "name": "Bullet Train",
      "text": "Use the Train travel card for 5 outer points",
      "effect": "bullet_train_reward"
    },
    {
      "id": "global-event-31",
      "name": "Himalayan NE",
      "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points",
      "effect": "himalayan_ne"
    },
    {
      "id": "global-event-9",
      "name": "Riots",
      "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1",
      "effect": "riots_discard"
    },
    {
      "id": "global-event-12",
      "name": "Footpath Reverie",
      "text": "Use the Trek travel card for 5 inner points",
      "effect": "footpath_reverie_reward"
    },
    {
      "id": "global-event-5",
      "name": "Bountiful Bhandara",
      "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected",
      "effect": "draw_2_cubes_bonus_5_outer"
    },
    {
      "id": "global-event-6",
      "name": "Turbulent Skies",
      "text": "No airport travel this round",
      "effect": "no_airport_travel"
    },
    {
      "id": "global-event-29",
      "name": "Solar South",
      "text": "Lose 2 energy cubes if you end in South",
      "effect": "solar_south"
    },
    {
      "id": "global-event-14",
      "name": "Desert Caravan",
      "text": "Use the Camel travel card for 5 outer points",
      "effect": "desert_caravan_reward"
    },
    {
      "id": "global-event-7",
      "name": "Election Campaigns",
      "text": "All trade yield 2x. No travel allowed this round",
      "effect": "double_trade_no_travel"
    },
    {
      "id": "global-event-4",
      "name": "Drought of Spirits",
      "text": "No inner journey cards can be collected this round",
      "effect": "no_inner_journey_cards"
    },
    {
      "id": "global-event-18",
      "name": "Hop on Hop off",
      "text": "Use the Bus travel card for 5 outer points",
      "effect": "hop_on_hop_off_reward"
    }
  ],
  "nameMode": false
}