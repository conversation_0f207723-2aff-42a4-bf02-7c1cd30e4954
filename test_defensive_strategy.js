/**
 * Test script for defensive bot strategy
 * Runs a simulation with configurable number of bots (2 or 3)
 * Updated to include new game features like global events, player characters, and vehicle types
 * Saves the simulation report as JSON in server/simulation_reports directory
 *
 * Usage: node test_defensive_strategy.js [numBots] [numGames]
 * Where numBots is either 2 or 3 (defaults to 2 if not specified)
 * Where numGames is the number of games to simulate (defaults to 5 if not specified)
 */

const SimulationClient = require('./server/simulation_client');

// Parse command line arguments
const args = process.argv.slice(2);
let numBots = 2; // Default to 2 bots
let numGames = 5; // Default to 5 games for better statistical analysis

if (args.length > 0) {
  const requestedBots = parseInt(args[0], 10);
  if (requestedBots === 2 || requestedBots === 3) {
    numBots = requestedBots;
  } else {
    console.warn(`Invalid number of bots: ${args[0]}. Must be either 2 or 3. Using default of 2 bots.`);
  }
}

if (args.length > 1) {
  const requestedGames = parseInt(args[1], 10);
  if (Number.isInteger(requestedGames) && requestedGames > 0) {
    numGames = requestedGames;
  } else {
    console.warn(`Invalid number of games: ${args[1]}. Must be a positive integer. Using default of 5 games.`);
  }
}

// Create strategies array based on number of bots
const strategies = Array(numBots).fill('defensive');

// Create a new simulation client
const client = new SimulationClient({
  host: 'localhost',
  port: 4000,
  strategies: strategies, // Use defensive strategy for all bots
  games: numGames,
  speed: 10, // Moderate speed for balance between observation and performance
  save: true,
  memory: false,
  verbose: true,
  enableFeatures: {
    globalEvents: true,
    playerCharacters: true,
    vehicles: true,
    handLimit: 4 // Set the new hand limit to 4 cards
  }
});

// Run the simulation
console.log(`Starting simulation with ${numBots} defensive strategy bots for ${numGames} games`);
console.log('Reports will be saved to server/simulation_reports/');

client.runSimulations()
  .then((results) => {
    console.log('Simulation completed successfully');
    console.log(`Total games played: ${results.length}`);
    
    // Add a summary of the results here
    const winners = results.filter(game => game.winner).map(game => game.winner.name);
    if (winners.length > 0) {
      console.log(`Winners: ${winners.join(', ')}`);
      
      // Count wins by player name
      const winCounts = {};
      winners.forEach(name => {
        winCounts[name] = (winCounts[name] || 0) + 1;
      });
      
      // Display win distribution
      console.log('\nWin Distribution:');
      Object.entries(winCounts).forEach(([name, count]) => {
        const percentage = ((count / winners.length) * 100).toFixed(1);
        console.log(`  ${name}: ${count} wins (${percentage}%)`);
      });
    }
    
    process.exit(0);
  })
  .catch(error => {
    console.error('Simulation failed:', error);
    process.exit(1);
  });