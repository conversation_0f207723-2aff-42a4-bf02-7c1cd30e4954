const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const gameState = require('../../server/gameState');
const globalEvents = gameState.GLOBAL_EVENT_CARDS;
const { createCanvas, registerFont } = require('canvas');

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../../global-event-images');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Register Caveat fonts for embedding
registerFont(path.join(__dirname, 'fonts', 'Caveat-Regular.ttf'), { family: 'Caveat', weight: 'normal' });
registerFont(path.join(__dirname, 'fonts', 'Caveat-Bold.ttf'), { family: 'Caveat', weight: 'bold' });
const caveatRegularBase64 = fs.readFileSync(path.join(__dirname, 'fonts', 'Caveat-Regular.ttf')).toString('base64');
const caveatBoldBase64 = fs.readFileSync(path.join(__dirname, 'fonts', 'Caveat-Bold.ttf')).toString('base64');

// Color definitions from client/src/styles/main.css
const colors = {
  // Base colors
  primaryOrange: '#e65100',
  secondaryOrange: '#ffb74d',
  lightOrange: '#fff8e1',
  darkOrange: '#d84315',
  brownText: '#5d4037',

  // Region Colors
  northColor: 'rgba(33, 150, 243, 0.95)',      // Vibrant blue
  westColor: 'rgba(76, 175, 80, 0.95)',        // Vibrant green
  southColor: 'rgba(255, 191, 102, 0.95)',     // Light orange
  centralColor: 'rgba(156, 39, 176, 0.95)',    // Vibrant purple
  eastColor: '#fd7c03ef',                      // Orange
  northeastColor: 'rgba(233, 30, 99, 0.95)',   // Vibrant pink

  // Energy Colors
  arthaColor: '#F39C12', // Orange/yellow for artha
  karmaColor: '#27AE60', // Green for karma
  gnanaColor: '#2980B9', // Blue for gnana
  bhaktiColor: '#8E44AD', // Purple for bhakti
};

// Map global event effects to image indices (matches frontend mapping)
const eventEffectToIndex = {
  'max_moves_2_and_cost_artha_north_east': 0,
  'gain_5_inner_no_cube_pickup': 1,
  'jyotirlinga_7_inner_or_bonus_cube': 2,
  'no_inner_journey_cards': 3,
  'draw_2_cubes_bonus_5_outer': 4,
  'no_airport_travel': 5,
  'double_trade_no_travel': 6,
  'triathlon_bonus': 7,
  'riots_discard': 8,
  'om_meditation': 9,
  'heavy_haul_reward': 20,
  'merchants_midas_reward': 22,
  'professors_insight_reward': 23,
  'pilgrims_grace_reward': 24,
  'engineers_precision_reward': 25,
  'frozen_north': 26,
  'solar_south': 28,
  'himalayan_ne': 30,
  'central_heart': 31,
  'eco_trail_reward': 32,
  'rajput_caravans_reward': 33,
  'urban_ride_reward': 34,
  'road_warriors_reward': 35,
  'rails_and_sails_reward': 36,
  'excess_baggage': 37,
  'no_outer_journey_cards': 38,
  'spirit_of_seva': 39,
  'pushkar_holy_dip_end_turn_reward': 40,
  'parikrama_in_clouds_reward': 41,
  'cultural_exchange': 42
};

// Helper function to wrap text into multiple lines
function wrapTextIntoLines(text, maxCharsPerLine) {
  if (!text) return [''];

  // Escape special characters for XML
  text = text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');

  const words = text.split(' ');
  const lines = [];
  let currentLine = '';

  words.forEach(word => {
    // If adding this word would exceed the max length, start a new line
    if (currentLine.length + word.length + 1 > maxCharsPerLine && currentLine.length > 0) {
      lines.push(currentLine);
      currentLine = word;
    } else {
      // Add the word to the current line (with a space if not the first word)
      currentLine = currentLine.length === 0 ? word : `${currentLine} ${word}`;
    }
  });

  // Add the last line if it's not empty
  if (currentLine.length > 0) {
    lines.push(currentLine);
  }

  return lines;
}

// Function to create a global event card image
async function createGlobalEventImage(event) {
  try {
    // Define dimensions
    const width = 900;
    const height = 1500;

    // Determine image index by matching effect prefix against mapping, else use numeric id
    const numericId = parseInt(event.id.replace('global-event-', ''), 10);
    const mappingKey = Object.keys(eventEffectToIndex).find(key => event.effect.startsWith(key));
    const imageIndex = mappingKey != null ? eventEffectToIndex[mappingKey] : numericId;
    const originalImagePath = path.join(__dirname, '../../client/public/assets/images/global_event_cards', `${imageIndex}.png`);
    let hasOriginalImage = false;

    try {
      if (fs.existsSync(originalImagePath)) {
        hasOriginalImage = true;
        console.log(`Found original image for global event ${event.id}: ${originalImagePath}`);
      } else {
        console.log(`Original image not found for global event ${event.id}: ${originalImagePath}`);
      }
    } catch (err) {
      console.log(`Error checking original image for global event ${event.id}: ${err.message}`);
    }

    // Create SVG for the card content (background, border, header, and placeholder only)
    const svgContent = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <style type="text/css">
            @font-face {
              font-family: 'Caveat'; font-weight: 400;
              src: url('data:font/ttf;base64,${caveatRegularBase64}') format('truetype');
            }
            @font-face {
              font-family: 'Caveat'; font-weight: 700;
              src: url('data:font/ttf;base64,${caveatBoldBase64}') format('truetype');
            }
            text { font-family: 'Caveat', cursive; }
          </style>
        </defs>

        <!-- Card background -->
        <rect x="0" y="0" width="${width}" height="${height}" fill="${colors.lightOrange}" rx="20" ry="20" />

        <!-- Card border -->
        <rect x="10" y="10" width="${width - 20}" height="${height - 20}" fill="none" stroke="${colors.secondaryOrange}" stroke-width="8" rx="15" ry="15" />

        <!-- Header with event name -->
        <rect x="45" y="45" width="${width - 90}" height="130" fill="${colors.primaryOrange}" rx="10" ry="10" />
        <text x="${width/2}" y="120" font-size="72" fill="white" text-anchor="middle" font-weight="bold">${event.name}</text>

        <!-- Placeholder for event image -->
        <rect x="110" y="300" width="${width - 220}" height="650" fill="white" stroke="${colors.secondaryOrange}" stroke-width="4" rx="10" ry="10" />
      </svg>
    `;

    // Save the output path
    const outputPath = path.join(outputDir, `GE${imageIndex}.png`);

    if (hasOriginalImage) {
      // Create the base image and composite resized original event art (20% bigger)
      const baseImage = await sharp(Buffer.from(svgContent)).png().toBuffer();
      const origW = width - 220;
      const origH = 650;
      const resizedW = Math.round(origW * 1.2);
      const resizedH = Math.round(origH * 1.2);
      const eventImage = await sharp(originalImagePath)
        .resize({ width: resizedW, height: resizedH, fit: 'contain', background: { r: 255, g: 255, b: 255, alpha: 1 } })
        .toBuffer();
      const left = Math.round((width - resizedW) / 2);
      const top = 300;
      // Composite original art and then draw description lines below the image
      const descLines = wrapTextIntoLines(event.text, 30);
      const margin = Math.round(resizedH * 0.2); // 20% of image height (reduced from 30% to pull text up by ~10%)
      const descStartY = top + resizedH + margin;
      const lineSpacing = 85; // vertical spacing between lines (increased from 70)
      const descSvg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        ${descLines.map((line, i) =>
          `<text x="${width/2}" y="${descStartY + i * lineSpacing}" font-size="64" fill="${colors.brownText}" text-anchor="middle" font-weight="bold" font-family="Caveat">${line}</text>`
        ).join('')}
      </svg>`;
      const descBuffer = Buffer.from(descSvg);
      await sharp(baseImage)
        .composite([
          { input: eventImage, top, left },
          { input: descBuffer, top: 0, left: 0 }
        ])
        .toFile(outputPath);
    } else {
      // Fallback: composite description onto base SVG
      const baseImage = await sharp(Buffer.from(svgContent)).png().toBuffer();
      // Compute description positioning
      const placeholderTop = 300;
      const placeholderHeight = 650;
      const margin = Math.round(placeholderHeight * 0.2); // 20% of placeholder height (reduced from 30% to pull text up by ~10%)
      const descStartY = placeholderTop + placeholderHeight + margin;
      const descLines = wrapTextIntoLines(event.text, 30);
      const descSvg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        ${descLines.map((line, i) =>
          `<text x="${width/2}" y="${descStartY + i * 85}" font-size="64" fill="${colors.brownText}" text-anchor="middle" font-weight="bold" font-family="Caveat">${line}</text>`
        ).join('')}
      </svg>`;
      const descBuffer = Buffer.from(descSvg);
      await sharp(baseImage)
        .composite([{ input: descBuffer, top: 0, left: 0 }])
        .toFile(outputPath);
    }

    console.log(`Created image for global event ${event.id}: ${event.name} at ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`Error creating image for global event ${event.id}:`, error);
    return false;
  }
}

// Process all global event cards
async function processAllEvents() {
  console.log(`Processing ${globalEvents.length} global event cards...`);

  let successCount = 0;
  let failCount = 0;

  for (const event of globalEvents) {
    const success = await createGlobalEventImage(event);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  console.log(`\nProcessing complete!`);
  console.log(`Successfully created: ${successCount} images`);
  console.log(`Failed: ${failCount} images`);
  console.log(`Output directory: ${outputDir}`);

  // Create CSV file for component.studio
  createCsvFile();
}

// Create CSV file for component.studio
function createCsvFile() {
  const csvPath = path.join(__dirname, '../ge.csv');
  let csvContent = 'quantity,name,image\n';
  // Mirror same mapping: match effect prefix or fallback to numeric
  globalEvents.forEach(event => {
    const numericId2 = parseInt(event.id.replace('global-event-', ''), 10);
    const mappingKey2 = Object.keys(eventEffectToIndex).find(key => event.effect.startsWith(key));
    const imageIndex2 = mappingKey2 != null ? eventEffectToIndex[mappingKey2] : numericId2;
    csvContent += `1,GE${imageIndex2},{{ images.GE${imageIndex2}.url }}\n`;
  });

  fs.writeFileSync(csvPath, csvContent);
  console.log(`Created CSV file at ${csvPath}`);
}

// Start processing
processAllEvents();
