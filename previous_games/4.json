{"comment": "closest game till date, <PERSON><PERSON> would have won next round with 7 om tokens", "players": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": 30, "hand": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}], "energyCubes": [], "omTemp": 0, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "outerScore": 51, "innerScore": 54, "collectedJourneys": [{"id": "JI18", "locationId": 38, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "reward": {"inner": 30}}, {"id": "JO16", "locationId": 31, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 30, "reward": {"outer": 24}}], "didMoveThisTurn": true, "didSelectionActionThisTurn": true, "character": {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "position": 52, "hand": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "energyCubes": ["gnana", "gnana"], "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "outerScore": 40, "innerScore": 44, "collectedJourneys": [{"id": "JO8", "locationId": 17, "reward": {"outer": 20}}, {"id": "JI21", "locationId": 42, "reward": {"inner": 24}}, {"id": "JI20", "locationId": 41, "reward": {"inner": 20}}, {"id": "JO14", "locationId": 29, "reward": {"outer": 20}}], "didMoveThisTurn": true, "didSelectionActionThisTurn": true, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}}], "started": true, "turnIndex": 0, "roundCount": 15, "travelDeck": [{"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}], "eventDeck": [{"id": "E4", "type": "extraHop"}, {"id": "E9", "type": "wildCube"}, {"id": "E8", "type": "wildCube"}, {"id": "E6", "type": "wildCube"}, {"id": "E7", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}], "travelDiscard": [{"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T15", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "eventDiscard": [], "journeyInnerDiscard": [{"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}], "journeyOuterDiscard": [{"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "faceUpTravel": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T5", "type": "travel", "value": 1, "vehicle": "camel"}], "faceUpEvent": [{"id": "E2", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "faceUpJourneyOuter": [{"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "locationCubes": {"1": "bhakti", "2": "artha", "3": "karma", "4": "bhakti", "5": "artha", "6": "artha", "7": "gnana", "8": "artha", "9": "karma", "10": null, "11": "gnana", "12": "artha", "13": "artha", "14": "artha", "15": null, "16": "gnana", "17": null, "18": "gnana", "19": "artha", "20": null, "21": "gnana", "22": "artha", "23": null, "24": null, "25": null, "26": "artha", "27": null, "28": "bhakti", "29": null, "30": null, "31": null, "32": "bhakti", "33": null, "34": "karma", "35": null, "36": "bhakti", "37": null, "38": null, "39": "artha", "40": "gnana", "41": null, "42": null, "43": "bhakti", "44": null, "45": "bhakti", "46": "gnana", "47": null, "48": "bhakti", "undefined": "karma"}, "locationOm": {"49": false, "50": false, "51": false, "52": false, "53": true, "54": true, "55": false, "56": false, "57": false, "58": false, "59": false, "60": false}, "finalRound": true, "finalRoundStarter": 0, "finalRoundEnd": 1, "gameEvents": [{"type": "DEAL_INITIAL_CARD", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743378861877, "data": {"cardType": "travel", "card": {"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743378861877, "data": {"cardType": "travel", "card": {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 0, "turnIndex": 0, "timestamp": 1743378861877, "data": {"cardType": "travel", "card": {"id": "T15", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 0, "turnIndex": 0, "timestamp": 1743378861877, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743379332950, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743379337920, "data": {"cardType": "travel", "drawnCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743379352945, "data": {"location": 55}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743379352945, "data": {"path": [65, 31, 30, 55], "travelCardIds": ["T14"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743379355686, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_DECK_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 0, "turnIndex": 1, "timestamp": 1743379397594, "data": {"cardType": "travel", "drawnCards": [{"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 0, "turnIndex": 1, "timestamp": 1743379409607, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 0, "turnIndex": 1, "timestamp": 1743379426556, "data": {"location": 56}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 0, "turnIndex": 1, "timestamp": 1743379426556, "data": {"path": [61, 4, 3, 56], "travelCardIds": ["T15"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 0, "turnIndex": 0, "timestamp": 1743379429664, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743379429664, "data": {"roundNumber": 1, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 0, "collectedJourneysCount": 0}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 0, "collectedJourneysCount": 0}]}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743379523047, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743379524046, "data": {"cardType": "travel", "drawnCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "boat"}], "pickFromTop": true}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743379528923, "data": {"path": [55, 30], "travelCardIds": ["T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743379530278, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_DECK_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 1, "turnIndex": 1, "timestamp": 1743379569424, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 1, "turnIndex": 1, "timestamp": 1743379607212, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 1, "turnIndex": 1, "timestamp": 1743379650630, "data": {"location": 57}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 1, "turnIndex": 1, "timestamp": 1743379650630, "data": {"path": [56, 3, 47, 57], "travelCardIds": ["T10", "T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 1, "turnIndex": 0, "timestamp": 1743379655442, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743379655442, "data": {"roundNumber": 2, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 1, "collectedJourneysCount": 0}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 0, "collectedJourneysCount": 0}]}}, {"type": "collectOm", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743379705799, "data": {"location": 58}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743379705799, "data": {"path": [30, 31, 32, 58], "travelCardIds": ["T9", "T4"], "extraHopCount": 0}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743379711446, "data": {"cardType": "travel", "drawnCards": [{"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743379712235, "data": {"cardType": "travel", "drawnCards": [{"id": "T15", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743379714142, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 2, "turnIndex": 1, "timestamp": 1743379775560, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 2, "turnIndex": 1, "timestamp": 1743379784776, "data": {"cardType": "travel", "drawnCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 2, "turnIndex": 1, "timestamp": 1743379814884, "data": {"location": 60}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 2, "turnIndex": 1, "timestamp": 1743379814884, "data": {"path": [57, 41, 48, 42, 43, 60], "travelCardIds": ["T13", "T8"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 2, "turnIndex": 0, "timestamp": 1743379823689, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743379823689, "data": {"roundNumber": 3, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 1, "collectedJourneysCount": 0}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 0, "innerScore": 0, "omTemp": 3, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 0, "collectedJourneysCount": 0}]}}, {"type": "collectOm", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743379932909, "data": {"location": 51}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743379932909, "data": {"path": [58, 32, 31, 65, 63, 20, 51], "travelCardIds": ["T12", "T14"], "extraHopCount": 0}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743379942027, "data": {"cardType": "travel", "drawnCards": [{"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743379943556, "data": {"cardType": "travel", "drawnCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickFromTop": true}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743379945562, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 3, "turnIndex": 1, "timestamp": 1743379958920, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 3, "turnIndex": 1, "timestamp": 1743379969100, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 3, "turnIndex": 1, "timestamp": 1743380052967, "data": {"path": [60, 43, 42, 66, 64, 29, 33], "travelCardIds": ["T11", "T6", "T1"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 3, "turnIndex": 0, "timestamp": 1743380056755, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743380056756, "data": {"roundNumber": 4, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 3, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 1, "collectedJourneysCount": 0}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 0, "innerScore": 0, "omTemp": 3, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 1, "collectedJourneysCount": 0}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743380111114, "data": {"path": [51, 20], "travelCardIds": ["T4"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743380115568, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 4, "turnIndex": 1, "timestamp": 1743380147505, "data": {"path": [33, 34, 17], "travelCardIds": ["T9"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 4, "turnIndex": 1, "timestamp": 1743380151421, "data": {"journeyCard": {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, "journeyType": "outer", "location": {"id": 17, "name": "<PERSON><PERSON><PERSON>", "region": "West", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["karma", "gnana"], "energyCubesAfter": ["gnana", "gnana"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 20, "innerScore": 0}}}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 4, "turnIndex": 0, "timestamp": 1743380154449, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743380154449, "data": {"roundNumber": 5, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 0, "omTemp": 3, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 0}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 1}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743380204105, "data": {"path": [20, 21, 22, 23, 38], "travelCardIds": ["T7", "T10"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743380208764, "data": {"journeyCard": {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, "journeyType": "inner", "location": {"id": 38, "name": "Tarapith", "region": "East", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["gnana", "bhakti", "karma"], "energyCubesAfter": [], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 0}, "newScore": {"outerScore": 0, "innerScore": 24}}}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743380220622, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 5, "turnIndex": 1, "timestamp": 1743380347484, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 5, "turnIndex": 1, "timestamp": 1743380351360, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 5, "turnIndex": 1, "timestamp": 1743380358834, "data": {"path": [17, 34, 35], "travelCardIds": ["T8"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 5, "turnIndex": 0, "timestamp": 1743380361086, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743380361086, "data": {"roundNumber": 6, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 24, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 1}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 0, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 2, "collectedJourneysCount": 1}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743380415121, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743380421419, "data": {"path": [38, 37], "travelCardIds": ["T2"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743380422869, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 6, "turnIndex": 1, "timestamp": 1743380584898, "data": {"path": [35, 34, 33, 29, 64, 66, 42], "travelCardIds": ["T14", "T13"], "extraHopCount": 0}}, {"type": "TRADE_ENERGY_CUBES", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 6, "turnIndex": 1, "timestamp": 1743380644750, "data": {"playerName": "Shivu", "selectedCubes": ["karma", "karma"], "receivedCube": "bhakti", "characterType": "<PERSON><PERSON><PERSON>"}}, {"type": "COLLECT_JOURNEY", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 6, "turnIndex": 1, "timestamp": 1743380655528, "data": {"journeyCard": {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, "journeyType": "inner", "location": {"id": 42, "name": "Kamakhya Temple", "region": "Northeast", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["gnana", "bhakti"], "energyCubesAfter": ["gnana", "gnana"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 20, "innerScore": 0}, "newScore": {"outerScore": 20, "innerScore": 24}}}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 6, "turnIndex": 0, "timestamp": 1743380657977, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743380657977, "data": {"roundNumber": 7, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 24, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 2, "energyCubesCount": 2, "collectedJourneysCount": 1}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 24, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 0, "energyCubesCount": 0, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743380807325, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743380814864, "data": {"path": [37, 65, 63, 25], "travelCardIds": ["T15"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743380818937, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 7, "turnIndex": 1, "timestamp": 1743380880681, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 7, "turnIndex": 1, "timestamp": 1743380889991, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 7, "turnIndex": 1, "timestamp": 1743380970073, "data": {"location": 50}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 7, "turnIndex": 1, "timestamp": 1743380970073, "data": {"path": [42, 66, 62, 11, 12, 50], "travelCardIds": ["T10", "T11"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 7, "turnIndex": 0, "timestamp": 1743380976080, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743380976080, "data": {"roundNumber": 8, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 24, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 3, "collectedJourneysCount": 1}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 24, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 0, "energyCubesCount": 0, "collectedJourneysCount": 2}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743381018816, "data": {"path": [25, 26, 27], "travelCardIds": ["T7"], "extraHopCount": 0}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743381049649, "data": {"cardType": "travel", "drawnCards": [{"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743381050431, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743381063559, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 8, "turnIndex": 1, "timestamp": 1743381138962, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 8, "turnIndex": 1, "timestamp": 1743381146244, "data": {"cardType": "travel", "pickedCards": [{"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 8, "turnIndex": 1, "timestamp": 1743381221318, "data": {"path": [50, 12, 11, 10], "travelCardIds": ["T14"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 8, "turnIndex": 0, "timestamp": 1743381234327, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743381234327, "data": {"roundNumber": 9, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 24, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 4, "collectedJourneysCount": 1}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 24, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 2}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743381245467, "data": {"path": [27, 26, 25, 24], "travelCardIds": ["T12"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743381252865, "data": {"journeyCard": {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, "journeyType": "inner", "location": {"id": 24, "name": "Padmanabhaswamy Temple", "region": "South", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["karma", "gnana", "bhakti", "bhakti", "gnana"], "energyCubesAfter": [], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 24}, "newScore": {"outerScore": 0, "innerScore": 54}}}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743381291210, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 9, "turnIndex": 1, "timestamp": 1743381333289, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 9, "turnIndex": 1, "timestamp": 1743381351824, "data": {"cardType": "travel", "pickedCards": [{"id": "T13", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 9, "turnIndex": 0, "timestamp": 1743381354280, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743381354280, "data": {"roundNumber": 10, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 2}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 24, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 1, "collectedJourneysCount": 2}]}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743381498546, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T15", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743381502729, "data": {"location": 59}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743381502729, "data": {"path": [24, 23, 59], "travelCardIds": ["T9"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743381504536, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 10, "turnIndex": 1, "timestamp": 1743381546740, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 10, "turnIndex": 1, "timestamp": 1743381551306, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "collectOm", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 10, "turnIndex": 1, "timestamp": 1743381555717, "data": {"location": 49}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 10, "turnIndex": 1, "timestamp": 1743381555717, "data": {"path": [10, 49], "travelCardIds": ["T4"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 10, "turnIndex": 0, "timestamp": 1743381560646, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743381560646, "data": {"roundNumber": 11, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 54, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 1, "collectedJourneysCount": 2}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 24, "omTemp": 3, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 1, "collectedJourneysCount": 2}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743381578645, "data": {"path": [59, 23], "travelCardIds": ["T3"], "extraHopCount": 0}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743381644046, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T14", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743381646385, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 11, "turnIndex": 1, "timestamp": 1743381670413, "data": {"path": [49, 10, 11, 62, 66, 42, 48, 41], "travelCardIds": ["T6", "T13", "T8"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 11, "turnIndex": 1, "timestamp": 1743381674853, "data": {"journeyCard": {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, "journeyType": "inner", "location": {"id": 41, "name": "Tawang Monastery", "region": "Northeast", "journeyType": "Inner"}, "omSpent": 1, "energyCubesBefore": ["bhakti", "gnana"], "energyCubesAfter": ["gnana", "gnana"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 20, "innerScore": 24}, "newScore": {"outerScore": 20, "innerScore": 44}}}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 11, "turnIndex": 0, "timestamp": 1743381699836, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743381699836, "data": {"roundNumber": 12, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 0, "innerScore": 54, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 5, "energyCubesCount": 2, "collectedJourneysCount": 2}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 44, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 1, "collectedJourneysCount": 3}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743381713195, "data": {"path": [23, 24, 63, 65, 31], "travelCardIds": ["T11", "T5"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743381716036, "data": {"journeyCard": {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, "journeyType": "outer", "location": {"id": 31, "name": "Ramoji Film City", "region": "East", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["karma", "karma", "artha"], "energyCubesAfter": [], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 0, "innerScore": 54}, "newScore": {"outerScore": 27, "innerScore": 54}}}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743381721665, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 12, "turnIndex": 1, "timestamp": 1743381843452, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 12, "turnIndex": 1, "timestamp": 1743381847912, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 12, "turnIndex": 1, "timestamp": 1743381856354, "data": {"path": [41, 48, 42, 66, 64, 29], "travelCardIds": ["T10", "T12"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 12, "turnIndex": 0, "timestamp": 1743381876731, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1743381876731, "data": {"roundNumber": 13, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 27, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 0, "collectedJourneysCount": 3}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 20, "innerScore": 44, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 2, "collectedJourneysCount": 3}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743381987017, "data": {"path": [31, 45, 44], "travelCardIds": ["T7"], "extraHopCount": 0}}, {"type": "PICK_DECK_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743381991199, "data": {"cardType": "travel", "drawnCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743382008394, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743382010475, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "COLLECT_JOURNEY", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 13, "turnIndex": 1, "timestamp": 1743382047640, "data": {"journeyCard": {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, "journeyType": "outer", "location": {"id": 29, "name": "<PERSON><PERSON><PERSON>", "region": "Central", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["gnana", "karma"], "energyCubesAfter": ["gnana", "gnana"], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 20, "innerScore": 44}, "newScore": {"outerScore": 40, "innerScore": 44}}}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 13, "turnIndex": 1, "timestamp": 1743382075123, "data": {"path": [29, 64, 15], "travelCardIds": ["T9"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 13, "turnIndex": 0, "timestamp": 1743382085232, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1743382085232, "data": {"roundNumber": 14, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 27, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 4, "energyCubesCount": 1, "collectedJourneysCount": 3}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 40, "innerScore": 44, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 0, "energyCubesCount": 2, "collectedJourneysCount": 4}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 0, "timestamp": 1743382088979, "data": {"path": [44, 45, 46, 47], "travelCardIds": ["T15"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 14, "turnIndex": 1, "timestamp": 1743382099306, "data": {"previousTurnIndex": 0, "newTurnIndex": 1}}, {"type": "PICK_DECK_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 14, "turnIndex": 1, "timestamp": 1743382124544, "data": {"cardType": "travel", "drawnCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 14, "turnIndex": 1, "timestamp": 1743382128538, "data": {"cardType": "travel", "drawnCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "collectOm", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 14, "turnIndex": 1, "timestamp": 1743382139493, "data": {"location": 52}}, {"type": "move", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 14, "turnIndex": 1, "timestamp": 1743382139493, "data": {"path": [15, 5, 52], "travelCardIds": ["T6"], "extraHopCount": 0}}, {"type": "END_TURN", "playerId": "Mla0mWnU01cHp5V7AAAD", "playerName": "Shivu", "roundCount": 14, "turnIndex": 0, "timestamp": 1743382147072, "data": {"previousTurnIndex": 1, "newTurnIndex": 0}}, {"type": "NEW_ROUND", "playerId": "system", "playerName": "System", "roundCount": 15, "turnIndex": 0, "timestamp": 1743382147072, "data": {"roundNumber": 15, "playerCount": 2, "playerStats": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 27, "innerScore": 54, "omTemp": 1, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 3, "energyCubesCount": 2, "collectedJourneysCount": 3}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 40, "innerScore": 44, "omTemp": 2, "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "handSize": 1, "energyCubesCount": 2, "collectedJourneysCount": 4}]}}, {"type": "move", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1743382158456, "data": {"path": [47, 46, 45, 31, 30], "travelCardIds": ["T14", "T1"], "extraHopCount": 0}}, {"type": "COLLECT_JOURNEY", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1743382161247, "data": {"journeyCard": {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, "journeyType": "outer", "location": {"id": 30, "name": "Charminar", "region": "East", "journeyType": "Outer"}, "omSpent": 1, "energyCubesBefore": ["artha", "karma"], "energyCubesAfter": [], "wildCubesUsed": 0, "scoreChange": {"previousScore": {"outerScore": 27, "innerScore": 54}, "newScore": {"outerScore": 51, "innerScore": 54}}}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "_Q45M2Y5XGAg1S1MAAAB", "playerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roundCount": 15, "turnIndex": 0, "timestamp": 1743382161247, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 105, "playerOmTotal": 4, "finalRoundStarter": 0, "finalRoundEnd": 1}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 15, "turnIndex": 0, "timestamp": 1743382164961, "data": {"winner": {"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}, "totalRounds": 15, "players": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 40, "innerScore": 44, "totalScore": 84, "omTotal": 6}]}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 15, "turnIndex": 0, "timestamp": 1743382164962, "data": {"winner": {"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}, "totalRounds": 15, "players": [{"id": "_Q45M2Y5XGAg1S1MAAAB", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outerScore": 51, "innerScore": 54, "totalScore": 105, "omTotal": 4}, {"id": "Mla0mWnU01cHp5V7AAAD", "name": "Shivu", "outerScore": 40, "innerScore": 44, "totalScore": 84, "omTotal": 6}]}}], "characterDeck": [{"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}]}