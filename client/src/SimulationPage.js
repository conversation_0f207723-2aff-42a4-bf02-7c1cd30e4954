import React from 'react';
import SimulationDashboard from './components/SimulationDashboard';
import './styles/main.css';
import './styles/simulation-page.css';

/**
 * Standalone Simulation Dashboard page
 */
function SimulationPage() {
  return (
    <div className="simulation-page">
      <header className="simulation-header">
        <h1>OM Journey Simulation Analysis</h1>
        <div className="header-controls">
          <button onClick={() => window.location.href = '/'}>
            Return to Game
          </button>
        </div>
      </header>
      
      <div className="simulation-container">
        <SimulationDashboard />
      </div>
      
      <footer className="simulation-footer">
        <p>OM Journey Bot Simulation Framework</p>
      </footer>
    </div>
  );
}

export default SimulationPage; 