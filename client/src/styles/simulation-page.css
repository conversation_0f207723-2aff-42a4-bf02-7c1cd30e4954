/* Simulation Page Styles */
.simulation-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.simulation-header {
  background-color: #2c3e50;
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.simulation-header h1 {
  margin: 0;
  font-size: 1.4rem;
}

.header-controls button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.header-controls button:hover {
  background-color: #2980b9;
}

.simulation-container {
  flex: 1;
  padding: 0;
  max-width: 100%;
  width: 100%;
}

.simulation-footer {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 15px 20px;
  text-align: center;
  font-size: 0.9rem;
} 