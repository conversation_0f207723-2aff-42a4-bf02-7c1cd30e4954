/**
 * Generate a sample simulation report JSON file
 * This script creates a mock simulation report for testing purposes
 * 
 * Usage: node generate_sample_report.js
 */

const fs = require('fs');
const path = require('path');

// Directory for saving simulation reports
const SIMULATION_REPORTS_DIR = path.join(__dirname, 'server', 'simulation_reports');

// Ensure the directory exists
if (!fs.existsSync(SIMULATION_REPORTS_DIR)) {
  console.log(`Creating simulation reports directory: ${SIMULATION_REPORTS_DIR}`);
  fs.mkdirSync(SIMULATION_REPORTS_DIR, { recursive: true });
}

// Generate sample game summary data
function generateSampleGameSummary(gameNumber) {
  // Sample players
  const players = [
    {
      name: 'Bot-A',
      id: `bot-${Math.random().toString(36).substring(2, 10)}`,
      totalScore: Math.floor(Math.random() * 60) + 40, // Random score between 40-100
      outerScore: Math.floor(Math.random() * 30) + 20, // Random outer score
      innerScore: Math.floor(Math.random() * 30) + 20, // Random inner score
      omTotal: Math.floor(Math.random() * 5) + 1, // Random OM tokens
      bonusScore: Math.floor(Math.random() * 10),
      dryTurns: Math.floor(Math.random() * 5),
      isWinner: gameNumber % 2 === 0, // Alternate winners
      journeyCards: generateSampleJourneyCards(),
      energyCubes: {
        bhakti: Math.floor(Math.random() * 5),
        gnana: Math.floor(Math.random() * 5),
        karma: Math.floor(Math.random() * 5),
        artha: Math.floor(Math.random() * 5),
        total: Math.floor(Math.random() * 12) + 3
      },
      totalHops: Math.floor(Math.random() * 30) + 10
    },
    {
      name: 'Bot-B',
      id: `bot-${Math.random().toString(36).substring(2, 10)}`,
      totalScore: Math.floor(Math.random() * 60) + 40, // Random score between 40-100
      outerScore: Math.floor(Math.random() * 30) + 20, // Random outer score
      innerScore: Math.floor(Math.random() * 30) + 20, // Random inner score
      omTotal: Math.floor(Math.random() * 5) + 1, // Random OM tokens
      bonusScore: Math.floor(Math.random() * 10),
      dryTurns: Math.floor(Math.random() * 5),
      isWinner: gameNumber % 2 === 1, // Alternate winners
      journeyCards: generateSampleJourneyCards(),
      energyCubes: {
        bhakti: Math.floor(Math.random() * 5),
        gnana: Math.floor(Math.random() * 5),
        karma: Math.floor(Math.random() * 5),
        artha: Math.floor(Math.random() * 5),
        total: Math.floor(Math.random() * 12) + 3
      },
      totalHops: Math.floor(Math.random() * 30) + 10
    }
  ];

  // Make sure one player is the winner
  const winnerIndex = gameNumber % 2;
  players[winnerIndex].isWinner = true;
  players[1 - winnerIndex].isWinner = false;
  
  // Ensure winner has higher score
  if (players[winnerIndex].totalScore <= players[1 - winnerIndex].totalScore) {
    players[winnerIndex].totalScore = players[1 - winnerIndex].totalScore + Math.floor(Math.random() * 20) + 5;
  }

  return {
    gameNumber,
    roundCount: Math.floor(Math.random() * 15) + 15, // Random rounds between 15-30
    totalEvents: Math.floor(Math.random() * 100) + 50,
    eventCounts: {
      move: Math.floor(Math.random() * 40) + 20,
      pickup: Math.floor(Math.random() * 20) + 5,
      journey_collected: Math.floor(Math.random() * 10) + 5,
      turn_start: Math.floor(Math.random() * 30) + 15,
      turn_end: Math.floor(Math.random() * 30) + 15,
      bonus_score: Math.floor(Math.random() * 5),
      dry_turn: Math.floor(Math.random() * 5)
    },
    players,
    winner: {
      name: players[winnerIndex].name,
      id: players[winnerIndex].id,
      totalScore: players[winnerIndex].totalScore
    }
  };
}

// Generate sample journey cards
function generateSampleJourneyCards() {
  const cardTypes = ['inner', 'outer', 'special'];
  const journeyCards = [];
  
  // Create 2-5 random journey cards
  const numCards = Math.floor(Math.random() * 4) + 2;
  
  for (let i = 0; i < numCards; i++) {
    const cardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
    journeyCards.push({
      id: `${cardType}_journey_${Math.floor(Math.random() * 100)}`,
      type: cardType,
      locationId: Math.floor(Math.random() * 40) + 1,
      points: Math.floor(Math.random() * 15) + 5
    });
  }
  
  return journeyCards;
}

// Calculate basic statistics for an array of numbers
function calculateStats(values) {
  if (!values || values.length === 0) {
    return { min: 0, max: 0, avg: 0 };
  }
  const min = Math.min(...values);
  const max = Math.max(...values);
  const sum = values.reduce((a, b) => a + b, 0);
  const avg = sum / values.length;
  
  return { min, max, avg };
}

// Generate the complete report
function generateSimulationReport(numGames = 5) {
  const gameSummaries = [];
  
  // Generate sample game summaries
  for (let i = 1; i <= numGames; i++) {
    gameSummaries.push(generateSampleGameSummary(i));
  }
  
  // Create the full report
  const report = {
    timestamp: new Date().toISOString(),
    options: {
      games: numGames,
      speed: 10,
      strategies: ['defensive', 'defensive'],
      save: true,
      memory: false,
      enableFeatures: {
        globalEvents: true,
        playerCharacters: true,
        vehicles: true,
        handLimit: 4
      },
      host: 'localhost',
      port: 4000,
      verbose: true
    },
    gameSummaries,
    statistics: generateStatsReport(gameSummaries)
  };
  
  return report;
}

// Generate statistics report object
function generateStatsReport(gameSummaries) {
  const totalGames = gameSummaries.length;
  
  // Strategy win rates
  const strategyWins = {
    defensive: Math.floor(totalGames / 2) // Each strategy wins half the games
  };
  
  const strategyPerformance = [
    {
      strategy: 'defensive',
      wins: Math.floor(totalGames / 2),
      winRate: 50.0
    }
  ];
  
  // Rounds statistics
  const rounds = gameSummaries.map(summary => summary.roundCount);
  const roundStats = calculateStats(rounds);
  
  // Score difference statistics
  const scoreDifferences = gameSummaries.map(summary => {
    const sortedPlayers = [...summary.players].sort((a, b) => b.totalScore - a.totalScore);
    return sortedPlayers.length >= 2 ? sortedPlayers[0].totalScore - sortedPlayers[1].totalScore : 0;
  });
  const scoreDiffStats = calculateStats(scoreDifferences);
  
  // Score distributions
  const scores = {
    outer: gameSummaries.flatMap(summary => summary.players.map(p => p.outerScore)),
    inner: gameSummaries.flatMap(summary => summary.players.map(p => p.innerScore)),
    total: gameSummaries.flatMap(summary => summary.players.map(p => p.totalScore))
  };
  
  const scoreStats = {
    outer: calculateStats(scores.outer),
    inner: calculateStats(scores.inner),
    total: calculateStats(scores.total)
  };
  
  // Journey card statistics
  const journeyCardStats = {
    inner: { min: 1, max: 3, avg: 1.7 },
    outer: { min: 1, max: 3, avg: 1.8 },
    special: { min: 0, max: 2, avg: 0.9 }
  };
  
  // Bonus scores
  const bonusScores = gameSummaries.flatMap(summary => {
    return summary.players.map(player => player.bonusScore || 0);
  });
  const bonusScoreStats = calculateStats(bonusScores);
  
  // Dry turns
  const dryTurns = gameSummaries.flatMap(summary => {
    return summary.players.map(player => player.dryTurns || 0);
  });
  const dryTurnStats = calculateStats(dryTurns);
  
  // Energy cubes
  const energyCubes = {
    bhakti: gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.bhakti || 0)),
    gnana: gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.gnana || 0)),
    karma: gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.karma || 0)),
    artha: gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.artha || 0)),
    total: gameSummaries.flatMap(summary => summary.players.map(p => p.energyCubes?.total || 0))
  };
  
  const energyStats = {
    bhakti: calculateStats(energyCubes.bhakti),
    gnana: calculateStats(energyCubes.gnana),
    karma: calculateStats(energyCubes.karma),
    artha: calculateStats(energyCubes.artha),
    total: calculateStats(energyCubes.total)
  };
  
  // Journey cards collected
  const journeyCardsCount = gameSummaries.flatMap(summary => 
    summary.players.map(p => p.journeyCards ? p.journeyCards.length : 0)
  );
  const journeyCollectionStats = calculateStats(journeyCardsCount);
  
  // Total hops
  const hops = gameSummaries.flatMap(summary => 
    summary.players.map(p => p.totalHops || 0)
  );
  const hopStats = calculateStats(hops);
  
  return {
    strategyPerformance,
    rounds: roundStats,
    scoreDifference: scoreDiffStats,
    scores: scoreStats,
    journeyCardsByType: journeyCardStats,
    bonusScores: bonusScoreStats,
    dryTurns: dryTurnStats,
    energyCubes: energyStats,
    journeyCardsCollected: journeyCollectionStats,
    totalHops: hopStats
  };
}

// Generate the report with 5 sample games
const report = generateSimulationReport(5);

// Generate a filename based on timestamp and strategies
const strategiesStr = report.options.strategies.join('-');
const numBots = report.options.strategies.length;
const numGames = report.options.games;
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const filename = `sample_sim_${numBots}bots_${strategiesStr}_${numGames}games_${timestamp}.json`;
const filepath = path.join(SIMULATION_REPORTS_DIR, filename);

// Write the file
console.log(`Generating sample simulation report to: ${filepath}`);
fs.writeFileSync(filepath, JSON.stringify(report, null, 2), 'utf8');
console.log('Sample simulation report saved successfully!');

// Print a summary of the report
console.log('\nSample Report Summary:');
console.log(`Total Games: ${report.gameSummaries.length}`);
console.log(`Simulation Date: ${new Date(report.timestamp).toLocaleString()}`);
console.log(`Win Distribution: ${report.statistics.strategyPerformance[0].strategy} - ${report.statistics.strategyPerformance[0].wins} wins (${report.statistics.strategyPerformance[0].winRate}%)`);
console.log(`Average Rounds per Game: ${report.statistics.rounds.avg.toFixed(1)}`);
console.log(`Average Score Difference: ${report.statistics.scoreDifference.avg.toFixed(1)}`);
console.log(`Average Total Score: ${report.statistics.scores.total.avg.toFixed(1)}`);
console.log(`Average Journey Cards Collected: ${report.statistics.journeyCardsCollected.avg.toFixed(1)}`);
console.log(`Average Energy Cubes Collected: ${report.statistics.energyCubes.total.avg.toFixed(1)}`); 