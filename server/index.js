// server/index.js

const express = require('express');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');
const fs = require('fs'); // Import the file system module
const path = require('path');

const GameState = require('./gameState');
const logger = require('./utils/logger');

// Import simulation components
const Simulation = require('./simulation/Simulation');

const PORT = process.env.PORT || 4000;

// Create a Map to store player sockets by player ID
const playerSockets = new Map();

// Memory monitoring configuration
const MEMORY_MONITOR = {
  enabled: false,
  checkIntervalMs: 30000, // Check every 30 seconds
  warningThresholdMB: 700, // Warn at 700MB
  criticalThresholdMB: 900, // Critical at 900MB
  gcThresholdMB: 800, // Run GC at 800MB
};

const app = express();
app.use(cors());
app.use(express.json()); // Add JSON body parsing middleware

// Serve static files from client/assets directory
app.use('/assets', express.static(path.join(__dirname, '../client/assets')));

// Also serve static files from client/public directory
app.use(express.static(path.join(__dirname, '../client/public')));

const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  allowEIO3: true,
  pingTimeout: 60000,
  pingInterval: 25000
});

const gameState = new GameState();
const SAVE_FILE = 'gameState.json'; // File to save the game state

// Create the simulation controller
const simulation = new Simulation({
  gameState,
  io,
  logger
});

// Note: Removed auto-loading from SAVE_FILE at server startup.

// Setup memory monitoring
if (MEMORY_MONITOR.enabled) {
  setupMemoryMonitoring();
}

// Main Express routes will be set up by setupRoutes() 
// Simulation API endpoints will be set up by setupSimulationEndpoints()

io.on('connection', (socket) => {
  console.log('A user connected:', socket.id);

  socket.emit('gameState', gameState.getState());
  
  // Listen for gameState updates (e.g., after global event effects are applied)
  gameState.on('gameStateUpdated', () => {
    console.log('[gameStateUpdated] Game state updated, emitting to all clients');
    emitGameState();
  });

  // Listen for Spirit of Seva event to show modal
  gameState.on('spiritOfSevaExecuted', (data) => {
    console.log('[spiritOfSevaExecuted] Spirit of Seva donations executed, emitting to all clients');
    io.emit('showSpiritOfSevaModal', data);
  });

  socket.on('joinGame', (playerName) => {
    if (gameState.players.length >= 4) {
      socket.emit('joinError', 'Cannot join. Game is full.');
      return;
    }

    if (gameState.started) {
      socket.emit('joinError', 'Cannot join. Game already started.');
      return;
    }

    logger.info(`Player ${playerName} (${socket.id}) joined the game`);
    const success = gameState.addPlayer(socket.id, playerName);

    if (success) {
      // Check if this client is a bot by checking the presence of 'bot' or 'Bot' in the name
      const isBot = playerName.includes('Bot') || playerName.includes('bot');
      if (isBot) {
        // Send the simulation speed configuration to the bot client
        socket.emit('botConfig', {
          thinkingSpeed: simulation?.simulationSpeed || 1.0,
          serverTime: Date.now(),
          omTrackEnabled: true, // Always enable Om track features for bots
          prioritizeOmTrack: false // Default value, can be overridden by game settings
        });
        logger.info(`Sent bot configuration to ${playerName} with thinking speed: ${simulation?.simulationSpeed || 1.0}x`);
      }

      // Save the player name on the socket for future lookup.
      socket.playerName = playerName;

      // Add the player's socket to the playerSockets Map
      playerSockets.set(socket.id, socket);

      // Send the updated game state to all players
      io.emit('gameState', gameState.getState());
    } else {
      socket.emit('joinError', 'Unable to join (game might be full or started).');
    }
  });

  socket.on('startGame', () => {
    console.log('[startGame] Received startGame request from socket:', socket.id);
    // Only when startGame is received, try to load the game state from file.
    if (fs.existsSync(SAVE_FILE)) {
      try {
        const data = fs.readFileSync(SAVE_FILE, 'utf8');
        const loadedState = JSON.parse(data);
        // Load saved state and reassign player ids based on matching names.
        gameState.loadState(loadedState, io);
        console.log('[startGame] Game state loaded from file.');
      } catch (error) {
        console.error('[startGame] Error loading game state:', error);
        // On error, start a new game.
        gameState.reset();
        const success = gameState.startGame();
        if (!success) {
          console.log('[startGame] Could not start game - need at least 2 players');
          socket.emit('startError', 'Need at least 2 players to start.');
          return;
        }
        console.log('[startGame] New game started successfully after load error');
      }
    } else {
      const success = gameState.startGame();
      if (!success) {
        console.log('[startGame] Could not start game - need at least 2 players');
        socket.emit('startError', 'Need at least 2 players to start.');
        return;
      }
      console.log('[startGame] New game started successfully');
    }

    // Double-check that game is now flagged as started
    console.log('[startGame] Game started state is now:', gameState.started);

    // Emit updated game state to all clients
    const stateToSend = gameState.getState();
    console.log('[startGame] Emitting gameState with started =', stateToSend.started);
    io.emit('gameState', stateToSend);
  });

  // Check for pending selections when emitting game state
  const emitGameState = () => {
    const state = gameState.getState();

    // Check for any pending selections and notify the appropriate player
    state.players.forEach(player => {
      const socket = playerSockets.get(player.id);
      if (!socket) {
        console.log(`[${new Date().toISOString()}] No socket found for player ${player.id}`);
        return;
      }

      // Check for pending travel card selection
      if (player.pendingCardSelection && player.pendingCardSelection.type === 'travel_card_loss') {
        // If only one card is available, auto-select it
        const travelCards = player.hand.filter(card => card.type === 'travel');
        if (travelCards.length === 1) {
          console.log(`[${new Date().toISOString()}] Auto-selecting only travel card for ${player.name} due to ${player.pendingCardSelection.effect} effect`);
          gameState.handleTravelCardSelection(player.id, [travelCards[0].id]);
        } else if (travelCards.length > 1) {
          // Emit the selection event to the client with detailed information
          console.log(`[${new Date().toISOString()}] Emitting needsTravelCardSelection to player ${player.id} for ${player.pendingCardSelection.effect} effect`);
          console.log(`[${new Date().toISOString()}] Player has ${travelCards.length} travel cards to choose from`);

          // Always send a fresh event to ensure the client receives it
          socket.emit('needsTravelCardSelection', {
            effect: player.pendingCardSelection.effect,
            region: player.pendingCardSelection.region,
            count: player.pendingCardSelection.count,
            cardsInHand: travelCards.length
          });
        } else {
          console.log(`[${new Date().toISOString()}] Player ${player.name} has no travel cards to discard. Clearing pendingCardSelection.`);
          delete player.pendingCardSelection;
        }
      }

      // Check for pending energy cube selection
      if (player.pendingCubeSelection && player.pendingCubeSelection.type === 'energy_cube_loss') {
        socket.emit('needsEnergyCubeSelection', {
          effect: player.pendingCubeSelection.effect,
          region: player.pendingCubeSelection.region,
          count: player.pendingCubeSelection.count
        });
      }

      // Check for pending Heavy Haul cube selection
      if (player.pendingHeavyHaulReward && player.pendingHeavyHaulReward.needsSelection) {
        socket.emit('needsHeavyHaulCubeSelection', {
          travelCardIds: [player.pendingHeavyHaulReward.cardId]
        });
      }
    });

    io.emit('gameState', state);
  };

  socket.on('movePlayer', ({ path, travelCardIds, extraHopCount, isTriathlon }) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} attempting to move:`, {
      path,
      travelCardIds,
      extraHopCount,
      isTriathlon: isTriathlon ? true : false
    });

    const success = gameState.movePlayer(socket.id, path, travelCardIds, extraHopCount, isTriathlon);
    if (success) {
      console.log(`[${new Date().toISOString()}] Player move successful`);

      // After a successful move, emit playerMoved event for animation to all clients
      io.emit('playerMoved', { playerId: socket.id, path: path });

      // After a successful move, check if there's a pending selection that needs to be handled
      const player = gameState._findPlayer(socket.id);
      if (player && player.pendingCardSelection && player.pendingCardSelection.type === 'travel_card_loss') {
        // If the player only has one travel card, automatically select it
        const travelCards = player.hand.filter(card => card.type === 'travel');
        if (travelCards.length === 1) {
          console.log(`[${new Date().toISOString()}] Auto-selecting only travel card for ${player.name} due to ${player.pendingCardSelection.effect} effect`);
          gameState.handleTravelCardSelection(socket.id, [travelCards[0].id]);
        } else if (travelCards.length > 1) {
          // If the player has multiple travel cards, prompt them to select one
          console.log(`[${new Date().toISOString()}] Player ${player.name} needs to select 1 of ${travelCards.length} travel cards to discard for ${player.pendingCardSelection.effect} effect`);
          socket.emit('needsTravelCardSelection', {
            effect: player.pendingCardSelection.effect,
            region: player.pendingCardSelection.region,
            count: player.pendingCardSelection.count
          });
        }
      }
    } else {
      console.log(`[${new Date().toISOString()}] Player move failed`);
      socket.emit('moveError', 'Unable to complete the move. Please check your path and cards.');
    }

    // Use the enhanced emitGameState function
    emitGameState();
  });

  // Add handler for travel card selection
  socket.on('initiateTravelCardSelection', () => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} initiated travel card selection`);
    // Forward to the client to open the travel card selection UI
    socket.emit('initiateTravelCardSelection');
  });

  // Handler for custom movement
  socket.on('customMove', ({ path, withAnimation }) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} attempting custom move:`, { path });

    // Validate the player and the path
    const player = gameState._findPlayer(socket.id);
    if (!player) {
      console.log(`[${new Date().toISOString()}] Player not found for custom move`);
      socket.emit('moveError', 'Player not found for custom move.');
      return;
    }

    // Check if the path starts from the player's current position
    if (path[0] !== player.position) {
      console.log(`[${new Date().toISOString()}] Custom move path must start from player's current position`);
      socket.emit('moveError', 'Custom move path must start from your current position.');
      return;
    }

    // Execute the custom move
    const success = gameState.movePlayer(socket.id, path, [], 0, false);
    if (success) {
      console.log(`[${new Date().toISOString()}] Custom move successful`);

      // If animation is requested, emit the playerMoved event with the path to all clients
      if (withAnimation) {
        io.emit('playerMoved', { playerId: socket.id, path: path });
      }
    } else {
      console.log(`[${new Date().toISOString()}] Custom move failed`);
      socket.emit('moveError', 'Unable to complete the custom move.');
    }

    // Use the enhanced emitGameState function
    emitGameState();
  });

  // Add handler for energy cube selection during Heavy Haul event
  socket.on('selectEnergyCubesForHeavyHaul', ({ selectedCubes, travelCardIds }) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} selected energy cubes for Heavy Haul:`, selectedCubes);

    // Get the player from game state
    const player = gameState._findPlayer(socket.id);

    // Ensure player exists
    if (!player) {
      console.log(`[${new Date().toISOString()}] Player not found for cube selection`);
      socket.emit('moveError', 'Player not found for Heavy Haul cube selection.');
      return;
    }

    // If the player doesn't have a pending reward yet, but has a truck card in hand and the event is active,
    // we'll create the pending reward now
    if (!player.pendingHeavyHaulReward &&
        gameState.currentGlobalEvent &&
        gameState.currentGlobalEvent.effect === 'heavy_haul_reward') {
      // Note: at this point, we don't have the specific card ID available
      // but we can still create the pending reward
      player.pendingHeavyHaulReward = {
        cardId: "unknown", // This is just a placeholder
        outerPoints: 10,    // Player gets 10 points for heavy haul
        needsSelection: true
      };
      console.log(`[${new Date().toISOString()}] Created pending Heavy Haul reward for ${player.name}`);
    }

    // Now pass the selected cubes to the game state
    const success = gameState.handleHeavyHaulCubeSelection(socket.id, selectedCubes, travelCardIds);
    if (success) {
      console.log(`[${new Date().toISOString()}] Heavy Haul cube selection successful`);
    } else {
      console.log(`[${new Date().toISOString()}] Heavy Haul cube selection failed`);
      socket.emit('moveError', 'Unable to process Heavy Haul cube selection.');
    }

    // Use the enhanced emitGameState function
    emitGameState();
  });

  // Add handler for selecting travel cards to discard (for region-based events)
  socket.on('selectTravelCardsToDiscard', ({ selectedCardIds }) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} selected travel cards to discard:`, selectedCardIds);

    const success = gameState.handleTravelCardSelection(socket.id, selectedCardIds);
    if (success) {
      console.log(`[${new Date().toISOString()}] Travel card discard selection successful`);
    } else {
      console.log(`[${new Date().toISOString()}] Travel card discard selection failed`);
      socket.emit('moveError', 'Unable to process travel card discard selection.');
    }

    // Use the enhanced emitGameState function
    emitGameState();
  });

  // Add handler for selecting energy cubes to discard (for region-based events)
  socket.on('selectEnergyCubesToDiscard', ({ selectedCubes }) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} selected energy cubes to discard:`, selectedCubes);

    const success = gameState.handleEnergyCubeSelection(socket.id, selectedCubes);
    if (success) {
      console.log(`[${new Date().toISOString()}] Energy cube discard selection successful`);
    } else {
      console.log(`[${new Date().toISOString()}] Energy cube discard selection failed`);
      socket.emit('moveError', 'Unable to process energy cube discard selection.');
    }

    // Use the enhanced emitGameState function
    emitGameState();
  });

  socket.on('pickCards', (payload) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} picking cards:`, payload);
    const result = gameState.pickCards(socket.id, payload);
    console.log(`[${new Date().toISOString()}] Result of picking cards:`, result ? "Success" : "Failed");
    console.log(`[${new Date().toISOString()}] Current face-up travel cards:`, gameState.faceUpTravel.map(c => c.id));

    // If picking face-up travel cards, broadcast animation data to all clients
    if (payload.type === 'travel' && payload.pickFromFaceUp && payload.pickFromFaceUp.length > 0 && result) {
      // Get the picked card objects to send with the animation
      const pickedCards = result.pickedCards || [];

      // Ensure we have valid card data to send
      const validCards = pickedCards.filter(card => card && typeof card === 'object' && card.id);

      if (validCards.length > 0) {
        // Create a map of card IDs for the animation
        // We don't need actual positions on the server side, clients will calculate them
        const cardPositions = {};
        validCards.forEach(card => {
          // Just set a placeholder - the client will calculate actual positions
          cardPositions[card.id] = true;
        });

        // Broadcast card pick animation event to all clients
        io.emit('cardPickAnimation', {
          playerId: socket.id,
          cards: validCards,
          cardIds: validCards.map(card => card.id),
          cardPositions: cardPositions
        });
      }
    }

    io.emit('gameState', gameState.getState());
  });

  socket.on('collectJourney', ({ journeyCardId, journeyType }) => {
    gameState.collectJourney(socket.id, journeyCardId, journeyType);
    io.emit('gameState', gameState.getState());
  });

  socket.on('endTurn', () => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} ending turn`);
    gameState.endTurn(socket.id);
    io.emit('gameState', gameState.getState());

    // Check if the game is over and emit a gameOver event with the winner
    if (gameState._isGameOver() || gameState._omTokenVictory) {
      let winner;

      // Check for OM token victory first
      if (gameState._omTokenVictory) {
        winner = gameState._findPlayer(gameState._omTokenVictor);
      } else {
        winner = gameState.getGameResult();
      }

      if (winner) {
        // Determine OM token threshold based on number of players
        const omTokenThreshold = gameState.players.length === 3 ? 5 : 7;
        const totalOm = gameState._totalOm(winner);
        const totalScore = winner.outerScore + winner.innerScore;

        // Determine which win condition was met
        const winByOm = gameState._omTokenVictory || totalOm >= omTokenThreshold;
        const winByScore = !gameState._omTokenVictory && totalScore >= 100;

        console.log(`[${new Date().toISOString()}] Game over! Winner: ${winner.name} (OM: ${totalOm}/${omTokenThreshold}, Score: ${totalScore}/100)`);
        console.log(`Win condition: ${winByOm ? 'OM Token Victory' : 'Score Convergence'}`);

        io.emit('gameOver', {
          winner: {
            id: winner.id,
            name: winner.name,
            outerScore: winner.outerScore,
            innerScore: winner.innerScore,
            totalScore: totalScore,
            omTotal: totalOm,
            winByOm: winByOm,
            winByScore: winByScore
          }
        });
      }
    }
  });

  socket.on('tradeEnergyCubes', (selectedCubes) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} trading energy cubes:`, selectedCubes);
    const success = gameState.tradeEnergyCubes(socket.id, selectedCubes);
    if (success) {
      console.log(`[${new Date().toISOString()}] Energy cube trade successful`);
    } else {
      console.log(`[${new Date().toISOString()}] Energy cube trade failed`);
      socket.emit('tradeError', 'Unable to complete the trade. Please check selected cubes.');
    }
    io.emit('gameState', gameState.getState());
  });

  // Handler for Cultural Exchange location swap
  socket.on('initiateLocationSwap', ({ targetPlayerId }) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} initiating location swap with player ${targetPlayerId}`);
    
    // Check if Cultural Exchange event is active
    if (gameState.currentGlobalEvent?.effect !== 'cultural_exchange') {
      console.log(`[${new Date().toISOString()}] Location swap failed: Cultural Exchange event not active`);
      socket.emit('swapError', 'Cultural Exchange event is not active');
      return;
    }
    
    const initiatingPlayer = gameState._findPlayer(socket.id);
    const targetPlayer = gameState._findPlayer(targetPlayerId);
    
    if (!initiatingPlayer || !targetPlayer) {
      console.log(`[${new Date().toISOString()}] Location swap failed: Player not found`);
      socket.emit('swapError', 'Player not found');
      return;
    }
    
    // Get the target player's socket to ask for confirmation
    const targetSocket = playerSockets.get(targetPlayerId);
    if (!targetSocket) {
      console.log(`[${new Date().toISOString()}] Location swap failed: Target player socket not found`);
      socket.emit('swapError', 'Target player not available');
      return;
    }
    
    // Ask the target player for confirmation
    targetSocket.emit('locationSwapRequest', {
      requestingPlayerId: socket.id,
      requestingPlayerName: initiatingPlayer.name,
      currentPosition: initiatingPlayer.position
    });
    
    console.log(`[${new Date().toISOString()}] Location swap request sent to player ${targetPlayerId}`);
  });
  
  // Handler for responding to a location swap request
  socket.on('respondToLocationSwap', ({ requestingPlayerId, accepted }) => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} responding to location swap from ${requestingPlayerId}: ${accepted ? 'Accepted' : 'Declined'}`);
    
    if (!accepted) {
      // Notify the requesting player that the swap was declined
      const requestingSocket = playerSockets.get(requestingPlayerId);
      if (requestingSocket) {
        requestingSocket.emit('locationSwapResponse', {
          accepted: false,
          message: 'Player declined the location swap'
        });
      }
      return;
    }
    
    // Execute the location swap
    const requestingPlayer = gameState._findPlayer(requestingPlayerId);
    const respondingPlayer = gameState._findPlayer(socket.id);
    
    if (!requestingPlayer || !respondingPlayer) {
      console.log(`[${new Date().toISOString()}] Location swap execution failed: Player not found`);
      return;
    }
    
    // Swap positions
    const tempPosition = requestingPlayer.position;
    requestingPlayer.position = respondingPlayer.position;
    respondingPlayer.position = tempPosition;
    
    // Award inner points to both players
    const previousInnerScoreRequesting = requestingPlayer.innerScore;
    const previousInnerScoreResponding = respondingPlayer.innerScore;
    
    requestingPlayer.innerScore += 5;
    respondingPlayer.innerScore += 5;
    
    // Record the event
    gameState._recordEvent('cultural_exchange_swap', requestingPlayerId, {
      previousInnerScore: previousInnerScoreRequesting,
      newInnerScore: requestingPlayer.innerScore,
      swappedWithPlayer: respondingPlayer.id,
      swappedFromPosition: tempPosition,
      swappedToPosition: requestingPlayer.position
    });
    
    gameState._recordEvent('cultural_exchange_swap', respondingPlayer.id, {
      previousInnerScore: previousInnerScoreResponding,
      newInnerScore: respondingPlayer.innerScore,
      swappedWithPlayer: requestingPlayer.id,
      swappedFromPosition: respondingPlayer.position,
      swappedToPosition: tempPosition
    });
    
    console.log(`[${new Date().toISOString()}] Location swap executed successfully between ${requestingPlayer.name} and ${respondingPlayer.name}`);
    
    // Notify both players that the swap was successful
    const requestingSocket = playerSockets.get(requestingPlayerId);
    if (requestingSocket) {
      requestingSocket.emit('locationSwapResponse', {
        accepted: true,
        message: `Successfully swapped positions with ${respondingPlayer.name}`
      });
    }
    
    socket.emit('locationSwapResponse', {
      accepted: true,
      message: `Successfully swapped positions with ${requestingPlayer.name}`
    });
    
    // Update game state for all clients
    emitGameState();
  });

  socket.on('saveGame', () => {
    const state = gameState.saveState();
    try {
      fs.writeFileSync(SAVE_FILE, JSON.stringify(state, null, 2), 'utf8');
      socket.emit('saveSuccess', 'Game state saved successfully.');
    } catch (error) {
      console.error('Error saving game state:', error);
      socket.emit('saveError', 'Failed to save game state.');
    }
  });

  // Handle toggling the nameMode setting
  socket.on('toggleNameMode', () => {
    console.log(`[${new Date().toISOString()}] Player ${socket.id} toggled nameMode setting`);
    // Toggle the nameMode setting
    gameState.nameMode = !gameState.nameMode;
    console.log(`[${new Date().toISOString()}] nameMode is now: ${gameState.nameMode}`);
    // Broadcast the updated game state to all clients
    io.emit('gameState', gameState.getState());
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);

    // Remove the player's socket from the playerSockets Map
    playerSockets.delete(socket.id);
  });

  // Handle adding a bot player (new handler for bot mode)
  socket.on('addBot', (botConfig) => {
    // This endpoint is now deprecated as bots connect directly via websockets
    socket.emit('joinError', 'Bot mode has been updated. Please start bot clients separately with npm run start-bot');

    logger.info(`Deprecated addBot call received - bots now connect directly via websockets`);
  });
});

// Setup event listeners for the simulation
simulation.on('simulationStarted', (data) => {
  io.emit('simulationStarted', data);
});

simulation.on('simulationStopped', (data) => {
  io.emit('simulationStopped', data);
});

simulation.on('simulationPaused', (data) => {
  io.emit('simulationPaused', data);
});

simulation.on('simulationResumed', (data) => {
  io.emit('simulationResumed', data);
});

simulation.on('gameCompleted', (data) => {
  io.emit('simulationGameCompleted', data);
});

simulation.on('allSimulationsCompleted', (data) => {
  io.emit('allSimulationsCompleted', data);
});

// Setup routes and start server only if we're running this file directly
if (require.main === module) {
  // Setup the routes
  setupRoutes(app, gameState);
  
  // Setup simulation endpoints
  setupSimulationEndpoints(app, simulation, gameState);
  
  // Start the server
  httpServer.listen(PORT, '0.0.0.0', () => {
    console.log(`Server listening on all interfaces at port ${PORT}`);
  });
}

/**
 * Get current memory usage statistics
 * @returns {object} Memory usage data
 */
function getMemoryUsage() {
  const memoryUsage = process.memoryUsage();
  return {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024),
    timestamp: Date.now()
  };
}

/**
 * Setup memory monitoring to prevent out-of-memory errors
 */
function setupMemoryMonitoring() {
  let lastGcTime = 0;

  // Check memory usage periodically
  const intervalId = setInterval(() => {
    const memoryUsage = getMemoryUsage();

    // Log memory usage
    logger.info(`Memory usage: RSS=${memoryUsage.rss}MB, Heap Used=${memoryUsage.heapUsed}MB`);

    // Check for warning threshold
    if (memoryUsage.heapUsed > MEMORY_MONITOR.warningThresholdMB) {
      logger.warn(`High memory usage: ${memoryUsage.heapUsed}MB`);

      // Try to free some memory
      if (simulation) {
        // Clear caches in simulation
        if (typeof simulation.clearCaches === 'function') {
          simulation.clearCaches();
        }

        // If bots have PathCalculators, clear their caches
        if (simulation.bots && simulation.bots.length) {
          simulation.bots.forEach(bot => {
            if (bot.strategy && bot.strategy.pathCalculator) {
              bot.strategy.pathCalculator.clearCache();
            }
          });
        }
      }
    }

    // Check for critical threshold
    if (memoryUsage.heapUsed > MEMORY_MONITOR.criticalThresholdMB) {
      logger.error(`Critical memory usage: ${memoryUsage.heapUsed}MB. Stopping simulation.`);

      // Stop any running simulation
      if (simulation && simulation.running) {
        simulation.stop();
      }
    }

    // Try to run garbage collection if available
    if (
      global.gc &&
      memoryUsage.heapUsed > MEMORY_MONITOR.gcThresholdMB &&
      Date.now() - lastGcTime > 60000 // Don't run GC more than once per minute
    ) {
      logger.info('Running garbage collection');
      global.gc();
      lastGcTime = Date.now();
    }
  }, MEMORY_MONITOR.checkIntervalMs);

  // Clean up on process exit
  process.on('exit', () => {
    clearInterval(intervalId);
  });
}

// Add a method to the Simulation class to clear caches
Simulation.prototype.clearCaches = function() {
  logger.info('Clearing simulation caches');

  // Reduce decision logs for all bots
  if (this.bots && this.bots.length) {
    this.bots.forEach(bot => {
      if (bot.decisionsLog && bot.decisionsLog.length > 10) {
        bot.decisionsLog = bot.decisionsLog.slice(-10);
      }
    });
  }
};

// Initialize the server and all components
function initServer(port = 4000) {
  return new Promise((resolve, reject) => {
    try {
      // Create HTTP server from Express app
      const httpServer = createServer(app);

      // Initialize Socket.IO
      const io = new Server(httpServer, {
        cors: {
          origin: '*',
        },
      });

      // Create game state
      const gameState = new GameState();
      const SAVE_FILE = 'gameState.json'; // File to save the game state

      // Create the simulation controller
      const simulation = new Simulation({
        gameState,
        io,
        logger
      });

      // Setup memory monitoring
      if (MEMORY_MONITOR.enabled) {
        setupMemoryMonitoring();
      }

      // Store instances in app locals for API routes to access
      app.locals.gameState = gameState;
      app.locals.simulation = simulation;

      // Set up routes
      setupRoutes(app, gameState);

      // Set up socket connections
      setupSocketConnections(io, gameState, simulation);

      // Setup simulation endpoints directly
      setupSimulationEndpoints(app, simulation, gameState);

      // Start the server - bind to all interfaces (0.0.0.0)
      httpServer.listen(port, '0.0.0.0', () => {
        logger.info(`Server listening on all interfaces at port ${port}`);
        resolve({ app, httpServer, io, gameState, simulation });
      });
    } catch (error) {
      reject(error);
    }
  });
}

// Setup all routes
function setupRoutes(app, gameState) {
  console.log('setting up main routes');
  app.get('/', (req, res) => {
    res.send('Om: The Journey server is running.');
  });

  // Memory monitoring endpoint
  app.get('/api/memory', (req, res) => {
    const memoryUsage = getMemoryUsage();
    res.json(memoryUsage);
  });

  // Game events endpoint
  app.get('/api/events', (req, res) => {
    const { limit, type, playerId } = req.query;

    let events = gameState.gameEvents || [];

    // Filter by type if specified
    if (type) {
      events = events.filter(event => event.type === type);
    }

    // Filter by player if specified
    if (playerId) {
      events = events.filter(event => event.playerId === playerId);
    }

    // Limit the number of events if specified
    if (limit && !isNaN(parseInt(limit))) {
      events = events.slice(-parseInt(limit));
    }

    res.json({
      totalEvents: gameState.gameEvents.length,
      filteredEvents: events.length,
      rounds: gameState.roundCount,
      events
    });
  });
  
  // Routes for simulation metrics
  app.get('/api/metrics', (req, res) => {
    // Basic game metrics
    const metrics = {
      roundCount: gameState.roundCount,
      totalEvents: gameState.gameEvents.length,
      playerCount: gameState.players.length,
      isGameOver: gameState._isGameOver(),
      isFinalRound: gameState._finalRound,
      roundsPerEventType: {},
      playerStats: gameState.players.map(p => {
        // Calculate total hops from move events for this player
        const playerMoveEvents = gameState.gameEvents.filter(
          event => event.type === 'move' && event.playerId === p.id
        );
        const totalHops = playerMoveEvents.reduce((total, event) => {
          // Path length represents the number of hops in a move
          return total + (event.data?.path?.length || 0);
        }, 0);

        // Get energy cubes total
        const energyCubesTotal = (p.energyCubes?.bhakti || 0) + (p.energyCubes?.gnana || 0) +
                                (p.energyCubes?.karma || 0) + (p.energyCubes?.artha || 0);

        return {
          id: p.id,
          name: p.name,
          outerScore: p.outerScore,
          innerScore: p.innerScore,
          totalScore: p.outerScore + p.innerScore,
          omTotal: gameState._totalOm(p),
          journeyCards: p.collectedJourneys.length,
          energyCubes: {
            bhakti: p.energyCubes?.bhakti || 0,
            gnana: p.energyCubes?.gnana || 0,
            karma: p.energyCubes?.karma || 0,
            artha: p.energyCubes?.artha || 0,
            total: energyCubesTotal
          },
          totalHops: totalHops,
          // For dashboard aggregated statistics
          avgEnergyCubes: energyCubesTotal,
          avgJourneyCards: p.collectedJourneys.length,
          avgHops: totalHops
        };
      })
    };

    // Count events by type
    if (gameState.gameEvents) {
      gameState.gameEvents.forEach(event => {
        if (!metrics.roundsPerEventType[event.type]) {
          metrics.roundsPerEventType[event.type] = 0;
        }
        metrics.roundsPerEventType[event.type]++;
      });
    }

    res.json(metrics);
  });

  // Add health check endpoint
  app.get('/api/health', (req, res) => {
    res.json({
      status: 'ok',
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      timestamp: Date.now(),
      simulation: {
        active: app.locals.simulation ? app.locals.simulation.running : false,
        bots: app.locals.simulation ? app.locals.simulation.bots.length : 0,
        gamesPlayed: app.locals.simulation ? app.locals.simulation.statistics.gamesPlayed : 0
      }
    });
  });
}

// Setup Socket.IO connections
function setupSocketConnections(io, gameState, simulation) {
  // Socket.IO setup is now handled in the main code directly
}

// Add simulation API endpoints
function setupSimulationEndpoints(app, simulation, gameState) {
  // Configure the simulation
  console.log('Setting up simulation API endpoints');
  
  app.get('/api/simulation/strategies', (req, res) => {
    res.json({
      strategies: simulation.getAvailableStrategies()
    });
  });
  
  app.post('/api/simulation/configure', (req, res) => {
    console.log('Called simulation configure endpoint');
    try {
      const { numGames, speed, outputStats, autoRestart, maxGames, simulationSpeed, saveResults, enableFeatures, forceReset, botThinkingSpeed } = req.body;

      const config = {
        autoRestart: autoRestart,
        maxGames: maxGames || numGames,
        simulationSpeed: simulationSpeed || speed,
        saveResults: saveResults || outputStats,
        enableFeatures,
        // Explicitly pass bot thinking speed if provided
        botThinkingSpeed: botThinkingSpeed || simulationSpeed || speed
      };

      // Log the configuration for debugging
      console.log(`[${new Date().toISOString()}] Configuring simulation with:`, JSON.stringify(config));

      // If forceReset is true, force reset the game state
      if (forceReset) {
        console.log(`[${new Date().toISOString()}] Force resetting game state`);
        // Force reset the game state and clear the started flag
        gameState.reset();
        gameState.started = false;
        
        // Also clear any bots from the simulation
        if (simulation.bots && simulation.bots.length > 0) {
          console.log(`[${new Date().toISOString()}] Clearing ${simulation.bots.length} bots`);
          simulation.bots = [];
        }
      }

      if (enableFeatures) {
        console.log(`[${new Date().toISOString()}] Enabled features:`, JSON.stringify(enableFeatures));

        // Store enabled features in simulation object for use in SimulationRunner
        simulation.enabledFeatures = enableFeatures;

        // Update game state with feature configurations
        if (simulation.gameState) {
          // Set hand limit if specified
          if (typeof enableFeatures.handLimit === 'number') {
            simulation.gameState.handLimit = enableFeatures.handLimit;
            console.log(`[${new Date().toISOString()}] Updated hand limit to ${enableFeatures.handLimit}`);
          }

          // Enable/disable global events
          simulation.gameState.globalEventsEnabled = !!enableFeatures.globalEvents;

          // Enable/disable character cards
          simulation.gameState.characterCardsEnabled = !!enableFeatures.playerCharacters;

          // Enable/disable vehicles
          simulation.gameState.vehiclesEnabled = !!enableFeatures.vehicles;
        }
      }

      simulation.configure(config);
      res.json({ status: 'ok', message: 'Simulation configured' });
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error configuring simulation:`, error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });

  // Add a bot to the simulation
  app.post('/api/simulation/add-bot', (req, res) => {
    try {
      // Accept either 'strategy' or 'strategyName' for compatibility
      const { name, strategy, strategyName } = req.body;
      const actualStrategy = strategyName || strategy || 'random';

      console.log(`[${new Date().toISOString()}] Adding bot with name: ${name}, strategy: ${actualStrategy}`);

      // Store available strategies for error checking
      const availableStrategies = simulation.getAvailableStrategies();
      console.log(`[${new Date().toISOString()}] Available strategies: ${availableStrategies.join(', ')}`);

      if (!availableStrategies.includes(actualStrategy)) {
        console.warn(`[${new Date().toISOString()}] Warning: Strategy '${actualStrategy}' may not be available. Using 'random' as fallback.`);
      }

      // Add the bot
      const bot = simulation.addBot({
        name,
        strategy: actualStrategy,  // Pass strategy for backwards compatibility
        strategyName: actualStrategy  // This is what BotFactory expects
      });

      // Debug: Count current bots after adding
      console.log(`[${new Date().toISOString()}] Current bot count: ${simulation.bots.length}`);

      res.json({
        status: 'ok',
        message: `Bot ${name} added with ${actualStrategy} strategy`,
        bot: {
          id: bot.id,
          name: bot.name,
          strategy: actualStrategy
        },
        totalBots: simulation.bots.length
      });
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error adding bot:`, error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });

  // Start the simulation
  app.post('/api/simulation/start', (req, res) => {
    try {
      console.log(`[${new Date().toISOString()}] Starting simulation with ${simulation.bots.length} bots`);

      // Verify that we have enough bots
      if (simulation.bots.length < 2) {
        console.error(`[${new Date().toISOString()}] Not enough bots to start simulation (have ${simulation.bots.length}, need at least 2)`);
        return res.status(400).json({
          status: 'error',
          message: `Need at least 2 bots to start simulation (have ${simulation.bots.length})`
        });
      }

      // List the bots we're starting with
      simulation.bots.forEach((bot, index) => {
        console.log(`[${new Date().toISOString()}] Bot ${index+1}: id=${bot.id}, name=${bot.name}, strategy=${bot.strategy?.name || 'unknown'}`);
      });

      // Start the simulation
      const result = simulation.start();
      console.log(`[${new Date().toISOString()}] Simulation started result:`, result);

      res.json({
        status: 'ok',
        message: 'Simulation started',
        gameNumber: result.gameNumber,
        botCount: simulation.bots.length,
        bots: simulation.bots.map(b => ({
          id: b.id,
          name: b.name,
          strategy: b.strategy?.name || 'unknown'
        }))
      });
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error starting simulation:`, error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });

  // Stop the simulation
  app.post('/api/simulation/stop', (req, res) => {
    try {
      simulation.stop();
      res.json({ status: 'ok', message: 'Simulation stopped' });
    } catch (error) {
      res.status(500).json({ status: 'error', message: error.message });
    }
  });

  // Get simulation status
  app.get('/api/simulation/status', (req, res) => {
    try {
      // Get stats from simulation object which contains the data we need
      const simulationStats = simulation.getStats();
      
      const status = {
        // The status field which the client checks to determine if a game is complete
        status: simulation.running ? 'running' : (simulation.paused ? 'paused' : 'stopped'),
        
        // Enhanced stats object with the fields client expects
        stats: {
          currentGame: simulation.currentSimulation ? {
            gameNumber: simulation.currentSimulation.gameNumber,
            turnCount: simulation.currentSimulation.turnCount,
            roundCount: gameState.roundCount,
            // Add more fields that might be useful for the client
            elapsedTime: Date.now() - (simulation.currentSimulation.startTime || Date.now())
          } : null,
          
          // Make sure lastCompletedGame is properly set when we have completed games
          lastCompletedGame: simulation.statistics.gamesPlayed > 0 ? {
            gameNumber: simulation.statistics.gamesPlayed,
            // Include details about the last game if available
            roundCount: simulationStats.lastGameRoundCount || 0,
            completed: true
          } : null,
          
          gamesPlayed: simulation.statistics.gamesPlayed,
          totalGames: simulation.config.maxGames || 0,
          // Add fields to help with debugging
          autoRestart: simulation.config.autoRestart || false
        },
        
        running: simulation.running,
        paused: simulation.paused,
        
        bots: simulation.bots.map(bot => ({
          id: bot.id,
          name: bot.name,
          strategy: typeof bot.strategy === 'object' ? bot.strategy.name : bot.strategy,
          turnsPlayed: bot.turnsPlayed || 0
        })),
        
        statistics: simulation.statistics,
        
        gameState: {
          roundCount: gameState.roundCount,
          turnCount: gameState.turnCount,
          started: gameState.started,
          players: gameState.players.map(p => ({
            id: p.id,
            name: p.name,
            score: (p.outerScore || 0) + (p.innerScore || 0),
            outerScore: p.outerScore || 0,
            innerScore: p.innerScore || 0
          }))
        }
      };
      
      res.json(status);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error getting simulation status:`, error);
      res.status(500).json({ status: 'error', message: error.message });
    }
  });
}

// Export the initServer function for use in start_server.js
module.exports = { initServer };