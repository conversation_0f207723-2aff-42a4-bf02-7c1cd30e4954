// Using createElement instead of JSX to avoid automatic JSX runtime imports
const React = require('react');
const { useState } = React;
const OmJourney = require('./Game');
const { Client } = require('boardgame.io/react');
const { SocketIO } = require('boardgame.io/multiplayer');
const { OmJourneyBoard } = require('./OmJourneyClient');

// Create the client component outside of the GameApp component
const GameClient = Client({
  game: OmJourney,
  board: OmJourneyBoard,
  numPlayers: 2,
  debug: false,
  multiplayer: SocketIO({ 
    server: 'http://localhost:8000',
    socketOpts: { 
      reconnectionAttempts: 3,
      timeout: 2000
    }
  })
});

const GameApp = () => {
  const [numPlayers, setNumPlayers] = useState(2);
  const [playerID, setPlayerID] = useState(null);
  const [playerName, setPlayerName] = useState('');
  const [gameID, setGameID] = useState('default');
  const [multiplayer, setMultiplayer] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);

  // Start a new game
  const startGame = () => {
    console.log(`Starting game with ID: ${gameID}, player: ${playerID}, name: ${playerName}`);
    setGameStarted(true);
  };

  // App navigation component
  const AppNavigation = () => 
    React.createElement('div', { className: "app-navigation" },
      React.createElement('a', { 
        href: "#", 
        className: "active" 
      }, "Game"),
      React.createElement('a', { 
        href: "#simulation"
      }, "Simulation Dashboard")
    );

  // Render the setup screen if the game is not started
  if (!gameStarted) {
    return React.createElement('div', { className: "app-container" },
      React.createElement(AppNavigation, null),
      React.createElement('div', { className: "game-setup" },
        React.createElement('h2', null, "Om: The Journey"),
        React.createElement('div', { className: "setup-options" },
          React.createElement('div', { className: "option-group" },
            React.createElement('label', null, "Number of Players:"),
            React.createElement('select', {
              value: numPlayers,
              onChange: (e) => setNumPlayers(parseInt(e.target.value))
            },
              React.createElement('option', { value: "2" }, "2"),
              React.createElement('option', { value: "3" }, "3"),
              React.createElement('option', { value: "4" }, "4")
            )
          ),
          React.createElement('div', { className: "option-group" },
            React.createElement('label', null, "Game ID:"),
            React.createElement('input', {
              type: "text",
              value: gameID,
              onChange: (e) => setGameID(e.target.value),
              placeholder: "Enter game ID"
            })
          ),
          React.createElement('div', { className: "option-group" },
            React.createElement('label', null, "Your Name:"),
            React.createElement('input', {
              type: "text",
              value: playerName,
              onChange: (e) => setPlayerName(e.target.value),
              placeholder: "Enter your name"
            })
          ),
          React.createElement('div', { className: "option-group" },
            React.createElement('label', null, "Player ID:"),
            React.createElement('select', {
              value: playerID || '',
              onChange: (e) => setPlayerID(e.target.value || null)
            },
              React.createElement('option', { value: "" }, "Select player"),
              [...Array(numPlayers)].map((_, i) => 
                React.createElement('option', { key: i, value: i.toString() }, `Player ${i + 1}`)
              )
            )
          ),
          React.createElement('button', {
            onClick: startGame,
            disabled: !playerID || !playerName || !gameID
          }, "Join Game")
        )
      )
    );
  }

  // Render the game
  return React.createElement('div', null,
    React.createElement(AppNavigation, null),
    React.createElement('div', { className: "game-container" },
      React.createElement('h1', { className: "game-title" }, "Om: The Journey"),
      React.createElement('div', { className: "game-header" },
        React.createElement('div', { className: "current-player" }, 
          React.createElement('span', null, "Current Player: "), 
          React.createElement('strong', null, `Player ${playerID}`)
        ),
        React.createElement('div', { className: "game-round" }, 
          React.createElement('span', null, "Round: "), 
          React.createElement('strong', null, "Infinity")
        )
      ),
      React.createElement(GameClient, {
        gameID,
        playerID,
        credentials: playerName
      })
    )
  );
};

module.exports = GameApp;