const { INVALID } = require('boardgame.io/core');
const boardgameio = require('boardgame.io/core');
const { TurnOrder } = require('boardgame.io/core');

// Constants
const MAX_ENERGY_CUBES = 5;
const MAX_OM_TOKENS = 3;
const OM_TOKEN_REQUIREMENTS = [1, 1, 2, 3];
const ENERGY_CUBE_TYPES = ['artha', 'karma', 'gnana', 'bhakti'];
const WIN_SCORE_THRESHOLD = 100;
// OM token threshold varies by player count: 5 for 3 players, 7 for 2 players
const WIN_OM_TOKENS = 7; // Default for 2 players

// Complete board data from the server implementation
const BOARD_DATA = {
  locations: [
    // ---------------------
    //  NORTH (8 main)
    // ---------------------
    { id: 1,  name: 'Vaishno Devi',           region: 'North',     journeyType: 'Inner' },
    { id: 2,  name: 'Chail',                  region: 'North',     journeyType: 'Outer' },
    { id: 3,  name: 'Golden Temple',          region: 'North',     journeyType: 'Inner' },
    { id: 4,  name: 'Durgiana Temple',        region: 'North',     journeyType: 'Inner' },
    { id: 6,  name: 'Taj Mahal',              region: 'North',     journeyType: 'Outer' },
    { id: 7,  name: 'Haridwar',               region: 'North',     journeyType: 'Inner' },
    { id: 9,  name: 'Valley of Flowers',      region: 'North',     journeyType: 'Outer' },

    //  NORTH Jyotirlingas (2)
    { id: 54, name: 'Kedarnath',              region: 'North',     journeyType: 'Inner' },
    { id: 56, name: 'Viswanath',              region: 'North',     journeyType: 'Inner' },

    //  NORTH Airport (1)
    { id: 61, name: 'North Airport',          region: 'North',     journeyType: 'Airport' },


    // ---------------------
    //  WEST (8 main)
    // ---------------------
    { id: 10, name: 'Ambaji Temple',          region: 'West',      journeyType: 'Inner' },
    { id: 11, name: 'Rann of Kutch',          region: 'West',      journeyType: 'Outer' },
    { id: 12, name: 'Jaisalmer Fort',         region: 'West',      journeyType: 'Outer' },
    { id: 13, name: 'Hawa Mahal',             region: 'West',      journeyType: 'Outer' },
    { id: 14, name: 'Brahma Temple, Pushkar', region: 'West',      journeyType: 'Inner' },
    { id: 16, name: 'Siddhivinayak Temple',   region: 'West',      journeyType: 'Inner' },
    { id: 19, name: 'Basilica of Bom Jesus',  region: 'West',      journeyType: 'Inner' },

    //  WEST Jyotirlingas (2)
    { id: 49, name: 'Somnath',                region: 'West',      journeyType: 'Inner' },
    { id: 50, name: 'Nageswar',               region: 'West',      journeyType: 'Inner' },

    //  WEST Airport (1)
    { id: 62, name: 'West Airport',           region: 'West',      journeyType: 'Airport' },


    // ---------------------
    //  SOUTH (8 main)
    // ---------------------
    { id: 20, name: 'Meenakshi Temple',       region: 'South',     journeyType: 'Inner' },
    { id: 21, name: 'Annamalaiyar Temple',    region: 'South',     journeyType: 'Inner' },
    { id: 22, name: 'Nilgiri Hills',          region: 'South',     journeyType: 'Outer' },
    { id: 23, name: 'Sabarimala',             region: 'South',     journeyType: 'Inner' },
    { id: 24, name: 'Padmanabhaswamy Temple', region: 'South',     journeyType: 'Inner' },
    { id: 25, name: 'Backwaters of Kerala',   region: 'South',     journeyType: 'Outer' },
    { id: 27, name: 'Hampi',                  region: 'South',     journeyType: 'Outer' },

    //  SOUTH Jyotirlingas (2)
    { id: 51, name: 'Mallikarjuna',           region: 'South',     journeyType: 'Inner' },
    { id: 59, name: 'Rameshwar',              region: 'South',     journeyType: 'Inner' },

    //  SOUTH Airport (1)
    { id: 63, name: 'South Airport',          region: 'South',     journeyType: 'Airport' },


    // ---------------------
    //  CENTRAL (8 main)
    // ---------------------
    { id: 5,  name: 'Kurukshetra',            region: 'Central',   journeyType: 'Outer' },
    { id: 15, name: 'Shirdi Sai Baba Temple', region: 'Central',   journeyType: 'Inner' },
    { id: 18, name: 'Ajanta and Ellora Caves',region: 'Central',   journeyType: 'Outer' },
    { id: 28, name: 'Mysore Palace',          region: 'Central',   journeyType: 'Outer' },
    { id: 29, name: 'Coorg',                  region: 'Central',   journeyType: 'Outer' },
    { id: 33, name: 'Khajuraho Temples',      region: 'Central',   journeyType: 'Outer' },
    { id: 34, name: 'Pachmarhi',              region: 'Central',   journeyType: 'Outer' },

    //  CENTRAL Jyotirlingas (2)
    { id: 52, name: 'Mahakaleswar',           region: 'Central',   journeyType: 'Inner' },
    { id: 53, name: 'Omkareshwar',            region: 'Central',   journeyType: 'Inner' },

    //  CENTRAL Airport (1)
    { id: 64, name: 'Central Airport',        region: 'Central',   journeyType: 'Airport' },


    // ---------------------
    //  EAST (8 main)
    // ---------------------
    { id: 30, name: 'Charminar',              region: 'East',      journeyType: 'Outer' },
    { id: 31, name: 'Ramoji Film City',       region: 'East',      journeyType: 'Outer' },
    { id: 32, name: 'Tirumala Venkateswara',  region: 'East',      journeyType: 'Inner' },
    { id: 36, name: 'Jagannath Temple',       region: 'East',      journeyType: 'Inner' },
    { id: 37, name: 'Chilika Lake',           region: 'East',      journeyType: 'Outer' },
    { id: 38, name: 'Tarapith',               region: 'East',      journeyType: 'Inner' },
    { id: 39, name: 'Betla National Park',    region: 'East',      journeyType: 'Outer' },

    //  EAST Jyotirlingas (2)
    { id: 55, name: 'Bhimashankar',           region: 'East',      journeyType: 'Inner' },
    { id: 58, name: 'Vaidyanath',             region: 'East',      journeyType: 'Inner' },

    //  EAST Airport (1)
    { id: 65, name: 'East Airport',           region: 'East',      journeyType: 'Airport' },


    // ---------------------
    //  NORTHEAST (8 main)
    // ---------------------
    { id: 42, name: 'Kamakhya Temple',        region: 'Northeast', journeyType: 'Inner' },
    { id: 43, name: 'Living Root Bridge',     region: 'Northeast', journeyType: 'Outer' },
    { id: 44, name: 'Ujjayanta Palace',       region: 'Northeast', journeyType: 'Outer' },
    { id: 45, name: 'Kangla Fort',            region: 'Northeast', journeyType: 'Outer' },
    { id: 46, name: 'Phawngpui Blue Mountain',region: 'Northeast', journeyType: 'Outer' },
    { id: 47, name: 'Dzükou Valley',          region: 'Northeast', journeyType: 'Outer' },
    { id: 48, name: 'Rumtek Monastery',       region: 'Northeast', journeyType: 'Inner' },

    //  NORTHEAST Jyotirlingas (2)
    { id: 57, name: 'Triambakeshwar',         region: 'Northeast', journeyType: 'Inner' },
    { id: 60, name: 'Grishneshwar',           region: 'Northeast', journeyType: 'Inner' },

    //  NORTHEAST Airport (1)
    { id: 66, name: 'Northeast Airport',      region: 'Northeast', journeyType: 'Airport' },
  ],

  edges: [
    //
    // N O R T H   R E G I O N
    //
    { from: 1, to: 2 },
    { from: 2, to: 3 },
    { from: 3, to: 4 },
    { from: 4, to: 6 },
    { from: 6, to: 7 },
    { from: 1, to: 9 },
    // Jyotirlingas connections
    { from: 9, to: 54 },  // Kedarnath
    { from: 3, to: 56 },  // Viswanath
    { from: 7, to: 54 },  // Haridwar to Kedarnath
    // Airport connections
    { from: 4, to: 61 },
    { from: 6, to: 61 },
    // Inter‐regional edges
    { from: 7, to: 11 },  // North <-> West
    { from: 3, to: 47 },  // North <-> Northeast

    //
    // W E S T   R E G I O N
    //
    { from: 10, to: 11 },
    { from: 11, to: 12 },
    { from: 12, to: 13 },
    { from: 13, to: 14 },
    { from: 14, to: 16 },
    { from: 14, to: 19 },
    { from: 13, to: 34 },  // West to Central
    // Jyotirlingas connections
    { from: 10, to: 49 }, // Somnath
    { from: 12, to: 50 }, // Nageswar
    // Airport connections
    { from: 11, to: 62 },
    { from: 13, to: 62 },
    { from: 19, to: 62 },
    // Inter‐regional edges
    { from: 16, to: 27 }, // West <-> South

    //
    // S O U T H   R E G I O N
    //
    { from: 20, to: 21 },
    { from: 21, to: 22 },
    { from: 22, to: 23 },
    { from: 23, to: 24 },
    { from: 24, to: 25 },
    { from: 25, to: 27 },
    // Jyotirlingas connections
    { from: 20, to: 51 }, // Mallikarjuna
    { from: 23, to: 59 }, // Rameshwar
    // Airport connections
    { from: 24, to: 63 },
    { from: 25, to: 63 },
    { from: 21, to: 63 },
    // Inter‐regional edges
    { from: 21, to: 28 }, // South <-> Central

    //
    // C E N T R A L   R E G I O N
    //
    { from: 5,  to: 15 },
    { from: 15, to: 18 },
    { from: 18, to: 28 },
    { from: 28, to: 29 },
    { from: 29, to: 33 },
    { from: 33, to: 34 },
    { from: 28, to: 37 },  // Central to East
    // Jyotirlingas connections
    { from: 5,  to: 52 }, // Mahakaleswar
    { from: 18, to: 53 }, // Omkareshwar
    // Airport connections
    { from: 15, to: 64 },
    { from: 29, to: 64 },

    //
    // E A S T   R E G I O N
    //
    { from: 30, to: 31 },
    { from: 31, to: 32 },
    { from: 32, to: 36 },
    { from: 65, to: 37 },
    { from: 37, to: 38 },
    { from: 38, to: 39 },
    // Jyotirlingas connections
    { from: 30, to: 55 }, // Bhimashankar
    { from: 32, to: 58 }, // Vaidyanath
    // Airport connections
    { from: 31, to: 65 },
    { from: 39, to: 65 },
    // Inter‐regional edges
    { from: 23, to: 38 }, // South <-> East
    { from: 31, to: 45 }, // East <-> Northeast

    //
    // N O R T H E A S T   R E G I O N
    //
    { from: 48, to: 42 },
    { from: 42, to: 43 },
    { from: 43, to: 44 },
    { from: 44, to: 45 },
    { from: 45, to: 46 },
    { from: 46, to: 47 },
    { from: 47, to: 57 },
    { from: 57, to: 48 },  // Triambakeshwar to Rumtek Monastery
    // Jyotirlingas connections
    { from: 43, to: 60 }, // Grishneshwar
    // Airport connections
    { from: 42, to: 66 },
    { from: 44, to: 66 },
  ],

  travelDeck: [
    { id: 'T1', type: 'travel', value: 1, vehicle: 'camel' },
    { id: 'T2', type: 'travel', value: 1, vehicle: 'horse' },
    { id: 'T3', type: 'travel', value: 1, vehicle: 'trek' },
    { id: 'T4', type: 'travel', value: 1, vehicle: 'cycle' },
    { id: 'T5', type: 'travel', value: 2, vehicle: 'bus' },
    { id: 'T6', type: 'travel', value: 2, vehicle: 'car' },
    { id: 'T7', type: 'travel', value: 2, vehicle: 'rickshaw' },
    { id: 'T8', type: 'travel', value: 2, vehicle: 'motorbike' },
    { id: 'T9', type: 'travel', value: 3, vehicle: 'helicopter' },
    { id: 'T10', type: 'travel', value: 3, vehicle: 'boat' },
    { id: 'T11', type: 'travel', value: 3, vehicle: 'train' },
    { id: 'T12', type: 'travel', value: 3, vehicle: 'truck' },
  ],

  eventDeck: [
    { id: 'E1', type: 'extraHop' },
    { id: 'E2', type: 'extraHop' },
    { id: 'E3', type: 'extraHop' },
    { id: 'E4', type: 'extraHop' },
    { id: 'E5', type: 'extraHop' },
    { id: 'E6', type: 'wildCube' },
    { id: 'E7', type: 'wildCube' },
    { id: 'E8', type: 'wildCube' },
    { id: 'E9', type: 'wildCube' },
  ],

  journeyDeckInner: [
    { id: 'JI1',  locationId: 1,  required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
    { id: 'JI2',  locationId: 3,  required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
    { id: 'JI3',  locationId: 4,  required: { bhakti: 1 },            reward: { inner: 20 } },
    { id: 'JI4',  locationId: 7,  required: { bhakti: 2, gnana: 1 }, reward: { inner: 27 } },
    { id: 'JI6',  locationId: 10, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
    { id: 'JI7',  locationId: 14, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
    { id: 'JI8',  locationId: 15, required: { bhakti: 1, gnana: 2 }, reward: { inner: 27 } },
    { id: 'JI9',  locationId: 16, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
    { id: 'JI10', locationId: 19, required: { bhakti: 2, gnana: 1 }, reward: { inner: 27 } },
    { id: 'JI11', locationId: 20, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
    { id: 'JI12', locationId: 21, required: { bhakti: 1 },            reward: { inner: 20 } },
    { id: 'JI13', locationId: 23, required: { bhakti: 1, gnana: 2 }, reward: { inner: 27 } },
    { id: 'JI14', locationId: 24, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
    { id: 'JI16', locationId: 32, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
    { id: 'JI17', locationId: 36, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
    { id: 'JI18', locationId: 38, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
    { id: 'JI21', locationId: 42, required: { bhakti: 1, gnana: 1 }, reward: { inner: 24 } },
    { id: 'JI22', locationId: 48, required: { bhakti: 2, gnana: 2 }, reward: { inner: 30 } },
  ],

  journeyDeckOuter: [
    { id: 'JO1',  locationId: 2,  required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
    { id: 'JO2',  locationId: 5,  required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
    { id: 'JO3',  locationId: 6,  required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
    { id: 'JO4',  locationId: 9,  required: { karma: 1 },           reward: { outer: 20 } },
    { id: 'JO5',  locationId: 11, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
    { id: 'JO6',  locationId: 12, required: { karma: 2, artha: 2 }, reward: { outer: 30 } },
    { id: 'JO7',  locationId: 13, required: { karma: 1 },           reward: { outer: 20 } },
    { id: 'JO9',  locationId: 18, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
    { id: 'JO10', locationId: 22, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
    { id: 'JO11', locationId: 25, required: { karma: 1 },           reward: { outer: 20 } },
    { id: 'JO12', locationId: 27, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
    { id: 'JO13', locationId: 28, required: { karma: 2, artha: 2 }, reward: { outer: 30 } },
    { id: 'JO14', locationId: 29, required: { karma: 1 },           reward: { outer: 20 } },
    { id: 'JO15', locationId: 30, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
    { id: 'JO16', locationId: 31, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
    { id: 'JO17', locationId: 33, required: { karma: 1 },           reward: { outer: 20 } },
    { id: 'JO18', locationId: 34, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
    { id: 'JO20', locationId: 37, required: { karma: 1 },           reward: { outer: 20 } },
    { id: 'JO21', locationId: 39, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
    { id: 'JO22', locationId: 43, required: { karma: 1, artha: 2 }, reward: { outer: 27 } },
    { id: 'JO23', locationId: 44, required: { karma: 2, artha: 2 }, reward: { outer: 30 } },
    { id: 'JO24', locationId: 45, required: { karma: 1 },           reward: { outer: 20 } },
    { id: 'JO25', locationId: 46, required: { karma: 1, artha: 1 }, reward: { outer: 24 } },
    { id: 'JO26', locationId: 47, required: { karma: 2, artha: 1 }, reward: { outer: 27 } },
  ],
};

// Define the complete set of global event effects
const GLOBAL_EVENTS = [
  { id: 'GE1', name: 'No Inner Journey Cards', effect: 'no_inner_journey_cards' },
  { id: 'GE2', name: 'Biker Gang Reward', effect: 'biker_gang_reward' },
  { id: 'GE3', name: 'Desert Caravan Reward', effect: 'desert_caravan_reward' },
  { id: 'GE4', name: 'Heavy Haul Reward', effect: 'heavy_haul_reward' },
  { id: 'GE5', name: 'Inner Journey Disruption (North)', effect: 'region_effect_inner', region: 'North' },
  { id: 'GE6', name: 'Outer Journey Blessing (West)', effect: 'region_effect_outer', region: 'West' },
  { id: 'GE7', name: 'Inner Journey Disruption (West)', effect: 'region_effect_inner', region: 'West' },
  { id: 'GE8', name: 'Outer Journey Blessing (North)', effect: 'region_effect_outer', region: 'North' },
  { id: 'GE9', name: 'Inner Journey Disruption (South)', effect: 'region_effect_inner', region: 'South' },
  { id: 'GE10', name: 'Outer Journey Blessing (South)', effect: 'region_effect_outer', region: 'South' },
  { id: 'GE11', name: 'Inner Journey Disruption (Central)', effect: 'region_effect_inner', region: 'Central' },
  { id: 'GE12', name: 'Outer Journey Blessing (Central)', effect: 'region_effect_outer', region: 'Central' },
  { id: 'GE13', name: 'Inner Journey Disruption (East)', effect: 'region_effect_inner', region: 'East' },
  { id: 'GE14', name: 'Outer Journey Blessing (East)', effect: 'region_effect_outer', region: 'East' },
  { id: 'GE15', name: 'Inner Journey Disruption (Northeast)', effect: 'region_effect_inner', region: 'Northeast' },
  { id: 'GE16', name: 'Outer Journey Blessing (Northeast)', effect: 'region_effect_outer', region: 'Northeast' },
  { id: 'GE17', name: 'Triple Card Reward', effect: 'triple_card_reward' },
  { id: 'GE18', name: 'Energy Cube Loss (Central)', effect: 'energy_cube_loss', region: 'Central' },
  { id: 'GE19', name: 'Energy Cube Loss (North)', effect: 'energy_cube_loss', region: 'North' },
  { id: 'GE20', name: 'Energy Cube Loss (South)', effect: 'energy_cube_loss', region: 'South' },
  { id: 'GE21', name: 'Energy Cube Loss (West)', effect: 'energy_cube_loss', region: 'West' },
  { id: 'GE22', name: 'Energy Cube Loss (East)', effect: 'energy_cube_loss', region: 'East' },
  { id: 'GE23', name: 'Energy Cube Loss (Northeast)', effect: 'energy_cube_loss', region: 'Northeast' },
  { id: 'GE24', name: 'Helicopter Bonus', effect: 'helicopter_bonus' },
  { id: 'GE25', name: 'Train Reward', effect: 'train_reward' }
];

// Character cards with special abilities
const CHARACTER_DECK = [
  {
    id: 'C1',
    name: 'The Sage',
    ability: 'gnana_trade_bonus',
    description: 'When trading for Gnana cubes, receive an extra cube'
  },
  {
    id: 'C2',
    name: 'The Devotee',
    ability: 'bhakti_trade_bonus',
    description: 'When trading for Bhakti cubes, receive an extra cube'
  },
  {
    id: 'C3',
    name: 'The Businessman',
    ability: 'artha_trade_bonus',
    description: 'When trading for Artha cubes, receive an extra cube'
  },
  {
    id: 'C4',
    name: 'The Worker',
    ability: 'karma_trade_bonus',
    description: 'When trading for Karma cubes, receive an extra cube'
  },
  {
    id: 'C5',
    name: 'The Teacher',
    ability: 'card_draw_bonus',
    description: 'Draw one extra card when picking travel cards'
  },
  {
    id: 'C6',
    name: 'The Explorer',
    ability: 'extra_hop',
    description: 'Once per turn, you may move one extra hop without using a card'
  },
  {
    id: 'C7',
    name: 'The Pilgrim',
    ability: 'inner_journey_discount',
    description: 'Pay one less cube for Inner Journey cards (minimum 1)'
  },
  {
    id: 'C8',
    name: 'The Tourist',
    ability: 'outer_journey_discount',
    description: 'Pay one less cube for Outer Journey cards (minimum 1)'
  }
];

/**
 * Helper to shuffle an array
 */
function shuffle(array) {
  const result = [...array];
  for (let i = result.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [result[i], result[j]] = [result[j], result[i]];
  }
  return result;
}

/**
 * Check if an edge exists between two locations
 */
function edgeExists(a, b) {
  return BOARD_DATA.edges.some(
    edge => (edge.from === a && edge.to === b) || (edge.from === b && edge.to === a)
  );
}

/**
 * Find the shortest path between two locations using breadth-first search
 */
function findShortestPath(startId, endId) {
  const visited = new Set([startId]);
  const queue = [[startId]]; // Queue of paths

  while (queue.length > 0) {
    const path = queue.shift();
    const currentId = path[path.length - 1];

    if (currentId === endId) {
      return path;
    }

    // Find all adjacent nodes
    const neighbors = BOARD_DATA.edges
      .filter(edge => edge.from === currentId || edge.to === currentId)
      .map(edge => edge.from === currentId ? edge.to : edge.from);

    for (const neighbor of neighbors) {
      if (!visited.has(neighbor)) {
        visited.add(neighbor);
        const newPath = [...path, neighbor];
        queue.push(newPath);
      }
    }
  }

  return null; // No path found
}

/**
 * Validate if a path is legal based on the board connections and travel cards
 */
function validatePath(path, travelCards, extraHopCards, hand) {
  // Check if path is connected
  for (let i = 0; i < path.length - 1; i++) {
    if (!edgeExists(path[i], path[i + 1])) {
      return false;
    }
  }

  // Calculate total hops
  const totalHops = path.length - 1;

  // Calculate travel card values
  const travelCardValues = travelCards.reduce((sum, cardId) => {
    const card = hand.find(c => c.id === cardId);
    return sum + (card ? card.value : 0);
  }, 0);

  // Add extra hops from extraHop cards
  const extraHops = extraHopCards ? extraHopCards.length : 0;

  // Total hops must equal the sum of travel card values plus extra hops
  return totalHops === travelCardValues + extraHops;
}

/**
 * Get a location by ID
 */
function getLocation(locationId) {
  return BOARD_DATA.locations.find(loc => loc.id === locationId);
}

/**
 * Get the journey cards available at a location
 */
function getJourneyCardsForLocation(locationId, G) {
  return G.journeyCards.filter(card => card.locationId === locationId);
}

/**
 * Check if a player can collect a journey
 */
function canCollectJourney(player, journeyCard, wild = 0) {
  const required = journeyCard.required;

  // For each required energy cube type, check if player has enough
  for (const [type, count] of Object.entries(required)) {
    if ((player.energyCubes[type] || 0) < count) {
      const deficit = count - (player.energyCubes[type] || 0);
      // Check if we can cover the deficit with wild cubes
      if (deficit > wild) {
        return false;
      }
      wild -= deficit;
    }
  }

  return true;
}

/**
 * Spend cubes for a journey card
 */
function spendCubesForJourney(player, journeyCard, wild = 0) {
  const required = journeyCard.required;
  const updatedPlayer = { ...player };
  const updatedCubes = { ...player.energyCubes };
  let usedWild = 0;

  // For each required energy cube type, spend the cubes
  for (const [type, count] of Object.entries(required)) {
    const available = updatedCubes[type] || 0;
    if (available >= count) {
      updatedCubes[type] = available - count;
    } else {
      const deficit = count - available;
      updatedCubes[type] = 0;
      wild -= deficit;
      usedWild += deficit;
    }
  }

  updatedPlayer.energyCubes = updatedCubes;
  return { player: updatedPlayer, usedWild };
}

/**
 * Apply character trade rewards for energy cube trades
 */
function applyCharacterTradeRewards(G, player, receivedCubeType) {
  // Skip if player has no character
  if (!player.character) {
    return player;
  }

  const updatedPlayer = { ...player };
  const updatedCubes = { ...player.energyCubes };

  // Apply character abilities
  switch (player.character.ability) {
    case 'bhakti_trade_bonus':
      if (receivedCubeType === 'bhakti') {
        updatedCubes.bhakti += 1;
        G.energyCubePiles.bhakti -= 1;
      }
      break;
    case 'gnana_trade_bonus':
      if (receivedCubeType === 'gnana') {
        updatedCubes.gnana += 1;
        G.energyCubePiles.gnana -= 1;
      }
      break;
    case 'karma_trade_bonus':
      if (receivedCubeType === 'karma') {
        updatedCubes.karma += 1;
        G.energyCubePiles.karma -= 1;
      }
      break;
    case 'artha_trade_bonus':
      if (receivedCubeType === 'artha') {
        updatedCubes.artha += 1;
        G.energyCubePiles.artha -= 1;
      }
      break;
  }

  updatedPlayer.energyCubes = updatedCubes;
  return updatedPlayer;
}

/**
 * Apply region-based effects based on the current global event
 */
function applyRegionBasedEffects(G, ctx, player, startRegion, path, usedCards) {
  // Skip if no current global event or not a region effect
  if (!G.currentGlobalEvent ||
      !(G.currentGlobalEvent.effect === 'region_effect_inner' ||
        G.currentGlobalEvent.effect === 'region_effect_outer' ||
        G.currentGlobalEvent.effect === 'energy_cube_loss')) {
    return player;
  }

  // Skip if the player's starting region doesn't match the affected region
  if (startRegion !== G.currentGlobalEvent.region) {
    return player;
  }

  // Create updated player
  const updatedPlayer = { ...player };

  // Apply the effect
  switch (G.currentGlobalEvent.effect) {
    case 'region_effect_inner':
      // Players starting in this region must discard a travel card
      if (player.hand.some(card => card.type === 'travel')) {
        updatedPlayer.pendingCardSelection = {
          type: 'travel_card_loss',
          effect: 'region_effect',
          region: G.currentGlobalEvent.region,
          count: 1
        };
      }
      break;
    case 'energy_cube_loss':
      // Players starting in this region must discard an energy cube
      if (Object.values(player.energyCubes).some(count => count > 0)) {
        updatedPlayer.pendingCubeSelection = {
          type: 'energy_cube_loss',
          effect: 'region_effect',
          region: G.currentGlobalEvent.region,
          count: 1
        };
      }
      break;
    case 'region_effect_outer':
      // Players gain a random energy cube when starting from this region
      const cubeType = getRandomEnergyCube();
      if (G.energyCubePiles[cubeType] > 0) {
        updatedPlayer.energyCubes[cubeType] = (updatedPlayer.energyCubes[cubeType] || 0) + 1;
        G.energyCubePiles[cubeType]--;

        // Record event
        G.gameEvents.push({
          type: 'cube_gain',
          playerId: player.id,
          round: G.roundCount,
          data: {
            cubeType,
            source: 'global_event',
            effect: G.currentGlobalEvent.effect
          }
        });
      }
      break;
  }

  return updatedPlayer;
}

/**
 * Apply travel card effects when cards are used for movement
 */
function applyTravelCardEffects(G, ctx, player, usedCards) {
  // Skip if no travel cards used
  if (!usedCards || !usedCards.length) {
    return player;
  }

  // Get card objects from IDs
  const cards = usedCards.map(cardId =>
    player.hand.find(card => card.id === cardId)
  ).filter(Boolean);

  // Create updated player
  let updatedPlayer = { ...player };

  // Apply global event effects
  if (G.currentGlobalEvent) {
    // Biker Gang Reward - Using any bike card awards an OM token
    if (G.currentGlobalEvent.effect === 'biker_gang_reward') {
      const bikeCard = cards.find(card => card.vehicle === 'motorbike' || card.vehicle === 'cycle');
      if (bikeCard) {
        // Award an OM token
        const omTypes = ['bhakti', 'gnana', 'karma', 'artha'];
        const randomType = omTypes[Math.floor(Math.random() * omTypes.length)];

        // Check if player has not reached max OM tokens
        const totalOmTokens = Object.values(updatedPlayer.omTokens).reduce((sum, count) => sum + count, 0);
        if (totalOmTokens < MAX_OM_TOKENS) {
          updatedPlayer.omTokens[randomType] = (updatedPlayer.omTokens[randomType] || 0) + 1;

          // Record event
          G.gameEvents.push({
            type: 'om_gain',
            playerId: player.id,
            round: G.roundCount,
            data: {
              omType: randomType,
              source: 'global_event',
              effect: 'biker_gang_reward',
              cardId: bikeCard.id
            }
          });
        }
      }
    }

    // Desert Caravan Reward - Using any camel card awards an OM token
    if (G.currentGlobalEvent.effect === 'desert_caravan_reward') {
      const camelCard = cards.find(card => card.vehicle === 'camel');
      if (camelCard) {
        // Award an OM token
        const omTypes = ['bhakti', 'gnana', 'karma', 'artha'];
        const randomType = omTypes[Math.floor(Math.random() * omTypes.length)];

        // Check if player has not reached max OM tokens
        const totalOmTokens = Object.values(updatedPlayer.omTokens).reduce((sum, count) => sum + count, 0);
        if (totalOmTokens < MAX_OM_TOKENS) {
          updatedPlayer.omTokens[randomType] = (updatedPlayer.omTokens[randomType] || 0) + 1;

          // Record event
          G.gameEvents.push({
            type: 'om_gain',
            playerId: player.id,
            round: G.roundCount,
            data: {
              omType: randomType,
              source: 'global_event',
              effect: 'desert_caravan_reward',
              cardId: camelCard.id
            }
          });
        }
      }
    }

    // Heavy Haul Reward - Using any truck card awards 10 outer journey points and energy cubes
    if (G.currentGlobalEvent.effect === 'heavy_haul_reward') {
      const truckCard = cards.find(card => card.vehicle === 'truck');
      if (truckCard) {
        // Create a pending reward that requires cube selection
        updatedPlayer.pendingHeavyHaulReward = {
          cardId: truckCard.id,
          outerPoints: 10,
          needsSelection: true
        };

        // Record event
        G.gameEvents.push({
          type: 'pending_reward',
          playerId: player.id,
          round: G.roundCount,
          data: {
            type: 'heavy_haul',
            cardId: truckCard.id
          }
        });
      }
    }

    // Train Reward - Using any train card allows drawing a random energy cube
    if (G.currentGlobalEvent.effect === 'train_reward') {
      const trainCard = cards.find(card => card.vehicle === 'train');
      if (trainCard) {
        // Add a random energy cube
        const cubeType = getRandomEnergyCube();
        if (G.energyCubePiles[cubeType] > 0) {
          updatedPlayer.energyCubes[cubeType] = (updatedPlayer.energyCubes[cubeType] || 0) + 1;
          G.energyCubePiles[cubeType]--;

          // Record event
          G.gameEvents.push({
            type: 'cube_gain',
            playerId: player.id,
            round: G.roundCount,
            data: {
              cubeType,
              source: 'global_event',
              effect: 'train_reward',
              cardId: trainCard.id
            }
          });
        }
      }
    }

    // Helicopter Bonus - Using any helicopter card allows extra Om token
    if (G.currentGlobalEvent.effect === 'helicopter_bonus') {
      const helicopterCard = cards.find(card => card.vehicle === 'helicopter');
      if (helicopterCard) {
        // Award an OM token
        const omTypes = ['bhakti', 'gnana', 'karma', 'artha'];
        const randomType = omTypes[Math.floor(Math.random() * omTypes.length)];

        // Check if player has not reached max OM tokens
        const totalOmTokens = Object.values(updatedPlayer.omTokens).reduce((sum, count) => sum + count, 0);
        if (totalOmTokens < MAX_OM_TOKENS) {
          updatedPlayer.omTokens[randomType] = (updatedPlayer.omTokens[randomType] || 0) + 1;

          // Record event
          G.gameEvents.push({
            type: 'om_gain',
            playerId: player.id,
            round: G.roundCount,
            data: {
              omType: randomType,
              source: 'global_event',
              effect: 'helicopter_bonus',
              cardId: helicopterCard.id
            }
          });
        }
      }
    }
  }

  return updatedPlayer;
}

/**
 * Handle the selection of energy cubes for Heavy Haul reward
 */
function handleHeavyHaulCubeSelection(G, ctx, playerId, selectedCubes, travelCardIds) {
  // Find the player
  const playerIndex = G.players.findIndex(p => p.id === playerId);
  if (playerIndex === -1) return false;

  const player = G.players[playerIndex];

  // Verify player has a pending Heavy Haul reward
  if (!player.pendingHeavyHaulReward || !player.pendingHeavyHaulReward.needsSelection) {
    return false;
  }

  // Verify card ID matches if we have travelCardIds
  if (travelCardIds && travelCardIds.length &&
      player.pendingHeavyHaulReward.cardId !== "unknown" &&
      !travelCardIds.includes(player.pendingHeavyHaulReward.cardId)) {
    return false;
  }

  // Update player's energy cubes
  const updatedPlayer = { ...player };

  // Add the selected energy cubes
  Object.entries(selectedCubes).forEach(([type, count]) => {
    if (G.energyCubePiles[type] >= count) {
      updatedPlayer.energyCubes[type] = (updatedPlayer.energyCubes[type] || 0) + count;
      G.energyCubePiles[type] -= count;
    }
  });

  // Add outer points
  updatedPlayer.outerScore += player.pendingHeavyHaulReward.outerPoints;

  // Clear the pending reward
  updatedPlayer.pendingHeavyHaulReward = null;

  // Record event
  G.gameEvents.push({
    type: 'heavy_haul_reward_claimed',
    playerId,
    round: G.roundCount,
    data: {
      selectedCubes,
      outerPoints: player.pendingHeavyHaulReward.outerPoints
    }
  });

  // Update player in game state
  G.players[playerIndex] = updatedPlayer;

  return true;
}

/**
 * Handle the selection of travel cards to discard (for region-based events)
 */
function handleTravelCardSelection(G, ctx, playerId, selectedCardIds) {
  // Find the player
  const playerIndex = G.players.findIndex(p => p.id === playerId);
  if (playerIndex === -1) return false;

  const player = G.players[playerIndex];

  // Verify player has a pending card selection
  if (!player.pendingCardSelection || player.pendingCardSelection.type !== 'travel_card_loss') {
    return false;
  }

  // Verify correct number of cards selected
  if (selectedCardIds.length !== player.pendingCardSelection.count) {
    return false;
  }

  // Verify selected cards are in player's hand and are travel cards
  const validSelection = selectedCardIds.every(cardId => {
    const card = player.hand.find(c => c.id === cardId);
    return card && card.type === 'travel';
  });

  if (!validSelection) return false;

  // Update player's hand
  const updatedPlayer = { ...player };
  updatedPlayer.hand = player.hand.filter(card => !selectedCardIds.includes(card.id));

  // Clear the pending selection
  updatedPlayer.pendingCardSelection = null;

  // Record event
  G.gameEvents.push({
    type: 'travel_card_discarded',
    playerId,
    round: G.roundCount,
    data: {
      selectedCardIds,
      effect: player.pendingCardSelection.effect,
      region: player.pendingCardSelection.region
    }
  });

  // Update player in game state
  G.players[playerIndex] = updatedPlayer;

  return true;
}

/**
 * Handle the selection of energy cubes to discard (for region-based events)
 */
function handleEnergyCubeSelection(G, ctx, playerId, selectedCubes) {
  // Find the player
  const playerIndex = G.players.findIndex(p => p.id === playerId);
  if (playerIndex === -1) return false;

  const player = G.players[playerIndex];

  // Verify player has a pending cube selection
  if (!player.pendingCubeSelection || player.pendingCubeSelection.type !== 'energy_cube_loss') {
    return false;
  }

  // Calculate total cubes selected
  const totalSelected = Object.values(selectedCubes).reduce((sum, count) => sum + count, 0);

  // Verify correct number of cubes selected
  if (totalSelected !== player.pendingCubeSelection.count) {
    return false;
  }

  // Verify player has the selected cubes
  const validSelection = Object.entries(selectedCubes).every(([type, count]) =>
    player.energyCubes[type] >= count
  );

  if (!validSelection) return false;

  // Update player's energy cubes
  const updatedPlayer = { ...player };
  const updatedCubes = { ...player.energyCubes };

  Object.entries(selectedCubes).forEach(([type, count]) => {
    updatedCubes[type] -= count;
    G.energyCubePiles[type] += count;
  });

  updatedPlayer.energyCubes = updatedCubes;

  // Clear the pending selection
  updatedPlayer.pendingCubeSelection = null;

  // Record event
  G.gameEvents.push({
    type: 'energy_cube_discarded',
    playerId,
    round: G.roundCount,
    data: {
      selectedCubes,
      effect: player.pendingCubeSelection.effect,
      region: player.pendingCubeSelection.region
    }
  });

  // Update player in game state
  G.players[playerIndex] = updatedPlayer;

  return true;
}

/**
 * Check win condition for the game
 */
function checkWinCondition(G) {
  // Determine OM token threshold based on number of players
  const playerCount = Object.keys(G.players).length;
  const omTokenThreshold = playerCount === 3 ? 5 : WIN_OM_TOKENS;

  // Check if any player has reached both the score threshold and the OM token requirement
  return G.players.some(player => {
    const totalScore = player.innerScore + player.outerScore;
    const totalOm = Object.values(player.omTokens).reduce((sum, count) => sum + count, 0);

    return totalScore >= WIN_SCORE_THRESHOLD && totalOm >= omTokenThreshold;
  });
}

/**
 * Get the game result (winner)
 */
function getGameResult(G) {
  // Determine OM token threshold based on number of players
  const playerCount = Object.keys(G.players).length;
  const omTokenThreshold = playerCount === 3 ? 5 : WIN_OM_TOKENS;

  // Find players who meet win conditions
  const winners = G.players.filter(player => {
    const totalScore = player.innerScore + player.outerScore;
    const totalOm = Object.values(player.omTokens).reduce((sum, count) => sum + count, 0);

    return totalScore >= WIN_SCORE_THRESHOLD && totalOm >= omTokenThreshold;
  });

  // If there are winners, return the one with the highest score
  if (winners.length > 0) {
    return winners.reduce((highest, player) => {
      const playerTotal = player.innerScore + player.outerScore;
      const highestTotal = highest.innerScore + highest.outerScore;

      return playerTotal > highestTotal ? player : highest;
    }, winners[0]);
  }

  return null;
}

/**
 * Get a random energy cube type
 */
function getRandomEnergyCube() {
  const types = ENERGY_CUBE_TYPES;
  return types[Math.floor(Math.random() * types.length)];
}

// Define the game directly as an object (no Game function wrapper)
const OmJourney = {
  name: 'om-journey',

  setup: (ctx) => {
    // Setup the initial game state
    return {
      players: {},
      // other state properties
    };
  },

  moves: {
    movePlayer: (G, ctx, path) => {
      // Move implementation
      return G;
    },

    pickCards: (G, ctx, options) => {
      // Pick cards implementation
      return G;
    },

    // ... other moves
  },

  turn: {
    order: TurnOrder.DEFAULT
  },

  endIf: (G, ctx) => {
    if (G.gameOver) {
      // Determine OM token threshold based on number of players
      const playerCount = Object.keys(G.players).length;
      const omTokenThreshold = playerCount === 3 ? 5 : WIN_OM_TOKENS;

      // Get the winner's total OM and score
      const winner = G.winner;
      if (winner) {
        const totalOm = Object.values(winner.omTokens).reduce((sum, count) => sum + count, 0);
        const totalScore = winner.innerScore + winner.outerScore;

        // Determine which win condition was met
        const winByOm = totalOm >= omTokenThreshold;
        const winByScore = totalScore >= WIN_SCORE_THRESHOLD;

        return {
          winner: {
            ...winner,
            omTotal: totalOm,
            totalScore: totalScore,
            winByOm: winByOm,
            winByScore: winByScore
          }
        };
      }

      return { winner: G.winner };
    }
  }
};

// Export the OmJourney game
module.exports = OmJourney;