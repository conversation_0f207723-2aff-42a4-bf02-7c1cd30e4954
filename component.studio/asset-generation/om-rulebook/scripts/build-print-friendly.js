const fs = require('fs');
const path = require('path');
const handlebars = require('handlebars');
const MarkdownIt = require('markdown-it');
const sass = require('sass');

const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true
});

// Paths
const srcDir = path.join(__dirname, '../src');
const outputDir = path.join(__dirname, '../output');
const pagesDir = path.join(srcDir, 'pages');
const templatesDir = path.join(srcDir, 'templates');
const stylesDir = path.join(srcDir, 'styles');
const assetsDir = path.join(__dirname, '../assets');
const outputAssetsDir = path.join(outputDir, 'assets');
const clientAssetsDir = path.join(__dirname, '../../../../client/public/assets');

// Ensure output directories exist
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

if (!fs.existsSync(outputAssetsDir)) {
    fs.mkdirSync(outputAssetsDir, { recursive: true });
}

// Create directories for assets if they don't exist
if (!fs.existsSync(path.join(outputAssetsDir, 'images'))) {
    fs.mkdirSync(path.join(outputAssetsDir, 'images'), { recursive: true });
}

if (!fs.existsSync(path.join(outputAssetsDir, 'icons'))) {
    fs.mkdirSync(path.join(outputAssetsDir, 'icons'), { recursive: true });
}

async function compileStyles() {
    console.log('Compiling SCSS...');
    try {
        const result = sass.compile(path.join(stylesDir, 'print-friendly.scss'));
        fs.writeFileSync(path.join(outputDir, 'print-friendly.css'), result.css);
        console.log('✓ Styles compiled successfully');
    } catch (error) {
        console.error('Error compiling SCSS:', error);
        throw error;
    }
}

function loadTemplate(templateName) {
    try {
        const templatePath = path.join(templatesDir, `${templateName}.hbs`);
        if (!fs.existsSync(templatePath)) {
            console.error(`Template not found: ${templatePath}`);
            throw new Error(`Template ${templateName}.hbs not found`);
        }
        const templateContent = fs.readFileSync(templatePath, 'utf8');
        return handlebars.compile(templateContent);
    } catch (error) {
        console.error(`Error loading template ${templateName}:`, error);
        throw error;
    }
}

function loadMarkdownPages() {
    const pages = [];
    
    if (!fs.existsSync(pagesDir)) {
        console.error(`Pages directory not found: ${pagesDir}`);
        return pages;
    }
    
    const pageFiles = fs.readdirSync(pagesDir)
        .filter(file => file.endsWith('.md'))
        .sort();

    for (const file of pageFiles) {
        const filePath = path.join(pagesDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Remove journey cards references
        let modifiedContent = content.replace(/journey card(s)?/gi, 'card$1');
        
        // Make sure "When you collect Om Tokens" is properly bold
        modifiedContent = modifiedContent.replace(/\*\*When you collect Om Tokens\*\*/g, '**When you collect Om Tokens**');
        
        const html = md.render(modifiedContent);
        
        pages.push({
            filename: file,
            title: extractTitle(modifiedContent),
            content: html
        });
    }

    return pages;
}

function extractTitle(markdown) {
    const match = markdown.match(/^#\s+(.+)$/m);
    return match ? match[1] : 'Om: The Journey';
}

function processImagesForPrint(html) {
    // Replace image tags with placeholders
    return html.replace(/<img.*?src="(.*?)".*?alt="(.*?)".*?>/g, (match, src, alt) => {
        // Determine size based on image filename or alt text
        let size = 'medium';
        if (src.includes('board') || alt.includes('board') || src.includes('large') || alt.includes('large')) {
            size = 'large';
        } else if (src.includes('small') || alt.includes('small') || src.includes('icon')) {
            size = 'small';
        }
        
        return `<div class="image-placeholder ${size}"><p>Image: ${alt || 'Placeholder'}</p></div>`;
    });
}

async function generatePrintFriendlyHTML() {
    console.log('Generating Print-Friendly HTML...');
    
    try {
        // Load templates
        console.log('Loading templates...');
        const baseTemplate = loadTemplate('print-friendly');
        
        // Load pages
        console.log('Loading content pages...');
        const pages = loadMarkdownPages();
        
        if (pages.length === 0) {
            console.warn('No markdown pages found!');
        }
        
        let fullContent = '';
        
        // Add all content pages
        for (const page of pages) {
            fullContent += `<section id="${page.filename.replace('.md', '')}" class="rulebook-section">`;
            fullContent += `<h1 class="section-title">${page.title}</h1>`;
            
            // Process images - replace with placeholders
            const processedContent = processImagesForPrint(page.content);
            fullContent += processedContent;
            
            fullContent += '</section>';
        }
        
        // Generate final HTML
        const finalHTML = baseTemplate({
            title: 'Om: The Journey Rulebook',
            content: fullContent
        });
        
        const htmlPath = path.join(outputDir, 'rulebook-print.html');
        fs.writeFileSync(htmlPath, finalHTML);
        console.log('✓ Print-Friendly HTML generated successfully');
        
        return htmlPath;
    } catch (error) {
        console.error('Error generating HTML:', error);
        throw error;
    }
}

async function copyAssets() {
    console.log('Copying assets...');
    
    try {
        // Copy cover art
        const coverArtSrc = path.join(clientAssetsDir, 'images/om_the_journey_poster.jpg');
        const coverArtDest = path.join(outputAssetsDir, 'images/om_the_journey_poster.jpg');
        
        if (fs.existsSync(coverArtSrc)) {
            fs.copyFileSync(coverArtSrc, coverArtDest);
            console.log('✓ Cover art copied successfully');
        } else {
            console.warn(`Cover art not found at: ${coverArtSrc}`);
        }
        
        // Copy icons
        const iconsDir = path.join(assetsDir, 'icons');
        const outputIconsDir = path.join(outputAssetsDir, 'icons');
        
        if (fs.existsSync(iconsDir)) {
            if (!fs.existsSync(outputIconsDir)) {
                fs.mkdirSync(outputIconsDir, { recursive: true });
            }
            
            copyRecursive(iconsDir, outputIconsDir);
        } else {
            console.warn(`Icons directory not found: ${iconsDir}`);
        }
        
        console.log('✓ Assets copied successfully');
    } catch (error) {
        console.error('Error copying assets:', error);
        throw error;
    }
}

function copyRecursive(src, dest) {
    if (!fs.existsSync(src)) {
        console.warn(`Source directory not found: ${src}`);
        return;
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
            if (!fs.existsSync(destPath)) {
                fs.mkdirSync(destPath, { recursive: true });
            }
            copyRecursive(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

async function main() {
    try {
        console.log('🚀 Building Om: The Journey Print-Friendly Rulebook...');
        
        // Step 1: Compile styles
        await compileStyles();
        
        // Step 2: Copy assets
        await copyAssets();
        
        // Step 3: Generate HTML
        await generatePrintFriendlyHTML();
        
        console.log('✅ Print-Friendly Rulebook generated successfully!');
        console.log(`📖 Open ${path.join(outputDir, 'rulebook-print.html')} to view and print the rulebook`);
    } catch (error) {
        console.error('❌ Build failed:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

// Export for use in other scripts
module.exports = { main }; 