/**
 * SimulationRunner.js
 * A utility class to set up and run simulations with proper game state initialization.
 */

const Simulation = require('./Simulation');
const GameState = require('../gameState');
const Logger = require('./Logger');
const path = require('path');
const fs = require('fs');

class SimulationRunner {
  /**
   * Create a new simulation runner
   * @param {object} options - Simulation options
   * @param {number} [options.numGames=1] - Number of games to run
   * @param {number} [options.speed=1] - Speed multiplier (0.1 to 10)
   * @param {Array} [options.bots] - Array of bot configurations {name, strategy}
   * @param {boolean} [options.saveOutput=true] - Whether to save simulation results
   * @param {boolean} [options.memoryStats=false] - Whether to track memory usage
   * @param {boolean} [options.verbose=false] - Verbose logging
   * @param {object} [options.enabledFeatures] - Enabled game features
   */
  constructor(options = {}) {
    this.options = {
      numGames: 1,
      speed: 1,
      bots: [
        { name: 'Bot-1', strategy: 'defensive' },
        { name: 'Bot-2', strategy: 'defensive' }
      ],
      saveOutput: true,
      memoryStats: false,
      verbose: false,
      enabledFeatures: {
        globalEvents: true,
        playerCharacters: true,
        vehicles: true,
        handLimit: 4
      },
      ...options
    };

    // Create a timestamp for this simulation run
    this.timestamp = Date.now();

    // Set up logger
    this.logger = new Logger({
      logToFile: true,
      logToConsole: this.options.verbose,
      logFilePath: path.join(__dirname, '..', 'logs', `simulation_${this.timestamp}.log`)
    });

    // Log enabled features
    this.logger.info(`Enabled features: ${JSON.stringify(this.options.enabledFeatures)}`);

    // Create game state
    this.gameState = new GameState();

    // Apply feature configurations to game state
    this._applyFeatureConfigurations();

    // Create the simulation
    this.simulation = new Simulation({
      gameState: this.gameState,
      logger: this.logger,
      io: { emit: () => {} } // Mock socket.io
    });

    // Configure the simulation
    this.simulation.configure({
      autoRestart: false,
      maxGames: this.options.numGames,
      saveResults: this.options.saveOutput,
      simulationSpeed: this.options.speed
    });

    // Log memory usage periodically
    if (this.options.memoryStats) {
      this.memoryInterval = setInterval(() => {
        const memUsage = process.memoryUsage();
        this.logger.info(`Memory usage: RSS=${Math.round(memUsage.rss / (1024 * 1024))}MB, Heap Used=${Math.round(memUsage.heapUsed / (1024 * 1024))}MB`);
      }, 30000); // Every 30 seconds
    }

    // Initialize the game board data
    this._initializeGameData();
  }

  /**
   * Apply feature configurations to the game state
   * @private
   */
  _applyFeatureConfigurations() {
    const features = this.options.enabledFeatures;

    // Apply hand limit if specified
    if (typeof features.handLimit === 'number') {
      this.gameState.handLimit = features.handLimit;
      this.logger.info(`Set hand limit to ${features.handLimit}`);
    }

    // Enable/disable global events
    this.gameState.globalEventsEnabled = !!features.globalEvents;
    this.logger.info(`Global events ${this.gameState.globalEventsEnabled ? 'enabled' : 'disabled'}`);

    // Enable/disable character cards
    this.gameState.characterCardsEnabled = !!features.playerCharacters;
    this.logger.info(`Character cards ${this.gameState.characterCardsEnabled ? 'enabled' : 'disabled'}`);

    // Enable/disable vehicles
    this.gameState.vehiclesEnabled = !!features.vehicles;
    this.logger.info(`Vehicles ${this.gameState.vehiclesEnabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Initialize game state with required data
   * @private
   */
  _initializeGameData() {
    try {
      // Pre-load board data
      const boardData = require('../boardData');

      // Pre-load character cards only if enabled
      let characterCards = [];
      if (this.gameState.characterCardsEnabled) {
        try {
          characterCards = require('../characterCards');
          this.logger.info(`Loaded ${characterCards.length} character cards`);
        } catch (error) {
          this.logger.warn(`Failed to load character cards: ${error.message}`);
        }
      }

      // Log board data details for debugging
      this.logger.info(`Loaded board data: ${boardData.locations.length} locations, ${boardData.edges.length} edges`);

      // Ensure game state has locations and edges data
      this.gameState.locations = boardData.locations;
      this.gameState.edges = boardData.edges;

      // Initialize decks with the predefined cards
      this.gameState.travelDeck = [...boardData.travelDeck];
      this.gameState.eventDeck = [...boardData.eventDeck];
      this.gameState.journeyDeckInner = [...boardData.journeyDeckInner];
      this.gameState.journeyDeckOuter = [...boardData.journeyDeckOuter];

      // Initialize global events only if enabled
      if (this.gameState.globalEventsEnabled && boardData.globalEventDeck) {
        this.gameState.globalEventDeck = [...boardData.globalEventDeck];
        this.logger.info(`Loaded ${this.gameState.globalEventDeck.length} global event cards`);
      } else {
        this.gameState.globalEventDeck = [];
      }

      // Initialize character cards only if enabled
      if (this.gameState.characterCardsEnabled) {
        this.gameState.characterDeck = [...characterCards];
      } else {
        this.gameState.characterDeck = [];
      }

      // Initialize vehicle types only if enabled
      if (this.gameState.vehiclesEnabled && boardData.vehicleTypes) {
        this.gameState.vehicleTypes = [...boardData.vehicleTypes];
        this.logger.info(`Loaded ${this.gameState.vehicleTypes.length} vehicle types`);
      } else {
        this.gameState.vehicleTypes = [];
      }

      // Initialize discard piles
      this.gameState.travelDiscard = [];
      this.gameState.eventDiscard = [];
      this.gameState.journeyDiscardInner = [];
      this.gameState.journeyDiscardOuter = [];
      this.gameState.globalEventDiscard = [];

      // Initialize face-up cards
      this.gameState.faceUpTravel = [];
      this.gameState.faceUpEvent = [];
      this.gameState.faceUpJourneyInner = [];
      this.gameState.faceUpJourneyOuter = [];

      // Initialize active global event
      this.gameState.activeGlobalEvent = null;

      // Shuffle all decks
      this._shuffleDeck(this.gameState.travelDeck);
      this._shuffleDeck(this.gameState.eventDeck);
      this._shuffleDeck(this.gameState.journeyDeckInner);
      this._shuffleDeck(this.gameState.journeyDeckOuter);

      // Only shuffle these decks if the features are enabled
      if (this.gameState.globalEventsEnabled) {
        this._shuffleDeck(this.gameState.globalEventDeck);
      }

      if (this.gameState.characterCardsEnabled) {
        this._shuffleDeck(this.gameState.characterDeck);
      }

      // Deal face-up cards
      for (let i = 0; i < 4; i++) {
        if (this.gameState.travelDeck.length > 0) {
          this.gameState.faceUpTravel.push(this.gameState.travelDeck.pop());
        }
        if (this.gameState.eventDeck.length > 0) {
          this.gameState.faceUpEvent.push(this.gameState.eventDeck.pop());
        }
        if (this.gameState.journeyDeckInner.length > 0) {
          this.gameState.faceUpJourneyInner.push(this.gameState.journeyDeckInner.pop());
        }
        if (this.gameState.journeyDeckOuter.length > 0) {
          this.gameState.faceUpJourneyOuter.push(this.gameState.journeyDeckOuter.pop());
        }
      }

      // Initialize location cubes and OM tokens
      this.gameState.locationCubes = {};
      this.gameState.locationOm = {};

      // Get all 48 standard locations
      const standardLocs = boardData.locations.filter(loc => loc.id <= 48).map(loc => loc.id);
      const energyTypes = ['artha', 'karma', 'gnana', 'bhakti'];
      const allCubes = [];

      // Create 12 cubes of each type (48 total)
      energyTypes.forEach(type => {
        for (let i = 0; i < 12; i++) {
          allCubes.push(type);
        }
      });

      // Shuffle locations and cubes
      this._shuffleDeck(standardLocs);
      this._shuffleDeck(allCubes);

      // Distribute the cubes randomly across all 48 locations
      for (let i = 0; i < 48; i++) {
        this.gameState.locationCubes[standardLocs[i]] = allCubes[i];
      }

      // Place OM tokens on Jyotirlingas (IDs 49-60)
      for (let i = 49; i <= 60; i++) {
        this.gameState.locationOm[i] = true;
      }

      // Initialize game state flags
      this.gameState.started = false;
      this.gameState.roundCount = 0;
      this.gameState.turnCount = 0;

      // Initialize players array
      this.gameState.players = [];

      this.logger.info('Game state initialized successfully');
    } catch (error) {
      this.logger.error(`Error initializing game data: ${error.message}`);
      throw error;
    }
  }

  /**
   * Shuffle a deck of cards
   * @param {Array} deck - The deck to shuffle
   * @private
   */
  _shuffleDeck(deck) {
    if (!deck || !Array.isArray(deck)) return;

    for (let i = deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [deck[i], deck[j]] = [deck[j], deck[i]];
    }
  }

  /**
   * Start the simulation
   * @returns {Promise} A promise that resolves when the simulation is done
   */
  async start() {
    try {
      // Add bots to the simulation
      for (const botConfig of this.options.bots) {
        this.simulation.addBot(botConfig);
      }

      this.logger.info(`Starting simulation with ${this.options.bots.length} bots and speed ${this.options.speed}`);

      // Setup global event handling only if enabled
      if (this.gameState.globalEventsEnabled) {
        this.simulation.on('roundStart', () => {
          this._handleGlobalEvent();
        });
      }

      // Setup character card assignment only if enabled
      if (this.gameState.characterCardsEnabled) {
        this.simulation.on('gameStart', () => {
          this._assignCharacterCards();
        });
      }

      // Define custom move handler to properly handle travel cards and vehicles
      this.simulation.movePlayer = (botId, path, cardIds, vehicleId) => {
        try {
          // Get the player state
          const player = this.gameState.players.find(p => p.id === botId);
          if (!player) {
            this.logger.error(`Player not found for bot ${botId}`);
            return false;
          }

          // Log the move
          this.logger.info(`movePlayer ${botId} ${JSON.stringify(path)} ${JSON.stringify(cardIds)} ${vehicleId || 'no vehicle'}`);

          // Find the travel cards in the player's hand by ID
          if (!player.hand) player.hand = [];
          const cardIndices = [];
          const cardsToUse = [];

          if (cardIds && cardIds.length > 0) {
            for (let i = 0; i < player.hand.length; i++) {
              const card = player.hand[i];
              if (card && cardIds.includes(card.id)) {
                cardIndices.push(i);
                cardsToUse.push(card);
              }
            }

            // Remove cards from hand (in reverse order to avoid index shifting)
            for (let i = cardIndices.length - 1; i >= 0; i--) {
              const card = player.hand.splice(cardIndices[i], 1)[0];

              // Add to discard pile
              if (!this.gameState.discardPile) this.gameState.discardPile = { travel: [], event: [], journey: [] };
              if (!this.gameState.discardPile.travel) this.gameState.discardPile.travel = [];
              this.gameState.discardPile.travel.push(card);
            }

            // Log cards used
            const cardInfo = cardsToUse.map(c => `${c.id}:${c.value}`).join(', ');
            this.logger.info(`Bot ${botId} used cards: [${cardInfo}] to move to ${path[path.length - 1]}`);
          } else {
            this.logger.warn(`Bot ${botId} attempted to move without specifying card IDs`);
          }

          // Handle vehicle usage if provided and vehicles are enabled
          if (vehicleId && this.gameState.vehiclesEnabled) {
            const vehicle = player.vehicles ? player.vehicles.find(v => v.id === vehicleId) : null;
            if (vehicle) {
              this.logger.info(`Bot ${botId} used vehicle ${vehicle.name} (${vehicle.id}) for movement`);
              // Apply any vehicle-specific effects
              // ...
            }
          }

          // Set player position to the last node in the path
          if (path && path.length > 0) {
            const targetNodeId = path[path.length - 1];
            
            // Verify travel
            const travelDistance = path.length - 1; // Number of hops
            const totalCardValue = cardsToUse.reduce((sum, card) => sum + (card.value || 0), 0);
            const hasValidTravel = totalCardValue >= travelDistance;
            
            if (!hasValidTravel) {
              this.logger.error(`Bot ${botId} invalid travel: distance=${travelDistance}, card values=${totalCardValue}`);
              return false;
            }
            
            // Update both position properties for consistency
            player.currentNodeId = targetNodeId;
            player.position = targetNodeId;
            
            // Log the move
            this.logger.info(`Bot ${botId} moved to node ${targetNodeId} (position updated to match currentNodeId)`);
            
            return true;
          }
          return false;
        } catch (error) {
          this.logger.error(`Error handling move for bot ${botId}: ${error.message}`);
          return false;
        }
      };

      // Start the simulation
      await this.simulation.start();

      // Cleanup resources when done
      this._cleanupResources();

      this.logger.info('Simulation completed successfully');

      return {
        timestamp: this.timestamp,
        players: this.gameState.players,
        rounds: this.gameState.roundCount,
        events: this.simulation.events
      };
    } catch (error) {
      this.logger.error(`Error running simulation: ${error.message}`);
      this._cleanupResources();
      throw error;
    }
  }

  /**
   * Handles global event processing at the start of each round
   * @private
   */
  _handleGlobalEvent() {
    // Skip if global events are disabled
    if (!this.gameState.globalEventsEnabled) {
      return;
    }

    // If a global event is active, check if it should expire
    if (this.gameState.activeGlobalEvent) {
      const event = this.gameState.activeGlobalEvent;
      if (event.duration && event.roundsActive >= event.duration) {
        // Event expires
        this.logger.info(`Global event ${event.name} (${event.id}) has expired after ${event.roundsActive} rounds`);
        this.gameState.globalEventDiscard.push(event);
        this.gameState.activeGlobalEvent = null;
      } else {
        // Increment rounds active
        event.roundsActive = (event.roundsActive || 0) + 1;
        this.logger.info(`Global event ${event.name} (${event.id}) is active for ${event.roundsActive} rounds`);
      }
    }

    // If no active global event, draw a new one
    if (!this.gameState.activeGlobalEvent && this.gameState.globalEventDeck.length > 0) {
      // 25% chance of drawing a global event if there isn't one active
      if (Math.random() < 0.25) {
        const newEvent = this.gameState.globalEventDeck.pop();
        newEvent.roundsActive = 1;
        this.gameState.activeGlobalEvent = newEvent;
        this.logger.info(`New global event activated: ${newEvent.name} (${newEvent.id})`);

        // Broadcast the event to all players
        this.simulation.broadcastEvent({
          type: 'globalEvent',
          data: {
            event: newEvent
          }
        });
      }
    }

    // If global event deck is empty, shuffle the discard pile
    if (this.gameState.globalEventDeck.length === 0 && this.gameState.globalEventDiscard.length > 0) {
      this.gameState.globalEventDeck = [...this.gameState.globalEventDiscard];
      this.gameState.globalEventDiscard = [];
      this._shuffleDeck(this.gameState.globalEventDeck);
      this.logger.info(`Global event deck reshuffled with ${this.gameState.globalEventDeck.length} cards`);
    }
  }

  /**
   * Assign character cards to players at the beginning of the game
   * @private
   */
  _assignCharacterCards() {
    // Skip if character cards are disabled
    if (!this.gameState.characterCardsEnabled) {
      return;
    }

    if (!this.gameState.characterDeck || this.gameState.characterDeck.length === 0) {
      this.logger.warn('No character cards available to assign');
      return;
    }

    // Shuffle the character deck if not already done
    this._shuffleDeck(this.gameState.characterDeck);

    // Assign character cards to each player
    for (const player of this.gameState.players) {
      if (this.gameState.characterDeck.length > 0) {
        player.character = this.gameState.characterDeck.pop();
        this.logger.info(`Assigned character ${player.character.name} to player ${player.name}`);

        // Apply character-specific initial setup
        if (player.character.initialSetup) {
          player.currentHandLimit = player.character.initialHandLimit || this.gameState.handLimit || 4;

          // Apply any other character-specific starting conditions
          if (player.character.startingResources) {
            if (player.character.startingResources.energyCubes) {
              player.energyCubes = { ...player.energyCubes, ...player.character.startingResources.energyCubes };
            }

            if (player.character.startingResources.omTokens) {
              player.omTokens = (player.omTokens || 0) + player.character.startingResources.omTokens;
            }
          }
        } else {
          // Default hand limit if not specified by character
          player.currentHandLimit = this.gameState.handLimit || 4;
        }
      }
    }
  }

  /**
   * Clean up resources when simulation is done
   * @private
   */
  _cleanupResources() {
    if (this.memoryInterval) {
      clearInterval(this.memoryInterval);
      this.memoryInterval = null;
    }
  }
}

module.exports = SimulationRunner;