import React, { useState, useEffect } from 'react';
import TradeModal from './TradeModal';

function CompactPlayerMat({ player, isActive, winner, socket, gameState }) {
  const outerOM = player.omSlotsOuter.reduce((a, b) => a + b, 0);
  const innerOM = player.omSlotsInner.reduce((a, b) => a + b, 0);
  const currentGlobalEvent = gameState?.currentGlobalEvent;

  // State for modals
  const [showTradeModal, setShowTradeModal] = useState(false);
  const [showCharacterModal, setShowCharacterModal] = useState(false);
  const [showSwapModal, setShowSwapModal] = useState(false);
  const [swapCompleted, setSwapCompleted] = useState(false);
  const [showSwapRequestModal, setShowSwapRequestModal] = useState(false);
  const [swapRequestDetails, setSwapRequestDetails] = useState(null);

  // Check if this player is the winner - ensure robust comparison
  const isWinner = Boolean(winner && winner.id && player.id === winner.id);

  // Determine which win condition was met (if this player is the winner)
  const winByOm = isWinner && winner.winByOm;
  const winByScore = isWinner && winner.winByScore;

  // Added more reliable isCurrentTurn check that doesn't rely on passed isActive prop
  // This ensures we're always showing controls based on the actual game state
  const isCurrentTurn = Boolean(
    gameState && 
    gameState.turnIndex !== undefined && 
    gameState.players && 
    gameState.turnIndex >= 0 && 
    gameState.turnIndex < gameState.players.length && 
    gameState.players[gameState.turnIndex]?.id === player.id
  );

  // Use our more reliable turn check - also log turn order status for debugging
  useEffect(() => {
    if (isCurrentTurn) {
      console.log(`It's ${player.name}'s turn - isCurrentTurn: true, turnIndex: ${gameState.turnIndex}`);
      
      // Reset swap completed state when this player becomes active
      setSwapCompleted(false);
    }
  }, [isCurrentTurn, player.name, gameState.turnIndex]);

  // Use our more reliable turn check
  const effectiveIsActive = isCurrentTurn;

  // Setup socket listener for location swap requests
  useEffect(() => {
    if (!socket) return;
    
    const handleLocationSwapRequest = (data) => {
      // Only show the request modal for the current player's mat
      if (player.id === socket.id) {
        setSwapRequestDetails(data);
        setShowSwapRequestModal(true);
      }
    };
    
    const handleLocationSwapResponse = (data) => {
      // Only process for the current player's mat
      if (player.id === socket.id) {
        if (data.accepted) {
          // Show success notification
          console.log('Location swap successful:', data.message);
          // Could add a toast notification here
        } else {
          // Show declined notification
          console.log('Location swap declined:', data.message);
          // Could add a toast notification here
        }
      }
    };
    
    // Add event listeners
    socket.on('locationSwapRequest', handleLocationSwapRequest);
    socket.on('locationSwapResponse', handleLocationSwapResponse);
    
    // Clean up event listeners
    return () => {
      socket.off('locationSwapRequest', handleLocationSwapRequest);
      socket.off('locationSwapResponse', handleLocationSwapResponse);
    };
  }, [socket, player.id]);

  // Check if player has all three travel cards (1, 2, and 3) and current global event is triathlon
  const showTriathlonButton = () => {
    // Check if current global event is Triathlon
    const isTriathlonEvent = currentGlobalEvent?.effect === 'triathlon_bonus';

    if (!isTriathlonEvent) return false;

    // Check for travel cards 1, 2, and 3
    const travelCardValues = player.hand
      .filter(card => card.value !== undefined)
      .map(card => card.value);

    return travelCardValues.includes(1) && travelCardValues.includes(2) && travelCardValues.includes(3);
  };

  // Check if Cultural Exchange event is active
  const showSwapButton = () => {
    // Check if current global event is Cultural Exchange
    const isCulturalExchangeEvent = currentGlobalEvent?.effect === 'cultural_exchange';
    
    // Only show for active player and only if swap hasn't been completed this turn
    return effectiveIsActive && isCulturalExchangeEvent && !swapCompleted;
  };

  // Handle triathlon button click
  const handleTriathlonClick = () => {
    if (showTriathlonButton()) {
      socket.emit('triathlonMovement');
    }
  };

  // Handle swap button click
  const handleSwapClick = () => {
    if (showSwapButton()) {
      setShowSwapModal(true);
    }
  };

  // Handle player swap selection
  const handleSwapSelection = (targetPlayerId) => {
    setShowSwapModal(false);
    
    // Emit event to server to handle the swap
    socket.emit('initiateLocationSwap', { targetPlayerId });
    
    // Mark swap as completed for this turn
    setSwapCompleted(true);
  };
  
  // Handle swap request response
  const handleSwapRequestResponse = (accepted) => {
    setShowSwapRequestModal(false);
    
    if (!swapRequestDetails) return;
    
    // Send response to server
    socket.emit('respondToLocationSwap', {
      requestingPlayerId: swapRequestDetails.requestingPlayerId,
      accepted
    });
    
    // Clear request details
    setSwapRequestDetails(null);
  };

  // For debugging - log when a winner is detected or when there's a potential matching issue
  useEffect(() => {
    if (winner) {
      console.log('CompactPlayerMat - Winner data available:', {
        winnerName: winner.name,
        winnerId: winner.id,
        playerName: player.name,
        playerId: player.id,
        isMatch: player.id === winner.id,
        isWinner
      });
    }
  }, [winner, player.id, isWinner, player.name]);

  // Render the swap request confirmation modal
  const renderSwapRequestModal = () => {
    if (!showSwapRequestModal || !swapRequestDetails) return null;
    
    // Get the requesting player's name and position
    const requestingPlayerName = swapRequestDetails.requestingPlayerName;
    const requestingPlayerPosition = swapRequestDetails.currentPosition;
    
    // Find location names for better context
    const yourLocationInfo = gameState.locations?.find(loc => loc.id === player.position);
    const theirLocationInfo = gameState.locations?.find(loc => loc.id === requestingPlayerPosition);
    
    const yourLocationName = yourLocationInfo?.name || `Location ${player.position}`;
    const theirLocationName = theirLocationInfo?.name || `Location ${requestingPlayerPosition}`;

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000
      }}>
        <div style={{
          backgroundColor: '#fff',
          borderRadius: '8px',
          padding: '20px',
          width: '90%',
          maxWidth: '320px',
          boxShadow: '0 5px 15px rgba(0, 0, 0, 0.3)'
        }}>
          <h3 style={{
            marginTop: 0,
            textAlign: 'center',
            borderBottom: '1px solid #eee',
            paddingBottom: '10px',
            color: '#9C27B0'
          }}>
            Location Swap Request
          </h3>

          <p style={{ textAlign: 'center' }}>
            <strong>{requestingPlayerName}</strong> would like to swap locations with you.
          </p>
          
          <div style={{
            margin: '15px 0',
            padding: '10px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
            fontSize: '0.9rem'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>Your location:</span>
              <strong>{yourLocationName} ({player.position})</strong>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span>Their location:</span>
              <strong>{theirLocationName} ({requestingPlayerPosition})</strong>
            </div>
          </div>
          
          <p style={{ textAlign: 'center', color: '#4CAF50', fontWeight: 'bold', marginBottom: '20px' }}>
            Both players will receive 5 inner points.
          </p>

          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: '10px'
          }}>
            <button
              onClick={() => handleSwapRequestResponse(false)}
              style={{
                flex: 1,
                padding: '10px',
                backgroundColor: '#f5f5f5',
                border: '1px solid #ddd',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Decline
            </button>
            
            <button
              onClick={() => handleSwapRequestResponse(true)}
              style={{
                flex: 1,
                padding: '10px',
                backgroundColor: '#9C27B0',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
              }}
            >
              Accept
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render the swap players modal
  const renderSwapModal = () => {
    if (!showSwapModal) return null;

    // Filter other players (not self)
    const otherPlayers = gameState?.players.filter(p => p.id !== player.id) || [];

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000
      }}>
        <div style={{
          backgroundColor: '#fff',
          borderRadius: '8px',
          padding: '20px',
          width: '90%',
          maxWidth: '320px',
          boxShadow: '0 5px 15px rgba(0, 0, 0, 0.3)'
        }}>
          <h3 style={{
            marginTop: 0,
            textAlign: 'center',
            borderBottom: '1px solid #eee',
            paddingBottom: '10px'
          }}>
            Swap Location With Player
          </h3>

          <p style={{ textAlign: 'center', fontSize: '0.9rem' }}>
            Choose a player to swap locations with.<br/>
            Both players will receive 5 inner points.
          </p>

          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            marginTop: '15px'
          }}>
            {otherPlayers.map(otherPlayer => (
              <button
                key={otherPlayer.id}
                onClick={() => handleSwapSelection(otherPlayer.id)}
                style={{
                  padding: '10px',
                  backgroundColor: 'var(--primary-color)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <span>{otherPlayer.name}</span>
                <span>Position: {otherPlayer.position}</span>
              </button>
            ))}
          </div>

          <button
            onClick={() => setShowSwapModal(false)}
            style={{
              width: '100%',
              padding: '10px',
              marginTop: '15px',
              backgroundColor: '#f5f5f5',
              border: '1px solid #ddd',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    );
  };

  // Render energy cubes visually in a compact format
  const renderEnergyCubes = () => {
    // Count cubes by type
    const cubeCounts = {
      artha: 0,
      karma: 0,
      gnana: 0,
      bhakti: 0
    };

    player.energyCubes.forEach(cube => {
      cubeCounts[cube.toLowerCase()]++;
    });

    return Object.entries(cubeCounts).map(([type, count]) => (
      count > 0 && (
        <div key={type} className="flex" style={{ alignItems: 'center', marginRight: '8px' }}>
          <div className={`energy-cube ${type}`} style={{ width: '12px', height: '12px', margin: '0 3px 0 0' }} />
          <span style={{ fontSize: '0.8rem' }}>{count}</span>
        </div>
      )
    ));
  };

  // Render player's character card in compact format
  const renderCharacter = () => {
    if (!player.character) return null;

    const characterType = player.character.type.toLowerCase();

    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          background: 'rgba(240, 240, 240, 0.5)',
          padding: '3px 6px',
          borderRadius: '4px',
          marginRight: '6px',
          cursor: 'pointer'
        }}
        onClick={() => setShowCharacterModal(true)}
        title="Click to view character details"
      >
        <div style={{
          width: '24px',
          height: '24px',
          borderRadius: '4px',
          background: '#ddd',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          fontSize: '8px',
          marginRight: '4px',
          fontWeight: 'bold',
          overflow: 'hidden'
        }}>
          <img
            src={`/assets/images/characters/${characterType}.jpg`}
            alt={player.character.type}
            className="character-debug-img-compact"
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            onLoad={(e) => {
              console.log(`CompactPlayerMat: Character image loaded successfully: ${characterType}.jpg`);
            }}
            onError={(e) => {
              // Fallback to first letter if image fails to load
              console.error(`Failed to load character image in CompactPlayerMat: ${characterType}.jpg, src: ${e.target.src}`);
              e.target.style.display = 'none';
              e.target.parentNode.innerText = characterType.charAt(0).toUpperCase();
            }}
          />
        </div>
        <span style={{ fontSize: '0.7rem' }}>{player.character.type}</span>
      </div>
    );
  };

  // Character detail modal
  const renderCharacterModal = () => {
    if (!showCharacterModal || !player.character) return null;

    const character = player.character;
    const characterType = character.type.toLowerCase();

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000
      }}>
        <div style={{
          backgroundColor: '#fff',
          borderRadius: '8px',
          padding: '20px',
          width: '90%',
          maxWidth: '320px',
          boxShadow: '0 5px 15px rgba(0, 0, 0, 0.3)',
          position: 'relative'
        }}>
          <button
            onClick={() => setShowCharacterModal(false)}
            style={{
              position: 'absolute',
              top: '10px',
              right: '10px',
              backgroundColor: 'transparent',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>

          <h3 style={{
            marginTop: 0,
            textAlign: 'center',
            borderBottom: '1px solid #eee',
            paddingBottom: '10px'
          }}>
            {character.type}
          </h3>

          <div style={{
            margin: '15px 0',
            textAlign: 'center'
          }}>
            <div style={{
              width: '200px',
              height: '200px',
              margin: '0 auto',
              borderRadius: '8px',
              overflow: 'hidden',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}>
              <img
                src={`/assets/images/characters/${characterType}.jpg`}
                alt={character.type}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
            </div>
          </div>

          <div style={{ marginTop: '15px' }}>
            <p style={{
              fontWeight: 'bold',
              marginBottom: '5px',
              fontSize: '0.9rem'
            }}>
              Trading Ability:
            </p>
            <p style={{ margin: 0, fontSize: '0.9rem' }}>
              {character.description}
            </p>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              marginTop: '15px',
              backgroundColor: '#f8f8f8',
              padding: '10px',
              borderRadius: '6px'
            }}>
              <div style={{ textAlign: 'center' }}>
                <p style={{ fontWeight: 'bold', margin: 0, fontSize: '0.9rem' }}>Takes</p>
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  marginTop: '5px',
                  gap: '5px'
                }}>
                  {character.ability.takes.map((cube, index) => (
                    <div
                      key={index}
                      className={`energy-cube ${cube.toLowerCase()}`}
                      style={{
                        width: '20px',
                        height: '20px'
                      }}
                    />
                  ))}
                </div>
              </div>

              <div style={{ textAlign: 'center' }}>
                <p style={{ fontWeight: 'bold', margin: 0, fontSize: '0.9rem' }}>Gives</p>
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  marginTop: '5px'
                }}>
                  <div
                    className={`energy-cube ${character.ability.gives.toLowerCase()}`}
                    style={{
                      width: '20px',
                      height: '20px'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render travel cards horizontally with clear value display
  const renderTravelCard = (card) => {
    const vehicleImagePath = `/assets/images/vehicles/${card.value}/${card.vehicle || 'camel'}.png`;

    return (
      <div
        key={card.id || `travel-${card.value}`}
        style={{
          width: '52px',
          height: '75px',
          backgroundColor: '#F5F5F5', // Light gray background for player's hand cards
          borderRadius: '4px',
          border: '1px solid #1976d2',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '0 4px 0 0',
          padding: '2px',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <img
          src={vehicleImagePath}
          alt={`${card.vehicle} (${card.value})`}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain'
          }}
          onError={(e) => {
            // Fallback if image doesn't load
            e.target.style.display = 'none';
          }}
        />
      </div>
    );
  };

  // Render player's hand cards with more details
  const renderHandCards = () => {
    if (!player.hand || player.hand.length === 0) {
      return (
        <div style={{
          color: '#999',
          fontStyle: 'italic',
          fontSize: '0.7rem',
          textAlign: 'center',
          padding: '4px 0'
        }}>
          No cards
        </div>
      );
    }

    // Separate travel cards from other cards
    const travelCards = player.hand.filter(card => card.value !== undefined);
    const otherCards = player.hand.filter(card => card.value === undefined);

    return (
      <div>
        {/* Render travel cards horizontally */}
        {travelCards.length > 0 && (
          <div>
            <div style={{ display: 'flex', flexWrap: 'wrap', marginBottom: '4px' }}>
              {travelCards.map(card => renderTravelCard(card))}
            </div>
          </div>
        )}

        {/* Render other cards as before */}
        {otherCards.length > 0 && (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            marginTop: travelCards.length > 0 ? '6px' : '0'
          }}>
            {otherCards.map((card, index) => {
              let cardText = '';
              let cardBg = '#e3f2fd';
              let cardColor = '#1976d2';

              if (card.type === 'extraHop') {
                cardText = 'Extra Hop';
                cardBg = '#e8f5e9';
                cardColor = '#2e7d32';
              } else if (card.type === 'event') {
                cardText = card.name || 'Event';
                cardBg = '#f3e5f5';
                cardColor = '#7b1fa2';
              } else if (card.type === 'journey') {
                cardText = 'Journey';
                cardBg = '#fff3e0';
                cardColor = '#f57c00';
              } else if (card.type === 'wildCube') {
                cardText = 'Wild Cube';
                cardBg = '#f3e5f5';
                cardColor = '#7b1fa2';
              }

              return (
                <div
                  key={index}
                  style={{
                    fontSize: '0.75rem',
                    padding: '3px 6px',
                    backgroundColor: cardBg,
                    borderRadius: '3px',
                    color: cardColor,
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                    display: 'flex',
                    justifyContent: 'space-between'
                  }}
                >
                  <span>{cardText}</span>
                  {card.id && <span style={{ fontSize: '0.65rem', opacity: 0.7 }}>#{card.id.slice(-3)}</span>}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // Add debug log
  console.log('CompactPlayerMat rendering for:', player.name, 'Character:', player.character, 'Character Type:', player.character?.type?.toLowerCase());

  // Determine if this is the current socket user's mat (for animation targeting)
  const isCurrentUser = player.id === socket.id;

  return (
    <div
      className={`compact-player-mat ${effectiveIsActive ? 'active' : ''} ${isWinner ? 'winner' : ''} ${isCurrentUser ? 'current-user' : ''}`}
      data-player-id={player.id}
      style={{
        width: '230px',
        padding: '8px',
        backgroundColor: isWinner ? 'rgba(255, 250, 205, 0.95)' : (effectiveIsActive ? 'rgba(255, 255, 240, 0.95)' : 'rgba(245, 245, 245, 0.85)'),
        borderRadius: '8px',
        boxShadow: isWinner ? '0 0 8px gold' : '0 2px 4px rgba(0,0,0,0.1)',
        margin: '4px 0',
        fontSize: '0.8rem',
        border: isWinner ? '2px solid gold' : (effectiveIsActive ? '2px solid var(--accent-color)' : '1px solid #ddd'),
        position: 'relative'
      }}
    >
      {isWinner && (
        <div
          style={{
            position: 'absolute',
            top: '-10px',
            right: '-10px',
            backgroundColor: 'gold',
            color: '#333',
            fontWeight: 'bold',
            padding: '3px 6px',
            borderRadius: '12px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            fontSize: '0.7rem',
            zIndex: 10,
            transform: 'rotate(10deg)'
          }}
        >
          {winByOm ? 'Winner by OM!' : (winByScore ? 'Winner by Score!' : 'Winner!')}
        </div>
      )}

      <h4 style={{
        margin: '0 0 5px 0',
        padding: '0 0 5px 0',
        borderBottom: isWinner ? '1px solid gold' : '1px solid #ddd',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: '0.9rem',
        color: isWinner ? 'goldenrod' : undefined,
        fontWeight: isWinner ? 'bold' : undefined
      }}>
        <span>{player.name}</span>
        <span style={{
          fontSize: '0.75rem',
          backgroundColor: isWinner ? 'gold' : undefined,
          padding: isWinner ? '0 5px' : undefined,
          borderRadius: isWinner ? '4px' : undefined,
          color: isWinner ? '#333' : undefined
        }}>
          {outerOM}/{innerOM}
        </span>
      </h4>

      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '5px',
        fontSize: '0.8rem'
      }}>
        <div>Pos: {player.position}</div>
        <div style={{
          fontWeight: isWinner ? 'bold' : undefined,
          color: isWinner ? 'goldenrod' : undefined
        }}>
          <strong>Score: O: {player.outerScore} I: {player.innerScore}</strong>
          {isWinner && <span style={{ marginLeft: '5px' }}>→ {player.outerScore + player.innerScore}</span>}
        </div>
        <div>OM: {player.omTemp.length}</div>
      </div>

      <div style={{
        display: 'flex',
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '5px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {renderCharacter()}
          {renderEnergyCubes()}
        </div>

        <div style={{
          display: 'flex',
          flexDirection: 'column',
          marginTop: '8px'
        }}>
          {/* Controls */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: '6px',
            marginBottom: '6px'
          }}>
            {/* Trade button */}
            {effectiveIsActive && player.character && player.energyCubes.length >= 1 && !player.didTradeThisTurn && (
              <button
                style={{
                  padding: '2px 6px',
                  fontSize: '0.7rem',
                  backgroundColor: 'var(--accent-color)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                }}
                onClick={() => setShowTradeModal(true)}
              >
                Trade
              </button>
            )}

            {/* Travel button */}
            {effectiveIsActive && player.hand.some(card => card.value !== undefined) && !player.didMoveThisTurn && (
              <button
                style={{
                  padding: '2px 6px',
                  fontSize: '0.7rem',
                  backgroundColor: 'var(--primary-color)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                }}
                onClick={() => socket.emit('initiateTravelCardSelection')}
              >
                Travel
              </button>
            )}

            {/* Triathlon button */}
            {showTriathlonButton() && effectiveIsActive && (
              <button
                style={{
                  padding: '2px 6px',
                  fontSize: '0.7rem',
                  background: 'linear-gradient(45deg, #FF9800, #FF5722)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  boxShadow: '0 0 5px rgba(255, 152, 0, 0.7)',
                  animation: 'pulse 1.5s infinite'
                }}
                onClick={handleTriathlonClick}
              >
                Triathlon
              </button>
            )}
            
            {/* Swap button for Cultural Exchange event */}
            {showSwapButton() && effectiveIsActive && (
              <button
                style={{
                  padding: '2px 6px',
                  fontSize: '0.7rem',
                  background: 'linear-gradient(45deg, #9C27B0, #673AB7)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  boxShadow: '0 0 5px rgba(156, 39, 176, 0.7)'
                }}
                onClick={handleSwapClick}
              >
                Swap
              </button>
            )}
          </div>
        </div>
      </div>

      <div style={{
        marginTop: '6px',
        borderTop: isWinner ? '1px solid gold' : '1px solid #eee',
        paddingTop: '6px'
      }}>
        <div style={{ fontSize: '0.75rem', fontWeight: 'bold', marginBottom: '2px' }}>
          Hand Cards ({player.hand.length}/5):
        </div>
        {renderHandCards()}
      </div>

      {/* Bottom section with player position */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginTop: '8px',
        justifyContent: 'space-between',
        fontSize: '0.8rem'
      }}>
        <div>
          <span style={{ fontWeight: 'bold' }}>Pos: </span>
          {player.position}
        </div>
      </div>

      {/* Modals */}
      {showTradeModal && (
        <TradeModal
          player={player}
          onClose={() => setShowTradeModal(false)}
          socket={socket}
        />
      )}
      {renderCharacterModal()}
      {renderSwapModal()}
      {renderSwapRequestModal()}
    </div>
  );
}

export default CompactPlayerMat;