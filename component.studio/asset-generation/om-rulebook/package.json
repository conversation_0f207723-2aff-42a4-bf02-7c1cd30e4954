{"name": "om-journey-rulebook", "version": "1.0.0", "description": "Professional grade rulebook for Om: The Journey board game", "main": "scripts/build-html.js", "scripts": {"build": "node scripts/build.js", "build:html": "node scripts/build-html.js", "build:print": "node scripts/build-print.js", "build:print-friendly": "node scripts/build-print-friendly.js", "watch": "node scripts/watch.js", "watch:html": "node scripts/watch-html.js", "dev": "npm run watch:html"}, "dependencies": {"chokidar": "^3.5.3", "express": "^4.18.2", "handlebars": "^4.7.8", "markdown-it": "^13.0.2", "open": "^9.1.0", "sass": "^1.69.0"}, "devDependencies": {"live-server": "^1.2.2"}, "keywords": ["om-journey", "rulebook", "boardgame", "html", "responsive"], "author": "Om: The Journey Team", "license": "MIT"}