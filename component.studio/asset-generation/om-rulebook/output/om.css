@import url("https://fonts.googleapis.com/css2?family=Bebas+Neue:wght@400&family=Lato:wght@400;700&display=swap");
html {
  font-size: 16px;
  line-height: 1.5;
  scroll-behavior: smooth;
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

body {
  font-family: "Lato", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  background: #FFFFFF;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  word-wrap: break-word;
}

h1 {
  font-family: "Be<PERSON> Neue", cursive;
  font-size: 2.5rem;
  color: #205F80;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
}

h2 {
  font-family: "Bebas Neue", cursive;
  font-size: 2rem;
  color: #205F80;
  text-transform: uppercase;
  margin: 1.5rem 0 1rem;
  font-weight: 400;
}

h3 {
  font-family: "Lato", sans-serif;
  font-size: 1.3rem;
  color: #205F80;
  font-weight: 700;
  margin: 1.2rem 0 0.8rem;
}

h4 {
  font-family: "Lato", sans-serif;
  font-size: 1.1rem;
  color: #E87E1E;
  font-weight: 700;
  margin: 1rem 0 0.5rem;
}

p {
  margin: 0 0 1rem;
}

.rulebook-container {
  max-width: 1200px;
  margin: 80px auto 2rem;
  padding: 0 1rem;
  overflow-x: hidden;
  word-wrap: break-word;
}

.rulebook-section {
  margin-bottom: 3rem;
  padding-top: 1rem;
}

.section-title {
  color: #205F80;
  border-bottom: 2px solid #E87E1E;
  padding-bottom: 0.5rem;
}

ul, ol {
  margin: 0 0 1rem;
  padding-left: 1.5rem;
}
ul li, ol li {
  margin-bottom: 0.5rem;
}

.callout-box {
  background: #F4EBDD;
  border: 2px solid #205F80;
  border-radius: 6px;
  padding: 1rem;
  margin: 1.5rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.callout-box.important {
  background: rgb(250.9193548387, 232.1129032258, 215.0806451613);
  border-color: #E87E1E;
}
.callout-box.setup {
  background: rgb(158.6, 206.046875, 230.9);
  border-color: #205F80;
}
.callout-box h3 {
  margin-top: 0;
}
.callout-box p:last-child {
  margin-bottom: 0;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.9rem;
  table-layout: fixed;
  word-wrap: break-word;
}
table th {
  background: #205F80;
  color: #FFFFFF;
  padding: 0.75rem;
  font-weight: 700;
  text-align: left;
}
table td {
  padding: 0.75rem;
  border-bottom: 1px solid rgb(56.6, 154.25, 205.4);
}
table tr:nth-child(even) {
  background: rgb(237.7666666667, 223.6666666667, 201.7333333333);
}

.component-table {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}
.component-table .component-item {
  border: 1px solid #205F80;
  border-radius: 6px;
  padding: 1rem;
  background: #F4EBDD;
}
.component-table .component-item img {
  max-width: 100%;
  height: auto;
  border: 2px solid white;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}
.component-table .component-item h4 {
  margin-top: 0;
  color: #205F80;
}

.cover-page {
  text-align: center;
  margin: 2rem 0 3rem;
}
.cover-page .game-title {
  font-family: "Bebas Neue", cursive;
  font-size: 3.5rem;
  color: #205F80;
  margin-bottom: 1rem;
}
.cover-page .game-subtitle {
  font-family: "Lato", sans-serif;
  font-size: 1.5rem;
  color: #E87E1E;
  margin-bottom: 2rem;
}
.cover-page .cover-image {
  max-width: 100%;
  height: auto;
  margin: 1.5rem 0;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 6px;
}

.energy-cube {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}
.energy-cube.artha {
  background-color: #FFD700;
}
.energy-cube.karma {
  background-color: #4CAF50;
}
.energy-cube.gnana {
  background-color: #1976D2;
}
.energy-cube.bhakti {
  background-color: #7B1FA2;
}

.om-token {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #E87E1E;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.icon-legend {
  max-width: 800px;
  margin: 0 auto;
}
.icon-legend h3 {
  text-align: center;
  margin-bottom: 1rem;
}
.icon-legend .legend-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
}
.icon-legend .legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}
.icon-legend .legend-item span {
  margin-left: 0.5rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1.5rem auto;
  border-radius: 4px;
}

/* Additional layout classes */
.sidebar-left, .sidebar-right {
  display: block;
}

.main-content {
  display: block;
}

.with-sidebar-left, .with-sidebar-right {
  display: block;
}

.board-diagram {
  margin: 2rem 0;
  text-align: center;
}

.text-center {
  text-align: center;
}

.game-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.board-section {
  margin: 1rem 0;
}

.example-turn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

.turn-header {
  font-weight: bold;
  color: #205F80;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
}

.turn-step {
  margin: 0.5rem 0;
  padding-left: 1rem;
}

.component-spread {
  margin: 2rem 0;
}

.component-category {
  margin-bottom: 2rem;
}

.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.component-item {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 1rem;
  background: #f9f9f9;
}

.component-image {
  background: #eee;
  border-radius: 4px;
  padding: 2rem;
  text-align: center;
  margin-bottom: 0.5rem;
  color: #666;
}

.component-image.placeholder {
  background: #f0f0f0;
  border: 2px dashed #ccc;
}

.component-name {
  font-weight: bold;
  color: #205F80;
  margin-bottom: 0.5rem;
}

.component-desc {
  font-size: 0.9rem;
  color: #666;
}

.reference-card {
  border: 1px solid #205F80;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  background: #f4ebdd;
}

.card-title {
  font-weight: bold;
  color: #205F80;
  margin-bottom: 0.5rem;
}

.card-cost {
  color: #E87E1E;
  margin-bottom: 0.5rem;
}

.card-points {
  font-weight: bold;
  color: #205F80;
}

.page-break {
  page-break-before: always;
  margin: 2rem 0;
}

.image-caption {
  text-align: center;
  font-size: 0.9rem;
  color: #205F80;
  margin-top: -1rem;
  margin-bottom: 1.5rem;
  font-style: italic;
}

@media (min-width: 768px) {
  .two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 1.5rem 0;
  }
  .three-column {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin: 1.5rem 0;
  }
  .sidebar-content {
    display: grid;
    grid-template-columns: 7fr 3fr;
    gap: 2rem;
    margin: 1.5rem 0;
  }
  .sidebar-content.sidebar-left {
    grid-template-columns: 3fr 7fr;
  }
  .sidebar-content .sidebar {
    background: #F4EBDD;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #E87E1E;
  }
  .sidebar-content .sidebar h3 {
    margin-top: 0;
  }
}
@media (max-width: 767px) {
  html {
    font-size: 14px;
  }
  h1 {
    font-size: 2rem;
  }
  h2 {
    font-size: 1.7rem;
  }
  .rulebook-container {
    padding: 0 0.75rem;
  }
  table {
    font-size: 0.8rem;
  }
  th, td {
    padding: 0.5rem;
    word-break: break-word;
  }
  .legend-row {
    flex-direction: column;
    align-items: flex-start;
  }
  .component-table {
    grid-template-columns: 1fr;
  }
  .callout-box {
    margin: 1rem 0;
    padding: 0.75rem;
  }
  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-x: auto;
  }
}