<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Journey Card - JI7</title>
  <style>
    :root {
      --primary-color: #4a6741;
      --accent-color: #375a60;
      --north-color: #375a60;
      --south-color: #c17817;
      --east-color: #9c3e3e;
      --west-color: #7a4e8c;
      --central-color: #4a6741;
      --northeast-color: #1e88e5;
      --artha-color: #ffc107;
      --karma-color: #9c3e3e;
      --gnana-color: #1e88e5;
      --bhakti-color: #7a4e8c;
    }
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .card-container {
      width: 825px;
      height: 1125px;
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
    }
    .card-header {
      background: var(--primary-color);
      color: white;
      padding: 16px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .location-name {
      margin: 0;
      font-size: 1.5rem;
      font-weight: bold;
    }
    .location-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
      margin-top: 4px;
    }
    .region-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: var(--west-color);
      border: 1px solid white;
    }
    .card-image {
      height: 500px;
      background-image: url(../client/public/assets/images/cards/14.png);
      background-size: cover;
      background-position: center;
      position: relative;
    }
    .journey-badge {
      position: absolute;
      bottom: 16px;
      right: 16px;
      background: var(--primary-color);
      color: white;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 1.4rem;
      font-weight: bold;
      border: 3px solid white;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    .card-details {
      padding: 24px;
    }
    .journey-type {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    .journey-type-label {
      font-size: 1rem;
      font-weight: bold;
      color: var(--primary-color);
      margin-right: 10px;
      text-transform: uppercase;
    }
    .journey-subtype {
      font-size: 0.9rem;
      color: #666;
      font-style: italic;
    }
    .section-title {
      font-size: 1rem;
      margin-bottom: 10px;
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
    }
    .energy-requirements {
      display: flex;
      gap: 8px;
      margin-bottom: 20px;
    }
    .energy-cube {
      width: 36px;
      height: 36px;
      border-radius: 6px;
      border: 1px solid #666;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    .reward-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    .reward-badge {
      background: var(--primary-color);
      color: white;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .reward-text {
      font-size: 1.1rem;
      font-weight: bold;
      color: var(--primary-color);
    }
    .card-footer {
      padding: 16px;
      border-top: 1px solid #eee;
      display: flex;
      justify-content: flex-end;
    }
    .card-id {
      color: #999;
      font-size: 0.8rem;
    }
  </style>
</head>
<body>
  <div class="card-container">
    <!-- Header with location name -->
    <div class="card-header">
      <div>
        <h2 class="location-name">Brahma Temple, Pushkar</h2>
        <div class="location-info">
          <span>#14</span>
          <div class="region-dot"></div>
          <span>West</span>
        </div>
      </div>
    </div>
    
    <!-- Main content with image and details -->
    <div style="display: flex; flex-direction: column; flex: 1; overflow: hidden;">
      <!-- Location image -->
      <div class="card-image">
        <!-- Journey type badge -->
        <div class="journey-badge">
          24
        </div>
      </div>
      
      <!-- Card details -->
      <div class="card-details">
        <div class="journey-type">
          <div class="journey-type-label">
            Inner Journey
          </div>
          <div class="journey-subtype">
            Inner
          </div>
        </div>
        
        <!-- Requirements section -->
        <div>
          <h3 class="section-title">
            Required Resources
          </h3>
          
          <div class="energy-requirements">
            
              <div class="energy-cube" style="background: var(--bhakti-color);"></div>
            
              <div class="energy-cube" style="background: var(--gnana-color);"></div>
            
            
          </div>
        </div>
        
        <!-- Reward section -->
        <div>
          <h3 class="section-title">
            Reward
          </h3>
          
          <div class="reward-section">
            <div style="display: flex; align-items: center; gap: 8px;">
              <span class="reward-badge">
                +24
              </span>
              <span class="reward-text">Inner OM Points</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Footer with card ID -->
    <div class="card-footer">
      <div class="card-id">
        Card ID: JI7
      </div>
    </div>
  </div>
</body>
</html>