{"description": "First 3 player game with bot and humans where <PERSON><PERSON> was edged by 1 point", "players": [{"id": "bxSSov-hJfc1P7PYAAAD", "name": "EternalPathfinder", "position": 33, "hand": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "energyCubes": ["bhakti", "bhakti", "gnana", "gnana"], "omTemp": [1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [{"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "outerScore": 24, "innerScore": 5, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}, {"id": "EFQ2cVSAZWWNYuGaAAAF", "name": "<PERSON><PERSON>", "position": 16, "hand": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "energyCubes": ["karma"], "omTemp": [1], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}], "outerScore": 25, "innerScore": 74, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}}, {"id": "NqwvtHfQhHF-hoGaAAAB", "name": "<PERSON><PERSON>", "position": 10, "hand": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "energyCubes": ["karma"], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "outerScore": 44, "innerScore": 56, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}], "started": true, "turnIndex": 2, "roundCount": 13, "travelDeck": [], "eventDeck": [{"id": "E7", "type": "wildCube"}, {"id": "E3", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}], "journeyDeckOuter": [{"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}], "travelDiscard": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "faceUpEvent": [{"id": "E9", "type": "wildCube"}, {"id": "E8", "type": "wildCube"}, {"id": "E1", "type": "extraHop"}, {"id": "E6", "type": "wildCube"}], "faceUpJourneyInner": [{"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "faceUpJourneyOuter": [{"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}], "locationCubes": {"2": "gnana", "3": "artha", "7": "artha", "9": "artha", "11": "karma", "13": "gnana", "20": "artha", "21": "bhakti", "27": "gnana", "28": "artha", "29": "karma", "31": "karma", "32": "artha", "34": "bhakti", "36": "gnana", "42": "karma", "43": "bhakti", "44": "artha", "48": "bhakti", "undefined": "gnana"}, "locationOm": {"56": true, "60": true}, "finalRound": true, "finalRoundStarter": 2, "finalRoundEnd": 1, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944422823, "data": {"eventId": "global-event-26", "eventName": "Engineer's Precision", "eventEffect": "engineers_precision_reward"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944701076, "data": {"cardType": "travel", "card": {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944701076, "data": {"cardType": "travel", "card": {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944701076, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944701076, "data": {"cardType": "travel", "card": {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944701076, "data": {"cardType": "travel", "card": {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944701076, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944703075, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 0, "turnIndex": 0, "timestamp": 1744944706081, "data": {"path": [63, 21, 20, 51], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 0, "turnIndex": 1, "timestamp": 1744944709082, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1744944859150, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1744944863628, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1744944872313, "data": {"path": [64, 15, 18, 53], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1744944875403, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1744944905874, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1744944910510, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 0, "turnIndex": 2, "timestamp": 1744944918703, "data": {"path": [65, 31, 30, 55], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1744944934498, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1744944934498, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 1, "turnIndex": 0, "timestamp": 1744944936598, "data": {"path": [51, 20, 21, 63, 24], "travelCards": ["T4", "T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 1, "turnIndex": 0, "timestamp": 1744944939602, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 1, "turnIndex": 1, "timestamp": 1744944942603, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1744944991937, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1744945066895, "data": {"vehicle": "camel", "effect": "desert_caravan_reward", "outerPoints": 5}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1744945066895, "data": {"path": [53, 18], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1744945078245, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 1, "turnCount": null}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1744945089644, "data": {"path": [55, 30, 31, 45], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 1, "turnIndex": 2, "timestamp": 1744945101248, "data": {"journeyType": "outer", "journeyCardId": "JO24", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1744945115791, "data": {"eventId": "global-event-25", "eventName": "<PERSON><PERSON><PERSON>'s Grace", "eventEffect": "pilgrims_grace_reward"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1744945115791, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 2, "turnIndex": 0, "timestamp": 1744945118277, "data": {"path": [24, 63, 61, 4], "travelCards": ["T3", "T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 2, "turnIndex": 0, "timestamp": 1744945121280, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 2, "turnIndex": 1, "timestamp": 1744945124283, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744945233503, "data": {"path": [18, 15, 64, 61, 4, 3, 2, 1], "travelCards": ["T6", "T8", "T12"], "extraHopCards": []}}, {"type": "character_trade_reward", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744945244360, "data": {"character": "pilgrim", "cubeReceived": "bhakti", "effect": "pilgrims_grace_reward", "innerPoints": 7}}, {"type": "trade", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744945244360, "data": {"cubesTraded": ["artha"], "cubeReceived": "bhakti", "count": 1}}, {"type": "collectJourney", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1744945248356, "data": {"journeyType": "inner", "journeyCardId": "JI1", "omRequirement": 1}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1744945256337, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 2, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1744945270954, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1744945275894, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 2, "turnIndex": 2, "timestamp": 1744945302173, "data": {"path": [45, 46, 47, 57], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1744945325919, "data": {"eventId": "global-event-24", "eventName": "Professor's Insight", "eventEffect": "professors_insight_reward"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1744945325919, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 3, "turnIndex": 0, "timestamp": 1744945327504, "data": {"path": [4, 61, 63, 21, 22], "travelCards": ["T10", "T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 3, "turnIndex": 0, "timestamp": 1744945330506, "data": {"journeyType": "outer", "journeyCardId": "JO10", "omRequirement": 1}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 3, "turnIndex": 1, "timestamp": 1744945333508, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1744945378911, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1744945398162, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1744945407685, "data": {"path": [1, 9, 54], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1744945413518, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 3, "turnCount": null}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1744945510682, "data": {"path": [57, 47], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 3, "turnIndex": 2, "timestamp": 1744945524722, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1744945530496, "data": {"eventId": "global-event-16", "eventName": "<PERSON><PERSON> Rhapsody", "eventEffect": "rickshaw_rhapsody_reward"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1744945530496, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 4, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 4, "turnIndex": 0, "timestamp": 1744945532893, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 4, "turnIndex": 1, "timestamp": 1744945535893, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1744945577380, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1744945584009, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1744945597551, "data": {"path": [54, 7, 11, 62, 13, 12], "travelCards": ["T9", "T8"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1744945608408, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 4, "turnCount": null}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1744945641322, "data": {"path": [47, 46], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "trade", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1744945647904, "data": {"cubesTraded": ["karma"], "cubeReceived": "artha", "count": 1}}, {"type": "collectJourney", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 4, "turnIndex": 2, "timestamp": 1744945654010, "data": {"journeyType": "outer", "journeyCardId": "JO25", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1744945660308, "data": {"eventId": "global-event-19", "eventName": "Bullet Train", "eventEffect": "bullet_train_reward"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1744945660308, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 5, "turnIndex": 0, "timestamp": 1744945662662, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 5, "turnIndex": 0, "timestamp": 1744945665665, "data": {"path": [22, 23, 59], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 5, "turnIndex": 1, "timestamp": 1744945668666, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1744945741026, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1744945829108, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1744945836954, "data": {"path": [12, 50], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1744945866968, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 5, "turnCount": null}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1744945975334, "data": {"path": [46, 45, 31, 32, 58], "travelCards": ["T7", "T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 5, "turnIndex": 2, "timestamp": 1744946000777, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1744946005474, "data": {"eventId": "global-event-31", "eventName": "Himalayan NE", "eventEffect": "himalayan_ne"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1744946005474, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 6, "turnIndex": 0, "timestamp": 1744946007138, "data": {"path": [59, 23], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 6, "turnIndex": 0, "timestamp": 1744946010140, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 6, "turnIndex": 1, "timestamp": 1744946013142, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1744946046172, "data": {"path": [50, 12, 13, 14], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1744946052727, "data": {"journeyType": "inner", "journeyCardId": "JI7", "omRequirement": 1}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1744946074845, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 6, "turnCount": null}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1744946090953, "data": {"path": [58, 32, 31, 30], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1744946114744, "data": {"cardType": "travel", "drawnCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 6, "turnIndex": 2, "timestamp": 1744946121718, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1744946125835, "data": {"eventId": "global-event-9", "eventName": "Riots", "eventEffect": "riots_discard"}}, {"type": "riots_discard_cube", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 7, "turnIndex": 0, "timestamp": 1744946125835, "data": {"cubeType": "bhakti"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1744946125836, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 7, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 7, "turnIndex": 0, "timestamp": 1744946127823, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 7, "turnIndex": 0, "timestamp": 1744946130826, "data": {"path": [23, 38], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 7, "turnIndex": 1, "timestamp": 1744946133828, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744946256424, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744946307779, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1744946323403, "data": {"path": [14, 13, 12, 11, 10, 49], "travelCards": ["T9", "T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1744946332504, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 7, "turnCount": null}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 7, "turnIndex": 2, "timestamp": 1744946387022, "data": {"path": [30, 31, 65, 37], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1744946402119, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1744946402119, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 8, "turnIndex": 0, "timestamp": 1744946403959, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 8, "turnIndex": 0, "timestamp": 1744946406962, "data": {"path": [38, 39], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 8, "turnIndex": 1, "timestamp": 1744946409963, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1744946446542, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1744946466758, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "region_based_reward", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1744946532259, "data": {"region": "West", "vehicle": "camel", "effect": "sandy_west", "innerPoints": 7}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1744946532259, "data": {"path": [49, 10, 11, 7, 6], "travelCards": ["T9", "T1"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1744946542206, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 8, "turnCount": null}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1744946558762, "data": {"path": [37, 65, 62, 19], "travelCards": ["T2", "T7"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 8, "turnIndex": 2, "timestamp": 1744946567213, "data": {"journeyType": "inner", "journeyCardId": "JI10", "omRequirement": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1744946570107, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1744946570107, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 9, "turnIndex": 0, "timestamp": 1744946572393, "data": {"path": [39, 65, 62, 13], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 9, "turnIndex": 0, "timestamp": 1744946575396, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 9, "turnIndex": 1, "timestamp": 1744946578398, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "move", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1744946738865, "data": {"path": [6, 61], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1744946749465, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1744946753645, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "kLSDH_K2B6i1Cc6HAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1744946755509, "data": {"nextPlayerId": "B9NHUMgInroNWNlmAAAD", "newRound": false, "roundCount": 9, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1744946826242, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1744946833718, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 9, "turnIndex": 2, "timestamp": 1744946850383, "data": {"path": [19, 62, 64, 15, 5, 52], "travelCards": ["T8", "T11"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1744946858379, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "endTurn", "playerId": "B9NHUMgInroNWNlmAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1744946858379, "data": {"nextPlayerId": "IvKpgNn9gpO_bX1aAAAB", "newRound": true, "roundCount": 10, "turnCount": null}}, {"type": "move", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 10, "turnIndex": 0, "timestamp": 1744946860139, "data": {"path": [13, 34, 33], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "IvKpgNn9gpO_bX1aAAAB", "playerName": "EternalPathfinder", "roundCount": 10, "turnIndex": 1, "timestamp": 1744946863141, "data": {"nextPlayerId": "kLSDH_K2B6i1Cc6HAAAF", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 1, "timestamp": 1744947258102, "data": {"timestamp": 1744947258102, "loadedStateRoundCount": 10, "playerCount": 3}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "E8Zd4T1gkK1NWuhtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1744947435856, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "E8Zd4T1gkK1NWuhtAAAD", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1744947439028, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 1, "timestamp": 1744947580598, "data": {"timestamp": 1744947580598, "loadedStateRoundCount": 10, "playerCount": 3}}, {"type": "endTurn", "playerId": "4yZgHr_3TaQXLkrHAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1744947591885, "data": {"nextPlayerId": "P25pR2Rrwc3YEpg8AAAF", "newRound": false, "roundCount": 10, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "P25pR2Rrwc3YEpg8AAAF", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1744947658886, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "P25pR2Rrwc3YEpg8AAAF", "playerName": "<PERSON><PERSON>", "roundCount": 10, "turnIndex": 2, "timestamp": 1744947664919, "data": {"path": [52, 5], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1744947667840, "data": {"eventId": "global-event-29", "eventName": "Solar South", "eventEffect": "solar_south"}}, {"type": "endTurn", "playerId": "P25pR2Rrwc3YEpg8AAAF", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1744947667840, "data": {"nextPlayerId": "rSbUjcWqvMZjhbf7AAAD", "newRound": true, "roundCount": 11, "turnCount": null}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "rSbUjcWqvMZjhbf7AAAD", "playerName": "EternalPathfinder", "roundCount": 11, "turnIndex": 0, "timestamp": 1744947669518, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "rSbUjcWqvMZjhbf7AAAD", "playerName": "EternalPathfinder", "roundCount": 11, "turnIndex": 1, "timestamp": 1744947672520, "data": {"nextPlayerId": "4yZgHr_3TaQXLkrHAAAB", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "collectJourney", "playerId": "4yZgHr_3TaQXLkrHAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1744948063489, "data": {"journeyType": "outer", "journeyCardId": "JO11", "omRequirement": 1}}, {"type": "endTurn", "playerId": "4yZgHr_3TaQXLkrHAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1744948071747, "data": {"nextPlayerId": "P25pR2Rrwc3YEpg8AAAF", "newRound": false, "roundCount": 11, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 2, "timestamp": 1744948112560, "data": {"timestamp": 1744948112560, "loadedStateRoundCount": 11, "playerCount": 3}}, {"type": "move", "playerId": "NqwvtHfQhHF-hoGaAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1744948125988, "data": {"path": [5, 15], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "NqwvtHfQhHF-hoGaAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 11, "turnIndex": 2, "timestamp": 1744948156668, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1744948160573, "data": {"eventId": "global-event-30", "eventName": "Breezy East", "eventEffect": "breezy_east"}}, {"type": "endTurn", "playerId": "NqwvtHfQhHF-hoGaAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1744948160574, "data": {"nextPlayerId": "bxSSov-hJfc1P7PYAAAD", "newRound": true, "roundCount": 12, "turnCount": null}}, {"type": "endTurn", "playerId": "bxSSov-hJfc1P7PYAAAD", "playerName": "EternalPathfinder", "roundCount": 12, "turnIndex": 1, "timestamp": 1744948162234, "data": {"nextPlayerId": "EFQ2cVSAZWWNYuGaAAAF", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "EFQ2cVSAZWWNYuGaAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1744948272063, "data": {"path": [25, 27, 16], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EFQ2cVSAZWWNYuGaAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1744948279541, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "EFQ2cVSAZWWNYuGaAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1744948282035, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "EFQ2cVSAZWWNYuGaAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1744948283500, "data": {"nextPlayerId": "NqwvtHfQhHF-hoGaAAAB", "newRound": false, "roundCount": 12, "turnCount": null}}, {"type": "move", "playerId": "NqwvtHfQhHF-hoGaAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1744948298221, "data": {"path": [15, 64, 62, 11, 10], "travelCards": ["T11", "T4"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "NqwvtHfQhHF-hoGaAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1744948306473, "data": {"journeyType": "inner", "journeyCardId": "JI6", "omRequirement": 1}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "NqwvtHfQhHF-hoGaAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 12, "turnIndex": 2, "timestamp": 1744948306473, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 100, "playerOmTotal": 4, "finalRoundStarter": 2, "finalRoundEnd": 1}}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1744948310659, "data": {"eventId": "global-event-13", "eventName": "<PERSON><PERSON> of Valor", "eventEffect": "steed_of_valor_reward"}}, {"type": "endTurn", "playerId": "NqwvtHfQhHF-hoGaAAAB", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1744948310659, "data": {"nextPlayerId": "bxSSov-hJfc1P7PYAAAD", "newRound": true, "roundCount": 13, "turnCount": null}}, {"type": "endTurn", "playerId": "bxSSov-hJfc1P7PYAAAD", "playerName": "EternalPathfinder", "roundCount": 13, "turnIndex": 1, "timestamp": 1744948313138, "data": {"nextPlayerId": "EFQ2cVSAZWWNYuGaAAAF", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "endTurn", "playerId": "EFQ2cVSAZWWNYuGaAAAF", "playerName": "<PERSON><PERSON>", "roundCount": 13, "turnIndex": 2, "timestamp": 1744948395523, "data": {"nextPlayerId": "NqwvtHfQhHF-hoGaAAAB", "newRound": false, "roundCount": 13, "turnCount": null}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 2, "timestamp": 1744948395526, "data": {"winner": {"id": "NqwvtHfQhHF-hoGaAAAB", "name": "<PERSON><PERSON>", "outerScore": 44, "innerScore": 56, "totalScore": 100, "omTotal": 4}, "totalRounds": 13, "players": [{"id": "bxSSov-hJfc1P7PYAAAD", "name": "EternalPathfinder", "outerScore": 24, "innerScore": 5, "totalScore": 29, "omTotal": 2}, {"id": "EFQ2cVSAZWWNYuGaAAAF", "name": "<PERSON><PERSON>", "outerScore": 25, "innerScore": 74, "totalScore": 99, "omTotal": 4}, {"id": "NqwvtHfQhHF-hoGaAAAB", "name": "<PERSON><PERSON>", "outerScore": 44, "innerScore": 56, "totalScore": 100, "omTotal": 4}]}}], "characterDeck": [{"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}], "energyCubePile": {"artha": 5, "karma": 6, "gnana": 6, "bhakti": 7}, "currentGlobalEvent": {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, "globalEventDeck": [{"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}], "globalEventDiscard": [{"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}], "nameMode": false}