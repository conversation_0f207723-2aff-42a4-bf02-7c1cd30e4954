/**
 * <PERSON><PERSON>t to start a standalone server for simulation testing
 * 
 * Usage:
 * node start_test_server.js
 * 
 * This will start the server on port 4000 by default
 */

const { startServer } = require('./start_server');

// Start the server with default configuration
const PORT = process.env.PORT || 4000;

startServer(PORT)
  .then(() => {
    console.log(`Test server started on port ${PORT}`);
    console.log(`You can now run the simulation client with:`);
    console.log(`node test_client.js http://localhost:${PORT}`);
  })
  .catch(err => {
    console.error('Failed to start test server:', err);
    process.exit(1);
  }); 