const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

(async () => {
  // Dimensions & DPI
  const DPI = 300;
  const widthIn = 5, heightIn = 24;
  const width = widthIn * DPI;
  const height = heightIn * DPI;

  // Track styling
  const trackThickness = Math.round(DPI * 0.1); // ~0.1in thick
  const marginIn = Math.round(DPI * 0.2);       // ~0.2in margin to inner loop
  const innerOffset = marginIn;
  const gapBetween = Math.round(DPI * 0.05);    // ~0.05in gap between loops
  const outerOffset = innerOffset + trackThickness + gapBetween;

  // Colors from main.css
  const outerColor = 'rgba(56,142,60,0.95)';    // --outer-color
  const innerColor = 'rgba(25,118,210,0.95)';   // --inner-color

  // Tick & label sizes
  const tickLen = trackThickness;
  const smallTickW = Math.ceil(trackThickness * 0.3);
  const medTickW   = Math.ceil(trackThickness * 0.6);
  const largeTickW = trackThickness;
  const labelSmall = Math.round(trackThickness * 0.8);
  const labelMed   = Math.round(trackThickness * 1.0);
  const labelNorm  = Math.round(trackThickness * 1.2);
  const labelPad   = Math.round(trackThickness * 1.5);

  // Build SVG container
  let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  svg += `<style>
    .outer-track{fill:none;stroke:${outerColor};stroke-width:${trackThickness}} 
    .inner-track{fill:none;stroke:${innerColor};stroke-width:${trackThickness}} 
    .tick-small{stroke:#666;stroke-width:${smallTickW}} 
    .tick-medium{stroke:#444;stroke-width:${medTickW}} 
    .tick-large{stroke:#000;stroke-width:${largeTickW}} 
    text{font-family:Poppins,sans-serif;fill:#333;font-size:${labelSmall}px;text-anchor:middle;dominant-baseline:middle} 
    .label-med{font-size:${labelMed}px} 
    .label-norm{font-size:${labelNorm}px} 
    .highlight{fill:#FF6584;font-weight:bold}
  </style>`;

  // Draw the two loops as paths
  svg += `<path class="outer-track" d="M${outerOffset},${height-outerOffset} L${outerOffset},${outerOffset} L${width-outerOffset},${outerOffset} L${width-outerOffset},${height-outerOffset}"/>`;
  svg += `<path class="inner-track" d="M${width-innerOffset},${height-innerOffset} L${width-innerOffset},${innerOffset} L${innerOffset},${innerOffset} L${innerOffset},${height-innerOffset}"/>`;

  // Tick & label for every number 0–100 on both loops
  for (let n = 0; n <= 100; n++) {
    // OUTER loop position
    let x, y, rot;
    if (n <= 25) {
      // left side, bottom→top
      x = outerOffset;
      y = height - outerOffset - (n / 25) * (height - 2 * outerOffset);
      rot = -90;
    } else if (n <= 75) {
      // top side, left→right
      x = outerOffset + ((n - 25) / 50) * (width - 2 * outerOffset);
      y = outerOffset;
      rot = 0;
    } else {
      // right side, top→bottom
      x = width - outerOffset;
      y = outerOffset + ((n - 75) / 25) * (height - 2 * outerOffset);
      rot = 90;
    }
    // Tick classification
    let tickClass = 'tick-small';
    if (n % 10 === 0) tickClass = 'tick-medium';
    if (n === 50) tickClass = 'tick-large';
    // Tick endpoints
    let x2, y2;
    if (rot === -90) {
      x2 = x - tickLen;
      y2 = y;
    } else if (rot === 90) {
      x2 = x + tickLen;
      y2 = y;
    } else {
      x2 = x;
      y2 = y - tickLen;
    }
    svg += `<line class="${tickClass}" x1="${x}" y1="${y}" x2="${x2}" y2="${y2}"/>`;
    // Label
    const cls = n === 50 ? 'highlight' : (n % 10 === 0 ? 'label-norm' : 'label-med');
    let lx = x + (rot === 0 ? 0 : (rot > 0 ? tickLen + labelPad : -tickLen - labelPad));
    let ly = y + (rot === 0 ? -tickLen - labelPad : 0);
    svg += `<text class="${cls}" transform="rotate(${rot},${x},${y})" x="${lx}" y="${ly}">${n}</text>`;

    // INNER loop position (mirrored direction)
    if (n <= 25) {
      x = width - innerOffset;
      y = height - innerOffset - (n / 25) * (height - 2 * innerOffset);
      rot = 90;
    } else if (n <= 75) {
      x = width - innerOffset - ((n - 25) / 50) * (width - 2 * innerOffset);
      y = innerOffset;
      rot = 0;
    } else {
      x = innerOffset;
      y = innerOffset + ((n - 75) / 25) * (height - 2 * innerOffset);
      rot = -90;
    }
    // Tick
    tickClass = 'tick-small';
    if (n % 10 === 0) tickClass = 'tick-medium';
    if (n === 50) tickClass = 'tick-large';
    if (rot === -90) {
      x2 = x - tickLen;
      y2 = y;
    } else if (rot === 90) {
      x2 = x + tickLen;
      y2 = y;
    } else {
      x2 = x;
      y2 = y - tickLen;
    }
    svg += `<line class="${tickClass}" x1="${x}" y1="${y}" x2="${x2}" y2="${y2}"/>`;
    // Label
    lx = x + (rot === 0 ? 0 : (rot > 0 ? -tickLen - labelPad : tickLen + labelPad));
    ly = y + (rot === 0 ? tickLen + labelPad : 0);
    svg += `<text class="${cls}" transform="rotate(${rot},${x},${y})" x="${lx}" y="${ly}">${n}</text>`;
  }

  svg += `</svg>`;

  // Ensure output directory exists
  const outPath = path.join(__dirname, 'score-track.png');
  await sharp(Buffer.from(svg)).png().toFile(outPath);
  console.log('Generated score track image at', outPath);
})(); 