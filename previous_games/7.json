{"description": "First time both players reached convergence where <PERSON><PERSON><PERSON> won by getting a 30 point card on the very last turn with the location having the exact cube", "players": [{"id": "wsn4AGGoHhnzqNKUAAAD", "name": "<PERSON><PERSON><PERSON>", "position": 41, "hand": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "energyCubes": ["karma"], "omTemp": [], "omSlotsOuter": [1, 0, 0, 0], "omSlotsInner": [1, 1, 2, 0], "collectedJourneys": [{"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JO15", "locationId": 30, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}], "outerScore": 34, "innerScore": 82, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "0ecABn_ELBq3cOfpAAAB", "name": "Mu<PERSON><PERSON>", "position": 28, "hand": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "energyCubes": [], "omTemp": [], "omSlotsOuter": [1, 1, 0, 0], "omSlotsInner": [1, 1, 0, 0], "collectedJourneys": [{"id": "JO11", "locationId": 25, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JI3", "locationId": 4, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}], "outerScore": 55, "innerScore": 66, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}}], "started": true, "turnIndex": 0, "roundCount": 14, "travelDeck": [], "eventDeck": [{"id": "E6", "type": "wildCube"}, {"id": "E5", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}, {"id": "E3", "type": "extraHop"}, {"id": "E7", "type": "wildCube"}], "journeyDeckInner": [{"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI13", "locationId": 23, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}], "journeyDeckOuter": [{"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO16", "locationId": 31, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO8", "locationId": 17, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO19", "locationId": 35, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO14", "locationId": 29, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO22", "locationId": 43, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO5", "locationId": 11, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO4", "locationId": 9, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO7", "locationId": 13, "required": {"karma": 1}, "reward": {"outer": 20}}], "travelDiscard": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "faceUpEvent": [{"id": "E9", "type": "wildCube"}, {"id": "E8", "type": "wildCube"}, {"id": "E4", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI19", "locationId": 40, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 30}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "faceUpJourneyOuter": [{"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 30}}, {"id": "JO1", "locationId": 2, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "locationCubes": {"1": "gnana", "2": "artha", "5": "artha", "8": "gnana", "9": "artha", "11": "gnana", "12": "karma", "14": "bhakti", "17": "karma", "19": "karma", "22": "karma", "23": "gnana", "24": "gnana", "27": "bhakti", "29": "gnana", "30": "gnana", "31": "gnana", "32": "karma", "33": "gnana", "34": "bhakti", "35": "gnana", "36": "artha", "37": "bhakti", "38": "bhakti", "39": "artha", "40": "artha", "43": "karma", "44": "artha", "46": "artha", "48": "karma", "undefined": "artha"}, "locationOm": {"49": true, "51": false, "55": true, "56": false, "58": true}, "finalRound": true, "finalRoundStarter": 0, "finalRoundEnd": 1, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724323093, "data": {"eventId": "global-event-10", "eventName": "Om Meditation", "eventEffect": "om_meditation"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "e9oiZ_4uMsksZtxxAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724366802, "data": {"cardType": "travel", "card": {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "e9oiZ_4uMsksZtxxAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724366802, "data": {"cardType": "travel", "card": {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "-cvWazLfCMeGtiexAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724366802, "data": {"cardType": "travel", "card": {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "-cvWazLfCMeGtiexAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724366802, "data": {"cardType": "travel", "card": {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724511939, "data": {"timestamp": 1743724511939, "loadedStateRoundCount": 0, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724633430, "data": {"cardType": "travel", "pickedCards": [{"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724643823, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724654322, "data": {"path": [63, 20], "travelCards": ["T3"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 0, "turnIndex": 0, "timestamp": 1743724656676}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743724891674, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743724900617, "data": {"path": [61, 4], "travelCards": ["T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 0, "turnIndex": 1, "timestamp": 1743724902470}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 1, "turnIndex": 0, "timestamp": 1743724902470, "data": {"eventId": "global-event-30", "eventName": "Breezy East", "eventEffect": "breezy_east"}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743724946314, "data": {"path": [20, 21], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743724949890, "data": {"journeyType": "inner", "journeyCardId": "JI12", "omRequirement": 1}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 1, "turnIndex": 0, "timestamp": 1743724954642}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743725157801, "data": {"path": [4, 61, 63, 25], "travelCards": ["T12"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743725161492, "data": {"journeyType": "outer", "journeyCardId": "JO11", "omRequirement": 1}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 1, "turnIndex": 1, "timestamp": 1743725172201}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 2, "turnIndex": 0, "timestamp": 1743725172201, "data": {"eventId": "global-event-1", "eventName": "Drizzle of Delay", "eventEffect": "max_moves_2_and_cost_artha_north_east"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743725287210, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743725294355, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743725329198, "data": {"path": [21, 28, 18], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 2, "turnIndex": 0, "timestamp": 1743725339169}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743725403092, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743725405681, "data": {"cardType": "travel", "drawnCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickFromTop": true}}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743725414853, "data": {"path": [25, 26], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 2, "turnIndex": 1, "timestamp": 1743725418088}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 3, "turnIndex": 0, "timestamp": 1743725418088, "data": {"eventId": "global-event-24", "eventName": "Professor's Insight", "eventEffect": "professors_insight_reward"}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743725480364, "data": {"path": [18, 53], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743725534539, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743725539976, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 3, "turnIndex": 0, "timestamp": 1743725543356}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743725611980, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743725624604, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743725641253, "data": {"path": [26, 25, 24, 23, 59], "travelCards": ["T12", "T4"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 3, "turnIndex": 1, "timestamp": 1743725644932}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 4, "turnIndex": 0, "timestamp": 1743725644932, "data": {"eventId": "global-event-20", "eventName": "Scenic Cruise", "eventEffect": "scenic_cruise_reward"}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743725713238, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743725729601, "data": {"vehicle": "boat", "effect": "scenic_cruise_reward", "outerPoints": 5}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743725729601, "data": {"path": [53, 18, 15, 5, 52], "travelCards": ["T10", "T1"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743725747351, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 4, "turnIndex": 0, "timestamp": 1743725756432}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743725769920, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743725813502, "data": {"vehicle": "boat", "effect": "scenic_cruise_reward", "outerPoints": 5}}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743725813502, "data": {"path": [59, 23, 24, 63, 61, 4], "travelCards": ["T10", "T7"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 4, "turnIndex": 1, "timestamp": 1743725844276}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 5, "turnIndex": 0, "timestamp": 1743725844276, "data": {"eventId": "global-event-2", "eventName": "<PERSON>wal<PERSON> Distraction", "eventEffect": "gain_5_inner_no_cube_pickup"}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743725874448, "data": {"path": [52, 5, 15, 64, 65, 31, 30], "travelCards": ["T9", "T12"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743725886266, "data": {"journeyType": "outer", "journeyCardId": "JO15", "omRequirement": 1}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 5, "turnIndex": 0, "timestamp": 1743725897946}, {"type": "collectJourney", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743725908660, "data": {"journeyType": "inner", "journeyCardId": "JI3", "omRequirement": 1}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 5, "turnIndex": 1, "timestamp": 1743725911208}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 6, "turnIndex": 0, "timestamp": 1743725911208, "data": {"eventId": "global-event-18", "eventName": "Hop on Hop off", "eventEffect": "hop_on_hop_off_reward"}}, {"type": "PICK_DECK_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743726005504, "data": {"cardType": "travel", "drawnCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickFromTop": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743726014553, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "travel_card_reward", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743726083147, "data": {"vehicle": "bus", "effect": "hop_on_hop_off_reward", "outerPoints": 5}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743726083147, "data": {"path": [30, 31, 45], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 6, "turnIndex": 0, "timestamp": 1743726088777}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743726163113, "data": {"path": [4, 3, 47, 57], "travelCards": ["T11"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743726175348, "data": {"cardType": "travel", "pickedCards": [{"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}], "pickedFromFaceUp": true}}, {"type": "PICK_DECK_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743726179823, "data": {"cardType": "travel", "drawnCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 6, "turnIndex": 1, "timestamp": 1743726199390}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 7, "turnIndex": 0, "timestamp": 1743726199390, "data": {"eventId": "global-event-28", "eventName": "<PERSON>", "eventEffect": "sandy_west"}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743726245554, "data": {"path": [45, 44, 43, 60], "travelCards": ["T9"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743726298086, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743726328311, "data": {"cardType": "travel", "pickedCards": [{"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 7, "turnIndex": 0, "timestamp": 1743726333377}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743726358033, "data": {"cardType": "travel", "pickedCards": [{"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}], "pickedFromFaceUp": true}}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743726368364, "data": {"path": [57, 47], "travelCards": ["T1"], "extraHopCards": []}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 7, "turnIndex": 1, "timestamp": 1743726373630}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 8, "turnIndex": 0, "timestamp": 1743726373630, "data": {"eventId": "global-event-14", "eventName": "Desert Caravan", "eventEffect": "desert_caravan_reward"}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743726451750, "data": {"path": [60, 43, 42], "travelCards": ["T7"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743726525375, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743726530505, "data": {"cardType": "travel", "pickedCards": [{"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 8, "turnIndex": 0, "timestamp": 1743726532485}, {"type": "move", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743726569107, "data": {"path": [47, 3], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743726581295, "data": {"cardType": "travel", "pickedCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743726585853, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "xX9jUkd99x1icRF8AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 8, "turnIndex": 1, "timestamp": 1743726589048}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 0, "timestamp": 1743726589049, "data": {"eventId": "global-event-27", "eventName": "Frozen North", "eventEffect": "frozen_north"}}, {"type": "move", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743726644205, "data": {"path": [42, 66, 64, 15], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743726699438, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "wdg3CxXby63SwqYpAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 9, "turnIndex": 0, "timestamp": 1743726705058}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 9, "turnIndex": 1, "timestamp": 1743727064829, "data": {"timestamp": 1743727064829, "loadedStateRoundCount": 9, "playerCount": 2}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "4VItfFXE63mhRfD3AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743727098587, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "4VItfFXE63mhRfD3AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 9, "turnIndex": 1, "timestamp": 1743727102296}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 10, "turnIndex": 0, "timestamp": 1743727102296, "data": {"eventId": "global-event-22", "eventName": "Up and Over", "eventEffect": "up_and_over_reward"}}, {"type": "move", "playerId": "euT5IayXH9QmvJRRAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743727210102, "data": {"path": [15, 64, 62, 13, 14, 16], "travelCards": ["T10", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "euT5IayXH9QmvJRRAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743727219784, "data": {"journeyType": "inner", "journeyCardId": "JI9", "omRequirement": 1}}, {"type": "endTurn", "playerId": "euT5IayXH9QmvJRRAAAB", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 10, "turnIndex": 0, "timestamp": 1743727225840}, {"type": "move", "playerId": "4VItfFXE63mhRfD3AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743727263153, "data": {"path": [6, 7], "travelCards": ["T2"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "4VItfFXE63mhRfD3AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743727266848, "data": {"journeyType": "inner", "journeyCardId": "JI4", "omRequirement": 1}}, {"type": "endTurn", "playerId": "4VItfFXE63mhRfD3AAAD", "playerName": "Mu<PERSON><PERSON>", "roundCount": 10, "turnIndex": 1, "timestamp": 1743727289598}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743727289598, "data": {"eventId": "global-event-3", "eventName": "<PERSON><PERSON>", "eventEffect": "jyotirlinga_7_inner_or_bonus_cube"}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 11, "turnIndex": 0, "timestamp": 1743727427479, "data": {"timestamp": 1743727427479, "loadedStateRoundCount": 11, "playerCount": 2}}, {"type": "move", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743727559534, "data": {"path": [16, 14, 13, 12, 50], "travelCards": ["T11", "T4"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743727605626, "data": {"cardType": "travel", "pickedCards": [{"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743727607945, "data": {"cardType": "travel", "pickedCards": [{"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 11, "turnIndex": 0, "timestamp": 1743727609174}, {"type": "move", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743727627271, "data": {"path": [7, 8, 54], "travelCards": ["T5"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743727633578, "data": {"cardType": "travel", "pickedCards": [{"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743727642615, "data": {"cardType": "travel", "pickedCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 11, "turnIndex": 1, "timestamp": 1743727645763}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 12, "turnIndex": 0, "timestamp": 1743727645763, "data": {"eventId": "global-event-25", "eventName": "<PERSON><PERSON><PERSON>'s Grace", "eventEffect": "pilgrims_grace_reward"}}, {"type": "move", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743727683279, "data": {"path": [50, 12, 13], "travelCards": ["T6"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743727697239, "data": {"cardType": "travel", "pickedCards": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "pickedFromFaceUp": true}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743727699696, "data": {"cardType": "travel", "pickedCards": [{"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 12, "turnIndex": 0, "timestamp": 1743727703614}, {"type": "move", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743727855427, "data": {"path": [54, 8, 7, 11, 10], "travelCards": ["T9", "T2"], "extraHopCards": []}}, {"type": "PICK_DECK_CARDS", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743727911586, "data": {"cardType": "travel", "drawnCards": [{"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}], "pickFromTop": true}}, {"type": "PICK_DECK_CARDS", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743727912539, "data": {"cardType": "travel", "drawnCards": [{"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}], "pickFromTop": true}}, {"type": "endTurn", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 12, "turnIndex": 1, "timestamp": 1743727914588}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 13, "turnIndex": 0, "timestamp": 1743727914588, "data": {"eventId": "global-event-17", "eventName": "Top Gear", "eventEffect": "top_gear_reward"}}, {"type": "move", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743727951688, "data": {"path": [13, 62, 66, 42, 48, 41], "travelCards": ["T11", "T7"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743727955192, "data": {"journeyType": "inner", "journeyCardId": "JI20", "omRequirement": 2}}, {"type": "FINAL_ROUND_TRIGGERED", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743727955192, "data": {"winCondition": "SCORE_THRESHOLD", "playerScore": 116, "playerOmTotal": 5, "finalRoundStarter": 0, "finalRoundEnd": 1}}, {"type": "endTurn", "playerId": "wsn4AGGoHhnzqNKUAAAD", "playerName": "<PERSON><PERSON><PERSON>", "roundCount": 13, "turnIndex": 0, "timestamp": 1743727961978}, {"type": "move", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743727975747, "data": {"path": [10, 11, 62, 64, 29, 28], "travelCards": ["T12", "T8"], "extraHopCards": []}}, {"type": "collectJourney", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743727978085, "data": {"journeyType": "outer", "journeyCardId": "JO13", "omRequirement": 1}}, {"type": "endTurn", "playerId": "0ecABn_ELBq3cOfpAAAB", "playerName": "Mu<PERSON><PERSON>", "roundCount": 13, "turnIndex": 1, "timestamp": 1743727980417}, {"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1743727980417, "data": {"eventId": "global-event-12", "eventName": "Footpath Reverie", "eventEffect": "footpath_reverie_reward"}}, {"type": "GAME_OVER", "playerId": "system", "playerName": "System", "roundCount": 14, "turnIndex": 0, "timestamp": 1743727980419, "data": {"winner": {"id": "0ecABn_ELBq3cOfpAAAB", "name": "Mu<PERSON><PERSON>", "outerScore": 55, "innerScore": 66, "totalScore": 121, "omTotal": 4}, "totalRounds": 14, "players": [{"id": "wsn4AGGoHhnzqNKUAAAD", "name": "<PERSON><PERSON><PERSON>", "outerScore": 34, "innerScore": 82, "totalScore": 116, "omTotal": 5}, {"id": "0ecABn_ELBq3cOfpAAAB", "name": "Mu<PERSON><PERSON>", "outerScore": 55, "innerScore": 66, "totalScore": 121, "omTotal": 4}]}}], "characterDeck": [{"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 2 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 2 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 2 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 2 of (gnana, artha, karma) for 1 bhakti-cube"}], "energyCubePile": {"artha": 5, "karma": 6, "gnana": 5, "bhakti": 9}, "currentGlobalEvent": {"id": "global-event-12", "name": "Footpath Reverie", "text": "Use the Trek travel card for 5 inner points", "effect": "footpath_reverie_reward"}, "globalEventDeck": [{"id": "global-event-15", "name": "Biker Gang", "text": "Use the Motorbike travel card for 5 outer points", "effect": "biker_gang_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}, {"id": "global-event-19", "name": "Bullet Train", "text": "Use the Train travel card for 5 outer points", "effect": "bullet_train_reward"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-16", "name": "<PERSON><PERSON> Rhapsody", "text": "Use the Rickshaw travel card for 5 outer points", "effect": "rickshaw_rhapsody_reward"}, {"id": "global-event-31", "name": "Himalayan NE", "text": "If starting in NE; moves cost 2x; if moved, gain 7 inner points", "effect": "himalayan_ne"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-29", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-11", "name": "Pedal Power", "text": "Use the Cycle travel card for 5 outer points", "effect": "pedal_power_reward"}, {"id": "global-event-32", "name": "Central Heart", "text": "If starting in Central; travel to gain 5 outer points", "effect": "central_heart"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-13", "name": "<PERSON><PERSON> of Valor", "text": "Use the Horse travel card for 5 outer points", "effect": "steed_of_valor_reward"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}], "globalEventDiscard": [{"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-30", "name": "Breezy East", "text": "If starting in East; boat or hike get 7 inner points; others lose 1 travel card", "effect": "breezy_east"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-20", "name": "Scenic Cruise", "text": "Use the Boat travel card for 5 outer points", "effect": "scenic_cruise_reward"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-18", "name": "Hop on Hop off", "text": "Use the Bus travel card for 5 outer points", "effect": "hop_on_hop_off_reward"}, {"id": "global-event-28", "name": "<PERSON>", "text": "If starting in West; camel rides get 7 inner points; others lose 1 travel card", "effect": "sandy_west"}, {"id": "global-event-14", "name": "Desert Caravan", "text": "Use the Camel travel card for 5 outer points", "effect": "desert_caravan_reward"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-22", "name": "Up and Over", "text": "Use the Helicopter travel card for 5 outer points", "effect": "up_and_over_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-17", "name": "Top Gear", "text": "Use the Car travel card for 5 outer points", "effect": "top_gear_reward"}]}