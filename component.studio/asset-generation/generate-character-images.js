const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, '../../character-images');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Define character data
const characters = [
  {
    id: 'pilgrim',
    type: 'Pilgrim',
    description: 'Can trade any 1 energy cube for 1 Bhakti cube.',
    ability: {
      takes: ['Any'],
      gives: 'Bhakti'
    }
  },
  {
    id: 'merchant',
    type: 'Merchant',
    description: 'Can trade any 1 energy cube for 1 Artha cube.',
    ability: {
      takes: ['Any'],
      gives: 'Artha'
    }
  },
  {
    id: 'professor',
    type: 'Professor',
    description: 'Can trade any 1 energy cube for 1 Gnana cube.',
    ability: {
      takes: ['Any'],
      gives: 'Gnana'
    }
  },
  {
    id: 'engineer',
    type: 'Engineer',
    description: 'Can trade any 1 energy cube for 1 Karma cube.',
    ability: {
      takes: ['Any'],
      gives: 'Karma'
    }
  }
];

// Color definitions from client/src/styles/main.css
const colors = {
  // Base colors
  primaryColor: '#4a6741',
  accentColor: '#375a60',

  // Energy Colors
  arthaColor: '#F39C12', // Orange/yellow for artha
  karmaColor: '#27AE60', // Green for karma
  gnanaColor: '#2980B9', // Blue for gnana
  bhaktiColor: '#8E44AD', // Purple for bhakti
  anyColor: '#999999', // Gray for "any" cube
};

// Helper function to wrap text into multiple lines
function wrapTextIntoLines(text, maxCharsPerLine) {
  if (!text) return [''];

  // Escape special characters for XML
  text = text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');

  const words = text.split(' ');
  const lines = [];
  let currentLine = '';

  words.forEach(word => {
    // If adding this word would exceed the max length, start a new line
    if (currentLine.length + word.length + 1 > maxCharsPerLine && currentLine.length > 0) {
      lines.push(currentLine);
      currentLine = word;
    } else {
      // Add the word to the current line (with a space if not the first word)
      currentLine = currentLine.length === 0 ? word : `${currentLine} ${word}`;
    }
  });

  // Add the last line if it's not empty
  if (currentLine.length > 0) {
    lines.push(currentLine);
  }

  return lines;
}

// Function to create a character card image
async function createCharacterImage(character) {
  try {
    // Define dimensions
    const width = 825;
    const height = 1125;

    // Check if the original character image exists
    const characterType = character.id.toLowerCase();
    const originalImagePath = path.join(__dirname, '../../client/public/assets/images/characters', `${characterType}.jpg`);
    let hasOriginalImage = false;

    try {
      if (fs.existsSync(originalImagePath)) {
        hasOriginalImage = true;
        console.log(`Found original image for character ${character.type}: ${originalImagePath}`);
      } else {
        console.log(`Original image not found for character ${character.type}: ${originalImagePath}`);
      }
    } catch (err) {
      console.log(`Error checking original image for character ${character.type}: ${err.message}`);
    }

    // Create SVG for the card content
    const svgContent = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <style>
          @font-face {
            font-family: 'Arial';
            font-style: normal;
            font-weight: normal;
          }
          text {
            font-family: 'Arial', sans-serif;
          }
        </style>

        <!-- Card background -->
        <rect x="0" y="0" width="${width}" height="${height}" fill="white" rx="20" ry="20" />

        <!-- Card border -->
        <rect x="10" y="10" width="${width - 20}" height="${height - 20}" fill="none" stroke="${colors.primaryColor}" stroke-width="8" rx="15" ry="15" />

        <!-- Header -->
        <rect x="40" y="40" width="${width - 80}" height="100" fill="${colors.accentColor}" rx="10" ry="10" />
        <text x="${width/2}" y="100" font-size="48" fill="white" text-anchor="middle" font-weight="bold">${character.type}</text>

        <!-- Image placeholder (center of card) -->
        <rect x="100" y="180" width="${width - 200}" height="500" fill="white" stroke="${colors.primaryColor}" stroke-width="4" rx="10" ry="10" />

        <!-- Trading ability title -->
        <text x="${width/2}" y="730" font-size="32" fill="${colors.accentColor}" text-anchor="middle" font-weight="bold">Trading Ability</text>

        <!-- Trading ability description -->
        ${wrapTextIntoLines(character.description, 40).map((line, i) =>
          `<text x="${width/2}" y="${780 + i * 40}" font-size="28" fill="#333" text-anchor="middle">
            ${line}
          </text>`
        ).join('')}

        <!-- Trading ability visualization -->
        <rect x="150" y="850" width="${width - 300}" height="150" fill="#f8f8f8" stroke="#ddd" stroke-width="2" rx="10" ry="10" />

        <!-- Takes section -->
        <text x="${width/2 - 150}" y="880" font-size="28" fill="#333" text-anchor="middle" font-weight="bold">Takes</text>
        ${character.ability.takes.map((cube, i) => {
          const cubeColor = colors[`${cube.toLowerCase()}Color`];
          const xPos = width/2 - 150 + (i - 0.5*(character.ability.takes.length-1)) * 50;
          return `
            <rect x="${xPos - 20}" y="900" width="40" height="40" fill="${cubeColor}" stroke="#333" stroke-width="1" rx="5" ry="5" />
            ${cube.toLowerCase() === 'any' ?
              `<text x="${xPos}" y="925" font-size="24" fill="white" text-anchor="middle" font-weight="bold">?</text>` : ''}
          `;
        }).join('')}

        <!-- Gives section -->
        <text x="${width/2 + 150}" y="880" font-size="28" fill="#333" text-anchor="middle" font-weight="bold">Gives</text>
        <rect x="${width/2 + 130}" y="900" width="40" height="40" fill="${colors[`${character.ability.gives.toLowerCase()}Color`]}" stroke="#333" stroke-width="1" rx="5" ry="5" />

        <!-- Arrow between takes and gives -->
        <path d="M ${width/2 - 50} 920 L ${width/2 + 50} 920" stroke="#333" stroke-width="3" />
        <path d="M ${width/2 + 40} 910 L ${width/2 + 50} 920 L ${width/2 + 40} 930" fill="none" stroke="#333" stroke-width="3" />

        <!-- Character ID for debugging -->
        <text x="50" y="${height - 50}" font-size="16" fill="#999" font-style="italic">ID: ${character.id}</text>
      </svg>
    `;

    // Save the output path
    const outputPath = path.join(outputDir, `${character.id}.png`);

    if (hasOriginalImage) {
      // Create the base image from SVG
      const baseImage = await sharp(Buffer.from(svgContent)).png().toBuffer();

      // Load and resize the original character image
      const characterImage = await sharp(originalImagePath)
        .resize({
          width: width - 200,
          height: 500,
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 }
        })
        .toBuffer();

      // Composite the original image onto the card
      await sharp(baseImage)
        .composite([
          {
            input: characterImage,
            top: 180,
            left: 100
          }
        ])
        .toFile(outputPath);
    } else {
      // Just use the SVG without the original image
      await sharp(Buffer.from(svgContent))
        .png()
        .toFile(outputPath);
    }

    console.log(`Created image for character ${character.type} at ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`Error creating image for character ${character.type}:`, error);
    return false;
  }
}

// Process all character cards
async function processAllCharacters() {
  console.log(`Processing ${characters.length} character cards...`);

  let successCount = 0;
  let failCount = 0;

  for (const character of characters) {
    const success = await createCharacterImage(character);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  console.log(`\nProcessing complete!`);
  console.log(`Successfully created: ${successCount} images`);
  console.log(`Failed: ${failCount} images`);
  console.log(`Output directory: ${outputDir}`);

  // Create CSV file for component.studio
  createCsvFile();
}

// Create CSV file for component.studio
function createCsvFile() {
  const csvPath = path.join(__dirname, '../ch.csv');
  let csvContent = 'quantity,name,image\n';

  characters.forEach(character => {
    csvContent += `1,${character.id},{{ images.${character.id}.url }}\n`;
  });

  fs.writeFileSync(csvPath, csvContent);
  console.log(`Created CSV file at ${csvPath}`);
}

// Start processing
processAllCharacters();
