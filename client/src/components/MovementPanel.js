import React, { useState } from 'react';

function MovementPanel({ socket, currentPosition, onCancel }) {
  const [pathStr, setPathStr] = useState(currentPosition ? `${currentPosition}` : '');
  const [customError, setCustomError] = useState('');

  const handleMove = () => {
    // Validate the path
    const path = pathStr
      .split(',')
      .map((x) => parseInt(x.trim(), 10))
      .filter((x) => !isNaN(x));
      
    if (path.length < 2) {
      setCustomError('Path must include at least a start and end position');
      return;
    }
    
    if (path[0] !== currentPosition) {
      setCustomError(`Path must start from your current position (${currentPosition})`);
      return;
    }
    
    // Clear any previous errors
    setCustomError('');
    
    // Send the custom movement request with playerMoved event for animation
    socket.emit('customMove', { path, withAnimation: true });
    
    // Reset and close
    setPathStr('');
    if (onCancel) onCancel();
  };

  return (
    <div className="card" style={{ marginBottom: '1rem', padding: '1rem' }}>
      <h3>Custom Movement</h3>
      
      <p style={{ fontSize: '0.9rem', marginBottom: '0.75rem', color: '#666' }}>
        Enter a path of location IDs (comma separated).<br/>
        For example: "{currentPosition}, 4, 7" would move from your current position to node 4, then to node 7.
      </p>
      
      <div className="flex flex-col gap-md">
        <input
          type="text"
          placeholder={`e.g. ${currentPosition}, 2, 3`}
          value={pathStr}
          onChange={(e) => setPathStr(e.target.value)}
        />
        
        {customError && (
          <div style={{ color: 'red', fontSize: '0.9rem' }}>
            {customError}
          </div>
        )}
        
        <div className="flex gap-md">
          <button 
            className="secondary" 
            onClick={onCancel}
            style={{ flex: 1 }}
          >
            Cancel
          </button>
          <button 
            style={{ flex: 1 }}
            onClick={handleMove}
          >
            Move
          </button>
        </div>
      </div>
    </div>
  );
}

export default MovementPanel;