#!/usr/bin/env node

// split-board.js
// Splits board.jpg into 4 equal quadrants at original resolution.

const sharp = require('sharp');
const path = require('path');

// Configuration: a 2x2 split; will inspect original dimensions
const COLUMNS = 2;
const ROWS = 2;

(async () => {
  try {
    const inputPath = path.resolve(__dirname, 'board.jpg');
    // Inspect original dimensions of the board image
    const { width: fullWidth, height: fullHeight } = await sharp(inputPath).metadata();
    const sheetWidth = Math.floor(fullWidth / COLUMNS);
    const sheetHeight = Math.floor(fullHeight / ROWS);
    const fullImage = sharp(inputPath);

    let sheetIndex = 1;
    for (let row = 0; row < ROWS; row++) {
      for (let col = 0; col < COLUMNS; col++) {
        const left = col * sheetWidth;
        const top = row * sheetHeight;
        const width = sheetWidth;
        const height = sheetHeight;
        const outputPath = path.resolve(__dirname, `board_sheet_${sheetIndex}.jpg`);
        await fullImage.clone()
          .extract({ left, top, width, height })
          .jpeg({ quality: 90 })
          .toFile(outputPath);
        console.log(`Created ${path.basename(outputPath)} (${width}x${height}px)`);
        sheetIndex++;
      }
    }

    console.log('All sheets generated successfully.');
  } catch (error) {
    console.error('Error splitting board:', error);
    process.exit(1);
  }
})(); 